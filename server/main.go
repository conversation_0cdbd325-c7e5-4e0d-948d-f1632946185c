package main

import (
	"context"
	"fmt"
	"github.com/vendasta/atlas/internal/crmcustomobjects"
	crm "github.com/vendasta/crm/sdks/go"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/gorilla/sessions"
	"github.com/vendasta/IAM/sdks/go/iaminterceptor"
	"github.com/vendasta/iam-resources/applications/atlas-microservice"

	"github.com/vendasta/atlas/internal/emailverification"

	"cloud.google.com/go/pubsub"
	iamv2 "github.com/vendasta/IAM/sdks/go/v2"
	"github.com/vendasta/gosdks/serverconfig/v3"
	"google.golang.org/grpc"

	iam "github.com/vendasta/IAM/sdks/go/v1"
	accountgroup "github.com/vendasta/account-group/sdks/go/v1"
	accounts "github.com/vendasta/accounts/sdks/go/v1"
	domain "github.com/vendasta/domain/sdks/go/v2"
	atlas_v1 "github.com/vendasta/generated-protos-go/atlas/v1"
	"github.com/vendasta/gosdks/bifrost"
	"github.com/vendasta/gosdks/cache"
	"github.com/vendasta/gosdks/catalogue"
	"github.com/vendasta/gosdks/config"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/registration"
	"github.com/vendasta/gosdks/statsd"
	"github.com/vendasta/gosdks/vax"
	"github.com/vendasta/gosdks/verrors"
	group "github.com/vendasta/group/sdks/go/v2"
	marketplaceapps "github.com/vendasta/marketplace-apps/sdks/go/v2"
	multilocation "github.com/vendasta/multi-location/sdks/go/v1"
	partner "github.com/vendasta/partner/sdks/go/v1"
	partnerv2 "github.com/vendasta/partner/sdks/go/v2"
	serviceprovidersdk "github.com/vendasta/service-provider-sdk/v1"
	"github.com/vendasta/sso/sdks/go/sso"
	tesseract "github.com/vendasta/tesseract/sdks/go/v1"
	vstatic "github.com/vendasta/vstatic/sdks/go/v1"
	vstore "github.com/vendasta/vstore/vstore/sdks/go/v1"

	aiassistants "github.com/vendasta/ai-assistants/sdks/go"
	"github.com/vendasta/atlas/internal/accountsdata"
	"github.com/vendasta/atlas/internal/api"
	"github.com/vendasta/atlas/internal/branding"
	defaultlocation "github.com/vendasta/atlas/internal/default-location"
	featureflags "github.com/vendasta/atlas/internal/feature_flags"
	"github.com/vendasta/atlas/internal/lang"
	"github.com/vendasta/atlas/internal/location"
	"github.com/vendasta/atlas/internal/navbardata"
	"github.com/vendasta/atlas/internal/partnermarket"
	"github.com/vendasta/atlas/internal/pin"
	pinRepo "github.com/vendasta/atlas/internal/pin/repo"
	"github.com/vendasta/atlas/internal/product"
	"github.com/vendasta/atlas/internal/sidenavigation"
)

const (
	APP_NAME = "atlas"
	httpPort = 11001
	grpcPort = 11000
)

func main() {
	var err error
	ctx := context.Background()
	env := config.CurEnv()

	//Setup Application logging and switch the logger
	if !config.IsLocal() {
		namespace := config.GetGkeNamespace()
		podName := config.GetGkePodName()
		if err = logging.Initialize(namespace, podName, APP_NAME); err != nil {
			logging.Criticalf(ctx, "Error initializing logger: %s", err.Error())
			os.Exit(-1)
		}
	}

	//Setup StatsD Client
	if err = statsd.Initialize(APP_NAME, nil); err != nil {
		logging.Criticalf(ctx, "Error initializing statsd client: %s", err.Error())
		os.Exit(-1)
	}
	iamClient, err := iam.NewClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing iam client %s", err.Error())
		os.Exit(-1)
	}
	iamAtlasApp := atlas.AtlasMicroservice{}
	iamV2Client, closers, err := iamv2.NewClientV2(ctx, env, iamAtlasApp.AppID(), iamAtlasApp.AppName())
	defer func() {
		for i := range closers {
			err := closers[i]()
			if err != nil {
				logging.Errorf(ctx, fmt.Sprintf("failed to close iam client %d %s", i, err.Error()))
			}
		}
	}()
	if err != nil {
		logging.Criticalf(ctx, "Error initializing IAMv2 client %s", err.Error())
		os.Exit(-1)
	}

	iamAuthService := iam.NewAuthService(iamClient, nil)
	//Create IAM Auth
	iamInterceptors, err := iaminterceptor.NewIAMInterceptor(env, strings.Split(os.Getenv("PUBLIC_ROUTES"), ",")...)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing iam interceptor: %s", err.Error())
		os.Exit(-1)
	}
	var ii = iamInterceptors.ServerInterceptor()

	//Create Logging Interceptor
	var loggingInterceptor = logging.Interceptor()

	errorInterceptor := verrors.ErrorConverterServerInterceptor(verrors.DefaultErrorMask)

	//Create Timeout Interceptor
	var timeoutInterceptor = vax.TimeoutInterceptor(20 * time.Second)

	//Create a GRPC Server
	logging.Infof(ctx, "Creating GRPC server...")
	grpcServer := serverconfig.CreateGrpcServer(serverconfig.WithUnaryInterceptors(errorInterceptor, loggingInterceptor, ii, timeoutInterceptor))

	ssoIPClient, err := sso.NewIdentityProviderClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing sso client: %s", err.Error())
		os.Exit(-1)
	}
	var cacheClient cache.Client
	if config.IsLocal() {
		cacheClient = cache.NewClientStub()
	} else {
		cacheClient, err = cache.NewRedisClient(os.Getenv("MEMORYSTORE_HOST"), os.Getenv("MEMORYSTORE_PORT"))
		if err != nil {
			logging.Criticalf(ctx, "Error initializing thor client: %s", err.Error())
			os.Exit(-1)
		}
	}

	vstaticClient, err := vstatic.NewClient(ctx, "atlas-client", env)
	if err != nil {
		logging.Criticalf(ctx, "Error creating vstatic client: %s", err.Error())
		os.Exit(-1)
	}

	ssoSPClient, err := sso.NewServiceProviderClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing sso client: %s", err.Error())
		os.Exit(-1)
	}
	serviceProviderServer, err := serviceprovidersdk.NewServiceProviderServer(env, ssoSPClient, "atlas")
	if err != nil {
		logging.Criticalf(ctx, "Error initializing Service Provider Server: %s", err.Error())
		os.Exit(-1)
	}

	tesseractClient, err := tesseract.NewClient(ctx, config.CurEnv(), grpc.WithUnaryInterceptor(logging.ClientInterceptor()))
	if err != nil {
		logging.Criticalf(ctx, "Failed to initialize tesseract client because %s", err.Error())
		os.Exit(-1)
	}

	partnerClient, err := partner.NewClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing partner whitelabel client: %s", err.Error())
		os.Exit(-1)
	}

	brandingClient, _, err := partnerv2.NewBrandingV2ServiceClientV3(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing Branding client: %s", err.Error())
		os.Exit(-1)
	}

	whitelabelClient, _, err := partnerv2.NewWhitelabelClientV3(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing Whitelabel client: %s", err.Error())
		os.Exit(-1)
	}

	accountGroupClient, err := accountgroup.NewClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing Account Group client: %s", err.Error())
		os.Exit(-1)
	}
	groupClient, err := group.NewClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing Group client: %s", err.Error())
		os.Exit(-1)
	}

	multiLocationClient, err := multilocation.NewMultiLocationClient(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "error registering multi-location client %s", err.Error())
		os.Exit(9)
	}

	crmCustomTypeService, _, err := crm.NewCrmCustomObjectTypeServiceClientV3(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "error registering crm custom object type client %s", err.Error())
		os.Exit(-1)
	}

	locationDataFetcher := partnermarket.NewLocationDataFetcher(accountGroupClient, groupClient, multiLocationClient)

	domainClient, err := domain.NewClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing Domain client: %s", err.Error())
		os.Exit(-1)
	}

	vStoreNamespace := vstore.Namespace(config.CurEnv(), APP_NAME)
	var vstoreOptions []vstore.Option
	if config.IsLocal() {
		vstoreOptions = []vstore.Option{vstore.UseExternalAddress()}
	}
	vstoreClient, closer, err := vstore.New(vStoreNamespace, vstoreOptions...)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing vstore client %s", err.Error())
		os.Exit(1)
	}
	defer closer()

	ssoClient, err := sso.NewIdentityProviderClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "Error registering sso client %s", err.Error())
		os.Exit(5)
	}
	marketplaceAppsClient, err := marketplaceapps.NewPartnerClient(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "Error registering marketplace apps client %s", err.Error())
		os.Exit(6)
	}
	client, err := pubsub.NewClient(ctx, "vbc-"+env.Name())
	if err != nil {
		os.Exit(7)
	}
	accountsClient, err := accounts.NewClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "error registering accounts client %s", err.Error())
		os.Exit(8)
	}
	goalClient, goalClientCloser, err := aiassistants.NewGoalServiceClientV3(ctx, env, true)
	if err != nil {
		logging.Criticalf(ctx, "Could not create the goal client: %s", err.Error())
		os.Exit(-1)
	}

	defer goalClientCloser()

	brandingService := branding.NewService(brandingClient)
	featureFlagClient := featureflags.New(partnerClient, goalClient)
	pinRepository := pinRepo.NewVStoreRepository(vstoreClient)
	pinSvc := pin.NewService(pinRepository, locationDataFetcher)
	languageSvc := lang.NewService(iamClient)
	crmCustomObjectFetcher := crmcustomobjects.NewService(featureFlagClient, crmCustomTypeService)

	// The authentication key is stored as a constant as we do not care about the leakage of this key as the cookie stores no vulnerable data
	emailVerificationCookieStoreKey := []byte{123, 160, 149, 206, 205, 86, 102, 21, 66, 139, 15, 79, 185, 238, 85, 136}

	cookieStore := sessions.NewCookieStore(emailVerificationCookieStoreKey, nil)
	ssoIDPAdminClient, err := sso.NewIdentityProviderAdminClient(ctx, env)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing SSO IdentityProviderAdmin client: %s", err.Error())
		os.Exit(12) //TODO: create exitcodes.go file
	}

	yeswareCenterID := os.Getenv("YESWARE_CENTER_ID")
	if yeswareCenterID == "" {
		logging.Warningf(ctx, "YESWARE_CENTER_ID is not set")
	}
	navbarDataClient, err := navbardata.NewService(env, iamClient, ssoIPClient, whitelabelClient, brandingService, locationDataFetcher, languageSvc, yeswareCenterID, featureFlagClient, iamV2Client)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing Navbar Data client: %s", err.Error())
		os.Exit(10)
	}

	defaultLocationRepository := defaultlocation.NewVStoreRepository(vstoreClient)
	productsFetcher := product.NewService(ssoClient, accountsClient, marketplaceAppsClient)
	accountsDataFetcher := accountsdata.NewService(accountsClient)
	locationService := location.NewService(accountGroupClient, groupClient, domainClient, multiLocationClient, iamAuthService, iamClient, defaultLocationRepository, partnerClient, tesseractClient)
	sideNavService := sidenavigation.NewService(whitelabelClient, brandingService, pinSvc, iamClient, iamV2Client, locationDataFetcher, domainClient, languageSvc, productsFetcher, accountsDataFetcher, locationService, featureFlagClient, accountGroupClient, tesseractClient, crmCustomObjectFetcher)
	atlasServer := api.NewAtlasServer(navbarDataClient, sideNavService, locationService, iamAuthService, client, iamClient, whitelabelClient, featureFlagClient)
	pinsServer := api.NewPinsServer(pinSvc)
	languagesServer := api.NewLanguagesServer(languageSvc)

	//--------- INSERT YOUR CODE HERE ------------
	atlas_v1.RegisterAtlasServer(grpcServer, atlasServer)
	atlas_v1.RegisterPinsServer(grpcServer, pinsServer)
	atlas_v1.RegisterLanguagesServer(grpcServer, languagesServer)
	catalogue.RegisterServer(grpcServer)
	//REGISTER_GRPC_SERVERS_HERE

	//Start HTTP API Server
	mux := http.NewServeMux()
	mux.Handle("/atlas.v1.Atlas/", serverconfig.CORS(bifrost.Wrap(grpcServer, strconv.Itoa(grpcPort))))
	mux.Handle("/atlas.v1.Pins/", serverconfig.CORS(bifrost.Wrap(grpcServer, strconv.Itoa(grpcPort))))
	mux.Handle("/atlas.v1.Languages/", serverconfig.CORS(bifrost.Wrap(grpcServer, strconv.Itoa(grpcPort))))

	mux.Handle("/assets/", serverconfig.CORS(api.AssetsHandler()))

	registration.HandleRegistration(mux, iamInterceptors, iamAuthService, "default-location", func(ctx context.Context) error {
		return defaultlocation.Register(ctx, vstoreClient)
	})
	registration.HandleIndex(mux, iamInterceptors, iamAuthService)

	cacheRepo := emailverification.NewRepository(cacheClient)
	emailVerificationServer := emailverification.NewEmailVerificationServer(iamClient, cookieStore, ssoIDPAdminClient, cacheRepo)

	// IAM Client
	mux.Handle("/entry", serviceProviderServer.EntryHandler(cacheClient, serviceprovidersdk.ForceAccountSelector()))
	mux.HandleFunc("/_ajax/v1/session", serviceProviderServer.SessionHandler(cacheClient))
	mux.HandleFunc("/logout", serviceProviderServer.LogoutHandler)
	mux.Handle("/to-display-email-verification", serverconfig.CORS(iamInterceptors.HTTPHandlerInterceptor(emailVerificationServer.ShouldDisplayInterstitial(),
		iaminterceptor.Scopes([]string{"openid"}),
	)))
	mux.Handle("/extend-email-verification-display-date", serverconfig.CORS(iamInterceptors.HTTPHandlerInterceptor(emailVerificationServer.ExtendNextDisplayTime(),
		iaminterceptor.Scopes([]string{"openid"}),
	)))

	mux.HandleFunc("/", serviceProviderServer.IndexHandler("/entry", vstaticClient, func(authToken string) (string, error) {
		err := iamClient.AccessManagementScreens(ctx, authToken, iam.READ)
		if err != nil {
			return "", err
		}
		return authToken, nil
	}))

	if err := serverconfig.StartAndListenServer(ctx, APP_NAME, grpcServer, mux,
		serverconfig.WithHTTPPort(httpPort),
		serverconfig.WithGRPCPort(grpcPort),
		serverconfig.WithHealthz(func() error { return nil }),
		serverconfig.EnableHTTPLogging(true),
	); err != nil {
		logging.Criticalf(ctx, "error running gRPC and HTTP server: %s", err.Error())
		os.Exit(1)
	}

}
