{
    "version": "0.2.0",
    "configurations": [
        // You may need to ensure you have dlv installed. To do this run the Go: Install/Update Tools from the 
        // Command Palette (ctrl+shift+P or cmd+shift+P), select dlv@latest, and click `Ok`
        {
            "name": "Run Against Demo",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/server/main.go",
            "env": {
               "ENVIRONMENT": "demo",
               "VENDASTA_APPLICATION_CREDENTIALS_JSON": "${env:HOME}/.config/vendasta/demo_default_credentials.json"
            }
        },
        {
            "name": "Run Against Production",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/server/main.go",
            "env": {
               "ENVIRONMENT": "prod",
               "VENDASTA_APPLICATION_CREDENTIALS_JSON": "${env:HOME}/.config/vendasta/prod_default_credentials.json"
            }
        }            
    ]
}