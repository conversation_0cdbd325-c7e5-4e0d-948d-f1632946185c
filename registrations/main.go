// This script handles registering all of your prerequisites for your service to run. VStore schemas, IAM resources,
// event broker types, anything that you need to register with another service one time before your service can run
// should be done in this script. Updates to any of these registrations will require rerunning the script.
//
// To run:
//
// ENVIRONMENT=<demo|prod> VENDASTA_APPLICATION_CREDENTIALS_JSON=`mscli auth path -e $ENVIRONMENT` go run main.go

package main

import (
	"context"
	"os"

	"github.com/vendasta/gosdks/config"
	"github.com/vendasta/gosdks/logging"
	vstore "github.com/vendasta/vstore/vstore/sdks/go/v1"

	"github.com/vendasta/atlas/internal/pin/repo"
)

const (
	APP_NAME = "atlas"
)

var (
	authorizedVStoreServiceAccounts = map[config.Env][]string{
		config.Prod: {"<EMAIL>"},
		config.Demo: {"<EMAIL>"},
	}
)

func main() {
	ctx := context.Background()

	err := registerVstore(ctx)
	if err != nil {
		logging.Criticalf(ctx, "Error registering vStore namespace and kinds: %s", err.Error())
		os.Exit(5)
	}
	return
}

func registerVstore(ctx context.Context) error {
	vStoreNamespace := vstore.Namespace(config.CurEnv(), APP_NAME)
	vstoreClient, closer, err := vstore.New(vStoreNamespace)
	if err != nil {
		logging.Criticalf(ctx, "Error initializing vstore client %s", err.Error())
		return err
	}
	defer closer()
	logging.Infof(ctx, "Registering vStore Namespace...")
	err = vstoreClient.RegisterNamespace(ctx, authorizedVStoreServiceAccounts[config.CurEnv()])
	if err != nil {
		logging.Criticalf(ctx, "Error registering vstore namespace %s", err.Error())
		return err
	}

	err = repo.Register(ctx, vstoreClient)
	if err != nil {
		logging.Criticalf(ctx, "Error registering pin repo %s", err.Error())
		return err
	}
	if err != nil {
		logging.Criticalf(ctx, "Error registering language repo %s", err.Error())
		return err
	}
	return nil
}
