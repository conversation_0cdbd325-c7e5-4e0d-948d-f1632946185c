// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v4.23.2
// source: google/iam/v1/policy.proto

package iampb

import (
	reflect "reflect"
	sync "sync"

	expr "google.golang.org/genproto/googleapis/type/expr"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The list of valid permission types for which logging can be configured.
// Admin writes are always logged, and are not configurable.
type AuditLogConfig_LogType int32

const (
	// Default case. Should never be this.
	AuditLogConfig_LOG_TYPE_UNSPECIFIED AuditLogConfig_LogType = 0
	// Admin reads. Example: CloudIAM getIamPolicy
	AuditLogConfig_ADMIN_READ AuditLogConfig_LogType = 1
	// Data writes. Example: CloudSQL Users create
	AuditLogConfig_DATA_WRITE AuditLogConfig_LogType = 2
	// Data reads. Example: CloudSQL Users list
	AuditLogConfig_DATA_READ AuditLogConfig_LogType = 3
)

// Enum value maps for AuditLogConfig_LogType.
var (
	AuditLogConfig_LogType_name = map[int32]string{
		0: "LOG_TYPE_UNSPECIFIED",
		1: "ADMIN_READ",
		2: "DATA_WRITE",
		3: "DATA_READ",
	}
	AuditLogConfig_LogType_value = map[string]int32{
		"LOG_TYPE_UNSPECIFIED": 0,
		"ADMIN_READ":           1,
		"DATA_WRITE":           2,
		"DATA_READ":            3,
	}
)

func (x AuditLogConfig_LogType) Enum() *AuditLogConfig_LogType {
	p := new(AuditLogConfig_LogType)
	*p = x
	return p
}

func (x AuditLogConfig_LogType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuditLogConfig_LogType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_iam_v1_policy_proto_enumTypes[0].Descriptor()
}

func (AuditLogConfig_LogType) Type() protoreflect.EnumType {
	return &file_google_iam_v1_policy_proto_enumTypes[0]
}

func (x AuditLogConfig_LogType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuditLogConfig_LogType.Descriptor instead.
func (AuditLogConfig_LogType) EnumDescriptor() ([]byte, []int) {
	return file_google_iam_v1_policy_proto_rawDescGZIP(), []int{3, 0}
}

// The type of action performed on a Binding in a policy.
type BindingDelta_Action int32

const (
	// Unspecified.
	BindingDelta_ACTION_UNSPECIFIED BindingDelta_Action = 0
	// Addition of a Binding.
	BindingDelta_ADD BindingDelta_Action = 1
	// Removal of a Binding.
	BindingDelta_REMOVE BindingDelta_Action = 2
)

// Enum value maps for BindingDelta_Action.
var (
	BindingDelta_Action_name = map[int32]string{
		0: "ACTION_UNSPECIFIED",
		1: "ADD",
		2: "REMOVE",
	}
	BindingDelta_Action_value = map[string]int32{
		"ACTION_UNSPECIFIED": 0,
		"ADD":                1,
		"REMOVE":             2,
	}
)

func (x BindingDelta_Action) Enum() *BindingDelta_Action {
	p := new(BindingDelta_Action)
	*p = x
	return p
}

func (x BindingDelta_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (BindingDelta_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_google_iam_v1_policy_proto_enumTypes[1].Descriptor()
}

func (BindingDelta_Action) Type() protoreflect.EnumType {
	return &file_google_iam_v1_policy_proto_enumTypes[1]
}

func (x BindingDelta_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use BindingDelta_Action.Descriptor instead.
func (BindingDelta_Action) EnumDescriptor() ([]byte, []int) {
	return file_google_iam_v1_policy_proto_rawDescGZIP(), []int{5, 0}
}

// The type of action performed on an audit configuration in a policy.
type AuditConfigDelta_Action int32

const (
	// Unspecified.
	AuditConfigDelta_ACTION_UNSPECIFIED AuditConfigDelta_Action = 0
	// Addition of an audit configuration.
	AuditConfigDelta_ADD AuditConfigDelta_Action = 1
	// Removal of an audit configuration.
	AuditConfigDelta_REMOVE AuditConfigDelta_Action = 2
)

// Enum value maps for AuditConfigDelta_Action.
var (
	AuditConfigDelta_Action_name = map[int32]string{
		0: "ACTION_UNSPECIFIED",
		1: "ADD",
		2: "REMOVE",
	}
	AuditConfigDelta_Action_value = map[string]int32{
		"ACTION_UNSPECIFIED": 0,
		"ADD":                1,
		"REMOVE":             2,
	}
)

func (x AuditConfigDelta_Action) Enum() *AuditConfigDelta_Action {
	p := new(AuditConfigDelta_Action)
	*p = x
	return p
}

func (x AuditConfigDelta_Action) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AuditConfigDelta_Action) Descriptor() protoreflect.EnumDescriptor {
	return file_google_iam_v1_policy_proto_enumTypes[2].Descriptor()
}

func (AuditConfigDelta_Action) Type() protoreflect.EnumType {
	return &file_google_iam_v1_policy_proto_enumTypes[2]
}

func (x AuditConfigDelta_Action) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AuditConfigDelta_Action.Descriptor instead.
func (AuditConfigDelta_Action) EnumDescriptor() ([]byte, []int) {
	return file_google_iam_v1_policy_proto_rawDescGZIP(), []int{6, 0}
}

// An Identity and Access Management (IAM) policy, which specifies access
// controls for Google Cloud resources.
//
// A `Policy` is a collection of `bindings`. A `binding` binds one or more
// `members`, or principals, to a single `role`. Principals can be user
// accounts, service accounts, Google groups, and domains (such as G Suite). A
// `role` is a named list of permissions; each `role` can be an IAM predefined
// role or a user-created custom role.
//
// For some types of Google Cloud resources, a `binding` can also specify a
// `condition`, which is a logical expression that allows access to a resource
// only if the expression evaluates to `true`. A condition can add constraints
// based on attributes of the request, the resource, or both. To learn which
// resources support conditions in their IAM policies, see the
// [IAM
// documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
//
// **JSON example:**
//
//	{
//	  "bindings": [
//	    {
//	      "role": "roles/resourcemanager.organizationAdmin",
//	      "members": [
//	        "user:<EMAIL>",
//	        "group:<EMAIL>",
//	        "domain:google.com",
//	        "serviceAccount:<EMAIL>"
//	      ]
//	    },
//	    {
//	      "role": "roles/resourcemanager.organizationViewer",
//	      "members": [
//	        "user:<EMAIL>"
//	      ],
//	      "condition": {
//	        "title": "expirable access",
//	        "description": "Does not grant access after Sep 2020",
//	        "expression": "request.time <
//	        timestamp('2020-10-01T00:00:00.000Z')",
//	      }
//	    }
//	  ],
//	  "etag": "BwWWja0YfJA=",
//	  "version": 3
//	}
//
// **YAML example:**
//
//	bindings:
//	- members:
//	  - user:<EMAIL>
//	  - group:<EMAIL>
//	  - domain:google.com
//	  - serviceAccount:<EMAIL>
//	  role: roles/resourcemanager.organizationAdmin
//	- members:
//	  - user:<EMAIL>
//	  role: roles/resourcemanager.organizationViewer
//	  condition:
//	    title: expirable access
//	    description: Does not grant access after Sep 2020
//	    expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
//	etag: BwWWja0YfJA=
//	version: 3
//
// For a description of IAM and its features, see the
// [IAM documentation](https://cloud.google.com/iam/docs/).
type Policy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Specifies the format of the policy.
	//
	// Valid values are `0`, `1`, and `3`. Requests that specify an invalid value
	// are rejected.
	//
	// Any operation that affects conditional role bindings must specify version
	// `3`. This requirement applies to the following operations:
	//
	// * Getting a policy that includes a conditional role binding
	// * Adding a conditional role binding to a policy
	// * Changing a conditional role binding in a policy
	// * Removing any role binding, with or without a condition, from a policy
	//   that includes conditions
	//
	// **Important:** If you use IAM Conditions, you must include the `etag` field
	// whenever you call `setIamPolicy`. If you omit this field, then IAM allows
	// you to overwrite a version `3` policy with a version `1` policy, and all of
	// the conditions in the version `3` policy are lost.
	//
	// If a policy does not include any conditions, operations on that policy may
	// specify any valid version or leave the field unset.
	//
	// To learn which resources support conditions in their IAM policies, see the
	// [IAM
	// documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
	Version int32 `protobuf:"varint,1,opt,name=version,proto3" json:"version,omitempty"`
	// Associates a list of `members`, or principals, with a `role`. Optionally,
	// may specify a `condition` that determines how and when the `bindings` are
	// applied. Each of the `bindings` must contain at least one principal.
	//
	// The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250
	// of these principals can be Google groups. Each occurrence of a principal
	// counts towards these limits. For example, if the `bindings` grant 50
	// different roles to `user:<EMAIL>`, and not to any other
	// principal, then you can add another 1,450 principals to the `bindings` in
	// the `Policy`.
	Bindings []*Binding `protobuf:"bytes,4,rep,name=bindings,proto3" json:"bindings,omitempty"`
	// Specifies cloud audit logging configuration for this policy.
	AuditConfigs []*AuditConfig `protobuf:"bytes,6,rep,name=audit_configs,json=auditConfigs,proto3" json:"audit_configs,omitempty"`
	// `etag` is used for optimistic concurrency control as a way to help
	// prevent simultaneous updates of a policy from overwriting each other.
	// It is strongly suggested that systems make use of the `etag` in the
	// read-modify-write cycle to perform policy updates in order to avoid race
	// conditions: An `etag` is returned in the response to `getIamPolicy`, and
	// systems are expected to put that etag in the request to `setIamPolicy` to
	// ensure that their change will be applied to the same version of the policy.
	//
	// **Important:** If you use IAM Conditions, you must include the `etag` field
	// whenever you call `setIamPolicy`. If you omit this field, then IAM allows
	// you to overwrite a version `3` policy with a version `1` policy, and all of
	// the conditions in the version `3` policy are lost.
	Etag []byte `protobuf:"bytes,3,opt,name=etag,proto3" json:"etag,omitempty"`
}

func (x *Policy) Reset() {
	*x = Policy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_iam_v1_policy_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Policy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Policy) ProtoMessage() {}

func (x *Policy) ProtoReflect() protoreflect.Message {
	mi := &file_google_iam_v1_policy_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Policy.ProtoReflect.Descriptor instead.
func (*Policy) Descriptor() ([]byte, []int) {
	return file_google_iam_v1_policy_proto_rawDescGZIP(), []int{0}
}

func (x *Policy) GetVersion() int32 {
	if x != nil {
		return x.Version
	}
	return 0
}

func (x *Policy) GetBindings() []*Binding {
	if x != nil {
		return x.Bindings
	}
	return nil
}

func (x *Policy) GetAuditConfigs() []*AuditConfig {
	if x != nil {
		return x.AuditConfigs
	}
	return nil
}

func (x *Policy) GetEtag() []byte {
	if x != nil {
		return x.Etag
	}
	return nil
}

// Associates `members`, or principals, with a `role`.
type Binding struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Role that is assigned to the list of `members`, or principals.
	// For example, `roles/viewer`, `roles/editor`, or `roles/owner`.
	Role string `protobuf:"bytes,1,opt,name=role,proto3" json:"role,omitempty"`
	// Specifies the principals requesting access for a Cloud Platform resource.
	// `members` can have the following values:
	//
	// * `allUsers`: A special identifier that represents anyone who is
	//    on the internet; with or without a Google account.
	//
	// * `allAuthenticatedUsers`: A special identifier that represents anyone
	//    who is authenticated with a Google account or a service account.
	//
	// * `user:{emailid}`: An email address that represents a specific Google
	//    account. For example, `<EMAIL>` .
	//
	//
	// * `serviceAccount:{emailid}`: An email address that represents a service
	//    account. For example, `<EMAIL>`.
	//
	// * `group:{emailid}`: An email address that represents a Google group.
	//    For example, `<EMAIL>`.
	//
	// * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique
	//    identifier) representing a user that has been recently deleted. For
	//    example, `<EMAIL>?uid=123456789012345678901`. If the user is
	//    recovered, this value reverts to `user:{emailid}` and the recovered user
	//    retains the role in the binding.
	//
	// * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus
	//    unique identifier) representing a service account that has been recently
	//    deleted. For example,
	//    `<EMAIL>?uid=123456789012345678901`.
	//    If the service account is undeleted, this value reverts to
	//    `serviceAccount:{emailid}` and the undeleted service account retains the
	//    role in the binding.
	//
	// * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique
	//    identifier) representing a Google group that has been recently
	//    deleted. For example, `<EMAIL>?uid=123456789012345678901`. If
	//    the group is recovered, this value reverts to `group:{emailid}` and the
	//    recovered group retains the role in the binding.
	//
	//
	// * `domain:{domain}`: The G Suite domain (primary) that represents all the
	//    users of that domain. For example, `google.com` or `example.com`.
	//
	//
	Members []string `protobuf:"bytes,2,rep,name=members,proto3" json:"members,omitempty"`
	// The condition that is associated with this binding.
	//
	// If the condition evaluates to `true`, then this binding applies to the
	// current request.
	//
	// If the condition evaluates to `false`, then this binding does not apply to
	// the current request. However, a different role binding might grant the same
	// role to one or more of the principals in this binding.
	//
	// To learn which resources support conditions in their IAM policies, see the
	// [IAM
	// documentation](https://cloud.google.com/iam/help/conditions/resource-policies).
	Condition *expr.Expr `protobuf:"bytes,3,opt,name=condition,proto3" json:"condition,omitempty"`
}

func (x *Binding) Reset() {
	*x = Binding{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_iam_v1_policy_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Binding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Binding) ProtoMessage() {}

func (x *Binding) ProtoReflect() protoreflect.Message {
	mi := &file_google_iam_v1_policy_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Binding.ProtoReflect.Descriptor instead.
func (*Binding) Descriptor() ([]byte, []int) {
	return file_google_iam_v1_policy_proto_rawDescGZIP(), []int{1}
}

func (x *Binding) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *Binding) GetMembers() []string {
	if x != nil {
		return x.Members
	}
	return nil
}

func (x *Binding) GetCondition() *expr.Expr {
	if x != nil {
		return x.Condition
	}
	return nil
}

// Specifies the audit configuration for a service.
// The configuration determines which permission types are logged, and what
// identities, if any, are exempted from logging.
// An AuditConfig must have one or more AuditLogConfigs.
//
// If there are AuditConfigs for both `allServices` and a specific service,
// the union of the two AuditConfigs is used for that service: the log_types
// specified in each AuditConfig are enabled, and the exempted_members in each
// AuditLogConfig are exempted.
//
// Example Policy with multiple AuditConfigs:
//
//	{
//	  "audit_configs": [
//	    {
//	      "service": "allServices",
//	      "audit_log_configs": [
//	        {
//	          "log_type": "DATA_READ",
//	          "exempted_members": [
//	            "user:<EMAIL>"
//	          ]
//	        },
//	        {
//	          "log_type": "DATA_WRITE"
//	        },
//	        {
//	          "log_type": "ADMIN_READ"
//	        }
//	      ]
//	    },
//	    {
//	      "service": "sampleservice.googleapis.com",
//	      "audit_log_configs": [
//	        {
//	          "log_type": "DATA_READ"
//	        },
//	        {
//	          "log_type": "DATA_WRITE",
//	          "exempted_members": [
//	            "user:<EMAIL>"
//	          ]
//	        }
//	      ]
//	    }
//	  ]
//	}
//
// For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ
// logging. It <NAME_EMAIL> from DATA_READ logging, and
// <EMAIL> from DATA_WRITE logging.
type AuditConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Specifies a service that will be enabled for audit logging.
	// For example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
	// `allServices` is a special value that covers all services.
	Service string `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"`
	// The configuration for logging of each type of permission.
	AuditLogConfigs []*AuditLogConfig `protobuf:"bytes,3,rep,name=audit_log_configs,json=auditLogConfigs,proto3" json:"audit_log_configs,omitempty"`
}

func (x *AuditConfig) Reset() {
	*x = AuditConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_iam_v1_policy_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuditConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditConfig) ProtoMessage() {}

func (x *AuditConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_iam_v1_policy_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditConfig.ProtoReflect.Descriptor instead.
func (*AuditConfig) Descriptor() ([]byte, []int) {
	return file_google_iam_v1_policy_proto_rawDescGZIP(), []int{2}
}

func (x *AuditConfig) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *AuditConfig) GetAuditLogConfigs() []*AuditLogConfig {
	if x != nil {
		return x.AuditLogConfigs
	}
	return nil
}

// Provides the configuration for logging a type of permissions.
// Example:
//
//	{
//	  "audit_log_configs": [
//	    {
//	      "log_type": "DATA_READ",
//	      "exempted_members": [
//	        "user:<EMAIL>"
//	      ]
//	    },
//	    {
//	      "log_type": "DATA_WRITE"
//	    }
//	  ]
//	}
//
// This enables 'DATA_READ' and 'DATA_WRITE' logging, while exempting
// <EMAIL> from DATA_READ logging.
type AuditLogConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The log type that this config enables.
	LogType AuditLogConfig_LogType `protobuf:"varint,1,opt,name=log_type,json=logType,proto3,enum=google.iam.v1.AuditLogConfig_LogType" json:"log_type,omitempty"`
	// Specifies the identities that do not cause logging for this type of
	// permission.
	// Follows the same format of
	// [Binding.members][google.iam.v1.Binding.members].
	ExemptedMembers []string `protobuf:"bytes,2,rep,name=exempted_members,json=exemptedMembers,proto3" json:"exempted_members,omitempty"`
}

func (x *AuditLogConfig) Reset() {
	*x = AuditLogConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_iam_v1_policy_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuditLogConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditLogConfig) ProtoMessage() {}

func (x *AuditLogConfig) ProtoReflect() protoreflect.Message {
	mi := &file_google_iam_v1_policy_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditLogConfig.ProtoReflect.Descriptor instead.
func (*AuditLogConfig) Descriptor() ([]byte, []int) {
	return file_google_iam_v1_policy_proto_rawDescGZIP(), []int{3}
}

func (x *AuditLogConfig) GetLogType() AuditLogConfig_LogType {
	if x != nil {
		return x.LogType
	}
	return AuditLogConfig_LOG_TYPE_UNSPECIFIED
}

func (x *AuditLogConfig) GetExemptedMembers() []string {
	if x != nil {
		return x.ExemptedMembers
	}
	return nil
}

// The difference delta between two policies.
type PolicyDelta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The delta for Bindings between two policies.
	BindingDeltas []*BindingDelta `protobuf:"bytes,1,rep,name=binding_deltas,json=bindingDeltas,proto3" json:"binding_deltas,omitempty"`
	// The delta for AuditConfigs between two policies.
	AuditConfigDeltas []*AuditConfigDelta `protobuf:"bytes,2,rep,name=audit_config_deltas,json=auditConfigDeltas,proto3" json:"audit_config_deltas,omitempty"`
}

func (x *PolicyDelta) Reset() {
	*x = PolicyDelta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_iam_v1_policy_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PolicyDelta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PolicyDelta) ProtoMessage() {}

func (x *PolicyDelta) ProtoReflect() protoreflect.Message {
	mi := &file_google_iam_v1_policy_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PolicyDelta.ProtoReflect.Descriptor instead.
func (*PolicyDelta) Descriptor() ([]byte, []int) {
	return file_google_iam_v1_policy_proto_rawDescGZIP(), []int{4}
}

func (x *PolicyDelta) GetBindingDeltas() []*BindingDelta {
	if x != nil {
		return x.BindingDeltas
	}
	return nil
}

func (x *PolicyDelta) GetAuditConfigDeltas() []*AuditConfigDelta {
	if x != nil {
		return x.AuditConfigDeltas
	}
	return nil
}

// One delta entry for Binding. Each individual change (only one member in each
// entry) to a binding will be a separate entry.
type BindingDelta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The action that was performed on a Binding.
	// Required
	Action BindingDelta_Action `protobuf:"varint,1,opt,name=action,proto3,enum=google.iam.v1.BindingDelta_Action" json:"action,omitempty"`
	// Role that is assigned to `members`.
	// For example, `roles/viewer`, `roles/editor`, or `roles/owner`.
	// Required
	Role string `protobuf:"bytes,2,opt,name=role,proto3" json:"role,omitempty"`
	// A single identity requesting access for a Cloud Platform resource.
	// Follows the same format of Binding.members.
	// Required
	Member string `protobuf:"bytes,3,opt,name=member,proto3" json:"member,omitempty"`
	// The condition that is associated with this binding.
	Condition *expr.Expr `protobuf:"bytes,4,opt,name=condition,proto3" json:"condition,omitempty"`
}

func (x *BindingDelta) Reset() {
	*x = BindingDelta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_iam_v1_policy_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindingDelta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindingDelta) ProtoMessage() {}

func (x *BindingDelta) ProtoReflect() protoreflect.Message {
	mi := &file_google_iam_v1_policy_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindingDelta.ProtoReflect.Descriptor instead.
func (*BindingDelta) Descriptor() ([]byte, []int) {
	return file_google_iam_v1_policy_proto_rawDescGZIP(), []int{5}
}

func (x *BindingDelta) GetAction() BindingDelta_Action {
	if x != nil {
		return x.Action
	}
	return BindingDelta_ACTION_UNSPECIFIED
}

func (x *BindingDelta) GetRole() string {
	if x != nil {
		return x.Role
	}
	return ""
}

func (x *BindingDelta) GetMember() string {
	if x != nil {
		return x.Member
	}
	return ""
}

func (x *BindingDelta) GetCondition() *expr.Expr {
	if x != nil {
		return x.Condition
	}
	return nil
}

// One delta entry for AuditConfig. Each individual change (only one
// exempted_member in each entry) to a AuditConfig will be a separate entry.
type AuditConfigDelta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The action that was performed on an audit configuration in a policy.
	// Required
	Action AuditConfigDelta_Action `protobuf:"varint,1,opt,name=action,proto3,enum=google.iam.v1.AuditConfigDelta_Action" json:"action,omitempty"`
	// Specifies a service that was configured for Cloud Audit Logging.
	// For example, `storage.googleapis.com`, `cloudsql.googleapis.com`.
	// `allServices` is a special value that covers all services.
	// Required
	Service string `protobuf:"bytes,2,opt,name=service,proto3" json:"service,omitempty"`
	// A single identity that is exempted from "data access" audit
	// logging for the `service` specified above.
	// Follows the same format of Binding.members.
	ExemptedMember string `protobuf:"bytes,3,opt,name=exempted_member,json=exemptedMember,proto3" json:"exempted_member,omitempty"`
	// Specifies the log_type that was be enabled. ADMIN_ACTIVITY is always
	// enabled, and cannot be configured.
	// Required
	LogType string `protobuf:"bytes,4,opt,name=log_type,json=logType,proto3" json:"log_type,omitempty"`
}

func (x *AuditConfigDelta) Reset() {
	*x = AuditConfigDelta{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_iam_v1_policy_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AuditConfigDelta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuditConfigDelta) ProtoMessage() {}

func (x *AuditConfigDelta) ProtoReflect() protoreflect.Message {
	mi := &file_google_iam_v1_policy_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuditConfigDelta.ProtoReflect.Descriptor instead.
func (*AuditConfigDelta) Descriptor() ([]byte, []int) {
	return file_google_iam_v1_policy_proto_rawDescGZIP(), []int{6}
}

func (x *AuditConfigDelta) GetAction() AuditConfigDelta_Action {
	if x != nil {
		return x.Action
	}
	return AuditConfigDelta_ACTION_UNSPECIFIED
}

func (x *AuditConfigDelta) GetService() string {
	if x != nil {
		return x.Service
	}
	return ""
}

func (x *AuditConfigDelta) GetExemptedMember() string {
	if x != nil {
		return x.ExemptedMember
	}
	return ""
}

func (x *AuditConfigDelta) GetLogType() string {
	if x != nil {
		return x.LogType
	}
	return ""
}

var File_google_iam_v1_policy_proto protoreflect.FileDescriptor

var file_google_iam_v1_policy_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x69, 0x61, 0x6d, 0x2f, 0x76, 0x31, 0x2f,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65, 0x2f, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xab, 0x01, 0x0a, 0x06, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x18,
	0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x08, 0x62, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x52, 0x08, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3f, 0x0a, 0x0d,
	0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x06, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x69, 0x61, 0x6d,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52,
	0x0c, 0x61, 0x75, 0x64, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x12, 0x12, 0x0a,
	0x04, 0x65, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x65, 0x74, 0x61,
	0x67, 0x22, 0x68, 0x0a, 0x07, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x12, 0x0a, 0x04,
	0x72, 0x6f, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x07, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x12, 0x2f, 0x0a, 0x09, 0x63, 0x6f,
	0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x45, 0x78, 0x70, 0x72,
	0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x72, 0x0a, 0x0b, 0x41,
	0x75, 0x64, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x12, 0x49, 0x0a, 0x11, 0x61, 0x75, 0x64, 0x69, 0x74, 0x5f, 0x6c, 0x6f,
	0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0f,
	0x61, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x73, 0x22,
	0xd1, 0x01, 0x0a, 0x0e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x40, 0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x4c, 0x6f, 0x67, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x4c, 0x6f, 0x67, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07, 0x6c, 0x6f, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x65, 0x78, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64,
	0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f,
	0x65, 0x78, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x73, 0x22,
	0x52, 0x0a, 0x07, 0x4c, 0x6f, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x4c, 0x4f,
	0x47, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x5f, 0x52, 0x45,
	0x41, 0x44, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x57, 0x52, 0x49,
	0x54, 0x45, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x41, 0x54, 0x41, 0x5f, 0x52, 0x45, 0x41,
	0x44, 0x10, 0x03, 0x22, 0xa2, 0x01, 0x0a, 0x0b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x44, 0x65,
	0x6c, 0x74, 0x61, 0x12, 0x42, 0x0a, 0x0e, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x64,
	0x65, 0x6c, 0x74, 0x61, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x52, 0x0d, 0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x73, 0x12, 0x4f, 0x0a, 0x13, 0x61, 0x75, 0x64, 0x69, 0x74,
	0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x64, 0x65, 0x6c, 0x74, 0x61, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x69, 0x61,
	0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x75, 0x64, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x44, 0x65, 0x6c, 0x74, 0x61, 0x52, 0x11, 0x61, 0x75, 0x64, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x73, 0x22, 0xde, 0x01, 0x0a, 0x0c, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x12, 0x3a, 0x0a, 0x06, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x2e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d,
	0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x2f, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79,
	0x70, 0x65, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x35, 0x0a, 0x06, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x12,
	0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a,
	0x06, 0x52, 0x45, 0x4d, 0x4f, 0x56, 0x45, 0x10, 0x02, 0x22, 0xe7, 0x01, 0x0a, 0x10, 0x41, 0x75,
	0x64, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x12, 0x3e,
	0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x26,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x75, 0x64, 0x69, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x44, 0x65, 0x6c, 0x74, 0x61, 0x2e,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x18,
	0x0a, 0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x65, 0x78, 0x65, 0x6d,
	0x70, 0x74, 0x65, 0x64, 0x5f, 0x6d, 0x65, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x65, 0x78, 0x65, 0x6d, 0x70, 0x74, 0x65, 0x64, 0x4d, 0x65, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x19, 0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x54, 0x79, 0x70, 0x65, 0x22, 0x35, 0x0a, 0x06,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x12, 0x41, 0x43, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07,
	0x0a, 0x03, 0x41, 0x44, 0x44, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x4d, 0x4f, 0x56,
	0x45, 0x10, 0x02, 0x42, 0x7c, 0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x69, 0x61, 0x6d, 0x2e, 0x76, 0x31, 0x42, 0x0b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x29, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x67, 0x6f, 0x2f, 0x69, 0x61, 0x6d,
	0x2f, 0x61, 0x70, 0x69, 0x76, 0x31, 0x2f, 0x69, 0x61, 0x6d, 0x70, 0x62, 0x3b, 0x69, 0x61, 0x6d,
	0x70, 0x62, 0xf8, 0x01, 0x01, 0xaa, 0x02, 0x13, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x43,
	0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x49, 0x61, 0x6d, 0x2e, 0x56, 0x31, 0xca, 0x02, 0x13, 0x47, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x49, 0x61, 0x6d, 0x5c, 0x56,
	0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_iam_v1_policy_proto_rawDescOnce sync.Once
	file_google_iam_v1_policy_proto_rawDescData = file_google_iam_v1_policy_proto_rawDesc
)

func file_google_iam_v1_policy_proto_rawDescGZIP() []byte {
	file_google_iam_v1_policy_proto_rawDescOnce.Do(func() {
		file_google_iam_v1_policy_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_iam_v1_policy_proto_rawDescData)
	})
	return file_google_iam_v1_policy_proto_rawDescData
}

var file_google_iam_v1_policy_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_google_iam_v1_policy_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_google_iam_v1_policy_proto_goTypes = []interface{}{
	(AuditLogConfig_LogType)(0),  // 0: google.iam.v1.AuditLogConfig.LogType
	(BindingDelta_Action)(0),     // 1: google.iam.v1.BindingDelta.Action
	(AuditConfigDelta_Action)(0), // 2: google.iam.v1.AuditConfigDelta.Action
	(*Policy)(nil),               // 3: google.iam.v1.Policy
	(*Binding)(nil),              // 4: google.iam.v1.Binding
	(*AuditConfig)(nil),          // 5: google.iam.v1.AuditConfig
	(*AuditLogConfig)(nil),       // 6: google.iam.v1.AuditLogConfig
	(*PolicyDelta)(nil),          // 7: google.iam.v1.PolicyDelta
	(*BindingDelta)(nil),         // 8: google.iam.v1.BindingDelta
	(*AuditConfigDelta)(nil),     // 9: google.iam.v1.AuditConfigDelta
	(*expr.Expr)(nil),            // 10: google.type.Expr
}
var file_google_iam_v1_policy_proto_depIdxs = []int32{
	4,  // 0: google.iam.v1.Policy.bindings:type_name -> google.iam.v1.Binding
	5,  // 1: google.iam.v1.Policy.audit_configs:type_name -> google.iam.v1.AuditConfig
	10, // 2: google.iam.v1.Binding.condition:type_name -> google.type.Expr
	6,  // 3: google.iam.v1.AuditConfig.audit_log_configs:type_name -> google.iam.v1.AuditLogConfig
	0,  // 4: google.iam.v1.AuditLogConfig.log_type:type_name -> google.iam.v1.AuditLogConfig.LogType
	8,  // 5: google.iam.v1.PolicyDelta.binding_deltas:type_name -> google.iam.v1.BindingDelta
	9,  // 6: google.iam.v1.PolicyDelta.audit_config_deltas:type_name -> google.iam.v1.AuditConfigDelta
	1,  // 7: google.iam.v1.BindingDelta.action:type_name -> google.iam.v1.BindingDelta.Action
	10, // 8: google.iam.v1.BindingDelta.condition:type_name -> google.type.Expr
	2,  // 9: google.iam.v1.AuditConfigDelta.action:type_name -> google.iam.v1.AuditConfigDelta.Action
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_google_iam_v1_policy_proto_init() }
func file_google_iam_v1_policy_proto_init() {
	if File_google_iam_v1_policy_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_iam_v1_policy_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Policy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_iam_v1_policy_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Binding); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_iam_v1_policy_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuditConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_iam_v1_policy_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuditLogConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_iam_v1_policy_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PolicyDelta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_iam_v1_policy_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindingDelta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_iam_v1_policy_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AuditConfigDelta); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_iam_v1_policy_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_iam_v1_policy_proto_goTypes,
		DependencyIndexes: file_google_iam_v1_policy_proto_depIdxs,
		EnumInfos:         file_google_iam_v1_policy_proto_enumTypes,
		MessageInfos:      file_google_iam_v1_policy_proto_msgTypes,
	}.Build()
	File_google_iam_v1_policy_proto = out.File
	file_google_iam_v1_policy_proto_rawDesc = nil
	file_google_iam_v1_policy_proto_goTypes = nil
	file_google_iam_v1_policy_proto_depIdxs = nil
}
