// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.21.9
// source: google/logging/v2/logging.proto

package loggingpb

import (
	context "context"
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	monitoredres "google.golang.org/genproto/googleapis/api/monitoredres"
	status "google.golang.org/genproto/googleapis/rpc/status"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status1 "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// An indicator of why entries were omitted.
type TailLogEntriesResponse_SuppressionInfo_Reason int32

const (
	// Unexpected default.
	TailLogEntriesResponse_SuppressionInfo_REASON_UNSPECIFIED TailLogEntriesResponse_SuppressionInfo_Reason = 0
	// Indicates suppression occurred due to relevant entries being
	// received in excess of rate limits. For quotas and limits, see
	// [Logging API quotas and
	// limits](https://cloud.google.com/logging/quotas#api-limits).
	TailLogEntriesResponse_SuppressionInfo_RATE_LIMIT TailLogEntriesResponse_SuppressionInfo_Reason = 1
	// Indicates suppression occurred due to the client not consuming
	// responses quickly enough.
	TailLogEntriesResponse_SuppressionInfo_NOT_CONSUMED TailLogEntriesResponse_SuppressionInfo_Reason = 2
)

// Enum value maps for TailLogEntriesResponse_SuppressionInfo_Reason.
var (
	TailLogEntriesResponse_SuppressionInfo_Reason_name = map[int32]string{
		0: "REASON_UNSPECIFIED",
		1: "RATE_LIMIT",
		2: "NOT_CONSUMED",
	}
	TailLogEntriesResponse_SuppressionInfo_Reason_value = map[string]int32{
		"REASON_UNSPECIFIED": 0,
		"RATE_LIMIT":         1,
		"NOT_CONSUMED":       2,
	}
)

func (x TailLogEntriesResponse_SuppressionInfo_Reason) Enum() *TailLogEntriesResponse_SuppressionInfo_Reason {
	p := new(TailLogEntriesResponse_SuppressionInfo_Reason)
	*p = x
	return p
}

func (x TailLogEntriesResponse_SuppressionInfo_Reason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TailLogEntriesResponse_SuppressionInfo_Reason) Descriptor() protoreflect.EnumDescriptor {
	return file_google_logging_v2_logging_proto_enumTypes[0].Descriptor()
}

func (TailLogEntriesResponse_SuppressionInfo_Reason) Type() protoreflect.EnumType {
	return &file_google_logging_v2_logging_proto_enumTypes[0]
}

func (x TailLogEntriesResponse_SuppressionInfo_Reason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TailLogEntriesResponse_SuppressionInfo_Reason.Descriptor instead.
func (TailLogEntriesResponse_SuppressionInfo_Reason) EnumDescriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_proto_rawDescGZIP(), []int{11, 0, 0}
}

// The parameters to DeleteLog.
type DeleteLogRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name of the log to delete:
	//
	// * `projects/[PROJECT_ID]/logs/[LOG_ID]`
	// * `organizations/[ORGANIZATION_ID]/logs/[LOG_ID]`
	// * `billingAccounts/[BILLING_ACCOUNT_ID]/logs/[LOG_ID]`
	// * `folders/[FOLDER_ID]/logs/[LOG_ID]`
	//
	// `[LOG_ID]` must be URL-encoded. For example,
	// `"projects/my-project-id/logs/syslog"`,
	// `"organizations/123/logs/cloudaudit.googleapis.com%2Factivity"`.
	//
	// For more information about log names, see
	// [LogEntry][google.logging.v2.LogEntry].
	LogName string `protobuf:"bytes,1,opt,name=log_name,json=logName,proto3" json:"log_name,omitempty"`
}

func (x *DeleteLogRequest) Reset() {
	*x = DeleteLogRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLogRequest) ProtoMessage() {}

func (x *DeleteLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLogRequest.ProtoReflect.Descriptor instead.
func (*DeleteLogRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_proto_rawDescGZIP(), []int{0}
}

func (x *DeleteLogRequest) GetLogName() string {
	if x != nil {
		return x.LogName
	}
	return ""
}

// The parameters to WriteLogEntries.
type WriteLogEntriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional. A default log resource name that is assigned to all log entries
	// in `entries` that do not specify a value for `log_name`:
	//
	// * `projects/[PROJECT_ID]/logs/[LOG_ID]`
	// * `organizations/[ORGANIZATION_ID]/logs/[LOG_ID]`
	// * `billingAccounts/[BILLING_ACCOUNT_ID]/logs/[LOG_ID]`
	// * `folders/[FOLDER_ID]/logs/[LOG_ID]`
	//
	// `[LOG_ID]` must be URL-encoded. For example:
	//
	//	"projects/my-project-id/logs/syslog"
	//	"organizations/123/logs/cloudaudit.googleapis.com%2Factivity"
	//
	// The permission `logging.logEntries.create` is needed on each project,
	// organization, billing account, or folder that is receiving new log
	// entries, whether the resource is specified in `logName` or in an
	// individual log entry.
	LogName string `protobuf:"bytes,1,opt,name=log_name,json=logName,proto3" json:"log_name,omitempty"`
	// Optional. A default monitored resource object that is assigned to all log
	// entries in `entries` that do not specify a value for `resource`. Example:
	//
	//	{ "type": "gce_instance",
	//	  "labels": {
	//	    "zone": "us-central1-a", "instance_id": "00000000000000000000" }}
	//
	// See [LogEntry][google.logging.v2.LogEntry].
	Resource *monitoredres.MonitoredResource `protobuf:"bytes,2,opt,name=resource,proto3" json:"resource,omitempty"`
	// Optional. Default labels that are added to the `labels` field of all log
	// entries in `entries`. If a log entry already has a label with the same key
	// as a label in this parameter, then the log entry's label is not changed.
	// See [LogEntry][google.logging.v2.LogEntry].
	Labels map[string]string `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Required. The log entries to send to Logging. The order of log
	// entries in this list does not matter. Values supplied in this method's
	// `log_name`, `resource`, and `labels` fields are copied into those log
	// entries in this list that do not include values for their corresponding
	// fields. For more information, see the
	// [LogEntry][google.logging.v2.LogEntry] type.
	//
	// If the `timestamp` or `insert_id` fields are missing in log entries, then
	// this method supplies the current time or a unique identifier, respectively.
	// The supplied values are chosen so that, among the log entries that did not
	// supply their own values, the entries earlier in the list will sort before
	// the entries later in the list. See the `entries.list` method.
	//
	// Log entries with timestamps that are more than the
	// [logs retention period](https://cloud.google.com/logging/quotas) in
	// the past or more than 24 hours in the future will not be available when
	// calling `entries.list`. However, those log entries can still be [exported
	// with
	// LogSinks](https://cloud.google.com/logging/docs/api/tasks/exporting-logs).
	//
	// To improve throughput and to avoid exceeding the
	// [quota limit](https://cloud.google.com/logging/quotas) for calls to
	// `entries.write`, you should try to include several log entries in this
	// list, rather than calling this method for each individual log entry.
	Entries []*LogEntry `protobuf:"bytes,4,rep,name=entries,proto3" json:"entries,omitempty"`
	// Optional. Whether valid entries should be written even if some other
	// entries fail due to INVALID_ARGUMENT or PERMISSION_DENIED errors. If any
	// entry is not written, then the response status is the error associated
	// with one of the failed entries and the response includes error details
	// keyed by the entries' zero-based index in the `entries.write` method.
	PartialSuccess bool `protobuf:"varint,5,opt,name=partial_success,json=partialSuccess,proto3" json:"partial_success,omitempty"`
	// Optional. If true, the request should expect normal response, but the
	// entries won't be persisted nor exported. Useful for checking whether the
	// logging API endpoints are working properly before sending valuable data.
	DryRun bool `protobuf:"varint,6,opt,name=dry_run,json=dryRun,proto3" json:"dry_run,omitempty"`
}

func (x *WriteLogEntriesRequest) Reset() {
	*x = WriteLogEntriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WriteLogEntriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteLogEntriesRequest) ProtoMessage() {}

func (x *WriteLogEntriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteLogEntriesRequest.ProtoReflect.Descriptor instead.
func (*WriteLogEntriesRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_proto_rawDescGZIP(), []int{1}
}

func (x *WriteLogEntriesRequest) GetLogName() string {
	if x != nil {
		return x.LogName
	}
	return ""
}

func (x *WriteLogEntriesRequest) GetResource() *monitoredres.MonitoredResource {
	if x != nil {
		return x.Resource
	}
	return nil
}

func (x *WriteLogEntriesRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *WriteLogEntriesRequest) GetEntries() []*LogEntry {
	if x != nil {
		return x.Entries
	}
	return nil
}

func (x *WriteLogEntriesRequest) GetPartialSuccess() bool {
	if x != nil {
		return x.PartialSuccess
	}
	return false
}

func (x *WriteLogEntriesRequest) GetDryRun() bool {
	if x != nil {
		return x.DryRun
	}
	return false
}

// Result returned from WriteLogEntries.
type WriteLogEntriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *WriteLogEntriesResponse) Reset() {
	*x = WriteLogEntriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WriteLogEntriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteLogEntriesResponse) ProtoMessage() {}

func (x *WriteLogEntriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteLogEntriesResponse.ProtoReflect.Descriptor instead.
func (*WriteLogEntriesResponse) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_proto_rawDescGZIP(), []int{2}
}

// Error details for WriteLogEntries with partial success.
type WriteLogEntriesPartialErrors struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// When `WriteLogEntriesRequest.partial_success` is true, records the error
	// status for entries that were not written due to a permanent error, keyed
	// by the entry's zero-based index in `WriteLogEntriesRequest.entries`.
	//
	// Failed requests for which no entries are written will not include
	// per-entry errors.
	LogEntryErrors map[int32]*status.Status `protobuf:"bytes,1,rep,name=log_entry_errors,json=logEntryErrors,proto3" json:"log_entry_errors,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *WriteLogEntriesPartialErrors) Reset() {
	*x = WriteLogEntriesPartialErrors{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WriteLogEntriesPartialErrors) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WriteLogEntriesPartialErrors) ProtoMessage() {}

func (x *WriteLogEntriesPartialErrors) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WriteLogEntriesPartialErrors.ProtoReflect.Descriptor instead.
func (*WriteLogEntriesPartialErrors) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_proto_rawDescGZIP(), []int{3}
}

func (x *WriteLogEntriesPartialErrors) GetLogEntryErrors() map[int32]*status.Status {
	if x != nil {
		return x.LogEntryErrors
	}
	return nil
}

// The parameters to `ListLogEntries`.
type ListLogEntriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Names of one or more parent resources from which to
	// retrieve log entries:
	//
	// *  `projects/[PROJECT_ID]`
	// *  `organizations/[ORGANIZATION_ID]`
	// *  `billingAccounts/[BILLING_ACCOUNT_ID]`
	// *  `folders/[FOLDER_ID]`
	//
	// May alternatively be one or more views:
	//
	//   - `projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]`
	//   - `organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]`
	//   - `billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]`
	//   - `folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]`
	//
	// Projects listed in the `project_ids` field are added to this list.
	ResourceNames []string `protobuf:"bytes,8,rep,name=resource_names,json=resourceNames,proto3" json:"resource_names,omitempty"`
	// Optional. A filter that chooses which log entries to return.  See [Advanced
	// Logs Queries](https://cloud.google.com/logging/docs/view/advanced-queries).
	// Only log entries that match the filter are returned.  An empty filter
	// matches all log entries in the resources listed in `resource_names`.
	// Referencing a parent resource that is not listed in `resource_names` will
	// cause the filter to return no results. The maximum length of the filter is
	// 20000 characters.
	Filter string `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// Optional. How the results should be sorted.  Presently, the only permitted
	// values are `"timestamp asc"` (default) and `"timestamp desc"`. The first
	// option returns entries in order of increasing values of
	// `LogEntry.timestamp` (oldest first), and the second option returns entries
	// in order of decreasing timestamps (newest first).  Entries with equal
	// timestamps are returned in order of their `insert_id` values.
	OrderBy string `protobuf:"bytes,3,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// Optional. The maximum number of results to return from this request. Default is 50.
	// If the value is negative or exceeds 1000, the request is rejected. The
	// presence of `next_page_token` in the response indicates that more results
	// might be available.
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional. If present, then retrieve the next batch of results from the
	// preceding call to this method.  `page_token` must be the value of
	// `next_page_token` from the previous response.  The values of other method
	// parameters should be identical to those in the previous call.
	PageToken string `protobuf:"bytes,5,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
}

func (x *ListLogEntriesRequest) Reset() {
	*x = ListLogEntriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLogEntriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLogEntriesRequest) ProtoMessage() {}

func (x *ListLogEntriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLogEntriesRequest.ProtoReflect.Descriptor instead.
func (*ListLogEntriesRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_proto_rawDescGZIP(), []int{4}
}

func (x *ListLogEntriesRequest) GetResourceNames() []string {
	if x != nil {
		return x.ResourceNames
	}
	return nil
}

func (x *ListLogEntriesRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListLogEntriesRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListLogEntriesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListLogEntriesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// Result returned from `ListLogEntries`.
type ListLogEntriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of log entries.  If `entries` is empty, `nextPageToken` may still be
	// returned, indicating that more entries may exist.  See `nextPageToken` for
	// more information.
	Entries []*LogEntry `protobuf:"bytes,1,rep,name=entries,proto3" json:"entries,omitempty"`
	// If there might be more results than those appearing in this response, then
	// `nextPageToken` is included.  To get the next set of results, call this
	// method again using the value of `nextPageToken` as `pageToken`.
	//
	// If a value for `next_page_token` appears and the `entries` field is empty,
	// it means that the search found no log entries so far but it did not have
	// time to search all the possible log entries.  Retry the method with this
	// value for `page_token` to continue the search.  Alternatively, consider
	// speeding up the search by changing your filter to specify a single log name
	// or resource type, or to narrow the time range of the search.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListLogEntriesResponse) Reset() {
	*x = ListLogEntriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLogEntriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLogEntriesResponse) ProtoMessage() {}

func (x *ListLogEntriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLogEntriesResponse.ProtoReflect.Descriptor instead.
func (*ListLogEntriesResponse) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_proto_rawDescGZIP(), []int{5}
}

func (x *ListLogEntriesResponse) GetEntries() []*LogEntry {
	if x != nil {
		return x.Entries
	}
	return nil
}

func (x *ListLogEntriesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// The parameters to ListMonitoredResourceDescriptors
type ListMonitoredResourceDescriptorsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional. The maximum number of results to return from this request.
	// Non-positive values are ignored.  The presence of `nextPageToken` in the
	// response indicates that more results might be available.
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional. If present, then retrieve the next batch of results from the
	// preceding call to this method.  `pageToken` must be the value of
	// `nextPageToken` from the previous response.  The values of other method
	// parameters should be identical to those in the previous call.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
}

func (x *ListMonitoredResourceDescriptorsRequest) Reset() {
	*x = ListMonitoredResourceDescriptorsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMonitoredResourceDescriptorsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMonitoredResourceDescriptorsRequest) ProtoMessage() {}

func (x *ListMonitoredResourceDescriptorsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMonitoredResourceDescriptorsRequest.ProtoReflect.Descriptor instead.
func (*ListMonitoredResourceDescriptorsRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_proto_rawDescGZIP(), []int{6}
}

func (x *ListMonitoredResourceDescriptorsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListMonitoredResourceDescriptorsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// Result returned from ListMonitoredResourceDescriptors.
type ListMonitoredResourceDescriptorsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of resource descriptors.
	ResourceDescriptors []*monitoredres.MonitoredResourceDescriptor `protobuf:"bytes,1,rep,name=resource_descriptors,json=resourceDescriptors,proto3" json:"resource_descriptors,omitempty"`
	// If there might be more results than those appearing in this response, then
	// `nextPageToken` is included.  To get the next set of results, call this
	// method again using the value of `nextPageToken` as `pageToken`.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListMonitoredResourceDescriptorsResponse) Reset() {
	*x = ListMonitoredResourceDescriptorsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListMonitoredResourceDescriptorsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMonitoredResourceDescriptorsResponse) ProtoMessage() {}

func (x *ListMonitoredResourceDescriptorsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMonitoredResourceDescriptorsResponse.ProtoReflect.Descriptor instead.
func (*ListMonitoredResourceDescriptorsResponse) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_proto_rawDescGZIP(), []int{7}
}

func (x *ListMonitoredResourceDescriptorsResponse) GetResourceDescriptors() []*monitoredres.MonitoredResourceDescriptor {
	if x != nil {
		return x.ResourceDescriptors
	}
	return nil
}

func (x *ListMonitoredResourceDescriptorsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// The parameters to ListLogs.
type ListLogsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name that owns the logs:
	//
	// *  `projects/[PROJECT_ID]`
	// *  `organizations/[ORGANIZATION_ID]`
	// *  `billingAccounts/[BILLING_ACCOUNT_ID]`
	// *  `folders/[FOLDER_ID]`
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Optional. The maximum number of results to return from this request.
	// Non-positive values are ignored.  The presence of `nextPageToken` in the
	// response indicates that more results might be available.
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// Optional. If present, then retrieve the next batch of results from the
	// preceding call to this method.  `pageToken` must be the value of
	// `nextPageToken` from the previous response.  The values of other method
	// parameters should be identical to those in the previous call.
	PageToken string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional. The resource name that owns the logs:
	//
	//   - `projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]`
	//   - `organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]`
	//   - `billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]`
	//   - `folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]`
	//
	// To support legacy queries, it could also be:
	//
	// *  `projects/[PROJECT_ID]`
	// *  `organizations/[ORGANIZATION_ID]`
	// *  `billingAccounts/[BILLING_ACCOUNT_ID]`
	// *  `folders/[FOLDER_ID]`
	ResourceNames []string `protobuf:"bytes,8,rep,name=resource_names,json=resourceNames,proto3" json:"resource_names,omitempty"`
}

func (x *ListLogsRequest) Reset() {
	*x = ListLogsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLogsRequest) ProtoMessage() {}

func (x *ListLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLogsRequest.ProtoReflect.Descriptor instead.
func (*ListLogsRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_proto_rawDescGZIP(), []int{8}
}

func (x *ListLogsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ListLogsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListLogsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListLogsRequest) GetResourceNames() []string {
	if x != nil {
		return x.ResourceNames
	}
	return nil
}

// Result returned from ListLogs.
type ListLogsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of log names. For example,
	// `"projects/my-project/logs/syslog"` or
	// `"organizations/123/logs/cloudresourcemanager.googleapis.com%2Factivity"`.
	LogNames []string `protobuf:"bytes,3,rep,name=log_names,json=logNames,proto3" json:"log_names,omitempty"`
	// If there might be more results than those appearing in this response, then
	// `nextPageToken` is included.  To get the next set of results, call this
	// method again using the value of `nextPageToken` as `pageToken`.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListLogsResponse) Reset() {
	*x = ListLogsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLogsResponse) ProtoMessage() {}

func (x *ListLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLogsResponse.ProtoReflect.Descriptor instead.
func (*ListLogsResponse) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_proto_rawDescGZIP(), []int{9}
}

func (x *ListLogsResponse) GetLogNames() []string {
	if x != nil {
		return x.LogNames
	}
	return nil
}

func (x *ListLogsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// The parameters to `TailLogEntries`.
type TailLogEntriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Name of a parent resource from which to retrieve log entries:
	//
	// *  `projects/[PROJECT_ID]`
	// *  `organizations/[ORGANIZATION_ID]`
	// *  `billingAccounts/[BILLING_ACCOUNT_ID]`
	// *  `folders/[FOLDER_ID]`
	//
	// May alternatively be one or more views:
	//
	//   - `projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]`
	//   - `organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]`
	//   - `billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]`
	//   - `folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]`
	ResourceNames []string `protobuf:"bytes,1,rep,name=resource_names,json=resourceNames,proto3" json:"resource_names,omitempty"`
	// Optional. A filter that chooses which log entries to return.  See [Advanced
	// Logs Filters](https://cloud.google.com/logging/docs/view/advanced_filters).
	// Only log entries that match the filter are returned.  An empty filter
	// matches all log entries in the resources listed in `resource_names`.
	// Referencing a parent resource that is not in `resource_names` will cause
	// the filter to return no results. The maximum length of the filter is 20000
	// characters.
	Filter string `protobuf:"bytes,2,opt,name=filter,proto3" json:"filter,omitempty"`
	// Optional. The amount of time to buffer log entries at the server before
	// being returned to prevent out of order results due to late arriving log
	// entries. Valid values are between 0-60000 milliseconds. Defaults to 2000
	// milliseconds.
	BufferWindow *durationpb.Duration `protobuf:"bytes,3,opt,name=buffer_window,json=bufferWindow,proto3" json:"buffer_window,omitempty"`
}

func (x *TailLogEntriesRequest) Reset() {
	*x = TailLogEntriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TailLogEntriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TailLogEntriesRequest) ProtoMessage() {}

func (x *TailLogEntriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TailLogEntriesRequest.ProtoReflect.Descriptor instead.
func (*TailLogEntriesRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_proto_rawDescGZIP(), []int{10}
}

func (x *TailLogEntriesRequest) GetResourceNames() []string {
	if x != nil {
		return x.ResourceNames
	}
	return nil
}

func (x *TailLogEntriesRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *TailLogEntriesRequest) GetBufferWindow() *durationpb.Duration {
	if x != nil {
		return x.BufferWindow
	}
	return nil
}

// Result returned from `TailLogEntries`.
type TailLogEntriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of log entries. Each response in the stream will order entries with
	// increasing values of `LogEntry.timestamp`. Ordering is not guaranteed
	// between separate responses.
	Entries []*LogEntry `protobuf:"bytes,1,rep,name=entries,proto3" json:"entries,omitempty"`
	// If entries that otherwise would have been included in the session were not
	// sent back to the client, counts of relevant entries omitted from the
	// session with the reason that they were not included. There will be at most
	// one of each reason per response. The counts represent the number of
	// suppressed entries since the last streamed response.
	SuppressionInfo []*TailLogEntriesResponse_SuppressionInfo `protobuf:"bytes,2,rep,name=suppression_info,json=suppressionInfo,proto3" json:"suppression_info,omitempty"`
}

func (x *TailLogEntriesResponse) Reset() {
	*x = TailLogEntriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TailLogEntriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TailLogEntriesResponse) ProtoMessage() {}

func (x *TailLogEntriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TailLogEntriesResponse.ProtoReflect.Descriptor instead.
func (*TailLogEntriesResponse) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_proto_rawDescGZIP(), []int{11}
}

func (x *TailLogEntriesResponse) GetEntries() []*LogEntry {
	if x != nil {
		return x.Entries
	}
	return nil
}

func (x *TailLogEntriesResponse) GetSuppressionInfo() []*TailLogEntriesResponse_SuppressionInfo {
	if x != nil {
		return x.SuppressionInfo
	}
	return nil
}

// Information about entries that were omitted from the session.
type TailLogEntriesResponse_SuppressionInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The reason that entries were omitted from the session.
	Reason TailLogEntriesResponse_SuppressionInfo_Reason `protobuf:"varint,1,opt,name=reason,proto3,enum=google.logging.v2.TailLogEntriesResponse_SuppressionInfo_Reason" json:"reason,omitempty"`
	// A lower bound on the count of entries omitted due to `reason`.
	SuppressedCount int32 `protobuf:"varint,2,opt,name=suppressed_count,json=suppressedCount,proto3" json:"suppressed_count,omitempty"`
}

func (x *TailLogEntriesResponse_SuppressionInfo) Reset() {
	*x = TailLogEntriesResponse_SuppressionInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TailLogEntriesResponse_SuppressionInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TailLogEntriesResponse_SuppressionInfo) ProtoMessage() {}

func (x *TailLogEntriesResponse_SuppressionInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TailLogEntriesResponse_SuppressionInfo.ProtoReflect.Descriptor instead.
func (*TailLogEntriesResponse_SuppressionInfo) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_proto_rawDescGZIP(), []int{11, 0}
}

func (x *TailLogEntriesResponse_SuppressionInfo) GetReason() TailLogEntriesResponse_SuppressionInfo_Reason {
	if x != nil {
		return x.Reason
	}
	return TailLogEntriesResponse_SuppressionInfo_REASON_UNSPECIFIED
}

func (x *TailLogEntriesResponse_SuppressionInfo) GetSuppressedCount() int32 {
	if x != nil {
		return x.SuppressedCount
	}
	return 0
}

var File_google_logging_v2_logging_proto protoreflect.FileDescriptor

var file_google_logging_v2_logging_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x32, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x11, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65,
	0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72,
	0x65, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x21, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x2f,
	0x6c, 0x6f, 0x67, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x72, 0x70, 0x63, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x51, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c,
	0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3d, 0x0a, 0x08, 0x6c, 0x6f, 0x67,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x22, 0xe0, 0x41, 0x02,
	0xfa, 0x41, 0x1c, 0x0a, 0x1a, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x52,
	0x07, 0x6c, 0x6f, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xae, 0x03, 0x0a, 0x16, 0x57, 0x72, 0x69,
	0x74, 0x65, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3d, 0x0a, 0x08, 0x6c, 0x6f, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x22, 0xe0, 0x41, 0x01, 0xfa, 0x41, 0x1c, 0x0a, 0x1a, 0x6c,
	0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69,
	0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x52, 0x07, 0x6c, 0x6f, 0x67, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x08, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x12, 0x52, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x35, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x45,
	0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x06,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x3a, 0x0a, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x65, 0x73, 0x12, 0x2c, 0x0a, 0x0f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x73, 0x75,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x01,
	0x52, 0x0e, 0x70, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x12, 0x1c, 0x0a, 0x07, 0x64, 0x72, 0x79, 0x5f, 0x72, 0x75, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x08, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x06, 0x64, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x1a, 0x39,
	0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x19, 0x0a, 0x17, 0x57, 0x72, 0x69,
	0x74, 0x65, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0xe4, 0x01, 0x0a, 0x1c, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x6f,
	0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x73, 0x12, 0x6d, 0x0a, 0x10, 0x6c, 0x6f, 0x67, 0x5f, 0x65, 0x6e, 0x74,
	0x72, 0x79, 0x5f, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x43, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72,
	0x69, 0x65, 0x73, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73,
	0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x6c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x73, 0x1a, 0x55, 0x0a, 0x13, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x28, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x72, 0x70, 0x63, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xe5, 0x01, 0x0a, 0x15,
	0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x49, 0x0a, 0x0e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x42, 0x22, 0xe0,
	0x41, 0x02, 0xfa, 0x41, 0x1c, 0x12, 0x1a, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f,
	0x67, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x12, 0x1b, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x1e, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x03, 0xe0, 0x41, 0x01, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x20, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05,
	0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12,
	0x22, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x22, 0x77, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x45, 0x6e,
	0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a,
	0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x65, 0x6e, 0x74,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e,
	0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x6f, 0x0a, 0x27,
	0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x65, 0x64, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x01, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xae, 0x01,
	0x0a, 0x28, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f,
	0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5a, 0x0a, 0x14, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f,
	0x72, 0x52, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xde,
	0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x22, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x1c, 0x12, 0x1a, 0x6c, 0x6f, 0x67, 0x67,
	0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x20,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x22, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x49, 0x0a, 0x0e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x42, 0x22, 0xe0, 0x41,
	0x01, 0xfa, 0x41, 0x1c, 0x12, 0x1a, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67,
	0x52, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22,
	0x57, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xa5, 0x01, 0x0a, 0x15, 0x54, 0x61, 0x69,
	0x6c, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2a, 0x0a, 0x0e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x1b,
	0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03,
	0xe0, 0x41, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x43, 0x0a, 0x0d, 0x62,
	0x75, 0x66, 0x66, 0x65, 0x72, 0x5f, 0x77, 0x69, 0x6e, 0x64, 0x6f, 0x77, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0,
	0x41, 0x01, 0x52, 0x0c, 0x62, 0x75, 0x66, 0x66, 0x65, 0x72, 0x57, 0x69, 0x6e, 0x64, 0x6f, 0x77,
	0x22, 0x92, 0x03, 0x0a, 0x16, 0x54, 0x61, 0x69, 0x6c, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x35, 0x0a, 0x07, 0x65,
	0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x65, 0x73, 0x12, 0x64, 0x0a, 0x10, 0x73, 0x75, 0x70, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f,
	0x6e, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x54, 0x61, 0x69, 0x6c, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x75, 0x70, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0f, 0x73, 0x75, 0x70, 0x70, 0x72, 0x65, 0x73,
	0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0xda, 0x01, 0x0a, 0x0f, 0x53, 0x75, 0x70,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x58, 0x0a, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x54, 0x61, 0x69, 0x6c, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x53, 0x75, 0x70, 0x70, 0x72, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x52, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x75, 0x70, 0x70, 0x72, 0x65,
	0x73, 0x73, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0f, 0x73, 0x75, 0x70, 0x70, 0x72, 0x65, 0x73, 0x73, 0x65, 0x64, 0x43, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x42, 0x0a, 0x06, 0x52, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x12, 0x52,
	0x45, 0x41, 0x53, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x41, 0x54, 0x45, 0x5f, 0x4c, 0x49, 0x4d, 0x49,
	0x54, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x4f, 0x54, 0x5f, 0x43, 0x4f, 0x4e, 0x53, 0x55,
	0x4d, 0x45, 0x44, 0x10, 0x02, 0x32, 0xe6, 0x0b, 0x0a, 0x10, 0x4c, 0x6f, 0x67, 0x67, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x56, 0x32, 0x12, 0x93, 0x02, 0x0a, 0x09, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x12, 0x23, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xc8, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xb6, 0x01, 0x2a,
	0x20, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6c, 0x6f, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x67, 0x73, 0x2f, 0x2a,
	0x7d, 0x5a, 0x1b, 0x2a, 0x19, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6c, 0x6f, 0x67, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x2a, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x67, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x27,
	0x2a, 0x25, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6c, 0x6f, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f,
	0x6c, 0x6f, 0x67, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x21, 0x2a, 0x1f, 0x2f, 0x76, 0x32, 0x2f, 0x7b,
	0x6c, 0x6f, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73,
	0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x67, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x29, 0x2a, 0x27, 0x2f, 0x76,
	0x32, 0x2f, 0x7b, 0x6c, 0x6f, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f,
	0x67, 0x73, 0x2f, 0x2a, 0x7d, 0xda, 0x41, 0x08, 0x6c, 0x6f, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0xa9, 0x01, 0x0a, 0x0f, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x29, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f,
	0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x6f,
	0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x57, 0x72, 0x69, 0x74, 0x65, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72,
	0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3f, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x16, 0x22, 0x11, 0x2f, 0x76, 0x32, 0x2f, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73,
	0x3a, 0x77, 0x72, 0x69, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0xda, 0x41, 0x20, 0x6c, 0x6f, 0x67, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2c, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x2c, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0xa3, 0x01, 0x0a,
	0x0e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12,
	0x28, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x69,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x22, 0x10, 0x2f, 0x76,
	0x32, 0x2f, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x3a, 0x6c, 0x69, 0x73, 0x74, 0x3a, 0x01,
	0x2a, 0xda, 0x41, 0x1e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x2c, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x2c, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x62, 0x79, 0x12, 0xc5, 0x01, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x3a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x3b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67,
	0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x12, 0x20, 0x2f, 0x76, 0x32, 0x2f, 0x6d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72, 0x73, 0x12, 0x88, 0x02, 0x0a, 0x08, 0x4c,
	0x69, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x12, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x4c, 0x6f, 0x67, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0xb2, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xa2, 0x01, 0x12, 0x15, 0x2f, 0x76, 0x32, 0x2f,
	0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x2a, 0x2f, 0x2a, 0x7d, 0x2f, 0x6c, 0x6f, 0x67,
	0x73, 0x5a, 0x1e, 0x12, 0x1c, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x6c, 0x6f, 0x67,
	0x73, 0x5a, 0x23, 0x12, 0x21, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a,
	0x7d, 0x2f, 0x6c, 0x6f, 0x67, 0x73, 0x5a, 0x1d, 0x12, 0x1b, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d,
	0x2f, 0x6c, 0x6f, 0x67, 0x73, 0x5a, 0x25, 0x12, 0x23, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x6c, 0x6f, 0x67, 0x73, 0xda, 0x41, 0x06, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x86, 0x01, 0x0a, 0x0e, 0x54, 0x61, 0x69, 0x6c, 0x4c, 0x6f,
	0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x28, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x69,
	0x6c, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x29, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x54, 0x61, 0x69, 0x6c, 0x4c, 0x6f, 0x67, 0x45, 0x6e,
	0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1b, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x15, 0x22, 0x10, 0x2f, 0x76, 0x32, 0x2f, 0x65, 0x6e, 0x74, 0x72, 0x69,
	0x65, 0x73, 0x3a, 0x74, 0x61, 0x69, 0x6c, 0x3a, 0x01, 0x2a, 0x28, 0x01, 0x30, 0x01, 0x1a, 0x8d,
	0x02, 0xca, 0x41, 0x16, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0xd2, 0x41, 0xf0, 0x01, 0x68, 0x74,
	0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2c, 0x68, 0x74, 0x74,
	0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x72, 0x65, 0x61, 0x64,
	0x2d, 0x6f, 0x6e, 0x6c, 0x79, 0x2c, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77,
	0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2c, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61,
	0x75, 0x74, 0x68, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x72, 0x65, 0x61, 0x64,
	0x2c, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68,
	0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x77, 0x72, 0x69, 0x74, 0x65, 0x42, 0xb5,
	0x01, 0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f,
	0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x42, 0x0c, 0x4c, 0x6f, 0x67, 0x67, 0x69, 0x6e,
	0x67, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f,
	0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x32, 0x3b, 0x6c, 0x6f, 0x67, 0x67, 0x69,
	0x6e, 0x67, 0xf8, 0x01, 0x01, 0xaa, 0x02, 0x17, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x43,
	0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x4c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x32, 0xca,
	0x02, 0x17, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x4c,
	0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x5c, 0x56, 0x32, 0xea, 0x02, 0x1a, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x3a, 0x3a, 0x4c, 0x6f, 0x67, 0x67, 0x69,
	0x6e, 0x67, 0x3a, 0x3a, 0x56, 0x32, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_logging_v2_logging_proto_rawDescOnce sync.Once
	file_google_logging_v2_logging_proto_rawDescData = file_google_logging_v2_logging_proto_rawDesc
)

func file_google_logging_v2_logging_proto_rawDescGZIP() []byte {
	file_google_logging_v2_logging_proto_rawDescOnce.Do(func() {
		file_google_logging_v2_logging_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_logging_v2_logging_proto_rawDescData)
	})
	return file_google_logging_v2_logging_proto_rawDescData
}

var file_google_logging_v2_logging_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_google_logging_v2_logging_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_google_logging_v2_logging_proto_goTypes = []interface{}{
	(TailLogEntriesResponse_SuppressionInfo_Reason)(0), // 0: google.logging.v2.TailLogEntriesResponse.SuppressionInfo.Reason
	(*DeleteLogRequest)(nil),                           // 1: google.logging.v2.DeleteLogRequest
	(*WriteLogEntriesRequest)(nil),                     // 2: google.logging.v2.WriteLogEntriesRequest
	(*WriteLogEntriesResponse)(nil),                    // 3: google.logging.v2.WriteLogEntriesResponse
	(*WriteLogEntriesPartialErrors)(nil),               // 4: google.logging.v2.WriteLogEntriesPartialErrors
	(*ListLogEntriesRequest)(nil),                      // 5: google.logging.v2.ListLogEntriesRequest
	(*ListLogEntriesResponse)(nil),                     // 6: google.logging.v2.ListLogEntriesResponse
	(*ListMonitoredResourceDescriptorsRequest)(nil),    // 7: google.logging.v2.ListMonitoredResourceDescriptorsRequest
	(*ListMonitoredResourceDescriptorsResponse)(nil),   // 8: google.logging.v2.ListMonitoredResourceDescriptorsResponse
	(*ListLogsRequest)(nil),                            // 9: google.logging.v2.ListLogsRequest
	(*ListLogsResponse)(nil),                           // 10: google.logging.v2.ListLogsResponse
	(*TailLogEntriesRequest)(nil),                      // 11: google.logging.v2.TailLogEntriesRequest
	(*TailLogEntriesResponse)(nil),                     // 12: google.logging.v2.TailLogEntriesResponse
	nil,                                                // 13: google.logging.v2.WriteLogEntriesRequest.LabelsEntry
	nil,                                                // 14: google.logging.v2.WriteLogEntriesPartialErrors.LogEntryErrorsEntry
	(*TailLogEntriesResponse_SuppressionInfo)(nil),     // 15: google.logging.v2.TailLogEntriesResponse.SuppressionInfo
	(*monitoredres.MonitoredResource)(nil),             // 16: google.api.MonitoredResource
	(*LogEntry)(nil),                                   // 17: google.logging.v2.LogEntry
	(*monitoredres.MonitoredResourceDescriptor)(nil),   // 18: google.api.MonitoredResourceDescriptor
	(*durationpb.Duration)(nil),                        // 19: google.protobuf.Duration
	(*status.Status)(nil),                              // 20: google.rpc.Status
	(*emptypb.Empty)(nil),                              // 21: google.protobuf.Empty
}
var file_google_logging_v2_logging_proto_depIdxs = []int32{
	16, // 0: google.logging.v2.WriteLogEntriesRequest.resource:type_name -> google.api.MonitoredResource
	13, // 1: google.logging.v2.WriteLogEntriesRequest.labels:type_name -> google.logging.v2.WriteLogEntriesRequest.LabelsEntry
	17, // 2: google.logging.v2.WriteLogEntriesRequest.entries:type_name -> google.logging.v2.LogEntry
	14, // 3: google.logging.v2.WriteLogEntriesPartialErrors.log_entry_errors:type_name -> google.logging.v2.WriteLogEntriesPartialErrors.LogEntryErrorsEntry
	17, // 4: google.logging.v2.ListLogEntriesResponse.entries:type_name -> google.logging.v2.LogEntry
	18, // 5: google.logging.v2.ListMonitoredResourceDescriptorsResponse.resource_descriptors:type_name -> google.api.MonitoredResourceDescriptor
	19, // 6: google.logging.v2.TailLogEntriesRequest.buffer_window:type_name -> google.protobuf.Duration
	17, // 7: google.logging.v2.TailLogEntriesResponse.entries:type_name -> google.logging.v2.LogEntry
	15, // 8: google.logging.v2.TailLogEntriesResponse.suppression_info:type_name -> google.logging.v2.TailLogEntriesResponse.SuppressionInfo
	20, // 9: google.logging.v2.WriteLogEntriesPartialErrors.LogEntryErrorsEntry.value:type_name -> google.rpc.Status
	0,  // 10: google.logging.v2.TailLogEntriesResponse.SuppressionInfo.reason:type_name -> google.logging.v2.TailLogEntriesResponse.SuppressionInfo.Reason
	1,  // 11: google.logging.v2.LoggingServiceV2.DeleteLog:input_type -> google.logging.v2.DeleteLogRequest
	2,  // 12: google.logging.v2.LoggingServiceV2.WriteLogEntries:input_type -> google.logging.v2.WriteLogEntriesRequest
	5,  // 13: google.logging.v2.LoggingServiceV2.ListLogEntries:input_type -> google.logging.v2.ListLogEntriesRequest
	7,  // 14: google.logging.v2.LoggingServiceV2.ListMonitoredResourceDescriptors:input_type -> google.logging.v2.ListMonitoredResourceDescriptorsRequest
	9,  // 15: google.logging.v2.LoggingServiceV2.ListLogs:input_type -> google.logging.v2.ListLogsRequest
	11, // 16: google.logging.v2.LoggingServiceV2.TailLogEntries:input_type -> google.logging.v2.TailLogEntriesRequest
	21, // 17: google.logging.v2.LoggingServiceV2.DeleteLog:output_type -> google.protobuf.Empty
	3,  // 18: google.logging.v2.LoggingServiceV2.WriteLogEntries:output_type -> google.logging.v2.WriteLogEntriesResponse
	6,  // 19: google.logging.v2.LoggingServiceV2.ListLogEntries:output_type -> google.logging.v2.ListLogEntriesResponse
	8,  // 20: google.logging.v2.LoggingServiceV2.ListMonitoredResourceDescriptors:output_type -> google.logging.v2.ListMonitoredResourceDescriptorsResponse
	10, // 21: google.logging.v2.LoggingServiceV2.ListLogs:output_type -> google.logging.v2.ListLogsResponse
	12, // 22: google.logging.v2.LoggingServiceV2.TailLogEntries:output_type -> google.logging.v2.TailLogEntriesResponse
	17, // [17:23] is the sub-list for method output_type
	11, // [11:17] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_google_logging_v2_logging_proto_init() }
func file_google_logging_v2_logging_proto_init() {
	if File_google_logging_v2_logging_proto != nil {
		return
	}
	file_google_logging_v2_log_entry_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_logging_v2_logging_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteLogRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WriteLogEntriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WriteLogEntriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WriteLogEntriesPartialErrors); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLogEntriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLogEntriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMonitoredResourceDescriptorsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListMonitoredResourceDescriptorsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLogsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListLogsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TailLogEntriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TailLogEntriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TailLogEntriesResponse_SuppressionInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_logging_v2_logging_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_google_logging_v2_logging_proto_goTypes,
		DependencyIndexes: file_google_logging_v2_logging_proto_depIdxs,
		EnumInfos:         file_google_logging_v2_logging_proto_enumTypes,
		MessageInfos:      file_google_logging_v2_logging_proto_msgTypes,
	}.Build()
	File_google_logging_v2_logging_proto = out.File
	file_google_logging_v2_logging_proto_rawDesc = nil
	file_google_logging_v2_logging_proto_goTypes = nil
	file_google_logging_v2_logging_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// LoggingServiceV2Client is the client API for LoggingServiceV2 service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type LoggingServiceV2Client interface {
	// Deletes all the log entries in a log for the _Default Log Bucket. The log
	// reappears if it receives new entries. Log entries written shortly before
	// the delete operation might not be deleted. Entries received after the
	// delete operation with a timestamp before the operation will be deleted.
	DeleteLog(ctx context.Context, in *DeleteLogRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Writes log entries to Logging. This API method is the
	// only way to send log entries to Logging. This method
	// is used, directly or indirectly, by the Logging agent
	// (fluentd) and all logging libraries configured to use Logging.
	// A single request may contain log entries for a maximum of 1000
	// different resources (projects, organizations, billing accounts or
	// folders)
	WriteLogEntries(ctx context.Context, in *WriteLogEntriesRequest, opts ...grpc.CallOption) (*WriteLogEntriesResponse, error)
	// Lists log entries.  Use this method to retrieve log entries that originated
	// from a project/folder/organization/billing account.  For ways to export log
	// entries, see [Exporting
	// Logs](https://cloud.google.com/logging/docs/export).
	ListLogEntries(ctx context.Context, in *ListLogEntriesRequest, opts ...grpc.CallOption) (*ListLogEntriesResponse, error)
	// Lists the descriptors for monitored resource types used by Logging.
	ListMonitoredResourceDescriptors(ctx context.Context, in *ListMonitoredResourceDescriptorsRequest, opts ...grpc.CallOption) (*ListMonitoredResourceDescriptorsResponse, error)
	// Lists the logs in projects, organizations, folders, or billing accounts.
	// Only logs that have entries are listed.
	ListLogs(ctx context.Context, in *ListLogsRequest, opts ...grpc.CallOption) (*ListLogsResponse, error)
	// Streaming read of log entries as they are ingested. Until the stream is
	// terminated, it will continue reading logs.
	TailLogEntries(ctx context.Context, opts ...grpc.CallOption) (LoggingServiceV2_TailLogEntriesClient, error)
}

type loggingServiceV2Client struct {
	cc grpc.ClientConnInterface
}

func NewLoggingServiceV2Client(cc grpc.ClientConnInterface) LoggingServiceV2Client {
	return &loggingServiceV2Client{cc}
}

func (c *loggingServiceV2Client) DeleteLog(ctx context.Context, in *DeleteLogRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/google.logging.v2.LoggingServiceV2/DeleteLog", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loggingServiceV2Client) WriteLogEntries(ctx context.Context, in *WriteLogEntriesRequest, opts ...grpc.CallOption) (*WriteLogEntriesResponse, error) {
	out := new(WriteLogEntriesResponse)
	err := c.cc.Invoke(ctx, "/google.logging.v2.LoggingServiceV2/WriteLogEntries", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loggingServiceV2Client) ListLogEntries(ctx context.Context, in *ListLogEntriesRequest, opts ...grpc.CallOption) (*ListLogEntriesResponse, error) {
	out := new(ListLogEntriesResponse)
	err := c.cc.Invoke(ctx, "/google.logging.v2.LoggingServiceV2/ListLogEntries", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loggingServiceV2Client) ListMonitoredResourceDescriptors(ctx context.Context, in *ListMonitoredResourceDescriptorsRequest, opts ...grpc.CallOption) (*ListMonitoredResourceDescriptorsResponse, error) {
	out := new(ListMonitoredResourceDescriptorsResponse)
	err := c.cc.Invoke(ctx, "/google.logging.v2.LoggingServiceV2/ListMonitoredResourceDescriptors", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loggingServiceV2Client) ListLogs(ctx context.Context, in *ListLogsRequest, opts ...grpc.CallOption) (*ListLogsResponse, error) {
	out := new(ListLogsResponse)
	err := c.cc.Invoke(ctx, "/google.logging.v2.LoggingServiceV2/ListLogs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *loggingServiceV2Client) TailLogEntries(ctx context.Context, opts ...grpc.CallOption) (LoggingServiceV2_TailLogEntriesClient, error) {
	stream, err := c.cc.NewStream(ctx, &_LoggingServiceV2_serviceDesc.Streams[0], "/google.logging.v2.LoggingServiceV2/TailLogEntries", opts...)
	if err != nil {
		return nil, err
	}
	x := &loggingServiceV2TailLogEntriesClient{stream}
	return x, nil
}

type LoggingServiceV2_TailLogEntriesClient interface {
	Send(*TailLogEntriesRequest) error
	Recv() (*TailLogEntriesResponse, error)
	grpc.ClientStream
}

type loggingServiceV2TailLogEntriesClient struct {
	grpc.ClientStream
}

func (x *loggingServiceV2TailLogEntriesClient) Send(m *TailLogEntriesRequest) error {
	return x.ClientStream.SendMsg(m)
}

func (x *loggingServiceV2TailLogEntriesClient) Recv() (*TailLogEntriesResponse, error) {
	m := new(TailLogEntriesResponse)
	if err := x.ClientStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

// LoggingServiceV2Server is the server API for LoggingServiceV2 service.
type LoggingServiceV2Server interface {
	// Deletes all the log entries in a log for the _Default Log Bucket. The log
	// reappears if it receives new entries. Log entries written shortly before
	// the delete operation might not be deleted. Entries received after the
	// delete operation with a timestamp before the operation will be deleted.
	DeleteLog(context.Context, *DeleteLogRequest) (*emptypb.Empty, error)
	// Writes log entries to Logging. This API method is the
	// only way to send log entries to Logging. This method
	// is used, directly or indirectly, by the Logging agent
	// (fluentd) and all logging libraries configured to use Logging.
	// A single request may contain log entries for a maximum of 1000
	// different resources (projects, organizations, billing accounts or
	// folders)
	WriteLogEntries(context.Context, *WriteLogEntriesRequest) (*WriteLogEntriesResponse, error)
	// Lists log entries.  Use this method to retrieve log entries that originated
	// from a project/folder/organization/billing account.  For ways to export log
	// entries, see [Exporting
	// Logs](https://cloud.google.com/logging/docs/export).
	ListLogEntries(context.Context, *ListLogEntriesRequest) (*ListLogEntriesResponse, error)
	// Lists the descriptors for monitored resource types used by Logging.
	ListMonitoredResourceDescriptors(context.Context, *ListMonitoredResourceDescriptorsRequest) (*ListMonitoredResourceDescriptorsResponse, error)
	// Lists the logs in projects, organizations, folders, or billing accounts.
	// Only logs that have entries are listed.
	ListLogs(context.Context, *ListLogsRequest) (*ListLogsResponse, error)
	// Streaming read of log entries as they are ingested. Until the stream is
	// terminated, it will continue reading logs.
	TailLogEntries(LoggingServiceV2_TailLogEntriesServer) error
}

// UnimplementedLoggingServiceV2Server can be embedded to have forward compatible implementations.
type UnimplementedLoggingServiceV2Server struct {
}

func (*UnimplementedLoggingServiceV2Server) DeleteLog(context.Context, *DeleteLogRequest) (*emptypb.Empty, error) {
	return nil, status1.Errorf(codes.Unimplemented, "method DeleteLog not implemented")
}
func (*UnimplementedLoggingServiceV2Server) WriteLogEntries(context.Context, *WriteLogEntriesRequest) (*WriteLogEntriesResponse, error) {
	return nil, status1.Errorf(codes.Unimplemented, "method WriteLogEntries not implemented")
}
func (*UnimplementedLoggingServiceV2Server) ListLogEntries(context.Context, *ListLogEntriesRequest) (*ListLogEntriesResponse, error) {
	return nil, status1.Errorf(codes.Unimplemented, "method ListLogEntries not implemented")
}
func (*UnimplementedLoggingServiceV2Server) ListMonitoredResourceDescriptors(context.Context, *ListMonitoredResourceDescriptorsRequest) (*ListMonitoredResourceDescriptorsResponse, error) {
	return nil, status1.Errorf(codes.Unimplemented, "method ListMonitoredResourceDescriptors not implemented")
}
func (*UnimplementedLoggingServiceV2Server) ListLogs(context.Context, *ListLogsRequest) (*ListLogsResponse, error) {
	return nil, status1.Errorf(codes.Unimplemented, "method ListLogs not implemented")
}
func (*UnimplementedLoggingServiceV2Server) TailLogEntries(LoggingServiceV2_TailLogEntriesServer) error {
	return status1.Errorf(codes.Unimplemented, "method TailLogEntries not implemented")
}

func RegisterLoggingServiceV2Server(s *grpc.Server, srv LoggingServiceV2Server) {
	s.RegisterService(&_LoggingServiceV2_serviceDesc, srv)
}

func _LoggingServiceV2_DeleteLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoggingServiceV2Server).DeleteLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.LoggingServiceV2/DeleteLog",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoggingServiceV2Server).DeleteLog(ctx, req.(*DeleteLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoggingServiceV2_WriteLogEntries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WriteLogEntriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoggingServiceV2Server).WriteLogEntries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.LoggingServiceV2/WriteLogEntries",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoggingServiceV2Server).WriteLogEntries(ctx, req.(*WriteLogEntriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoggingServiceV2_ListLogEntries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLogEntriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoggingServiceV2Server).ListLogEntries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.LoggingServiceV2/ListLogEntries",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoggingServiceV2Server).ListLogEntries(ctx, req.(*ListLogEntriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoggingServiceV2_ListMonitoredResourceDescriptors_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMonitoredResourceDescriptorsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoggingServiceV2Server).ListMonitoredResourceDescriptors(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.LoggingServiceV2/ListMonitoredResourceDescriptors",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoggingServiceV2Server).ListMonitoredResourceDescriptors(ctx, req.(*ListMonitoredResourceDescriptorsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoggingServiceV2_ListLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LoggingServiceV2Server).ListLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.LoggingServiceV2/ListLogs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LoggingServiceV2Server).ListLogs(ctx, req.(*ListLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _LoggingServiceV2_TailLogEntries_Handler(srv interface{}, stream grpc.ServerStream) error {
	return srv.(LoggingServiceV2Server).TailLogEntries(&loggingServiceV2TailLogEntriesServer{stream})
}

type LoggingServiceV2_TailLogEntriesServer interface {
	Send(*TailLogEntriesResponse) error
	Recv() (*TailLogEntriesRequest, error)
	grpc.ServerStream
}

type loggingServiceV2TailLogEntriesServer struct {
	grpc.ServerStream
}

func (x *loggingServiceV2TailLogEntriesServer) Send(m *TailLogEntriesResponse) error {
	return x.ServerStream.SendMsg(m)
}

func (x *loggingServiceV2TailLogEntriesServer) Recv() (*TailLogEntriesRequest, error) {
	m := new(TailLogEntriesRequest)
	if err := x.ServerStream.RecvMsg(m); err != nil {
		return nil, err
	}
	return m, nil
}

var _LoggingServiceV2_serviceDesc = grpc.ServiceDesc{
	ServiceName: "google.logging.v2.LoggingServiceV2",
	HandlerType: (*LoggingServiceV2Server)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DeleteLog",
			Handler:    _LoggingServiceV2_DeleteLog_Handler,
		},
		{
			MethodName: "WriteLogEntries",
			Handler:    _LoggingServiceV2_WriteLogEntries_Handler,
		},
		{
			MethodName: "ListLogEntries",
			Handler:    _LoggingServiceV2_ListLogEntries_Handler,
		},
		{
			MethodName: "ListMonitoredResourceDescriptors",
			Handler:    _LoggingServiceV2_ListMonitoredResourceDescriptors_Handler,
		},
		{
			MethodName: "ListLogs",
			Handler:    _LoggingServiceV2_ListLogs_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "TailLogEntries",
			Handler:       _LoggingServiceV2_TailLogEntries_Handler,
			ServerStreams: true,
			ClientStreams: true,
		},
	},
	Metadata: "google/logging/v2/logging.proto",
}
