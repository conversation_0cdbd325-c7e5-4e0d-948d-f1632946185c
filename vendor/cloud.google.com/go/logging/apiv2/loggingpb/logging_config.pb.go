// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.21.9
// source: google/logging/v2/logging_config.proto

package loggingpb

import (
	context "context"
	reflect "reflect"
	sync "sync"

	_ "google.golang.org/genproto/googleapis/api/annotations"
	longrunning "google.golang.org/genproto/googleapis/longrunning"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// LogBucket lifecycle states.
type LifecycleState int32

const (
	// Unspecified state. This is only used/useful for distinguishing unset
	// values.
	LifecycleState_LIFECYCLE_STATE_UNSPECIFIED LifecycleState = 0
	// The normal and active state.
	LifecycleState_ACTIVE LifecycleState = 1
	// The resource has been marked for deletion by the user. For some resources
	// (e.g. buckets), this can be reversed by an un-delete operation.
	LifecycleState_DELETE_REQUESTED LifecycleState = 2
)

// Enum value maps for LifecycleState.
var (
	LifecycleState_name = map[int32]string{
		0: "LIFECYCLE_STATE_UNSPECIFIED",
		1: "ACTIVE",
		2: "DELETE_REQUESTED",
	}
	LifecycleState_value = map[string]int32{
		"LIFECYCLE_STATE_UNSPECIFIED": 0,
		"ACTIVE":                      1,
		"DELETE_REQUESTED":            2,
	}
)

func (x LifecycleState) Enum() *LifecycleState {
	p := new(LifecycleState)
	*p = x
	return p
}

func (x LifecycleState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LifecycleState) Descriptor() protoreflect.EnumDescriptor {
	return file_google_logging_v2_logging_config_proto_enumTypes[0].Descriptor()
}

func (LifecycleState) Type() protoreflect.EnumType {
	return &file_google_logging_v2_logging_config_proto_enumTypes[0]
}

func (x LifecycleState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LifecycleState.Descriptor instead.
func (LifecycleState) EnumDescriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{0}
}

// List of different operation states.
// High level state of the operation. This is used to report the job's
// current state to the user. Once a long running operation is created,
// the current state of the operation can be queried even before the
// operation is finished and the final result is available.
type OperationState int32

const (
	// Should not be used.
	OperationState_OPERATION_STATE_UNSPECIFIED OperationState = 0
	// The operation is scheduled.
	OperationState_OPERATION_STATE_SCHEDULED OperationState = 1
	// Waiting for necessary permissions.
	OperationState_OPERATION_STATE_WAITING_FOR_PERMISSIONS OperationState = 2
	// The operation is running.
	OperationState_OPERATION_STATE_RUNNING OperationState = 3
	// The operation was completed successfully.
	OperationState_OPERATION_STATE_SUCCEEDED OperationState = 4
	// The operation failed.
	OperationState_OPERATION_STATE_FAILED OperationState = 5
	// The operation was cancelled by the user.
	OperationState_OPERATION_STATE_CANCELLED OperationState = 6
)

// Enum value maps for OperationState.
var (
	OperationState_name = map[int32]string{
		0: "OPERATION_STATE_UNSPECIFIED",
		1: "OPERATION_STATE_SCHEDULED",
		2: "OPERATION_STATE_WAITING_FOR_PERMISSIONS",
		3: "OPERATION_STATE_RUNNING",
		4: "OPERATION_STATE_SUCCEEDED",
		5: "OPERATION_STATE_FAILED",
		6: "OPERATION_STATE_CANCELLED",
	}
	OperationState_value = map[string]int32{
		"OPERATION_STATE_UNSPECIFIED":             0,
		"OPERATION_STATE_SCHEDULED":               1,
		"OPERATION_STATE_WAITING_FOR_PERMISSIONS": 2,
		"OPERATION_STATE_RUNNING":                 3,
		"OPERATION_STATE_SUCCEEDED":               4,
		"OPERATION_STATE_FAILED":                  5,
		"OPERATION_STATE_CANCELLED":               6,
	}
)

func (x OperationState) Enum() *OperationState {
	p := new(OperationState)
	*p = x
	return p
}

func (x OperationState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OperationState) Descriptor() protoreflect.EnumDescriptor {
	return file_google_logging_v2_logging_config_proto_enumTypes[1].Descriptor()
}

func (OperationState) Type() protoreflect.EnumType {
	return &file_google_logging_v2_logging_config_proto_enumTypes[1]
}

func (x OperationState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OperationState.Descriptor instead.
func (OperationState) EnumDescriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{1}
}

// Deprecated. This is unused.
type LogSink_VersionFormat int32

const (
	// An unspecified format version that will default to V2.
	LogSink_VERSION_FORMAT_UNSPECIFIED LogSink_VersionFormat = 0
	// `LogEntry` version 2 format.
	LogSink_V2 LogSink_VersionFormat = 1
	// `LogEntry` version 1 format.
	LogSink_V1 LogSink_VersionFormat = 2
)

// Enum value maps for LogSink_VersionFormat.
var (
	LogSink_VersionFormat_name = map[int32]string{
		0: "VERSION_FORMAT_UNSPECIFIED",
		1: "V2",
		2: "V1",
	}
	LogSink_VersionFormat_value = map[string]int32{
		"VERSION_FORMAT_UNSPECIFIED": 0,
		"V2":                         1,
		"V1":                         2,
	}
)

func (x LogSink_VersionFormat) Enum() *LogSink_VersionFormat {
	p := new(LogSink_VersionFormat)
	*p = x
	return p
}

func (x LogSink_VersionFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LogSink_VersionFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_google_logging_v2_logging_config_proto_enumTypes[2].Descriptor()
}

func (LogSink_VersionFormat) Type() protoreflect.EnumType {
	return &file_google_logging_v2_logging_config_proto_enumTypes[2]
}

func (x LogSink_VersionFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LogSink_VersionFormat.Descriptor instead.
func (LogSink_VersionFormat) EnumDescriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{2, 0}
}

// Describes a repository in which log entries are stored.
type LogBucket struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. The resource name of the bucket.
	//
	// For example:
	//
	//	`projects/my-project/locations/global/buckets/my-bucket`
	//
	// For a list of supported locations, see [Supported
	// Regions](https://cloud.google.com/logging/docs/region-support)
	//
	// For the location of `global` it is unspecified where log entries are
	// actually stored.
	//
	// After a bucket has been created, the location cannot be changed.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Describes this bucket.
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// Output only. The creation timestamp of the bucket. This is not set for any of the
	// default buckets.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// Output only. The last update timestamp of the bucket.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// Logs will be retained by default for this amount of time, after which they
	// will automatically be deleted. The minimum retention period is 1 day. If
	// this value is set to zero at bucket creation time, the default time of 30
	// days will be used.
	RetentionDays int32 `protobuf:"varint,11,opt,name=retention_days,json=retentionDays,proto3" json:"retention_days,omitempty"`
	// Whether the bucket is locked.
	//
	// The retention period on a locked bucket cannot be changed. Locked buckets
	// may only be deleted if they are empty.
	Locked bool `protobuf:"varint,9,opt,name=locked,proto3" json:"locked,omitempty"`
	// Output only. The bucket lifecycle state.
	LifecycleState LifecycleState `protobuf:"varint,12,opt,name=lifecycle_state,json=lifecycleState,proto3,enum=google.logging.v2.LifecycleState" json:"lifecycle_state,omitempty"`
	// Log entry field paths that are denied access in this bucket.
	//
	// The following fields and their children are eligible: `textPayload`,
	// `jsonPayload`, `protoPayload`, `httpRequest`, `labels`, `sourceLocation`.
	//
	// Restricting a repeated field will restrict all values. Adding a parent will
	// block all child fields. (e.g. `foo.bar` will block `foo.bar.baz`)
	RestrictedFields []string `protobuf:"bytes,15,rep,name=restricted_fields,json=restrictedFields,proto3" json:"restricted_fields,omitempty"`
	// The CMEK settings of the log bucket. If present, new log entries written to
	// this log bucket are encrypted using the CMEK key provided in this
	// configuration. If a log bucket has CMEK settings, the CMEK settings cannot
	// be disabled later by updating the log bucket. Changing the KMS key is
	// allowed.
	CmekSettings *CmekSettings `protobuf:"bytes,19,opt,name=cmek_settings,json=cmekSettings,proto3" json:"cmek_settings,omitempty"`
}

func (x *LogBucket) Reset() {
	*x = LogBucket{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogBucket) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogBucket) ProtoMessage() {}

func (x *LogBucket) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogBucket.ProtoReflect.Descriptor instead.
func (*LogBucket) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{0}
}

func (x *LogBucket) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LogBucket) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LogBucket) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *LogBucket) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *LogBucket) GetRetentionDays() int32 {
	if x != nil {
		return x.RetentionDays
	}
	return 0
}

func (x *LogBucket) GetLocked() bool {
	if x != nil {
		return x.Locked
	}
	return false
}

func (x *LogBucket) GetLifecycleState() LifecycleState {
	if x != nil {
		return x.LifecycleState
	}
	return LifecycleState_LIFECYCLE_STATE_UNSPECIFIED
}

func (x *LogBucket) GetRestrictedFields() []string {
	if x != nil {
		return x.RestrictedFields
	}
	return nil
}

func (x *LogBucket) GetCmekSettings() *CmekSettings {
	if x != nil {
		return x.CmekSettings
	}
	return nil
}

// Describes a view over log entries in a bucket.
type LogView struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The resource name of the view.
	//
	// For example:
	//
	//	`projects/my-project/locations/global/buckets/my-bucket/views/my-view`
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Describes this view.
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// Output only. The creation timestamp of the view.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// Output only. The last update timestamp of the view.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// Filter that restricts which log entries in a bucket are visible in this
	// view.
	//
	// Filters are restricted to be a logical AND of ==/!= of any of the
	// following:
	//
	//   - originating project/folder/organization/billing account.
	//   - resource type
	//   - log id
	//
	// For example:
	//
	//	SOURCE("projects/myproject") AND resource.type = "gce_instance"
	//	                             AND LOG_ID("stdout")
	Filter string `protobuf:"bytes,7,opt,name=filter,proto3" json:"filter,omitempty"`
}

func (x *LogView) Reset() {
	*x = LogView{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogView) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogView) ProtoMessage() {}

func (x *LogView) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogView.ProtoReflect.Descriptor instead.
func (*LogView) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{1}
}

func (x *LogView) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LogView) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LogView) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *LogView) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *LogView) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

// Describes a sink used to export log entries to one of the following
// destinations in any project: a Cloud Storage bucket, a BigQuery dataset, a
// Pub/Sub topic or a Cloud Logging log bucket. A logs filter controls which log
// entries are exported. The sink must be created within a project,
// organization, billing account, or folder.
type LogSink struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The client-assigned sink identifier, unique within the project.
	//
	// For example: `"my-syslog-errors-to-pubsub"`. Sink identifiers are limited
	// to 100 characters and can include only the following characters: upper and
	// lower-case alphanumeric characters, underscores, hyphens, and periods.
	// First character has to be alphanumeric.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The export destination:
	//
	//	"storage.googleapis.com/[GCS_BUCKET]"
	//	"bigquery.googleapis.com/projects/[PROJECT_ID]/datasets/[DATASET]"
	//	"pubsub.googleapis.com/projects/[PROJECT_ID]/topics/[TOPIC_ID]"
	//
	// The sink's `writer_identity`, set when the sink is created, must have
	// permission to write to the destination or else the log entries are not
	// exported. For more information, see
	// [Exporting Logs with
	// Sinks](https://cloud.google.com/logging/docs/api/tasks/exporting-logs).
	Destination string `protobuf:"bytes,3,opt,name=destination,proto3" json:"destination,omitempty"`
	// Optional. An [advanced logs
	// filter](https://cloud.google.com/logging/docs/view/advanced-queries). The
	// only exported log entries are those that are in the resource owning the
	// sink and that match the filter.
	//
	// For example:
	//
	//	`logName="projects/[PROJECT_ID]/logs/[LOG_ID]" AND severity>=ERROR`
	Filter string `protobuf:"bytes,5,opt,name=filter,proto3" json:"filter,omitempty"`
	// Optional. A description of this sink.
	//
	// The maximum length of the description is 8000 characters.
	Description string `protobuf:"bytes,18,opt,name=description,proto3" json:"description,omitempty"`
	// Optional. If set to true, then this sink is disabled and it does not export any log
	// entries.
	Disabled bool `protobuf:"varint,19,opt,name=disabled,proto3" json:"disabled,omitempty"`
	// Optional. Log entries that match any of these exclusion filters will not be exported.
	//
	// If a log entry is matched by both `filter` and one of `exclusion_filters`
	// it will not be exported.
	Exclusions []*LogExclusion `protobuf:"bytes,16,rep,name=exclusions,proto3" json:"exclusions,omitempty"`
	// Deprecated. This field is unused.
	//
	// Deprecated: Do not use.
	OutputVersionFormat LogSink_VersionFormat `protobuf:"varint,6,opt,name=output_version_format,json=outputVersionFormat,proto3,enum=google.logging.v2.LogSink_VersionFormat" json:"output_version_format,omitempty"`
	// Output only. An IAM identity&mdash;a service account or group&mdash;under which Cloud
	// Logging writes the exported log entries to the sink's destination. This
	// field is set by
	// [sinks.create][google.logging.v2.ConfigServiceV2.CreateSink] and
	// [sinks.update][google.logging.v2.ConfigServiceV2.UpdateSink] based on the
	// value of `unique_writer_identity` in those methods.
	//
	// Until you grant this identity write-access to the destination, log entry
	// exports from this sink will fail. For more information, see [Granting
	// Access for a
	// Resource](https://cloud.google.com/iam/docs/granting-roles-to-service-accounts#granting_access_to_a_service_account_for_a_resource).
	// Consult the destination service's documentation to determine the
	// appropriate IAM roles to assign to the identity.
	//
	// Sinks that have a destination that is a log bucket in the same project as
	// the sink do not have a writer_identity and no additional permissions are
	// required.
	WriterIdentity string `protobuf:"bytes,8,opt,name=writer_identity,json=writerIdentity,proto3" json:"writer_identity,omitempty"`
	// Optional. This field applies only to sinks owned by organizations and folders. If the
	// field is false, the default, only the logs owned by the sink's parent
	// resource are available for export. If the field is true, then log entries
	// from all the projects, folders, and billing accounts contained in the
	// sink's parent resource are also available for export. Whether a particular
	// log entry from the children is exported depends on the sink's filter
	// expression.
	//
	// For example, if this field is true, then the filter
	// `resource.type=gce_instance` would export all Compute Engine VM instance
	// log entries from all projects in the sink's parent.
	//
	// To only export entries from certain child projects, filter on the project
	// part of the log name:
	//
	//	logName:("projects/test-project1/" OR "projects/test-project2/") AND
	//	resource.type=gce_instance
	IncludeChildren bool `protobuf:"varint,9,opt,name=include_children,json=includeChildren,proto3" json:"include_children,omitempty"`
	// Destination dependent options.
	//
	// Types that are assignable to Options:
	//
	//	*LogSink_BigqueryOptions
	Options isLogSink_Options `protobuf_oneof:"options"`
	// Output only. The creation timestamp of the sink.
	//
	// This field may not be present for older sinks.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// Output only. The last update timestamp of the sink.
	//
	// This field may not be present for older sinks.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *LogSink) Reset() {
	*x = LogSink{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogSink) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogSink) ProtoMessage() {}

func (x *LogSink) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogSink.ProtoReflect.Descriptor instead.
func (*LogSink) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{2}
}

func (x *LogSink) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LogSink) GetDestination() string {
	if x != nil {
		return x.Destination
	}
	return ""
}

func (x *LogSink) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *LogSink) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LogSink) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

func (x *LogSink) GetExclusions() []*LogExclusion {
	if x != nil {
		return x.Exclusions
	}
	return nil
}

// Deprecated: Do not use.
func (x *LogSink) GetOutputVersionFormat() LogSink_VersionFormat {
	if x != nil {
		return x.OutputVersionFormat
	}
	return LogSink_VERSION_FORMAT_UNSPECIFIED
}

func (x *LogSink) GetWriterIdentity() string {
	if x != nil {
		return x.WriterIdentity
	}
	return ""
}

func (x *LogSink) GetIncludeChildren() bool {
	if x != nil {
		return x.IncludeChildren
	}
	return false
}

func (m *LogSink) GetOptions() isLogSink_Options {
	if m != nil {
		return m.Options
	}
	return nil
}

func (x *LogSink) GetBigqueryOptions() *BigQueryOptions {
	if x, ok := x.GetOptions().(*LogSink_BigqueryOptions); ok {
		return x.BigqueryOptions
	}
	return nil
}

func (x *LogSink) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *LogSink) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

type isLogSink_Options interface {
	isLogSink_Options()
}

type LogSink_BigqueryOptions struct {
	// Optional. Options that affect sinks exporting data to BigQuery.
	BigqueryOptions *BigQueryOptions `protobuf:"bytes,12,opt,name=bigquery_options,json=bigqueryOptions,proto3,oneof"`
}

func (*LogSink_BigqueryOptions) isLogSink_Options() {}

// Options that change functionality of a sink exporting data to BigQuery.
type BigQueryOptions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Optional. Whether to use [BigQuery's partition
	// tables](https://cloud.google.com/bigquery/docs/partitioned-tables). By
	// default, Cloud Logging creates dated tables based on the log entries'
	// timestamps, e.g. syslog_20170523. With partitioned tables the date suffix
	// is no longer present and [special query
	// syntax](https://cloud.google.com/bigquery/docs/querying-partitioned-tables)
	// has to be used instead. In both cases, tables are sharded based on UTC
	// timezone.
	UsePartitionedTables bool `protobuf:"varint,1,opt,name=use_partitioned_tables,json=usePartitionedTables,proto3" json:"use_partitioned_tables,omitempty"`
	// Output only. True if new timestamp column based partitioning is in use, false if legacy
	// ingestion-time partitioning is in use.
	//
	// All new sinks will have this field set true and will use timestamp column
	// based partitioning. If use_partitioned_tables is false, this value has no
	// meaning and will be false. Legacy sinks using partitioned tables will have
	// this field set to false.
	UsesTimestampColumnPartitioning bool `protobuf:"varint,3,opt,name=uses_timestamp_column_partitioning,json=usesTimestampColumnPartitioning,proto3" json:"uses_timestamp_column_partitioning,omitempty"`
}

func (x *BigQueryOptions) Reset() {
	*x = BigQueryOptions{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BigQueryOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BigQueryOptions) ProtoMessage() {}

func (x *BigQueryOptions) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BigQueryOptions.ProtoReflect.Descriptor instead.
func (*BigQueryOptions) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{3}
}

func (x *BigQueryOptions) GetUsePartitionedTables() bool {
	if x != nil {
		return x.UsePartitionedTables
	}
	return false
}

func (x *BigQueryOptions) GetUsesTimestampColumnPartitioning() bool {
	if x != nil {
		return x.UsesTimestampColumnPartitioning
	}
	return false
}

// The parameters to `ListBuckets`.
type ListBucketsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The parent resource whose buckets are to be listed:
	//
	//	"projects/[PROJECT_ID]/locations/[LOCATION_ID]"
	//	"organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]"
	//	"folders/[FOLDER_ID]/locations/[LOCATION_ID]"
	//
	// Note: The locations portion of the resource must be specified, but
	// supplying the character `-` in place of [LOCATION_ID] will return all
	// buckets.
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Optional. If present, then retrieve the next batch of results from the preceding call
	// to this method. `pageToken` must be the value of `nextPageToken` from the
	// previous response. The values of other method parameters should be
	// identical to those in the previous call.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional. The maximum number of results to return from this request. Non-positive
	// values are ignored. The presence of `nextPageToken` in the response
	// indicates that more results might be available.
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListBucketsRequest) Reset() {
	*x = ListBucketsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBucketsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBucketsRequest) ProtoMessage() {}

func (x *ListBucketsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBucketsRequest.ProtoReflect.Descriptor instead.
func (*ListBucketsRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{4}
}

func (x *ListBucketsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ListBucketsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListBucketsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// The response from ListBuckets.
type ListBucketsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of buckets.
	Buckets []*LogBucket `protobuf:"bytes,1,rep,name=buckets,proto3" json:"buckets,omitempty"`
	// If there might be more results than appear in this response, then
	// `nextPageToken` is included. To get the next set of results, call the same
	// method again using the value of `nextPageToken` as `pageToken`.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListBucketsResponse) Reset() {
	*x = ListBucketsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListBucketsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListBucketsResponse) ProtoMessage() {}

func (x *ListBucketsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListBucketsResponse.ProtoReflect.Descriptor instead.
func (*ListBucketsResponse) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{5}
}

func (x *ListBucketsResponse) GetBuckets() []*LogBucket {
	if x != nil {
		return x.Buckets
	}
	return nil
}

func (x *ListBucketsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// The parameters to `CreateBucket`.
type CreateBucketRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource in which to create the log bucket:
	//
	//	"projects/[PROJECT_ID]/locations/[LOCATION_ID]"
	//
	// For example:
	//
	//	`"projects/my-project/locations/global"`
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Required. A client-assigned identifier such as `"my-bucket"`. Identifiers are limited
	// to 100 characters and can include only letters, digits, underscores,
	// hyphens, and periods.
	BucketId string `protobuf:"bytes,2,opt,name=bucket_id,json=bucketId,proto3" json:"bucket_id,omitempty"`
	// Required. The new bucket. The region specified in the new bucket must be compliant
	// with any Location Restriction Org Policy. The name field in the bucket is
	// ignored.
	Bucket *LogBucket `protobuf:"bytes,3,opt,name=bucket,proto3" json:"bucket,omitempty"`
}

func (x *CreateBucketRequest) Reset() {
	*x = CreateBucketRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateBucketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateBucketRequest) ProtoMessage() {}

func (x *CreateBucketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateBucketRequest.ProtoReflect.Descriptor instead.
func (*CreateBucketRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{6}
}

func (x *CreateBucketRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *CreateBucketRequest) GetBucketId() string {
	if x != nil {
		return x.BucketId
	}
	return ""
}

func (x *CreateBucketRequest) GetBucket() *LogBucket {
	if x != nil {
		return x.Bucket
	}
	return nil
}

// The parameters to `UpdateBucket`.
type UpdateBucketRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The full resource name of the bucket to update.
	//
	//	"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//	"organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//	"folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//
	// For example:
	//
	//	`"projects/my-project/locations/global/buckets/my-bucket"`
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The updated bucket.
	Bucket *LogBucket `protobuf:"bytes,2,opt,name=bucket,proto3" json:"bucket,omitempty"`
	// Required. Field mask that specifies the fields in `bucket` that need an update. A
	// bucket field will be overwritten if, and only if, it is in the update mask.
	// `name` and output only fields cannot be updated.
	//
	// For a detailed `FieldMask` definition, see:
	// https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#google.protobuf.FieldMask
	//
	// For example: `updateMask=retention_days`
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,4,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateBucketRequest) Reset() {
	*x = UpdateBucketRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateBucketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateBucketRequest) ProtoMessage() {}

func (x *UpdateBucketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateBucketRequest.ProtoReflect.Descriptor instead.
func (*UpdateBucketRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateBucketRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateBucketRequest) GetBucket() *LogBucket {
	if x != nil {
		return x.Bucket
	}
	return nil
}

func (x *UpdateBucketRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// The parameters to `GetBucket`.
type GetBucketRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name of the bucket:
	//
	//	"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//	"organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//	"folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//
	// For example:
	//
	//	`"projects/my-project/locations/global/buckets/my-bucket"`
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetBucketRequest) Reset() {
	*x = GetBucketRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetBucketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBucketRequest) ProtoMessage() {}

func (x *GetBucketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBucketRequest.ProtoReflect.Descriptor instead.
func (*GetBucketRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{8}
}

func (x *GetBucketRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// The parameters to `DeleteBucket`.
type DeleteBucketRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The full resource name of the bucket to delete.
	//
	//	"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//	"organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//	"folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//
	// For example:
	//
	//	`"projects/my-project/locations/global/buckets/my-bucket"`
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteBucketRequest) Reset() {
	*x = DeleteBucketRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteBucketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteBucketRequest) ProtoMessage() {}

func (x *DeleteBucketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteBucketRequest.ProtoReflect.Descriptor instead.
func (*DeleteBucketRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteBucketRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// The parameters to `UndeleteBucket`.
type UndeleteBucketRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The full resource name of the bucket to undelete.
	//
	//	"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//	"organizations/[ORGANIZATION_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//	"folders/[FOLDER_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	//
	// For example:
	//
	//	`"projects/my-project/locations/global/buckets/my-bucket"`
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *UndeleteBucketRequest) Reset() {
	*x = UndeleteBucketRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UndeleteBucketRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UndeleteBucketRequest) ProtoMessage() {}

func (x *UndeleteBucketRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UndeleteBucketRequest.ProtoReflect.Descriptor instead.
func (*UndeleteBucketRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{10}
}

func (x *UndeleteBucketRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// The parameters to `ListViews`.
type ListViewsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The bucket whose views are to be listed:
	//
	//	"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Optional. If present, then retrieve the next batch of results from the preceding call
	// to this method. `pageToken` must be the value of `nextPageToken` from the
	// previous response. The values of other method parameters should be
	// identical to those in the previous call.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional. The maximum number of results to return from this request.
	//
	// Non-positive values are ignored. The presence of `nextPageToken` in the
	// response indicates that more results might be available.
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListViewsRequest) Reset() {
	*x = ListViewsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListViewsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListViewsRequest) ProtoMessage() {}

func (x *ListViewsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListViewsRequest.ProtoReflect.Descriptor instead.
func (*ListViewsRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{11}
}

func (x *ListViewsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ListViewsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListViewsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// The response from ListViews.
type ListViewsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of views.
	Views []*LogView `protobuf:"bytes,1,rep,name=views,proto3" json:"views,omitempty"`
	// If there might be more results than appear in this response, then
	// `nextPageToken` is included. To get the next set of results, call the same
	// method again using the value of `nextPageToken` as `pageToken`.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListViewsResponse) Reset() {
	*x = ListViewsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListViewsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListViewsResponse) ProtoMessage() {}

func (x *ListViewsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListViewsResponse.ProtoReflect.Descriptor instead.
func (*ListViewsResponse) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{12}
}

func (x *ListViewsResponse) GetViews() []*LogView {
	if x != nil {
		return x.Views
	}
	return nil
}

func (x *ListViewsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// The parameters to `CreateView`.
type CreateViewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The bucket in which to create the view
	//
	//	`"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]"`
	//
	// For example:
	//
	//	`"projects/my-project/locations/global/buckets/my-bucket"`
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Required. The id to use for this view.
	ViewId string `protobuf:"bytes,2,opt,name=view_id,json=viewId,proto3" json:"view_id,omitempty"`
	// Required. The new view.
	View *LogView `protobuf:"bytes,3,opt,name=view,proto3" json:"view,omitempty"`
}

func (x *CreateViewRequest) Reset() {
	*x = CreateViewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateViewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateViewRequest) ProtoMessage() {}

func (x *CreateViewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateViewRequest.ProtoReflect.Descriptor instead.
func (*CreateViewRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{13}
}

func (x *CreateViewRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *CreateViewRequest) GetViewId() string {
	if x != nil {
		return x.ViewId
	}
	return ""
}

func (x *CreateViewRequest) GetView() *LogView {
	if x != nil {
		return x.View
	}
	return nil
}

// The parameters to `UpdateView`.
type UpdateViewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The full resource name of the view to update
	//
	//	"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
	//
	// For example:
	//
	//	`"projects/my-project/locations/global/buckets/my-bucket/views/my-view"`
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The updated view.
	View *LogView `protobuf:"bytes,2,opt,name=view,proto3" json:"view,omitempty"`
	// Optional. Field mask that specifies the fields in `view` that need
	// an update. A field will be overwritten if, and only if, it is
	// in the update mask. `name` and output only fields cannot be updated.
	//
	// For a detailed `FieldMask` definition, see
	// https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#google.protobuf.FieldMask
	//
	// For example: `updateMask=filter`
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,4,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateViewRequest) Reset() {
	*x = UpdateViewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateViewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateViewRequest) ProtoMessage() {}

func (x *UpdateViewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateViewRequest.ProtoReflect.Descriptor instead.
func (*UpdateViewRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{14}
}

func (x *UpdateViewRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateViewRequest) GetView() *LogView {
	if x != nil {
		return x.View
	}
	return nil
}

func (x *UpdateViewRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// The parameters to `GetView`.
type GetViewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name of the policy:
	//
	//	"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
	//
	// For example:
	//
	//	`"projects/my-project/locations/global/buckets/my-bucket/views/my-view"`
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetViewRequest) Reset() {
	*x = GetViewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetViewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetViewRequest) ProtoMessage() {}

func (x *GetViewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetViewRequest.ProtoReflect.Descriptor instead.
func (*GetViewRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{15}
}

func (x *GetViewRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// The parameters to `DeleteView`.
type DeleteViewRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The full resource name of the view to delete:
	//
	//	"projects/[PROJECT_ID]/locations/[LOCATION_ID]/buckets/[BUCKET_ID]/views/[VIEW_ID]"
	//
	// For example:
	//
	//	`"projects/my-project/locations/global/buckets/my-bucket/views/my-view"`
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteViewRequest) Reset() {
	*x = DeleteViewRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteViewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteViewRequest) ProtoMessage() {}

func (x *DeleteViewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteViewRequest.ProtoReflect.Descriptor instead.
func (*DeleteViewRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteViewRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// The parameters to `ListSinks`.
type ListSinksRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The parent resource whose sinks are to be listed:
	//
	//	"projects/[PROJECT_ID]"
	//	"organizations/[ORGANIZATION_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]"
	//	"folders/[FOLDER_ID]"
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Optional. If present, then retrieve the next batch of results from the
	// preceding call to this method. `pageToken` must be the value of
	// `nextPageToken` from the previous response. The values of other method
	// parameters should be identical to those in the previous call.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional. The maximum number of results to return from this request.
	// Non-positive values are ignored. The presence of `nextPageToken` in the
	// response indicates that more results might be available.
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListSinksRequest) Reset() {
	*x = ListSinksRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSinksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSinksRequest) ProtoMessage() {}

func (x *ListSinksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSinksRequest.ProtoReflect.Descriptor instead.
func (*ListSinksRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{17}
}

func (x *ListSinksRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ListSinksRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListSinksRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Result returned from `ListSinks`.
type ListSinksResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of sinks.
	Sinks []*LogSink `protobuf:"bytes,1,rep,name=sinks,proto3" json:"sinks,omitempty"`
	// If there might be more results than appear in this response, then
	// `nextPageToken` is included. To get the next set of results, call the same
	// method again using the value of `nextPageToken` as `pageToken`.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListSinksResponse) Reset() {
	*x = ListSinksResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSinksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSinksResponse) ProtoMessage() {}

func (x *ListSinksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSinksResponse.ProtoReflect.Descriptor instead.
func (*ListSinksResponse) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{18}
}

func (x *ListSinksResponse) GetSinks() []*LogSink {
	if x != nil {
		return x.Sinks
	}
	return nil
}

func (x *ListSinksResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// The parameters to `GetSink`.
type GetSinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name of the sink:
	//
	//	"projects/[PROJECT_ID]/sinks/[SINK_ID]"
	//	"organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
	//	"folders/[FOLDER_ID]/sinks/[SINK_ID]"
	//
	// For example:
	//
	//	`"projects/my-project/sinks/my-sink"`
	SinkName string `protobuf:"bytes,1,opt,name=sink_name,json=sinkName,proto3" json:"sink_name,omitempty"`
}

func (x *GetSinkRequest) Reset() {
	*x = GetSinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSinkRequest) ProtoMessage() {}

func (x *GetSinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSinkRequest.ProtoReflect.Descriptor instead.
func (*GetSinkRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{19}
}

func (x *GetSinkRequest) GetSinkName() string {
	if x != nil {
		return x.SinkName
	}
	return ""
}

// The parameters to `CreateSink`.
type CreateSinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource in which to create the sink:
	//
	//	"projects/[PROJECT_ID]"
	//	"organizations/[ORGANIZATION_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]"
	//	"folders/[FOLDER_ID]"
	//
	// For examples:
	//
	//	`"projects/my-project"`
	//	`"organizations/*********"`
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Required. The new sink, whose `name` parameter is a sink identifier that
	// is not already in use.
	Sink *LogSink `protobuf:"bytes,2,opt,name=sink,proto3" json:"sink,omitempty"`
	// Optional. Determines the kind of IAM identity returned as `writer_identity`
	// in the new sink. If this value is omitted or set to false, and if the
	// sink's parent is a project, then the value returned as `writer_identity` is
	// the same group or service account used by Cloud Logging before the addition
	// of writer identities to this API. The sink's destination must be in the
	// same project as the sink itself.
	//
	// If this field is set to true, or if the sink is owned by a non-project
	// resource such as an organization, then the value of `writer_identity` will
	// be a unique service account used only for exports from the new sink. For
	// more information, see `writer_identity` in [LogSink][google.logging.v2.LogSink].
	UniqueWriterIdentity bool `protobuf:"varint,3,opt,name=unique_writer_identity,json=uniqueWriterIdentity,proto3" json:"unique_writer_identity,omitempty"`
}

func (x *CreateSinkRequest) Reset() {
	*x = CreateSinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSinkRequest) ProtoMessage() {}

func (x *CreateSinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSinkRequest.ProtoReflect.Descriptor instead.
func (*CreateSinkRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{20}
}

func (x *CreateSinkRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *CreateSinkRequest) GetSink() *LogSink {
	if x != nil {
		return x.Sink
	}
	return nil
}

func (x *CreateSinkRequest) GetUniqueWriterIdentity() bool {
	if x != nil {
		return x.UniqueWriterIdentity
	}
	return false
}

// The parameters to `UpdateSink`.
type UpdateSinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The full resource name of the sink to update, including the parent
	// resource and the sink identifier:
	//
	//	"projects/[PROJECT_ID]/sinks/[SINK_ID]"
	//	"organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
	//	"folders/[FOLDER_ID]/sinks/[SINK_ID]"
	//
	// For example:
	//
	//	`"projects/my-project/sinks/my-sink"`
	SinkName string `protobuf:"bytes,1,opt,name=sink_name,json=sinkName,proto3" json:"sink_name,omitempty"`
	// Required. The updated sink, whose name is the same identifier that appears as part
	// of `sink_name`.
	Sink *LogSink `protobuf:"bytes,2,opt,name=sink,proto3" json:"sink,omitempty"`
	// Optional. See [sinks.create][google.logging.v2.ConfigServiceV2.CreateSink]
	// for a description of this field. When updating a sink, the effect of this
	// field on the value of `writer_identity` in the updated sink depends on both
	// the old and new values of this field:
	//
	//   - If the old and new values of this field are both false or both true,
	//     then there is no change to the sink's `writer_identity`.
	//   - If the old value is false and the new value is true, then
	//     `writer_identity` is changed to a unique service account.
	//   - It is an error if the old value is true and the new value is
	//     set to false or defaulted to false.
	UniqueWriterIdentity bool `protobuf:"varint,3,opt,name=unique_writer_identity,json=uniqueWriterIdentity,proto3" json:"unique_writer_identity,omitempty"`
	// Optional. Field mask that specifies the fields in `sink` that need
	// an update. A sink field will be overwritten if, and only if, it is
	// in the update mask. `name` and output only fields cannot be updated.
	//
	// An empty `updateMask` is temporarily treated as using the following mask
	// for backwards compatibility purposes:
	//
	//	`destination,filter,includeChildren`
	//
	// At some point in the future, behavior will be removed and specifying an
	// empty `updateMask` will be an error.
	//
	// For a detailed `FieldMask` definition, see
	// https://developers.google.com/protocol-buffers/docs/reference/google.protobuf#google.protobuf.FieldMask
	//
	// For example: `updateMask=filter`
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,4,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateSinkRequest) Reset() {
	*x = UpdateSinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSinkRequest) ProtoMessage() {}

func (x *UpdateSinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSinkRequest.ProtoReflect.Descriptor instead.
func (*UpdateSinkRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{21}
}

func (x *UpdateSinkRequest) GetSinkName() string {
	if x != nil {
		return x.SinkName
	}
	return ""
}

func (x *UpdateSinkRequest) GetSink() *LogSink {
	if x != nil {
		return x.Sink
	}
	return nil
}

func (x *UpdateSinkRequest) GetUniqueWriterIdentity() bool {
	if x != nil {
		return x.UniqueWriterIdentity
	}
	return false
}

func (x *UpdateSinkRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// The parameters to `DeleteSink`.
type DeleteSinkRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The full resource name of the sink to delete, including the parent
	// resource and the sink identifier:
	//
	//	"projects/[PROJECT_ID]/sinks/[SINK_ID]"
	//	"organizations/[ORGANIZATION_ID]/sinks/[SINK_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]/sinks/[SINK_ID]"
	//	"folders/[FOLDER_ID]/sinks/[SINK_ID]"
	//
	// For example:
	//
	//	`"projects/my-project/sinks/my-sink"`
	SinkName string `protobuf:"bytes,1,opt,name=sink_name,json=sinkName,proto3" json:"sink_name,omitempty"`
}

func (x *DeleteSinkRequest) Reset() {
	*x = DeleteSinkRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSinkRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSinkRequest) ProtoMessage() {}

func (x *DeleteSinkRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSinkRequest.ProtoReflect.Descriptor instead.
func (*DeleteSinkRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{22}
}

func (x *DeleteSinkRequest) GetSinkName() string {
	if x != nil {
		return x.SinkName
	}
	return ""
}

// Specifies a set of log entries that are filtered out by a sink. If
// your Google Cloud resource receives a large volume of log entries, you can
// use exclusions to reduce your chargeable logs. Note that exclusions on
// organization-level and folder-level sinks don't apply to child resources.
// Note also that you cannot modify the _Required sink or exclude logs from it.
type LogExclusion struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. A client-assigned identifier, such as `"load-balancer-exclusion"`.
	// Identifiers are limited to 100 characters and can include only letters,
	// digits, underscores, hyphens, and periods. First character has to be
	// alphanumeric.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Optional. A description of this exclusion.
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// Required. An [advanced logs
	// filter](https://cloud.google.com/logging/docs/view/advanced-queries) that
	// matches the log entries to be excluded. By using the [sample
	// function](https://cloud.google.com/logging/docs/view/advanced-queries#sample),
	// you can exclude less than 100% of the matching log entries.
	//
	// For example, the following query matches 99% of low-severity log entries
	// from Google Cloud Storage buckets:
	//
	//	`resource.type=gcs_bucket severity<ERROR sample(insertId, 0.99)`
	Filter string `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	// Optional. If set to True, then this exclusion is disabled and it does not
	// exclude any log entries. You can [update an
	// exclusion][google.logging.v2.ConfigServiceV2.UpdateExclusion] to change the
	// value of this field.
	Disabled bool `protobuf:"varint,4,opt,name=disabled,proto3" json:"disabled,omitempty"`
	// Output only. The creation timestamp of the exclusion.
	//
	// This field may not be present for older exclusions.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// Output only. The last update timestamp of the exclusion.
	//
	// This field may not be present for older exclusions.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *LogExclusion) Reset() {
	*x = LogExclusion{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LogExclusion) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogExclusion) ProtoMessage() {}

func (x *LogExclusion) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogExclusion.ProtoReflect.Descriptor instead.
func (*LogExclusion) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{23}
}

func (x *LogExclusion) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LogExclusion) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LogExclusion) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *LogExclusion) GetDisabled() bool {
	if x != nil {
		return x.Disabled
	}
	return false
}

func (x *LogExclusion) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *LogExclusion) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// The parameters to `ListExclusions`.
type ListExclusionsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The parent resource whose exclusions are to be listed.
	//
	//	"projects/[PROJECT_ID]"
	//	"organizations/[ORGANIZATION_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]"
	//	"folders/[FOLDER_ID]"
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Optional. If present, then retrieve the next batch of results from the
	// preceding call to this method. `pageToken` must be the value of
	// `nextPageToken` from the previous response. The values of other method
	// parameters should be identical to those in the previous call.
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// Optional. The maximum number of results to return from this request.
	// Non-positive values are ignored. The presence of `nextPageToken` in the
	// response indicates that more results might be available.
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
}

func (x *ListExclusionsRequest) Reset() {
	*x = ListExclusionsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExclusionsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExclusionsRequest) ProtoMessage() {}

func (x *ListExclusionsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExclusionsRequest.ProtoReflect.Descriptor instead.
func (*ListExclusionsRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{24}
}

func (x *ListExclusionsRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *ListExclusionsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListExclusionsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

// Result returned from `ListExclusions`.
type ListExclusionsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of exclusions.
	Exclusions []*LogExclusion `protobuf:"bytes,1,rep,name=exclusions,proto3" json:"exclusions,omitempty"`
	// If there might be more results than appear in this response, then
	// `nextPageToken` is included. To get the next set of results, call the same
	// method again using the value of `nextPageToken` as `pageToken`.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
}

func (x *ListExclusionsResponse) Reset() {
	*x = ListExclusionsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListExclusionsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListExclusionsResponse) ProtoMessage() {}

func (x *ListExclusionsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListExclusionsResponse.ProtoReflect.Descriptor instead.
func (*ListExclusionsResponse) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{25}
}

func (x *ListExclusionsResponse) GetExclusions() []*LogExclusion {
	if x != nil {
		return x.Exclusions
	}
	return nil
}

func (x *ListExclusionsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// The parameters to `GetExclusion`.
type GetExclusionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name of an existing exclusion:
	//
	//	"projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
	//	"organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
	//	"folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]"
	//
	// For example:
	//
	//	`"projects/my-project/exclusions/my-exclusion"`
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetExclusionRequest) Reset() {
	*x = GetExclusionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetExclusionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetExclusionRequest) ProtoMessage() {}

func (x *GetExclusionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetExclusionRequest.ProtoReflect.Descriptor instead.
func (*GetExclusionRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{26}
}

func (x *GetExclusionRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// The parameters to `CreateExclusion`.
type CreateExclusionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The parent resource in which to create the exclusion:
	//
	//	"projects/[PROJECT_ID]"
	//	"organizations/[ORGANIZATION_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]"
	//	"folders/[FOLDER_ID]"
	//
	// For examples:
	//
	//	`"projects/my-logging-project"`
	//	`"organizations/*********"`
	Parent string `protobuf:"bytes,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// Required. The new exclusion, whose `name` parameter is an exclusion name
	// that is not already used in the parent resource.
	Exclusion *LogExclusion `protobuf:"bytes,2,opt,name=exclusion,proto3" json:"exclusion,omitempty"`
}

func (x *CreateExclusionRequest) Reset() {
	*x = CreateExclusionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateExclusionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateExclusionRequest) ProtoMessage() {}

func (x *CreateExclusionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateExclusionRequest.ProtoReflect.Descriptor instead.
func (*CreateExclusionRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{27}
}

func (x *CreateExclusionRequest) GetParent() string {
	if x != nil {
		return x.Parent
	}
	return ""
}

func (x *CreateExclusionRequest) GetExclusion() *LogExclusion {
	if x != nil {
		return x.Exclusion
	}
	return nil
}

// The parameters to `UpdateExclusion`.
type UpdateExclusionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name of the exclusion to update:
	//
	//	"projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
	//	"organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
	//	"folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]"
	//
	// For example:
	//
	//	`"projects/my-project/exclusions/my-exclusion"`
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Required. New values for the existing exclusion. Only the fields specified in
	// `update_mask` are relevant.
	Exclusion *LogExclusion `protobuf:"bytes,2,opt,name=exclusion,proto3" json:"exclusion,omitempty"`
	// Required. A non-empty list of fields to change in the existing exclusion. New values
	// for the fields are taken from the corresponding fields in the
	// [LogExclusion][google.logging.v2.LogExclusion] included in this request. Fields not mentioned in
	// `update_mask` are not changed and are ignored in the request.
	//
	// For example, to change the filter and description of an exclusion,
	// specify an `update_mask` of `"filter,description"`.
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,3,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateExclusionRequest) Reset() {
	*x = UpdateExclusionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateExclusionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateExclusionRequest) ProtoMessage() {}

func (x *UpdateExclusionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateExclusionRequest.ProtoReflect.Descriptor instead.
func (*UpdateExclusionRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{28}
}

func (x *UpdateExclusionRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateExclusionRequest) GetExclusion() *LogExclusion {
	if x != nil {
		return x.Exclusion
	}
	return nil
}

func (x *UpdateExclusionRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// The parameters to `DeleteExclusion`.
type DeleteExclusionRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name of an existing exclusion to delete:
	//
	//	"projects/[PROJECT_ID]/exclusions/[EXCLUSION_ID]"
	//	"organizations/[ORGANIZATION_ID]/exclusions/[EXCLUSION_ID]"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]/exclusions/[EXCLUSION_ID]"
	//	"folders/[FOLDER_ID]/exclusions/[EXCLUSION_ID]"
	//
	// For example:
	//
	//	`"projects/my-project/exclusions/my-exclusion"`
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *DeleteExclusionRequest) Reset() {
	*x = DeleteExclusionRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteExclusionRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteExclusionRequest) ProtoMessage() {}

func (x *DeleteExclusionRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteExclusionRequest.ProtoReflect.Descriptor instead.
func (*DeleteExclusionRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{29}
}

func (x *DeleteExclusionRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// The parameters to
// [GetCmekSettings][google.logging.v2.ConfigServiceV2.GetCmekSettings].
//
// See [Enabling CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption) for
// more information.
type GetCmekSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource for which to retrieve CMEK settings.
	//
	//	"projects/[PROJECT_ID]/cmekSettings"
	//	"organizations/[ORGANIZATION_ID]/cmekSettings"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]/cmekSettings"
	//	"folders/[FOLDER_ID]/cmekSettings"
	//
	// For example:
	//
	//	`"organizations/12345/cmekSettings"`
	//
	// Note: CMEK for the Log Router can be configured for Google Cloud projects,
	// folders, organizations and billing accounts. Once configured for an
	// organization, it applies to all projects and folders in the Google Cloud
	// organization.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetCmekSettingsRequest) Reset() {
	*x = GetCmekSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetCmekSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCmekSettingsRequest) ProtoMessage() {}

func (x *GetCmekSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCmekSettingsRequest.ProtoReflect.Descriptor instead.
func (*GetCmekSettingsRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{30}
}

func (x *GetCmekSettingsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// The parameters to
// [UpdateCmekSettings][google.logging.v2.ConfigServiceV2.UpdateCmekSettings].
//
// See [Enabling CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption) for
// more information.
type UpdateCmekSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name for the CMEK settings to update.
	//
	//	"projects/[PROJECT_ID]/cmekSettings"
	//	"organizations/[ORGANIZATION_ID]/cmekSettings"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]/cmekSettings"
	//	"folders/[FOLDER_ID]/cmekSettings"
	//
	// For example:
	//
	//	`"organizations/12345/cmekSettings"`
	//
	// Note: CMEK for the Log Router can currently only be configured for Google
	// Cloud organizations. Once configured, it applies to all projects and
	// folders in the Google Cloud organization.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The CMEK settings to update.
	//
	// See [Enabling CMEK for Log
	// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
	// for more information.
	CmekSettings *CmekSettings `protobuf:"bytes,2,opt,name=cmek_settings,json=cmekSettings,proto3" json:"cmek_settings,omitempty"`
	// Optional. Field mask identifying which fields from `cmek_settings` should
	// be updated. A field will be overwritten if and only if it is in the update
	// mask. Output only fields cannot be updated.
	//
	// See [FieldMask][google.protobuf.FieldMask] for more information.
	//
	// For example: `"updateMask=kmsKeyName"`
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,3,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateCmekSettingsRequest) Reset() {
	*x = UpdateCmekSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateCmekSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCmekSettingsRequest) ProtoMessage() {}

func (x *UpdateCmekSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCmekSettingsRequest.ProtoReflect.Descriptor instead.
func (*UpdateCmekSettingsRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{31}
}

func (x *UpdateCmekSettingsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateCmekSettingsRequest) GetCmekSettings() *CmekSettings {
	if x != nil {
		return x.CmekSettings
	}
	return nil
}

func (x *UpdateCmekSettingsRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// Describes the customer-managed encryption key (CMEK) settings associated with
// a project, folder, organization, billing account, or flexible resource.
//
// Note: CMEK for the Log Router can currently only be configured for Google
// Cloud organizations. Once configured, it applies to all projects and folders
// in the Google Cloud organization.
//
// See [Enabling CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption) for
// more information.
type CmekSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. The resource name of the CMEK settings.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// The resource name for the configured Cloud KMS key.
	//
	// KMS key name format:
	//
	//	"projects/[PROJECT_ID]/locations/[LOCATION]/keyRings/[KEYRING]/cryptoKeys/[KEY]"
	//
	// For example:
	//
	//	`"projects/my-project/locations/us-central1/keyRings/my-ring/cryptoKeys/my-key"`
	//
	// To enable CMEK for the Log Router, set this field to a valid
	// `kms_key_name` for which the associated service account has the required
	// cloudkms.cryptoKeyEncrypterDecrypter roles assigned for the key.
	//
	// The Cloud KMS key used by the Log Router can be updated by changing the
	// `kms_key_name` to a new valid key name or disabled by setting the key name
	// to an empty string. Encryption operations that are in progress will be
	// completed with the key that was in use when they started. Decryption
	// operations will be completed using the key that was used at the time of
	// encryption unless access to that key has been revoked.
	//
	// To disable CMEK for the Log Router, set this field to an empty string.
	//
	// See [Enabling CMEK for Log
	// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
	// for more information.
	KmsKeyName string `protobuf:"bytes,2,opt,name=kms_key_name,json=kmsKeyName,proto3" json:"kms_key_name,omitempty"`
	// Output only. The service account that will be used by the Log Router to access your
	// Cloud KMS key.
	//
	// Before enabling CMEK for Log Router, you must first assign the
	// cloudkms.cryptoKeyEncrypterDecrypter role to the service account that
	// the Log Router will use to access your Cloud KMS key. Use
	// [GetCmekSettings][google.logging.v2.ConfigServiceV2.GetCmekSettings] to
	// obtain the service account ID.
	//
	// See [Enabling CMEK for Log
	// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
	// for more information.
	ServiceAccountId string `protobuf:"bytes,3,opt,name=service_account_id,json=serviceAccountId,proto3" json:"service_account_id,omitempty"`
}

func (x *CmekSettings) Reset() {
	*x = CmekSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CmekSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CmekSettings) ProtoMessage() {}

func (x *CmekSettings) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CmekSettings.ProtoReflect.Descriptor instead.
func (*CmekSettings) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{32}
}

func (x *CmekSettings) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CmekSettings) GetKmsKeyName() string {
	if x != nil {
		return x.KmsKeyName
	}
	return ""
}

func (x *CmekSettings) GetServiceAccountId() string {
	if x != nil {
		return x.ServiceAccountId
	}
	return ""
}

// The parameters to
// [GetSettings][google.logging.v2.ConfigServiceV2.GetSettings].
//
// See [Enabling CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption) for
// more information.
type GetSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource for which to retrieve settings.
	//
	//	"projects/[PROJECT_ID]/settings"
	//	"organizations/[ORGANIZATION_ID]/settings"
	//	"billingAccounts/[BILLING_ACCOUNT_ID]/settings"
	//	"folders/[FOLDER_ID]/settings"
	//
	// For example:
	//
	//	`"organizations/12345/settings"`
	//
	// Note: Settings for the Log Router can be get for Google Cloud projects,
	// folders, organizations and billing accounts. Currently it can only be
	// configured for organizations. Once configured for an organization, it
	// applies to all projects and folders in the Google Cloud organization.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *GetSettingsRequest) Reset() {
	*x = GetSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSettingsRequest) ProtoMessage() {}

func (x *GetSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSettingsRequest.ProtoReflect.Descriptor instead.
func (*GetSettingsRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{33}
}

func (x *GetSettingsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// The parameters to
// [UpdateSettings][google.logging.v2.ConfigServiceV2.UpdateSettings].
//
// See [Enabling CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption) for
// more information.
type UpdateSettingsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The resource name for the settings to update.
	//
	//	"organizations/[ORGANIZATION_ID]/settings"
	//
	// For example:
	//
	//	`"organizations/12345/settings"`
	//
	// Note: Settings for the Log Router can currently only be configured for
	// Google Cloud organizations. Once configured, it applies to all projects and
	// folders in the Google Cloud organization.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The settings to update.
	//
	// See [Enabling CMEK for Log
	// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
	// for more information.
	Settings *Settings `protobuf:"bytes,2,opt,name=settings,proto3" json:"settings,omitempty"`
	// Optional. Field mask identifying which fields from `settings` should
	// be updated. A field will be overwritten if and only if it is in the update
	// mask. Output only fields cannot be updated.
	//
	// See [FieldMask][google.protobuf.FieldMask] for more information.
	//
	// For example: `"updateMask=kmsKeyName"`
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,3,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateSettingsRequest) Reset() {
	*x = UpdateSettingsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSettingsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSettingsRequest) ProtoMessage() {}

func (x *UpdateSettingsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSettingsRequest.ProtoReflect.Descriptor instead.
func (*UpdateSettingsRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{34}
}

func (x *UpdateSettingsRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateSettingsRequest) GetSettings() *Settings {
	if x != nil {
		return x.Settings
	}
	return nil
}

func (x *UpdateSettingsRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// Describes the settings associated with a project, folder, organization,
// billing account, or flexible resource.
type Settings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Output only. The resource name of the settings.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Optional. The resource name for the configured Cloud KMS key.
	//
	// KMS key name format:
	//
	//	"projects/[PROJECT_ID]/locations/[LOCATION]/keyRings/[KEYRING]/cryptoKeys/[KEY]"
	//
	// For example:
	//
	//	`"projects/my-project/locations/us-central1/keyRings/my-ring/cryptoKeys/my-key"`
	//
	// To enable CMEK for the Log Router, set this field to a valid
	// `kms_key_name` for which the associated service account has the required
	// `roles/cloudkms.cryptoKeyEncrypterDecrypter` role assigned for the key.
	//
	// The Cloud KMS key used by the Log Router can be updated by changing the
	// `kms_key_name` to a new valid key name. Encryption operations that are in
	// progress will be completed with the key that was in use when they started.
	// Decryption operations will be completed using the key that was used at the
	// time of encryption unless access to that key has been revoked.
	//
	// To disable CMEK for the Log Router, set this field to an empty string.
	//
	// See [Enabling CMEK for Log
	// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
	// for more information.
	KmsKeyName string `protobuf:"bytes,2,opt,name=kms_key_name,json=kmsKeyName,proto3" json:"kms_key_name,omitempty"`
	// Output only. The service account that will be used by the Log Router to access your
	// Cloud KMS key.
	//
	// Before enabling CMEK for Log Router, you must first assign the role
	// `roles/cloudkms.cryptoKeyEncrypterDecrypter` to the service account that
	// the Log Router will use to access your Cloud KMS key. Use
	// [GetSettings][google.logging.v2.ConfigServiceV2.GetSettings] to
	// obtain the service account ID.
	//
	// See [Enabling CMEK for Log
	// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
	// for more information.
	KmsServiceAccountId string `protobuf:"bytes,3,opt,name=kms_service_account_id,json=kmsServiceAccountId,proto3" json:"kms_service_account_id,omitempty"`
	// Optional. The Cloud region that will be used for _Default and _Required log buckets
	// for newly created projects and folders. For example `europe-west1`.
	// This setting does not affect the location of custom log buckets.
	StorageLocation string `protobuf:"bytes,4,opt,name=storage_location,json=storageLocation,proto3" json:"storage_location,omitempty"`
	// Optional. If set to true, the _Default sink in newly created projects and folders
	// will created in a disabled state. This can be used to automatically disable
	// log ingestion if there is already an aggregated sink configured in the
	// hierarchy. The _Default sink can be re-enabled manually if needed.
	DisableDefaultSink bool `protobuf:"varint,5,opt,name=disable_default_sink,json=disableDefaultSink,proto3" json:"disable_default_sink,omitempty"`
}

func (x *Settings) Reset() {
	*x = Settings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Settings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Settings) ProtoMessage() {}

func (x *Settings) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Settings.ProtoReflect.Descriptor instead.
func (*Settings) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{35}
}

func (x *Settings) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Settings) GetKmsKeyName() string {
	if x != nil {
		return x.KmsKeyName
	}
	return ""
}

func (x *Settings) GetKmsServiceAccountId() string {
	if x != nil {
		return x.KmsServiceAccountId
	}
	return ""
}

func (x *Settings) GetStorageLocation() string {
	if x != nil {
		return x.StorageLocation
	}
	return ""
}

func (x *Settings) GetDisableDefaultSink() bool {
	if x != nil {
		return x.DisableDefaultSink
	}
	return false
}

// The parameters to CopyLogEntries.
type CopyLogEntriesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Log bucket from which to copy log entries.
	//
	// For example:
	//
	//	`"projects/my-project/locations/global/buckets/my-source-bucket"`
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Optional. A filter specifying which log entries to copy. The filter must be no more
	// than 20k characters. An empty filter matches all log entries.
	Filter string `protobuf:"bytes,3,opt,name=filter,proto3" json:"filter,omitempty"`
	// Required. Destination to which to copy log entries.
	Destination string `protobuf:"bytes,4,opt,name=destination,proto3" json:"destination,omitempty"`
}

func (x *CopyLogEntriesRequest) Reset() {
	*x = CopyLogEntriesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyLogEntriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyLogEntriesRequest) ProtoMessage() {}

func (x *CopyLogEntriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyLogEntriesRequest.ProtoReflect.Descriptor instead.
func (*CopyLogEntriesRequest) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{36}
}

func (x *CopyLogEntriesRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CopyLogEntriesRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *CopyLogEntriesRequest) GetDestination() string {
	if x != nil {
		return x.Destination
	}
	return ""
}

// Metadata for CopyLogEntries long running operations.
type CopyLogEntriesMetadata struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The create time of an operation.
	StartTime *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// The end time of an operation.
	EndTime *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// State of an operation.
	State OperationState `protobuf:"varint,3,opt,name=state,proto3,enum=google.logging.v2.OperationState" json:"state,omitempty"`
	// Identifies whether the user has requested cancellation of the operation.
	CancellationRequested bool `protobuf:"varint,4,opt,name=cancellation_requested,json=cancellationRequested,proto3" json:"cancellation_requested,omitempty"`
	// CopyLogEntries RPC request.
	Request *CopyLogEntriesRequest `protobuf:"bytes,5,opt,name=request,proto3" json:"request,omitempty"`
	// Estimated progress of the operation (0 - 100%).
	Progress int32 `protobuf:"varint,6,opt,name=progress,proto3" json:"progress,omitempty"`
	// The IAM identity of a service account that must be granted access to the
	// destination.
	//
	// If the service account is not granted permission to the destination within
	// an hour, the operation will be cancelled.
	//
	// For example: `"serviceAccount:<EMAIL>"`
	WriterIdentity string `protobuf:"bytes,7,opt,name=writer_identity,json=writerIdentity,proto3" json:"writer_identity,omitempty"`
}

func (x *CopyLogEntriesMetadata) Reset() {
	*x = CopyLogEntriesMetadata{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyLogEntriesMetadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyLogEntriesMetadata) ProtoMessage() {}

func (x *CopyLogEntriesMetadata) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyLogEntriesMetadata.ProtoReflect.Descriptor instead.
func (*CopyLogEntriesMetadata) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{37}
}

func (x *CopyLogEntriesMetadata) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *CopyLogEntriesMetadata) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *CopyLogEntriesMetadata) GetState() OperationState {
	if x != nil {
		return x.State
	}
	return OperationState_OPERATION_STATE_UNSPECIFIED
}

func (x *CopyLogEntriesMetadata) GetCancellationRequested() bool {
	if x != nil {
		return x.CancellationRequested
	}
	return false
}

func (x *CopyLogEntriesMetadata) GetRequest() *CopyLogEntriesRequest {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *CopyLogEntriesMetadata) GetProgress() int32 {
	if x != nil {
		return x.Progress
	}
	return 0
}

func (x *CopyLogEntriesMetadata) GetWriterIdentity() string {
	if x != nil {
		return x.WriterIdentity
	}
	return ""
}

// Response type for CopyLogEntries long running operations.
type CopyLogEntriesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Number of log entries copied.
	LogEntriesCopiedCount int64 `protobuf:"varint,1,opt,name=log_entries_copied_count,json=logEntriesCopiedCount,proto3" json:"log_entries_copied_count,omitempty"`
}

func (x *CopyLogEntriesResponse) Reset() {
	*x = CopyLogEntriesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_logging_v2_logging_config_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CopyLogEntriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CopyLogEntriesResponse) ProtoMessage() {}

func (x *CopyLogEntriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_google_logging_v2_logging_config_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CopyLogEntriesResponse.ProtoReflect.Descriptor instead.
func (*CopyLogEntriesResponse) Descriptor() ([]byte, []int) {
	return file_google_logging_v2_logging_config_proto_rawDescGZIP(), []int{38}
}

func (x *CopyLogEntriesResponse) GetLogEntriesCopiedCount() int64 {
	if x != nil {
		return x.LogEntriesCopiedCount
	}
	return 0
}

var File_google_logging_v2_logging_config_proto protoreflect.FileDescriptor

var file_google_logging_v2_logging_config_proto_rawDesc = []byte{
	0x0a, 0x26, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x32, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x1a, 0x1c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x19, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x23,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x6c, 0x6f, 0x6e, 0x67, 0x72, 0x75, 0x6e, 0x6e, 0x69,
	0x6e, 0x67, 0x2f, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xf5, 0x05, 0x0a, 0x09, 0x4c, 0x6f, 0x67, 0x42, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42,
	0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x61,
	0x79, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74,
	0x69, 0x6f, 0x6e, 0x44, 0x61, 0x79, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x6c, 0x6f, 0x63, 0x6b, 0x65,
	0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x6c, 0x6f, 0x63, 0x6b, 0x65, 0x64, 0x12,
	0x4f, 0x0a, 0x0f, 0x6c, 0x69, 0x66, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x66,
	0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x03,
	0x52, 0x0e, 0x6c, 0x69, 0x66, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x73,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x65, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x44, 0x0a,
	0x0d, 0x63, 0x6d, 0x65, 0x6b, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f,
	0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0c, 0x63, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x3a, 0xa5, 0x02, 0xea, 0x41, 0xa1, 0x02, 0x0a, 0x20, 0x6c, 0x6f, 0x67, 0x67,
	0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x38, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d,
	0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x7d, 0x12, 0x42, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x73, 0x2f, 0x7b, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x7d, 0x12, 0x36, 0x66, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x73, 0x2f, 0x7b, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x7d, 0x2f, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x7d, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x7d, 0x12, 0x47, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x2f, 0x7b, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x73, 0x2f, 0x7b, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x7d, 0x22, 0xb5, 0x04, 0x0a, 0x07,
	0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65, 0x77, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x3a, 0xd7, 0x02, 0xea, 0x41, 0xd3, 0x02,
	0x0a, 0x1e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65, 0x77,
	0x12, 0x45, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a,
	0x65, 0x63, 0x74, 0x7d, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x73, 0x2f, 0x7b, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x7d, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73,
	0x2f, 0x7b, 0x76, 0x69, 0x65, 0x77, 0x7d, 0x12, 0x4f, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x73, 0x2f, 0x7b, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x7d, 0x2f, 0x76, 0x69, 0x65, 0x77,
	0x73, 0x2f, 0x7b, 0x76, 0x69, 0x65, 0x77, 0x7d, 0x12, 0x43, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x73, 0x2f, 0x7b, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x7d, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f,
	0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x7d,
	0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x2f, 0x7b, 0x76, 0x69, 0x65, 0x77, 0x7d, 0x12, 0x54, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x7b,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d,
	0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x7d, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x2f, 0x7b, 0x76, 0x69,
	0x65, 0x77, 0x7d, 0x22, 0xa2, 0x07, 0x0a, 0x07, 0x4c, 0x6f, 0x67, 0x53, 0x69, 0x6e, 0x6b, 0x12,
	0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x09, 0xe0,
	0x41, 0x02, 0xfa, 0x41, 0x03, 0x0a, 0x01, 0x2a, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x13, 0x20, 0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x01,
	0x52, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x44, 0x0a, 0x0a, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x10, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x42,
	0x03, 0xe0, 0x41, 0x01, 0x52, 0x0a, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x12, 0x60, 0x0a, 0x15, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x5f, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x28, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x53, 0x69, 0x6e, 0x6b, 0x2e, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x42, 0x02, 0x18, 0x01, 0x52, 0x13, 0x6f,
	0x75, 0x74, 0x70, 0x75, 0x74, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x12, 0x2c, 0x0a, 0x0f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03,
	0x52, 0x0e, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x12, 0x2e, 0x0a, 0x10, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x5f, 0x63, 0x68, 0x69, 0x6c,
	0x64, 0x72, 0x65, 0x6e, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52,
	0x0f, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e,
	0x12, 0x54, 0x0a, 0x10, 0x62, 0x69, 0x67, 0x71, 0x75, 0x65, 0x72, 0x79, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x42,
	0x69, 0x67, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x03,
	0xe0, 0x41, 0x01, 0x48, 0x00, 0x52, 0x0f, 0x62, 0x69, 0x67, 0x71, 0x75, 0x65, 0x72, 0x79, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x40, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x3f, 0x0a, 0x0d, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x1e, 0x0a, 0x1a, 0x56,
	0x45, 0x52, 0x53, 0x49, 0x4f, 0x4e, 0x5f, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x06, 0x0a, 0x02, 0x56,
	0x32, 0x10, 0x01, 0x12, 0x06, 0x0a, 0x02, 0x56, 0x31, 0x10, 0x02, 0x3a, 0xbf, 0x01, 0xea, 0x41,
	0xbb, 0x01, 0x0a, 0x1e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x53, 0x69,
	0x6e, 0x6b, 0x12, 0x1f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x7b, 0x73, 0x69,
	0x6e, 0x6b, 0x7d, 0x12, 0x29, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x7d, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x7d, 0x12, 0x1d,
	0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x7d,
	0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x7d, 0x12, 0x2e, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x7b,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d,
	0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x7d, 0x42, 0x09, 0x0a,
	0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x9e, 0x01, 0x0a, 0x0f, 0x42, 0x69, 0x67,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x39, 0x0a, 0x16,
	0x75, 0x73, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x64, 0x5f,
	0x74, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41,
	0x01, 0x52, 0x14, 0x75, 0x73, 0x65, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65,
	0x64, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x12, 0x50, 0x0a, 0x22, 0x75, 0x73, 0x65, 0x73, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e,
	0x5f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x1f, 0x75, 0x73, 0x65, 0x73, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x43, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x50, 0x61, 0x72,
	0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x22, 0x9c, 0x01, 0x0a, 0x12, 0x4c, 0x69,
	0x73, 0x74, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x40, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x28, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x22, 0x12, 0x20, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e,
	0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4c, 0x6f, 0x67, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x20, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x75, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74,
	0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x36, 0x0a, 0x07, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x07,
	0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22,
	0xb4, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x40, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x28, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x22, 0x12,
	0x20, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x42, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x09, 0x62, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x08, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x49, 0x64, 0x12, 0x39, 0x0a, 0x06, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x4c, 0x6f, 0x67, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06,
	0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x22, 0xd0, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x28, 0xe0, 0x41,
	0x02, 0xfa, 0x41, 0x22, 0x0a, 0x20, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67,
	0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x39, 0x0a, 0x06,
	0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x6f, 0x67, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b, 0x22, 0x50, 0x0a, 0x10, 0x47, 0x65, 0x74,
	0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x28, 0xe0, 0x41, 0x02,
	0xfa, 0x41, 0x22, 0x0a, 0x20, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x42,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x53, 0x0a, 0x13, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3c, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x28, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x22, 0x0a, 0x20, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e,
	0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x4c, 0x6f, 0x67, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x22, 0x55, 0x0a, 0x15, 0x55, 0x6e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3c, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x28, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x22, 0x0a,
	0x20, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x42, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x75, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x56,
	0x69, 0x65, 0x77, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x06, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x01, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x20, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x03, 0xe0, 0x41, 0x01, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x6d,
	0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x05, 0x76, 0x69, 0x65, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65, 0x77, 0x52, 0x05,
	0x76, 0x69, 0x65, 0x77, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x83, 0x01,
	0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x12, 0x1c, 0x0a, 0x07, 0x76, 0x69, 0x65, 0x77, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x76, 0x69, 0x65, 0x77, 0x49, 0x64, 0x12, 0x33,
	0x0a, 0x04, 0x76, 0x69, 0x65, 0x77, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65, 0x77, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x76,
	0x69, 0x65, 0x77, 0x22, 0xa3, 0x01, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x76, 0x69, 0x65, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65, 0x77, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x04, 0x76, 0x69, 0x65, 0x77, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b, 0x22, 0x4c, 0x0a, 0x0e, 0x47, 0x65, 0x74,
	0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0xe0, 0x41, 0x02, 0xfa, 0x41,
	0x20, 0x0a, 0x1e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65,
	0x77, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x4f, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0xe0, 0x41, 0x02, 0xfa,
	0x41, 0x20, 0x0a, 0x1e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x56, 0x69,
	0x65, 0x77, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x98, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x69, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3e, 0x0a,
	0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0xe0,
	0x41, 0x02, 0xfa, 0x41, 0x20, 0x12, 0x1e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f,
	0x67, 0x53, 0x69, 0x6e, 0x6b, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x0a,
	0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x20, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x22, 0x6d, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x69, 0x6e, 0x6b, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x30, 0x0a, 0x05, 0x73, 0x69, 0x6e, 0x6b,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x53,
	0x69, 0x6e, 0x6b, 0x52, 0x05, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65,
	0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x22, 0x55, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x09, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x20, 0x0a,
	0x1e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x53, 0x69, 0x6e, 0x6b, 0x52,
	0x08, 0x73, 0x69, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xc3, 0x01, 0x0a, 0x11, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x3e, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x26, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x20, 0x12, 0x1e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4c, 0x6f, 0x67, 0x53, 0x69, 0x6e, 0x6b, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12,
	0x33, 0x0a, 0x04, 0x73, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x53, 0x69, 0x6e, 0x6b, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04,
	0x73, 0x69, 0x6e, 0x6b, 0x12, 0x39, 0x0a, 0x16, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x77,
	0x72, 0x69, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x14, 0x75, 0x6e, 0x69, 0x71, 0x75,
	0x65, 0x57, 0x72, 0x69, 0x74, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x22,
	0x8a, 0x02, 0x0a, 0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x69, 0x6e, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x09, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x20,
	0x0a, 0x1e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x53, 0x69, 0x6e, 0x6b,
	0x52, 0x08, 0x73, 0x69, 0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x73, 0x69,
	0x6e, 0x6b, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67,
	0x53, 0x69, 0x6e, 0x6b, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x73, 0x69, 0x6e, 0x6b, 0x12,
	0x39, 0x0a, 0x16, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x5f, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x42,
	0x03, 0xe0, 0x41, 0x01, 0x52, 0x14, 0x75, 0x6e, 0x69, 0x71, 0x75, 0x65, 0x57, 0x72, 0x69, 0x74,
	0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x42, 0x03, 0xe0, 0x41, 0x01,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b, 0x22, 0x58, 0x0a, 0x11,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x43, 0x0a, 0x09, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x26, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x20, 0x0a, 0x1e, 0x6c, 0x6f,
	0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x53, 0x69, 0x6e, 0x6b, 0x52, 0x08, 0x73, 0x69,
	0x6e, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xff, 0x03, 0x0a, 0x0c, 0x4c, 0x6f, 0x67, 0x45, 0x78,
	0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x08, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x08, 0x64, 0x69, 0x73,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x40, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x3a, 0xec, 0x01, 0xea, 0x41, 0xe8, 0x01,
	0x0a, 0x23, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x45, 0x78, 0x63, 0x6c,
	0x75, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f,
	0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x7d,
	0x12, 0x33, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x65, 0x78, 0x63, 0x6c, 0x75,
	0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x12, 0x27, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x7b,
	0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x7d, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x12, 0x38,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f,
	0x7b, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x7d, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x7d, 0x22, 0xa2, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73,
	0x74, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x43, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2b, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x25, 0x12, 0x23, 0x6c, 0x6f, 0x67, 0x67,
	0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x22, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x01,
	0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x20, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03,
	0xe0, 0x41, 0x01, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x81, 0x01,
	0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a, 0x0a, 0x65, 0x78, 0x63, 0x6c,
	0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x22, 0x56, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2b, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x25, 0x0a, 0x23,
	0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xa1, 0x01, 0x0a, 0x16, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x43, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2b, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x25, 0x12, 0x23, 0x6c, 0x6f,
	0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x42, 0x0a, 0x09, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x09, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0xdf, 0x01,
	0x0a, 0x16, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2b, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x25, 0x0a, 0x23,
	0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73,
	0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x09, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x09, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x40, 0x0a,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x42, 0x03,
	0xe0, 0x41, 0x02, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b, 0x22,
	0x59, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2b, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x25, 0x0a,
	0x23, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4c, 0x6f, 0x67, 0x45, 0x78, 0x63, 0x6c, 0x75,
	0x73, 0x69, 0x6f, 0x6e, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x59, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x43, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3f, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2b, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x25, 0x0a, 0x23, 0x6c, 0x6f, 0x67, 0x67,
	0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x43, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xc1, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x43, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x49, 0x0a, 0x0d,
	0x63, 0x6d, 0x65, 0x6b, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67,
	0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0c, 0x63, 0x6d, 0x65, 0x6b, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46,
	0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b, 0x22, 0xc3, 0x02, 0x0a, 0x0c, 0x43, 0x6d,
	0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0c, 0x6b, 0x6d, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6b, 0x6d, 0x73, 0x4b, 0x65,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x10, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x3a, 0xc4, 0x01, 0xea, 0x41, 0xc0, 0x01, 0x0a,
	0x23, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x43, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x12, 0x1f, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d, 0x2f, 0x63, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x29, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x7d, 0x2f, 0x63, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x1d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x66, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x7d, 0x2f, 0x63, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x2e, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x2f, 0x7b, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x7d, 0x2f, 0x63, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22,
	0x51, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3b, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x27, 0xe0, 0x41, 0x02, 0xfa, 0x41, 0x21, 0x0a, 0x1f, 0x6c, 0x6f, 0x67,
	0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0xb0, 0x01, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61,
	0x73, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x4d, 0x61, 0x73, 0x6b, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4d, 0x61, 0x73, 0x6b, 0x22, 0x9e, 0x03, 0x0a, 0x08, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0c, 0x6b,
	0x6d, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x0a, 0x6b, 0x6d, 0x73, 0x4b, 0x65, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x38, 0x0a, 0x16, 0x6b, 0x6d, 0x73, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x13, 0x6b, 0x6d, 0x73, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x10,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x0f, 0x73, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x35, 0x0a, 0x14,
	0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f,
	0x73, 0x69, 0x6e, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52,
	0x12, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53,
	0x69, 0x6e, 0x6b, 0x3a, 0xb0, 0x01, 0xea, 0x41, 0xac, 0x01, 0x0a, 0x1f, 0x6c, 0x6f, 0x67, 0x67,
	0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x1b, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x7b, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x7d, 0x2f,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x25, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x19, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x7d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x2a, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x7b, 0x62, 0x69, 0x6c,
	0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x2f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x74, 0x0a, 0x15, 0x43, 0x6f, 0x70, 0x79, 0x4c, 0x6f,
	0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x01, 0x52, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x25, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x83, 0x03, 0x0a,
	0x16, 0x43, 0x6f, 0x70, 0x79, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x4d,
	0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x35, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x37, 0x0a, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x35, 0x0a, 0x16, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x15, 0x63, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x6c, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x65, 0x64, 0x12, 0x42, 0x0a, 0x07, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43,
	0x6f, 0x70, 0x79, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x77, 0x72, 0x69,
	0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x77, 0x72, 0x69, 0x74, 0x65, 0x72, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x22, 0x51, 0x0a, 0x16, 0x43, 0x6f, 0x70, 0x79, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74,
	0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x37, 0x0a, 0x18,
	0x6c, 0x6f, 0x67, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x5f, 0x63, 0x6f, 0x70, 0x69,
	0x65, 0x64, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x15,
	0x6c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x43, 0x6f, 0x70, 0x69, 0x65, 0x64,
	0x43, 0x6f, 0x75, 0x6e, 0x74, 0x2a, 0x53, 0x0a, 0x0e, 0x4c, 0x69, 0x66, 0x65, 0x63, 0x79, 0x63,
	0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x4c, 0x49, 0x46, 0x45, 0x43,
	0x59, 0x43, 0x4c, 0x45, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x45, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x5f, 0x52,
	0x45, 0x51, 0x55, 0x45, 0x53, 0x54, 0x45, 0x44, 0x10, 0x02, 0x2a, 0xf4, 0x01, 0x0a, 0x0e, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1f, 0x0a,
	0x1b, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x1d,
	0x0a, 0x19, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54,
	0x45, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x2b, 0x0a,
	0x27, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45,
	0x5f, 0x57, 0x41, 0x49, 0x54, 0x49, 0x4e, 0x47, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x50, 0x45, 0x52,
	0x4d, 0x49, 0x53, 0x53, 0x49, 0x4f, 0x4e, 0x53, 0x10, 0x02, 0x12, 0x1b, 0x0a, 0x17, 0x4f, 0x50,
	0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x52, 0x55,
	0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x50, 0x45, 0x52, 0x41,
	0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x55, 0x43, 0x43, 0x45,
	0x45, 0x44, 0x45, 0x44, 0x10, 0x04, 0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54,
	0x49, 0x4f, 0x4e, 0x5f, 0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x05, 0x12, 0x1d, 0x0a, 0x19, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f,
	0x53, 0x54, 0x41, 0x54, 0x45, 0x5f, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x4c, 0x45, 0x44, 0x10,
	0x06, 0x32, 0x8c, 0x45, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x56, 0x32, 0x12, 0xdc, 0x02, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x73, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c,
	0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0xfd, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xed, 0x01, 0x12, 0x24,
	0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x2a, 0x2f, 0x2a, 0x2f,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x62, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x73, 0x5a, 0x2d, 0x12, 0x2b, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x73, 0x5a, 0x32, 0x12, 0x30, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f,
	0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x5a, 0x2c, 0x12, 0x2a, 0x2f, 0x76, 0x32, 0x2f, 0x7b,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a,
	0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x73, 0x5a, 0x34, 0x12, 0x32, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x2a, 0x7d, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0xda, 0x41, 0x06, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x12, 0xb9, 0x02, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x42, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x12, 0x23, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x42,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x22, 0xe8, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xe1, 0x01, 0x12,
	0x24, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x2a, 0x2f, 0x2a, 0x2f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x2d, 0x12, 0x2b, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x32, 0x12, 0x30, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d,
	0x65, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x2c, 0x12, 0x2a, 0x2f, 0x76, 0x32, 0x2f,
	0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x28, 0x12, 0x26, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d,
	0x12, 0xf3, 0x02, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x12, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x42, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x6f,
	0x67, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x22, 0x9c, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x95,
	0x02, 0x22, 0x24, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x2a,
	0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f,
	0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x3a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5a,
	0x35, 0x22, 0x2b, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x3a, 0x06,
	0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5a, 0x3a, 0x22, 0x30, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x2a, 0x7d, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x3a, 0x06, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x5a, 0x34, 0x22, 0x2a, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73,
	0x3a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5a, 0x3c, 0x22, 0x32, 0x2f, 0x76, 0x32, 0x2f,
	0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x3a, 0x06,
	0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0xf3, 0x02, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x22, 0x9c, 0x02,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x95, 0x02, 0x32, 0x24, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x2a, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x06, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x5a, 0x35, 0x32, 0x2b, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5a, 0x3a, 0x32, 0x30,
	0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d,
	0x3a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5a, 0x34, 0x32, 0x2a, 0x2f, 0x76, 0x32, 0x2f,
	0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f,
	0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5a, 0x3c,
	0x32, 0x32, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0xc5, 0x02, 0x0a,
	0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x26, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xf4, 0x01,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0xed, 0x01, 0x2a, 0x24, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x2a, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x2d, 0x2a,
	0x2b, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x32, 0x2a, 0x30,
	0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d,
	0x5a, 0x2c, 0x2a, 0x2a, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x34,
	0x2a, 0x32, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x62, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x73, 0x2f, 0x2a, 0x7d, 0x12, 0x85, 0x03, 0x0a, 0x0e, 0x55, 0x6e, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x28, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x6e, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xb0, 0x02, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0xa9, 0x02, 0x22, 0x2d, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x2a,
	0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x75, 0x6e, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x3a, 0x01, 0x2a, 0x5a, 0x39, 0x22, 0x34, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x75, 0x6e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x3a, 0x01, 0x2a,
	0x5a, 0x3e, 0x22, 0x39, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x6f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x75, 0x6e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x3a, 0x01, 0x2a,
	0x5a, 0x38, 0x22, 0x33, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x75,
	0x6e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x5a, 0x40, 0x22, 0x3b, 0x2f, 0x76,
	0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d,
	0x3a, 0x75, 0x6e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0xfe, 0x02, 0x0a,
	0x09, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x73, 0x12, 0x23, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x69, 0x65, 0x77, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xa5, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x95, 0x02, 0x12,
	0x2c, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x2a, 0x2f, 0x2a,
	0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x5a, 0x35, 0x12,
	0x33, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x76,
	0x69, 0x65, 0x77, 0x73, 0x5a, 0x3a, 0x12, 0x38, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f,
	0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73,
	0x5a, 0x34, 0x12, 0x32, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d,
	0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d,
	0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x5a, 0x3c, 0x12, 0x3a, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x76,
	0x69, 0x65, 0x77, 0x73, 0xda, 0x41, 0x06, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0xdb, 0x02,
	0x0a, 0x07, 0x47, 0x65, 0x74, 0x56, 0x69, 0x65, 0x77, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65,
	0x74, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65, 0x77, 0x22, 0x90, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x89, 0x02, 0x12, 0x2c, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x2a, 0x2f,
	0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x2f, 0x2a, 0x7d,
	0x5a, 0x35, 0x12, 0x33, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x76,
	0x69, 0x65, 0x77, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x3a, 0x12, 0x38, 0x2f, 0x76, 0x32, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a,
	0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73,
	0x2f, 0x2a, 0x7d, 0x5a, 0x34, 0x12, 0x32, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a,
	0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x30, 0x12, 0x2e, 0x2f, 0x76, 0x32,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73,
	0x2f, 0x2a, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x2f, 0x2a, 0x7d, 0x12, 0x8b, 0x03, 0x0a, 0x0a,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x24, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65, 0x77, 0x22, 0xba, 0x02, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0xb3, 0x02, 0x22, 0x2c, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x3d, 0x2a, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x76,
	0x69, 0x65, 0x77, 0x73, 0x3a, 0x04, 0x76, 0x69, 0x65, 0x77, 0x5a, 0x3b, 0x22, 0x33, 0x2f, 0x76,
	0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a,
	0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x76, 0x69, 0x65, 0x77,
	0x73, 0x3a, 0x04, 0x76, 0x69, 0x65, 0x77, 0x5a, 0x40, 0x22, 0x38, 0x2f, 0x76, 0x32, 0x2f, 0x7b,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x76, 0x69,
	0x65, 0x77, 0x73, 0x3a, 0x04, 0x76, 0x69, 0x65, 0x77, 0x5a, 0x3a, 0x22, 0x32, 0x2f, 0x76, 0x32,
	0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73,
	0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x3a,
	0x04, 0x76, 0x69, 0x65, 0x77, 0x5a, 0x42, 0x22, 0x3a, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x76, 0x69,
	0x65, 0x77, 0x73, 0x3a, 0x04, 0x76, 0x69, 0x65, 0x77, 0x12, 0x8b, 0x03, 0x0a, 0x0a, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x56, 0x69, 0x65, 0x77, 0x22, 0xba, 0x02, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0xb3, 0x02, 0x32, 0x2c, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d,
	0x2a, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f,
	0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x2f,
	0x2a, 0x7d, 0x3a, 0x04, 0x76, 0x69, 0x65, 0x77, 0x5a, 0x3b, 0x32, 0x33, 0x2f, 0x76, 0x32, 0x2f,
	0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a,
	0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x2f, 0x2a, 0x7d, 0x3a,
	0x04, 0x76, 0x69, 0x65, 0x77, 0x5a, 0x40, 0x32, 0x38, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x2f, 0x2a,
	0x7d, 0x3a, 0x04, 0x76, 0x69, 0x65, 0x77, 0x5a, 0x3a, 0x32, 0x32, 0x2f, 0x76, 0x32, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x6c,
	0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x04, 0x76,
	0x69, 0x65, 0x77, 0x5a, 0x42, 0x32, 0x3a, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x2f, 0x2a,
	0x7d, 0x3a, 0x04, 0x76, 0x69, 0x65, 0x77, 0x12, 0xe9, 0x02, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x56, 0x69, 0x65, 0x77, 0x12, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x56, 0x69, 0x65, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x9c, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x95, 0x02, 0x2a, 0x2c,
	0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x2a, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x73, 0x2f, 0x2a, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x35, 0x2a, 0x33,
	0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63,
	0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a,
	0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73,
	0x2f, 0x2a, 0x7d, 0x5a, 0x3a, 0x2a, 0x38, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65,
	0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a,
	0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73, 0x2f, 0x2a, 0x7d, 0x5a,
	0x34, 0x2a, 0x32, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x2a, 0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x76, 0x69, 0x65,
	0x77, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x3c, 0x2a, 0x3a, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a,
	0x2f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x76, 0x69, 0x65, 0x77, 0x73,
	0x2f, 0x2a, 0x7d, 0x12, 0x90, 0x02, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x69, 0x6e, 0x6b,
	0x73, 0x12, 0x23, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x69, 0x6e, 0x6b, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x69, 0x6e, 0x6b, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb7, 0x01, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0xa7, 0x01, 0x12, 0x16, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72,
	0x65, 0x6e, 0x74, 0x3d, 0x2a, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x5a, 0x1f,
	0x12, 0x1d, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x5a,
	0x24, 0x12, 0x22, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f,
	0x73, 0x69, 0x6e, 0x6b, 0x73, 0x5a, 0x1e, 0x12, 0x1c, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x2f,
	0x73, 0x69, 0x6e, 0x6b, 0x73, 0x5a, 0x26, 0x12, 0x24, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61,
	0x72, 0x65, 0x6e, 0x74, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0xda, 0x41, 0x06,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0x9e, 0x02, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x53, 0x69,
	0x6e, 0x6b, 0x12, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x69, 0x6e, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c,
	0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x53, 0x69, 0x6e,
	0x6b, 0x22, 0xd3, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xc0, 0x01, 0x12, 0x1b, 0x2f, 0x76, 0x32,
	0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x2a, 0x2f, 0x2a, 0x2f,
	0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x24, 0x12, 0x22, 0x2f, 0x76, 0x32, 0x2f,
	0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65,
	0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x29,
	0x12, 0x27, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a,
	0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x23, 0x12, 0x21, 0x2f, 0x76, 0x32,
	0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66, 0x6f, 0x6c, 0x64,
	0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x2b,
	0x12, 0x29, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73,
	0x2f, 0x2a, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a, 0x7d, 0xda, 0x41, 0x09, 0x73, 0x69,
	0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0xab, 0x02, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x69, 0x6e, 0x6b, 0x12, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x4c, 0x6f, 0x67, 0x53, 0x69, 0x6e, 0x6b, 0x22, 0xda, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0xc5, 0x01, 0x22, 0x16, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d,
	0x2a, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x3a, 0x04, 0x73, 0x69, 0x6e, 0x6b,
	0x5a, 0x25, 0x22, 0x1d, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d,
	0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x69, 0x6e, 0x6b,
	0x73, 0x3a, 0x04, 0x73, 0x69, 0x6e, 0x6b, 0x5a, 0x2a, 0x22, 0x22, 0x2f, 0x76, 0x32, 0x2f, 0x7b,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x3a, 0x04, 0x73,
	0x69, 0x6e, 0x6b, 0x5a, 0x24, 0x22, 0x1c, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x69,
	0x6e, 0x6b, 0x73, 0x3a, 0x04, 0x73, 0x69, 0x6e, 0x6b, 0x5a, 0x2c, 0x22, 0x24, 0x2f, 0x76, 0x32,
	0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x69, 0x6e, 0x6b,
	0x73, 0x3a, 0x04, 0x73, 0x69, 0x6e, 0x6b, 0xda, 0x41, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x2c, 0x73, 0x69, 0x6e, 0x6b, 0x12, 0x9f, 0x04, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x69, 0x6e, 0x6b, 0x12, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f,
	0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c,
	0x6f, 0x67, 0x53, 0x69, 0x6e, 0x6b, 0x22, 0xce, 0x03, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x99, 0x03,
	0x1a, 0x1b, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x3d, 0x2a, 0x2f, 0x2a, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x04, 0x73,
	0x69, 0x6e, 0x6b, 0x5a, 0x2a, 0x1a, 0x22, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a,
	0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x04, 0x73, 0x69, 0x6e, 0x6b, 0x5a,
	0x2f, 0x1a, 0x27, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x2a, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x04, 0x73, 0x69, 0x6e, 0x6b,
	0x5a, 0x29, 0x1a, 0x21, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x69, 0x6e,
	0x6b, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x04, 0x73, 0x69, 0x6e, 0x6b, 0x5a, 0x31, 0x1a, 0x29, 0x2f,
	0x76, 0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f,
	0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x04, 0x73, 0x69, 0x6e, 0x6b, 0x5a, 0x2a,
	0x32, 0x22, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x69, 0x6e, 0x6b,
	0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x04, 0x73, 0x69, 0x6e, 0x6b, 0x5a, 0x2f, 0x32, 0x27, 0x2f, 0x76,
	0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x69, 0x6e,
	0x6b, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x04, 0x73, 0x69, 0x6e, 0x6b, 0x5a, 0x29, 0x32, 0x21, 0x2f,
	0x76, 0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a, 0x7d,
	0x3a, 0x04, 0x73, 0x69, 0x6e, 0x6b, 0x5a, 0x31, 0x32, 0x29, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x73,
	0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73,
	0x2f, 0x2a, 0x7d, 0x3a, 0x04, 0x73, 0x69, 0x6e, 0x6b, 0xda, 0x41, 0x1a, 0x73, 0x69, 0x6e, 0x6b,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x73, 0x69, 0x6e, 0x6b, 0x2c, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0xda, 0x41, 0x0e, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x2c, 0x73, 0x69, 0x6e, 0x6b, 0x12, 0xa0, 0x02, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x53, 0x69, 0x6e, 0x6b, 0x12, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x53, 0x69, 0x6e, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0xd3, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xc0, 0x01, 0x2a, 0x1b,
	0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x2a,
	0x2f, 0x2a, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x24, 0x2a, 0x22, 0x2f,
	0x76, 0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a,
	0x7d, 0x5a, 0x29, 0x2a, 0x27, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x23, 0x2a, 0x21,
	0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a,
	0x7d, 0x5a, 0x2b, 0x2a, 0x29, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x73, 0x69, 0x6e, 0x6b, 0x73, 0x2f, 0x2a, 0x7d, 0xda, 0x41,
	0x09, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0xb8, 0x02, 0x0a, 0x0e, 0x4c,
	0x69, 0x73, 0x74, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x28, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0xd0, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xc0, 0x01, 0x12, 0x1b, 0x2f, 0x76,
	0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x2a, 0x2f, 0x2a, 0x7d, 0x2f, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x5a, 0x24, 0x12, 0x22, 0x2f, 0x76, 0x32,
	0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74,
	0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x5a,
	0x29, 0x12, 0x27, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x5a, 0x23, 0x12, 0x21, 0x2f, 0x76,
	0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72,
	0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x5a,
	0x2b, 0x12, 0x29, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x62,
	0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a,
	0x7d, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0xda, 0x41, 0x06, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x12, 0xa8, 0x02, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x45, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x78,
	0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0xce, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xc0, 0x01, 0x12, 0x1b, 0x2f, 0x76, 0x32, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x2a, 0x2f, 0x2a, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x24, 0x12, 0x22, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x29, 0x12, 0x27,
	0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x23, 0x12, 0x21, 0x2f, 0x76, 0x32, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x2b, 0x12, 0x29,
	0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x65, 0x78, 0x63, 0x6c,
	0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0xda, 0x41, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0xf1, 0x02, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x6c, 0x75,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f,
	0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45,
	0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e,
	0x22, 0x91, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xf7, 0x01, 0x22, 0x1b, 0x2f, 0x76, 0x32, 0x2f,
	0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x2a, 0x2f, 0x2a, 0x7d, 0x2f, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x09, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69,
	0x6f, 0x6e, 0x5a, 0x2f, 0x22, 0x22, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e,
	0x74, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x09, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73,
	0x69, 0x6f, 0x6e, 0x5a, 0x34, 0x22, 0x27, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x2a, 0x7d, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x09,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x5a, 0x2e, 0x22, 0x21, 0x2f, 0x76, 0x32,
	0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73,
	0x2f, 0x2a, 0x7d, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x09,
	0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x5a, 0x36, 0x22, 0x29, 0x2f, 0x76, 0x32,
	0x2f, 0x7b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x65, 0x78, 0x63, 0x6c,
	0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x09, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f,
	0x6e, 0xda, 0x41, 0x10, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x2c, 0x65, 0x78, 0x63, 0x6c, 0x75,
	0x73, 0x69, 0x6f, 0x6e, 0x12, 0xfb, 0x02, 0x0a, 0x0f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45,
	0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67,
	0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x4c, 0x6f, 0x67, 0x45, 0x78, 0x63, 0x6c, 0x75,
	0x73, 0x69, 0x6f, 0x6e, 0x22, 0x9b, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xf7, 0x01, 0x32, 0x1b,
	0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x2a, 0x2f, 0x2a, 0x2f, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x09, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x5a, 0x2f, 0x32, 0x22, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x65,
	0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x09, 0x65, 0x78,
	0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x5a, 0x34, 0x32, 0x27, 0x2f, 0x76, 0x32, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x2a, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x2a, 0x7d, 0x3a, 0x09, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x5a, 0x2e, 0x32,
	0x21, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65,
	0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x2a, 0x7d, 0x3a, 0x09, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x5a, 0x36, 0x32,
	0x29, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69,
	0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x3a, 0x09, 0x65, 0x78, 0x63, 0x6c,
	0x75, 0x73, 0x69, 0x6f, 0x6e, 0xda, 0x41, 0x1a, 0x6e, 0x61, 0x6d, 0x65, 0x2c, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x2c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61,
	0x73, 0x6b, 0x12, 0xa5, 0x02, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x78, 0x63,
	0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x45, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xce, 0x01, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0xc0, 0x01, 0x2a, 0x1b, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x2a,
	0x2f, 0x2a, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d,
	0x5a, 0x24, 0x2a, 0x22, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72,
	0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x29, 0x2a, 0x27, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x2a, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a,
	0x7d, 0x5a, 0x23, 0x2a, 0x21, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66,
	0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x5a, 0x2b, 0x2a, 0x29, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e,
	0x61, 0x6d, 0x65, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x2f, 0x65, 0x78, 0x63, 0x6c, 0x75, 0x73, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x2a, 0x7d, 0xda, 0x41, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0xa7, 0x02, 0x0a, 0x0f, 0x47,
	0x65, 0x74, 0x43, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x29,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x32, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6d,
	0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0xc7, 0x01, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0xc0, 0x01, 0x12, 0x1b, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d,
	0x2a, 0x2f, 0x2a, 0x7d, 0x2f, 0x63, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x5a, 0x24, 0x12, 0x22, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x63, 0x6d, 0x65, 0x6b, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5a, 0x29, 0x12, 0x27, 0x2f, 0x76, 0x32, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x63, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x5a, 0x23, 0x12, 0x21, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d,
	0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x63, 0x6d, 0x65, 0x6b, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5a, 0x2b, 0x12, 0x29, 0x2f, 0x76, 0x32, 0x2f, 0x7b,
	0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x63, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x12, 0xd1, 0x01, 0x0a, 0x12, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43,
	0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x2c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6d,
	0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x6c, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x66, 0x32, 0x1b, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x2a, 0x2f,
	0x2a, 0x7d, 0x2f, 0x63, 0x6d, 0x65, 0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x3a,
	0x0d, 0x63, 0x6d, 0x65, 0x6b, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5a, 0x38,
	0x32, 0x27, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x63, 0x6d, 0x65,
	0x6b, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x3a, 0x0d, 0x63, 0x6d, 0x65, 0x6b, 0x5f,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x8e, 0x02, 0x0a, 0x0b, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x47, 0x65, 0x74,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0xba, 0x01, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0xac, 0x01, 0x12, 0x17, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d,
	0x65, 0x3d, 0x2a, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5a,
	0x20, 0x12, 0x1e, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x5a, 0x25, 0x12, 0x23, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5a, 0x1f, 0x12, 0x1d, 0x2f, 0x76, 0x32, 0x2f,
	0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d,
	0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5a, 0x27, 0x12, 0x25, 0x2f, 0x76, 0x32,
	0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0xda, 0x41, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0xf6, 0x01, 0x0a, 0x0e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x28, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x22, 0x9c, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7f, 0x32, 0x17, 0x2f, 0x76,
	0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x2a, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x3a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x5a,
	0x2f, 0x32, 0x23, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x6f, 0x72, 0x67,
	0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x3a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x5a, 0x29, 0x32, 0x1d, 0x2f, 0x76, 0x32, 0x2f, 0x7b, 0x6e, 0x61, 0x6d, 0x65, 0x3d, 0x66, 0x6f,
	0x6c, 0x64, 0x65, 0x72, 0x73, 0x2f, 0x2a, 0x7d, 0x2f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x3a, 0x08, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0xda, 0x41, 0x14, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61,
	0x73, 0x6b, 0x12, 0xa9, 0x01, 0x0a, 0x0e, 0x43, 0x6f, 0x70, 0x79, 0x4c, 0x6f, 0x67, 0x45, 0x6e,
	0x74, 0x72, 0x69, 0x65, 0x73, 0x12, 0x28, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c,
	0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x2e, 0x43, 0x6f, 0x70, 0x79, 0x4c, 0x6f,
	0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x6c, 0x6f, 0x6e, 0x67, 0x72, 0x75, 0x6e,
	0x6e, 0x69, 0x6e, 0x67, 0x2e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x4e,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x15, 0x22, 0x10, 0x2f, 0x76, 0x32, 0x2f, 0x65, 0x6e, 0x74, 0x72,
	0x69, 0x65, 0x73, 0x3a, 0x63, 0x6f, 0x70, 0x79, 0x3a, 0x01, 0x2a, 0xca, 0x41, 0x30, 0x0a, 0x16,
	0x43, 0x6f, 0x70, 0x79, 0x4c, 0x6f, 0x67, 0x45, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x43, 0x6f, 0x70, 0x79, 0x4c, 0x6f, 0x67, 0x45,
	0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74, 0x61, 0x1a, 0xdf,
	0x01, 0xca, 0x41, 0x16, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0xd2, 0x41, 0xc2, 0x01, 0x68, 0x74,
	0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2c, 0x68, 0x74, 0x74,
	0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x2d, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x2e, 0x72, 0x65, 0x61, 0x64,
	0x2d, 0x6f, 0x6e, 0x6c, 0x79, 0x2c, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77,
	0x77, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x61, 0x75, 0x74, 0x68, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x2c, 0x68, 0x74, 0x74, 0x70, 0x73, 0x3a, 0x2f, 0x2f, 0x77, 0x77, 0x77, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x61,
	0x75, 0x74, 0x68, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x72, 0x65, 0x61, 0x64,
	0x42, 0xd9, 0x03, 0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x32, 0x42, 0x12, 0x4c, 0x6f, 0x67, 0x67,
	0x69, 0x6e, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01,
	0x5a, 0x38, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e,
	0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2f,
	0x76, 0x32, 0x3b, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0xf8, 0x01, 0x01, 0xaa, 0x02, 0x17,
	0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x2e, 0x4c, 0x6f, 0x67,
	0x67, 0x69, 0x6e, 0x67, 0x2e, 0x56, 0x32, 0xca, 0x02, 0x17, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x5c, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x5c, 0x4c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x5c, 0x56,
	0x32, 0xea, 0x02, 0x1a, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x3a, 0x3a, 0x43, 0x6c, 0x6f, 0x75,
	0x64, 0x3a, 0x3a, 0x4c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x3a, 0x3a, 0x56, 0x32, 0xea, 0x41,
	0x60, 0x0a, 0x2b, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69,
	0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x31,
	0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6f,
	0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x2f, 0x6c, 0x6f, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x7d, 0xea, 0x41, 0x4e, 0x0a, 0x25, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x46, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25, 0x66, 0x6f, 0x6c,
	0x64, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x66, 0x6f, 0x6c, 0x64, 0x65, 0x72, 0x7d, 0x2f, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x7d, 0xea, 0x41, 0x67, 0x0a, 0x2d, 0x6c, 0x6f, 0x67, 0x67, 0x69, 0x6e, 0x67, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x42, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x73, 0x2f, 0x7b, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x7d, 0x2f, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x7d, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_logging_v2_logging_config_proto_rawDescOnce sync.Once
	file_google_logging_v2_logging_config_proto_rawDescData = file_google_logging_v2_logging_config_proto_rawDesc
)

func file_google_logging_v2_logging_config_proto_rawDescGZIP() []byte {
	file_google_logging_v2_logging_config_proto_rawDescOnce.Do(func() {
		file_google_logging_v2_logging_config_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_logging_v2_logging_config_proto_rawDescData)
	})
	return file_google_logging_v2_logging_config_proto_rawDescData
}

var file_google_logging_v2_logging_config_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_google_logging_v2_logging_config_proto_msgTypes = make([]protoimpl.MessageInfo, 39)
var file_google_logging_v2_logging_config_proto_goTypes = []interface{}{
	(LifecycleState)(0),               // 0: google.logging.v2.LifecycleState
	(OperationState)(0),               // 1: google.logging.v2.OperationState
	(LogSink_VersionFormat)(0),        // 2: google.logging.v2.LogSink.VersionFormat
	(*LogBucket)(nil),                 // 3: google.logging.v2.LogBucket
	(*LogView)(nil),                   // 4: google.logging.v2.LogView
	(*LogSink)(nil),                   // 5: google.logging.v2.LogSink
	(*BigQueryOptions)(nil),           // 6: google.logging.v2.BigQueryOptions
	(*ListBucketsRequest)(nil),        // 7: google.logging.v2.ListBucketsRequest
	(*ListBucketsResponse)(nil),       // 8: google.logging.v2.ListBucketsResponse
	(*CreateBucketRequest)(nil),       // 9: google.logging.v2.CreateBucketRequest
	(*UpdateBucketRequest)(nil),       // 10: google.logging.v2.UpdateBucketRequest
	(*GetBucketRequest)(nil),          // 11: google.logging.v2.GetBucketRequest
	(*DeleteBucketRequest)(nil),       // 12: google.logging.v2.DeleteBucketRequest
	(*UndeleteBucketRequest)(nil),     // 13: google.logging.v2.UndeleteBucketRequest
	(*ListViewsRequest)(nil),          // 14: google.logging.v2.ListViewsRequest
	(*ListViewsResponse)(nil),         // 15: google.logging.v2.ListViewsResponse
	(*CreateViewRequest)(nil),         // 16: google.logging.v2.CreateViewRequest
	(*UpdateViewRequest)(nil),         // 17: google.logging.v2.UpdateViewRequest
	(*GetViewRequest)(nil),            // 18: google.logging.v2.GetViewRequest
	(*DeleteViewRequest)(nil),         // 19: google.logging.v2.DeleteViewRequest
	(*ListSinksRequest)(nil),          // 20: google.logging.v2.ListSinksRequest
	(*ListSinksResponse)(nil),         // 21: google.logging.v2.ListSinksResponse
	(*GetSinkRequest)(nil),            // 22: google.logging.v2.GetSinkRequest
	(*CreateSinkRequest)(nil),         // 23: google.logging.v2.CreateSinkRequest
	(*UpdateSinkRequest)(nil),         // 24: google.logging.v2.UpdateSinkRequest
	(*DeleteSinkRequest)(nil),         // 25: google.logging.v2.DeleteSinkRequest
	(*LogExclusion)(nil),              // 26: google.logging.v2.LogExclusion
	(*ListExclusionsRequest)(nil),     // 27: google.logging.v2.ListExclusionsRequest
	(*ListExclusionsResponse)(nil),    // 28: google.logging.v2.ListExclusionsResponse
	(*GetExclusionRequest)(nil),       // 29: google.logging.v2.GetExclusionRequest
	(*CreateExclusionRequest)(nil),    // 30: google.logging.v2.CreateExclusionRequest
	(*UpdateExclusionRequest)(nil),    // 31: google.logging.v2.UpdateExclusionRequest
	(*DeleteExclusionRequest)(nil),    // 32: google.logging.v2.DeleteExclusionRequest
	(*GetCmekSettingsRequest)(nil),    // 33: google.logging.v2.GetCmekSettingsRequest
	(*UpdateCmekSettingsRequest)(nil), // 34: google.logging.v2.UpdateCmekSettingsRequest
	(*CmekSettings)(nil),              // 35: google.logging.v2.CmekSettings
	(*GetSettingsRequest)(nil),        // 36: google.logging.v2.GetSettingsRequest
	(*UpdateSettingsRequest)(nil),     // 37: google.logging.v2.UpdateSettingsRequest
	(*Settings)(nil),                  // 38: google.logging.v2.Settings
	(*CopyLogEntriesRequest)(nil),     // 39: google.logging.v2.CopyLogEntriesRequest
	(*CopyLogEntriesMetadata)(nil),    // 40: google.logging.v2.CopyLogEntriesMetadata
	(*CopyLogEntriesResponse)(nil),    // 41: google.logging.v2.CopyLogEntriesResponse
	(*timestamppb.Timestamp)(nil),     // 42: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),     // 43: google.protobuf.FieldMask
	(*emptypb.Empty)(nil),             // 44: google.protobuf.Empty
	(*longrunning.Operation)(nil),     // 45: google.longrunning.Operation
}
var file_google_logging_v2_logging_config_proto_depIdxs = []int32{
	42, // 0: google.logging.v2.LogBucket.create_time:type_name -> google.protobuf.Timestamp
	42, // 1: google.logging.v2.LogBucket.update_time:type_name -> google.protobuf.Timestamp
	0,  // 2: google.logging.v2.LogBucket.lifecycle_state:type_name -> google.logging.v2.LifecycleState
	35, // 3: google.logging.v2.LogBucket.cmek_settings:type_name -> google.logging.v2.CmekSettings
	42, // 4: google.logging.v2.LogView.create_time:type_name -> google.protobuf.Timestamp
	42, // 5: google.logging.v2.LogView.update_time:type_name -> google.protobuf.Timestamp
	26, // 6: google.logging.v2.LogSink.exclusions:type_name -> google.logging.v2.LogExclusion
	2,  // 7: google.logging.v2.LogSink.output_version_format:type_name -> google.logging.v2.LogSink.VersionFormat
	6,  // 8: google.logging.v2.LogSink.bigquery_options:type_name -> google.logging.v2.BigQueryOptions
	42, // 9: google.logging.v2.LogSink.create_time:type_name -> google.protobuf.Timestamp
	42, // 10: google.logging.v2.LogSink.update_time:type_name -> google.protobuf.Timestamp
	3,  // 11: google.logging.v2.ListBucketsResponse.buckets:type_name -> google.logging.v2.LogBucket
	3,  // 12: google.logging.v2.CreateBucketRequest.bucket:type_name -> google.logging.v2.LogBucket
	3,  // 13: google.logging.v2.UpdateBucketRequest.bucket:type_name -> google.logging.v2.LogBucket
	43, // 14: google.logging.v2.UpdateBucketRequest.update_mask:type_name -> google.protobuf.FieldMask
	4,  // 15: google.logging.v2.ListViewsResponse.views:type_name -> google.logging.v2.LogView
	4,  // 16: google.logging.v2.CreateViewRequest.view:type_name -> google.logging.v2.LogView
	4,  // 17: google.logging.v2.UpdateViewRequest.view:type_name -> google.logging.v2.LogView
	43, // 18: google.logging.v2.UpdateViewRequest.update_mask:type_name -> google.protobuf.FieldMask
	5,  // 19: google.logging.v2.ListSinksResponse.sinks:type_name -> google.logging.v2.LogSink
	5,  // 20: google.logging.v2.CreateSinkRequest.sink:type_name -> google.logging.v2.LogSink
	5,  // 21: google.logging.v2.UpdateSinkRequest.sink:type_name -> google.logging.v2.LogSink
	43, // 22: google.logging.v2.UpdateSinkRequest.update_mask:type_name -> google.protobuf.FieldMask
	42, // 23: google.logging.v2.LogExclusion.create_time:type_name -> google.protobuf.Timestamp
	42, // 24: google.logging.v2.LogExclusion.update_time:type_name -> google.protobuf.Timestamp
	26, // 25: google.logging.v2.ListExclusionsResponse.exclusions:type_name -> google.logging.v2.LogExclusion
	26, // 26: google.logging.v2.CreateExclusionRequest.exclusion:type_name -> google.logging.v2.LogExclusion
	26, // 27: google.logging.v2.UpdateExclusionRequest.exclusion:type_name -> google.logging.v2.LogExclusion
	43, // 28: google.logging.v2.UpdateExclusionRequest.update_mask:type_name -> google.protobuf.FieldMask
	35, // 29: google.logging.v2.UpdateCmekSettingsRequest.cmek_settings:type_name -> google.logging.v2.CmekSettings
	43, // 30: google.logging.v2.UpdateCmekSettingsRequest.update_mask:type_name -> google.protobuf.FieldMask
	38, // 31: google.logging.v2.UpdateSettingsRequest.settings:type_name -> google.logging.v2.Settings
	43, // 32: google.logging.v2.UpdateSettingsRequest.update_mask:type_name -> google.protobuf.FieldMask
	42, // 33: google.logging.v2.CopyLogEntriesMetadata.start_time:type_name -> google.protobuf.Timestamp
	42, // 34: google.logging.v2.CopyLogEntriesMetadata.end_time:type_name -> google.protobuf.Timestamp
	1,  // 35: google.logging.v2.CopyLogEntriesMetadata.state:type_name -> google.logging.v2.OperationState
	39, // 36: google.logging.v2.CopyLogEntriesMetadata.request:type_name -> google.logging.v2.CopyLogEntriesRequest
	7,  // 37: google.logging.v2.ConfigServiceV2.ListBuckets:input_type -> google.logging.v2.ListBucketsRequest
	11, // 38: google.logging.v2.ConfigServiceV2.GetBucket:input_type -> google.logging.v2.GetBucketRequest
	9,  // 39: google.logging.v2.ConfigServiceV2.CreateBucket:input_type -> google.logging.v2.CreateBucketRequest
	10, // 40: google.logging.v2.ConfigServiceV2.UpdateBucket:input_type -> google.logging.v2.UpdateBucketRequest
	12, // 41: google.logging.v2.ConfigServiceV2.DeleteBucket:input_type -> google.logging.v2.DeleteBucketRequest
	13, // 42: google.logging.v2.ConfigServiceV2.UndeleteBucket:input_type -> google.logging.v2.UndeleteBucketRequest
	14, // 43: google.logging.v2.ConfigServiceV2.ListViews:input_type -> google.logging.v2.ListViewsRequest
	18, // 44: google.logging.v2.ConfigServiceV2.GetView:input_type -> google.logging.v2.GetViewRequest
	16, // 45: google.logging.v2.ConfigServiceV2.CreateView:input_type -> google.logging.v2.CreateViewRequest
	17, // 46: google.logging.v2.ConfigServiceV2.UpdateView:input_type -> google.logging.v2.UpdateViewRequest
	19, // 47: google.logging.v2.ConfigServiceV2.DeleteView:input_type -> google.logging.v2.DeleteViewRequest
	20, // 48: google.logging.v2.ConfigServiceV2.ListSinks:input_type -> google.logging.v2.ListSinksRequest
	22, // 49: google.logging.v2.ConfigServiceV2.GetSink:input_type -> google.logging.v2.GetSinkRequest
	23, // 50: google.logging.v2.ConfigServiceV2.CreateSink:input_type -> google.logging.v2.CreateSinkRequest
	24, // 51: google.logging.v2.ConfigServiceV2.UpdateSink:input_type -> google.logging.v2.UpdateSinkRequest
	25, // 52: google.logging.v2.ConfigServiceV2.DeleteSink:input_type -> google.logging.v2.DeleteSinkRequest
	27, // 53: google.logging.v2.ConfigServiceV2.ListExclusions:input_type -> google.logging.v2.ListExclusionsRequest
	29, // 54: google.logging.v2.ConfigServiceV2.GetExclusion:input_type -> google.logging.v2.GetExclusionRequest
	30, // 55: google.logging.v2.ConfigServiceV2.CreateExclusion:input_type -> google.logging.v2.CreateExclusionRequest
	31, // 56: google.logging.v2.ConfigServiceV2.UpdateExclusion:input_type -> google.logging.v2.UpdateExclusionRequest
	32, // 57: google.logging.v2.ConfigServiceV2.DeleteExclusion:input_type -> google.logging.v2.DeleteExclusionRequest
	33, // 58: google.logging.v2.ConfigServiceV2.GetCmekSettings:input_type -> google.logging.v2.GetCmekSettingsRequest
	34, // 59: google.logging.v2.ConfigServiceV2.UpdateCmekSettings:input_type -> google.logging.v2.UpdateCmekSettingsRequest
	36, // 60: google.logging.v2.ConfigServiceV2.GetSettings:input_type -> google.logging.v2.GetSettingsRequest
	37, // 61: google.logging.v2.ConfigServiceV2.UpdateSettings:input_type -> google.logging.v2.UpdateSettingsRequest
	39, // 62: google.logging.v2.ConfigServiceV2.CopyLogEntries:input_type -> google.logging.v2.CopyLogEntriesRequest
	8,  // 63: google.logging.v2.ConfigServiceV2.ListBuckets:output_type -> google.logging.v2.ListBucketsResponse
	3,  // 64: google.logging.v2.ConfigServiceV2.GetBucket:output_type -> google.logging.v2.LogBucket
	3,  // 65: google.logging.v2.ConfigServiceV2.CreateBucket:output_type -> google.logging.v2.LogBucket
	3,  // 66: google.logging.v2.ConfigServiceV2.UpdateBucket:output_type -> google.logging.v2.LogBucket
	44, // 67: google.logging.v2.ConfigServiceV2.DeleteBucket:output_type -> google.protobuf.Empty
	44, // 68: google.logging.v2.ConfigServiceV2.UndeleteBucket:output_type -> google.protobuf.Empty
	15, // 69: google.logging.v2.ConfigServiceV2.ListViews:output_type -> google.logging.v2.ListViewsResponse
	4,  // 70: google.logging.v2.ConfigServiceV2.GetView:output_type -> google.logging.v2.LogView
	4,  // 71: google.logging.v2.ConfigServiceV2.CreateView:output_type -> google.logging.v2.LogView
	4,  // 72: google.logging.v2.ConfigServiceV2.UpdateView:output_type -> google.logging.v2.LogView
	44, // 73: google.logging.v2.ConfigServiceV2.DeleteView:output_type -> google.protobuf.Empty
	21, // 74: google.logging.v2.ConfigServiceV2.ListSinks:output_type -> google.logging.v2.ListSinksResponse
	5,  // 75: google.logging.v2.ConfigServiceV2.GetSink:output_type -> google.logging.v2.LogSink
	5,  // 76: google.logging.v2.ConfigServiceV2.CreateSink:output_type -> google.logging.v2.LogSink
	5,  // 77: google.logging.v2.ConfigServiceV2.UpdateSink:output_type -> google.logging.v2.LogSink
	44, // 78: google.logging.v2.ConfigServiceV2.DeleteSink:output_type -> google.protobuf.Empty
	28, // 79: google.logging.v2.ConfigServiceV2.ListExclusions:output_type -> google.logging.v2.ListExclusionsResponse
	26, // 80: google.logging.v2.ConfigServiceV2.GetExclusion:output_type -> google.logging.v2.LogExclusion
	26, // 81: google.logging.v2.ConfigServiceV2.CreateExclusion:output_type -> google.logging.v2.LogExclusion
	26, // 82: google.logging.v2.ConfigServiceV2.UpdateExclusion:output_type -> google.logging.v2.LogExclusion
	44, // 83: google.logging.v2.ConfigServiceV2.DeleteExclusion:output_type -> google.protobuf.Empty
	35, // 84: google.logging.v2.ConfigServiceV2.GetCmekSettings:output_type -> google.logging.v2.CmekSettings
	35, // 85: google.logging.v2.ConfigServiceV2.UpdateCmekSettings:output_type -> google.logging.v2.CmekSettings
	38, // 86: google.logging.v2.ConfigServiceV2.GetSettings:output_type -> google.logging.v2.Settings
	38, // 87: google.logging.v2.ConfigServiceV2.UpdateSettings:output_type -> google.logging.v2.Settings
	45, // 88: google.logging.v2.ConfigServiceV2.CopyLogEntries:output_type -> google.longrunning.Operation
	63, // [63:89] is the sub-list for method output_type
	37, // [37:63] is the sub-list for method input_type
	37, // [37:37] is the sub-list for extension type_name
	37, // [37:37] is the sub-list for extension extendee
	0,  // [0:37] is the sub-list for field type_name
}

func init() { file_google_logging_v2_logging_config_proto_init() }
func file_google_logging_v2_logging_config_proto_init() {
	if File_google_logging_v2_logging_config_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_logging_v2_logging_config_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogBucket); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogView); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogSink); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BigQueryOptions); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBucketsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListBucketsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateBucketRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateBucketRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetBucketRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteBucketRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UndeleteBucketRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListViewsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListViewsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateViewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateViewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetViewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteViewRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSinksRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSinksResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSinkRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LogExclusion); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExclusionsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListExclusionsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetExclusionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateExclusionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateExclusionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteExclusionRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetCmekSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateCmekSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CmekSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSettingsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Settings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyLogEntriesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyLogEntriesMetadata); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_logging_v2_logging_config_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CopyLogEntriesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_logging_v2_logging_config_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*LogSink_BigqueryOptions)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_logging_v2_logging_config_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   39,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_google_logging_v2_logging_config_proto_goTypes,
		DependencyIndexes: file_google_logging_v2_logging_config_proto_depIdxs,
		EnumInfos:         file_google_logging_v2_logging_config_proto_enumTypes,
		MessageInfos:      file_google_logging_v2_logging_config_proto_msgTypes,
	}.Build()
	File_google_logging_v2_logging_config_proto = out.File
	file_google_logging_v2_logging_config_proto_rawDesc = nil
	file_google_logging_v2_logging_config_proto_goTypes = nil
	file_google_logging_v2_logging_config_proto_depIdxs = nil
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConnInterface

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion6

// ConfigServiceV2Client is the client API for ConfigServiceV2 service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type ConfigServiceV2Client interface {
	// Lists log buckets.
	ListBuckets(ctx context.Context, in *ListBucketsRequest, opts ...grpc.CallOption) (*ListBucketsResponse, error)
	// Gets a log bucket.
	GetBucket(ctx context.Context, in *GetBucketRequest, opts ...grpc.CallOption) (*LogBucket, error)
	// Creates a log bucket that can be used to store log entries. After a bucket
	// has been created, the bucket's location cannot be changed.
	CreateBucket(ctx context.Context, in *CreateBucketRequest, opts ...grpc.CallOption) (*LogBucket, error)
	// Updates a log bucket. This method replaces the following fields in the
	// existing bucket with values from the new bucket: `retention_period`
	//
	// If the retention period is decreased and the bucket is locked,
	// `FAILED_PRECONDITION` will be returned.
	//
	// If the bucket has a `lifecycle_state` of `DELETE_REQUESTED`, then
	// `FAILED_PRECONDITION` will be returned.
	//
	// After a bucket has been created, the bucket's location cannot be changed.
	UpdateBucket(ctx context.Context, in *UpdateBucketRequest, opts ...grpc.CallOption) (*LogBucket, error)
	// Deletes a log bucket.
	//
	// Changes the bucket's `lifecycle_state` to the `DELETE_REQUESTED` state.
	// After 7 days, the bucket will be purged and all log entries in the bucket
	// will be permanently deleted.
	DeleteBucket(ctx context.Context, in *DeleteBucketRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Undeletes a log bucket. A bucket that has been deleted can be undeleted
	// within the grace period of 7 days.
	UndeleteBucket(ctx context.Context, in *UndeleteBucketRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Lists views on a log bucket.
	ListViews(ctx context.Context, in *ListViewsRequest, opts ...grpc.CallOption) (*ListViewsResponse, error)
	// Gets a view on a log bucket..
	GetView(ctx context.Context, in *GetViewRequest, opts ...grpc.CallOption) (*LogView, error)
	// Creates a view over log entries in a log bucket. A bucket may contain a
	// maximum of 30 views.
	CreateView(ctx context.Context, in *CreateViewRequest, opts ...grpc.CallOption) (*LogView, error)
	// Updates a view on a log bucket. This method replaces the following fields
	// in the existing view with values from the new view: `filter`.
	// If an `UNAVAILABLE` error is returned, this indicates that system is not in
	// a state where it can update the view. If this occurs, please try again in a
	// few minutes.
	UpdateView(ctx context.Context, in *UpdateViewRequest, opts ...grpc.CallOption) (*LogView, error)
	// Deletes a view on a log bucket.
	// If an `UNAVAILABLE` error is returned, this indicates that system is not in
	// a state where it can delete the view. If this occurs, please try again in a
	// few minutes.
	DeleteView(ctx context.Context, in *DeleteViewRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Lists sinks.
	ListSinks(ctx context.Context, in *ListSinksRequest, opts ...grpc.CallOption) (*ListSinksResponse, error)
	// Gets a sink.
	GetSink(ctx context.Context, in *GetSinkRequest, opts ...grpc.CallOption) (*LogSink, error)
	// Creates a sink that exports specified log entries to a destination. The
	// export of newly-ingested log entries begins immediately, unless the sink's
	// `writer_identity` is not permitted to write to the destination. A sink can
	// export log entries only from the resource owning the sink.
	CreateSink(ctx context.Context, in *CreateSinkRequest, opts ...grpc.CallOption) (*LogSink, error)
	// Updates a sink. This method replaces the following fields in the existing
	// sink with values from the new sink: `destination`, and `filter`.
	//
	// The updated sink might also have a new `writer_identity`; see the
	// `unique_writer_identity` field.
	UpdateSink(ctx context.Context, in *UpdateSinkRequest, opts ...grpc.CallOption) (*LogSink, error)
	// Deletes a sink. If the sink has a unique `writer_identity`, then that
	// service account is also deleted.
	DeleteSink(ctx context.Context, in *DeleteSinkRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Lists all the exclusions on the _Default sink in a parent resource.
	ListExclusions(ctx context.Context, in *ListExclusionsRequest, opts ...grpc.CallOption) (*ListExclusionsResponse, error)
	// Gets the description of an exclusion in the _Default sink.
	GetExclusion(ctx context.Context, in *GetExclusionRequest, opts ...grpc.CallOption) (*LogExclusion, error)
	// Creates a new exclusion in the _Default sink in a specified parent
	// resource. Only log entries belonging to that resource can be excluded. You
	// can have up to 10 exclusions in a resource.
	CreateExclusion(ctx context.Context, in *CreateExclusionRequest, opts ...grpc.CallOption) (*LogExclusion, error)
	// Changes one or more properties of an existing exclusion in the _Default
	// sink.
	UpdateExclusion(ctx context.Context, in *UpdateExclusionRequest, opts ...grpc.CallOption) (*LogExclusion, error)
	// Deletes an exclusion in the _Default sink.
	DeleteExclusion(ctx context.Context, in *DeleteExclusionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Gets the Logging CMEK settings for the given resource.
	//
	// Note: CMEK for the Log Router can be configured for Google Cloud projects,
	// folders, organizations and billing accounts. Once configured for an
	// organization, it applies to all projects and folders in the Google Cloud
	// organization.
	//
	// See [Enabling CMEK for Log
	// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
	// for more information.
	GetCmekSettings(ctx context.Context, in *GetCmekSettingsRequest, opts ...grpc.CallOption) (*CmekSettings, error)
	// Updates the Log Router CMEK settings for the given resource.
	//
	// Note: CMEK for the Log Router can currently only be configured for Google
	// Cloud organizations. Once configured, it applies to all projects and
	// folders in the Google Cloud organization.
	//
	// [UpdateCmekSettings][google.logging.v2.ConfigServiceV2.UpdateCmekSettings]
	// will fail if 1) `kms_key_name` is invalid, or 2) the associated service
	// account does not have the required
	// `roles/cloudkms.cryptoKeyEncrypterDecrypter` role assigned for the key, or
	// 3) access to the key is disabled.
	//
	// See [Enabling CMEK for Log
	// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
	// for more information.
	UpdateCmekSettings(ctx context.Context, in *UpdateCmekSettingsRequest, opts ...grpc.CallOption) (*CmekSettings, error)
	// Gets the Log Router settings for the given resource.
	//
	// Note: Settings for the Log Router can be get for Google Cloud projects,
	// folders, organizations and billing accounts. Currently it can only be
	// configured for organizations. Once configured for an organization, it
	// applies to all projects and folders in the Google Cloud organization.
	//
	// See [Enabling CMEK for Log
	// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
	// for more information.
	GetSettings(ctx context.Context, in *GetSettingsRequest, opts ...grpc.CallOption) (*Settings, error)
	// Updates the Log Router settings for the given resource.
	//
	// Note: Settings for the Log Router can currently only be configured for
	// Google Cloud organizations. Once configured, it applies to all projects and
	// folders in the Google Cloud organization.
	//
	// [UpdateSettings][google.logging.v2.ConfigServiceV2.UpdateSettings]
	// will fail if 1) `kms_key_name` is invalid, or 2) the associated service
	// account does not have the required
	// `roles/cloudkms.cryptoKeyEncrypterDecrypter` role assigned for the key, or
	// 3) access to the key is disabled. 4) `location_id` is not supported by
	// Logging. 5) `location_id` violate OrgPolicy.
	//
	// See [Enabling CMEK for Log
	// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
	// for more information.
	UpdateSettings(ctx context.Context, in *UpdateSettingsRequest, opts ...grpc.CallOption) (*Settings, error)
	// Copies a set of log entries from a log bucket to a Cloud Storage bucket.
	CopyLogEntries(ctx context.Context, in *CopyLogEntriesRequest, opts ...grpc.CallOption) (*longrunning.Operation, error)
}

type configServiceV2Client struct {
	cc grpc.ClientConnInterface
}

func NewConfigServiceV2Client(cc grpc.ClientConnInterface) ConfigServiceV2Client {
	return &configServiceV2Client{cc}
}

func (c *configServiceV2Client) ListBuckets(ctx context.Context, in *ListBucketsRequest, opts ...grpc.CallOption) (*ListBucketsResponse, error) {
	out := new(ListBucketsResponse)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/ListBuckets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) GetBucket(ctx context.Context, in *GetBucketRequest, opts ...grpc.CallOption) (*LogBucket, error) {
	out := new(LogBucket)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/GetBucket", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) CreateBucket(ctx context.Context, in *CreateBucketRequest, opts ...grpc.CallOption) (*LogBucket, error) {
	out := new(LogBucket)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/CreateBucket", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) UpdateBucket(ctx context.Context, in *UpdateBucketRequest, opts ...grpc.CallOption) (*LogBucket, error) {
	out := new(LogBucket)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/UpdateBucket", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) DeleteBucket(ctx context.Context, in *DeleteBucketRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/DeleteBucket", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) UndeleteBucket(ctx context.Context, in *UndeleteBucketRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/UndeleteBucket", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) ListViews(ctx context.Context, in *ListViewsRequest, opts ...grpc.CallOption) (*ListViewsResponse, error) {
	out := new(ListViewsResponse)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/ListViews", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) GetView(ctx context.Context, in *GetViewRequest, opts ...grpc.CallOption) (*LogView, error) {
	out := new(LogView)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/GetView", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) CreateView(ctx context.Context, in *CreateViewRequest, opts ...grpc.CallOption) (*LogView, error) {
	out := new(LogView)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/CreateView", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) UpdateView(ctx context.Context, in *UpdateViewRequest, opts ...grpc.CallOption) (*LogView, error) {
	out := new(LogView)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/UpdateView", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) DeleteView(ctx context.Context, in *DeleteViewRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/DeleteView", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) ListSinks(ctx context.Context, in *ListSinksRequest, opts ...grpc.CallOption) (*ListSinksResponse, error) {
	out := new(ListSinksResponse)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/ListSinks", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) GetSink(ctx context.Context, in *GetSinkRequest, opts ...grpc.CallOption) (*LogSink, error) {
	out := new(LogSink)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/GetSink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) CreateSink(ctx context.Context, in *CreateSinkRequest, opts ...grpc.CallOption) (*LogSink, error) {
	out := new(LogSink)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/CreateSink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) UpdateSink(ctx context.Context, in *UpdateSinkRequest, opts ...grpc.CallOption) (*LogSink, error) {
	out := new(LogSink)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/UpdateSink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) DeleteSink(ctx context.Context, in *DeleteSinkRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/DeleteSink", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) ListExclusions(ctx context.Context, in *ListExclusionsRequest, opts ...grpc.CallOption) (*ListExclusionsResponse, error) {
	out := new(ListExclusionsResponse)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/ListExclusions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) GetExclusion(ctx context.Context, in *GetExclusionRequest, opts ...grpc.CallOption) (*LogExclusion, error) {
	out := new(LogExclusion)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/GetExclusion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) CreateExclusion(ctx context.Context, in *CreateExclusionRequest, opts ...grpc.CallOption) (*LogExclusion, error) {
	out := new(LogExclusion)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/CreateExclusion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) UpdateExclusion(ctx context.Context, in *UpdateExclusionRequest, opts ...grpc.CallOption) (*LogExclusion, error) {
	out := new(LogExclusion)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/UpdateExclusion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) DeleteExclusion(ctx context.Context, in *DeleteExclusionRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/DeleteExclusion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) GetCmekSettings(ctx context.Context, in *GetCmekSettingsRequest, opts ...grpc.CallOption) (*CmekSettings, error) {
	out := new(CmekSettings)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/GetCmekSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) UpdateCmekSettings(ctx context.Context, in *UpdateCmekSettingsRequest, opts ...grpc.CallOption) (*CmekSettings, error) {
	out := new(CmekSettings)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/UpdateCmekSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) GetSettings(ctx context.Context, in *GetSettingsRequest, opts ...grpc.CallOption) (*Settings, error) {
	out := new(Settings)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/GetSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) UpdateSettings(ctx context.Context, in *UpdateSettingsRequest, opts ...grpc.CallOption) (*Settings, error) {
	out := new(Settings)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/UpdateSettings", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *configServiceV2Client) CopyLogEntries(ctx context.Context, in *CopyLogEntriesRequest, opts ...grpc.CallOption) (*longrunning.Operation, error) {
	out := new(longrunning.Operation)
	err := c.cc.Invoke(ctx, "/google.logging.v2.ConfigServiceV2/CopyLogEntries", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConfigServiceV2Server is the server API for ConfigServiceV2 service.
type ConfigServiceV2Server interface {
	// Lists log buckets.
	ListBuckets(context.Context, *ListBucketsRequest) (*ListBucketsResponse, error)
	// Gets a log bucket.
	GetBucket(context.Context, *GetBucketRequest) (*LogBucket, error)
	// Creates a log bucket that can be used to store log entries. After a bucket
	// has been created, the bucket's location cannot be changed.
	CreateBucket(context.Context, *CreateBucketRequest) (*LogBucket, error)
	// Updates a log bucket. This method replaces the following fields in the
	// existing bucket with values from the new bucket: `retention_period`
	//
	// If the retention period is decreased and the bucket is locked,
	// `FAILED_PRECONDITION` will be returned.
	//
	// If the bucket has a `lifecycle_state` of `DELETE_REQUESTED`, then
	// `FAILED_PRECONDITION` will be returned.
	//
	// After a bucket has been created, the bucket's location cannot be changed.
	UpdateBucket(context.Context, *UpdateBucketRequest) (*LogBucket, error)
	// Deletes a log bucket.
	//
	// Changes the bucket's `lifecycle_state` to the `DELETE_REQUESTED` state.
	// After 7 days, the bucket will be purged and all log entries in the bucket
	// will be permanently deleted.
	DeleteBucket(context.Context, *DeleteBucketRequest) (*emptypb.Empty, error)
	// Undeletes a log bucket. A bucket that has been deleted can be undeleted
	// within the grace period of 7 days.
	UndeleteBucket(context.Context, *UndeleteBucketRequest) (*emptypb.Empty, error)
	// Lists views on a log bucket.
	ListViews(context.Context, *ListViewsRequest) (*ListViewsResponse, error)
	// Gets a view on a log bucket..
	GetView(context.Context, *GetViewRequest) (*LogView, error)
	// Creates a view over log entries in a log bucket. A bucket may contain a
	// maximum of 30 views.
	CreateView(context.Context, *CreateViewRequest) (*LogView, error)
	// Updates a view on a log bucket. This method replaces the following fields
	// in the existing view with values from the new view: `filter`.
	// If an `UNAVAILABLE` error is returned, this indicates that system is not in
	// a state where it can update the view. If this occurs, please try again in a
	// few minutes.
	UpdateView(context.Context, *UpdateViewRequest) (*LogView, error)
	// Deletes a view on a log bucket.
	// If an `UNAVAILABLE` error is returned, this indicates that system is not in
	// a state where it can delete the view. If this occurs, please try again in a
	// few minutes.
	DeleteView(context.Context, *DeleteViewRequest) (*emptypb.Empty, error)
	// Lists sinks.
	ListSinks(context.Context, *ListSinksRequest) (*ListSinksResponse, error)
	// Gets a sink.
	GetSink(context.Context, *GetSinkRequest) (*LogSink, error)
	// Creates a sink that exports specified log entries to a destination. The
	// export of newly-ingested log entries begins immediately, unless the sink's
	// `writer_identity` is not permitted to write to the destination. A sink can
	// export log entries only from the resource owning the sink.
	CreateSink(context.Context, *CreateSinkRequest) (*LogSink, error)
	// Updates a sink. This method replaces the following fields in the existing
	// sink with values from the new sink: `destination`, and `filter`.
	//
	// The updated sink might also have a new `writer_identity`; see the
	// `unique_writer_identity` field.
	UpdateSink(context.Context, *UpdateSinkRequest) (*LogSink, error)
	// Deletes a sink. If the sink has a unique `writer_identity`, then that
	// service account is also deleted.
	DeleteSink(context.Context, *DeleteSinkRequest) (*emptypb.Empty, error)
	// Lists all the exclusions on the _Default sink in a parent resource.
	ListExclusions(context.Context, *ListExclusionsRequest) (*ListExclusionsResponse, error)
	// Gets the description of an exclusion in the _Default sink.
	GetExclusion(context.Context, *GetExclusionRequest) (*LogExclusion, error)
	// Creates a new exclusion in the _Default sink in a specified parent
	// resource. Only log entries belonging to that resource can be excluded. You
	// can have up to 10 exclusions in a resource.
	CreateExclusion(context.Context, *CreateExclusionRequest) (*LogExclusion, error)
	// Changes one or more properties of an existing exclusion in the _Default
	// sink.
	UpdateExclusion(context.Context, *UpdateExclusionRequest) (*LogExclusion, error)
	// Deletes an exclusion in the _Default sink.
	DeleteExclusion(context.Context, *DeleteExclusionRequest) (*emptypb.Empty, error)
	// Gets the Logging CMEK settings for the given resource.
	//
	// Note: CMEK for the Log Router can be configured for Google Cloud projects,
	// folders, organizations and billing accounts. Once configured for an
	// organization, it applies to all projects and folders in the Google Cloud
	// organization.
	//
	// See [Enabling CMEK for Log
	// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
	// for more information.
	GetCmekSettings(context.Context, *GetCmekSettingsRequest) (*CmekSettings, error)
	// Updates the Log Router CMEK settings for the given resource.
	//
	// Note: CMEK for the Log Router can currently only be configured for Google
	// Cloud organizations. Once configured, it applies to all projects and
	// folders in the Google Cloud organization.
	//
	// [UpdateCmekSettings][google.logging.v2.ConfigServiceV2.UpdateCmekSettings]
	// will fail if 1) `kms_key_name` is invalid, or 2) the associated service
	// account does not have the required
	// `roles/cloudkms.cryptoKeyEncrypterDecrypter` role assigned for the key, or
	// 3) access to the key is disabled.
	//
	// See [Enabling CMEK for Log
	// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
	// for more information.
	UpdateCmekSettings(context.Context, *UpdateCmekSettingsRequest) (*CmekSettings, error)
	// Gets the Log Router settings for the given resource.
	//
	// Note: Settings for the Log Router can be get for Google Cloud projects,
	// folders, organizations and billing accounts. Currently it can only be
	// configured for organizations. Once configured for an organization, it
	// applies to all projects and folders in the Google Cloud organization.
	//
	// See [Enabling CMEK for Log
	// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
	// for more information.
	GetSettings(context.Context, *GetSettingsRequest) (*Settings, error)
	// Updates the Log Router settings for the given resource.
	//
	// Note: Settings for the Log Router can currently only be configured for
	// Google Cloud organizations. Once configured, it applies to all projects and
	// folders in the Google Cloud organization.
	//
	// [UpdateSettings][google.logging.v2.ConfigServiceV2.UpdateSettings]
	// will fail if 1) `kms_key_name` is invalid, or 2) the associated service
	// account does not have the required
	// `roles/cloudkms.cryptoKeyEncrypterDecrypter` role assigned for the key, or
	// 3) access to the key is disabled. 4) `location_id` is not supported by
	// Logging. 5) `location_id` violate OrgPolicy.
	//
	// See [Enabling CMEK for Log
	// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
	// for more information.
	UpdateSettings(context.Context, *UpdateSettingsRequest) (*Settings, error)
	// Copies a set of log entries from a log bucket to a Cloud Storage bucket.
	CopyLogEntries(context.Context, *CopyLogEntriesRequest) (*longrunning.Operation, error)
}

// UnimplementedConfigServiceV2Server can be embedded to have forward compatible implementations.
type UnimplementedConfigServiceV2Server struct {
}

func (*UnimplementedConfigServiceV2Server) ListBuckets(context.Context, *ListBucketsRequest) (*ListBucketsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListBuckets not implemented")
}
func (*UnimplementedConfigServiceV2Server) GetBucket(context.Context, *GetBucketRequest) (*LogBucket, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBucket not implemented")
}
func (*UnimplementedConfigServiceV2Server) CreateBucket(context.Context, *CreateBucketRequest) (*LogBucket, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateBucket not implemented")
}
func (*UnimplementedConfigServiceV2Server) UpdateBucket(context.Context, *UpdateBucketRequest) (*LogBucket, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateBucket not implemented")
}
func (*UnimplementedConfigServiceV2Server) DeleteBucket(context.Context, *DeleteBucketRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteBucket not implemented")
}
func (*UnimplementedConfigServiceV2Server) UndeleteBucket(context.Context, *UndeleteBucketRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UndeleteBucket not implemented")
}
func (*UnimplementedConfigServiceV2Server) ListViews(context.Context, *ListViewsRequest) (*ListViewsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListViews not implemented")
}
func (*UnimplementedConfigServiceV2Server) GetView(context.Context, *GetViewRequest) (*LogView, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetView not implemented")
}
func (*UnimplementedConfigServiceV2Server) CreateView(context.Context, *CreateViewRequest) (*LogView, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateView not implemented")
}
func (*UnimplementedConfigServiceV2Server) UpdateView(context.Context, *UpdateViewRequest) (*LogView, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateView not implemented")
}
func (*UnimplementedConfigServiceV2Server) DeleteView(context.Context, *DeleteViewRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteView not implemented")
}
func (*UnimplementedConfigServiceV2Server) ListSinks(context.Context, *ListSinksRequest) (*ListSinksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSinks not implemented")
}
func (*UnimplementedConfigServiceV2Server) GetSink(context.Context, *GetSinkRequest) (*LogSink, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSink not implemented")
}
func (*UnimplementedConfigServiceV2Server) CreateSink(context.Context, *CreateSinkRequest) (*LogSink, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSink not implemented")
}
func (*UnimplementedConfigServiceV2Server) UpdateSink(context.Context, *UpdateSinkRequest) (*LogSink, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSink not implemented")
}
func (*UnimplementedConfigServiceV2Server) DeleteSink(context.Context, *DeleteSinkRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSink not implemented")
}
func (*UnimplementedConfigServiceV2Server) ListExclusions(context.Context, *ListExclusionsRequest) (*ListExclusionsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListExclusions not implemented")
}
func (*UnimplementedConfigServiceV2Server) GetExclusion(context.Context, *GetExclusionRequest) (*LogExclusion, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetExclusion not implemented")
}
func (*UnimplementedConfigServiceV2Server) CreateExclusion(context.Context, *CreateExclusionRequest) (*LogExclusion, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateExclusion not implemented")
}
func (*UnimplementedConfigServiceV2Server) UpdateExclusion(context.Context, *UpdateExclusionRequest) (*LogExclusion, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateExclusion not implemented")
}
func (*UnimplementedConfigServiceV2Server) DeleteExclusion(context.Context, *DeleteExclusionRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteExclusion not implemented")
}
func (*UnimplementedConfigServiceV2Server) GetCmekSettings(context.Context, *GetCmekSettingsRequest) (*CmekSettings, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCmekSettings not implemented")
}
func (*UnimplementedConfigServiceV2Server) UpdateCmekSettings(context.Context, *UpdateCmekSettingsRequest) (*CmekSettings, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCmekSettings not implemented")
}
func (*UnimplementedConfigServiceV2Server) GetSettings(context.Context, *GetSettingsRequest) (*Settings, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSettings not implemented")
}
func (*UnimplementedConfigServiceV2Server) UpdateSettings(context.Context, *UpdateSettingsRequest) (*Settings, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSettings not implemented")
}
func (*UnimplementedConfigServiceV2Server) CopyLogEntries(context.Context, *CopyLogEntriesRequest) (*longrunning.Operation, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CopyLogEntries not implemented")
}

func RegisterConfigServiceV2Server(s *grpc.Server, srv ConfigServiceV2Server) {
	s.RegisterService(&_ConfigServiceV2_serviceDesc, srv)
}

func _ConfigServiceV2_ListBuckets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListBucketsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).ListBuckets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/ListBuckets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).ListBuckets(ctx, req.(*ListBucketsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_GetBucket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBucketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).GetBucket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/GetBucket",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).GetBucket(ctx, req.(*GetBucketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_CreateBucket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateBucketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).CreateBucket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/CreateBucket",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).CreateBucket(ctx, req.(*CreateBucketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_UpdateBucket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateBucketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).UpdateBucket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/UpdateBucket",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).UpdateBucket(ctx, req.(*UpdateBucketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_DeleteBucket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteBucketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).DeleteBucket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/DeleteBucket",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).DeleteBucket(ctx, req.(*DeleteBucketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_UndeleteBucket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UndeleteBucketRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).UndeleteBucket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/UndeleteBucket",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).UndeleteBucket(ctx, req.(*UndeleteBucketRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_ListViews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListViewsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).ListViews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/ListViews",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).ListViews(ctx, req.(*ListViewsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_GetView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetViewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).GetView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/GetView",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).GetView(ctx, req.(*GetViewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_CreateView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateViewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).CreateView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/CreateView",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).CreateView(ctx, req.(*CreateViewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_UpdateView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateViewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).UpdateView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/UpdateView",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).UpdateView(ctx, req.(*UpdateViewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_DeleteView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteViewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).DeleteView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/DeleteView",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).DeleteView(ctx, req.(*DeleteViewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_ListSinks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSinksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).ListSinks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/ListSinks",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).ListSinks(ctx, req.(*ListSinksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_GetSink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).GetSink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/GetSink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).GetSink(ctx, req.(*GetSinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_CreateSink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).CreateSink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/CreateSink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).CreateSink(ctx, req.(*CreateSinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_UpdateSink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).UpdateSink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/UpdateSink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).UpdateSink(ctx, req.(*UpdateSinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_DeleteSink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSinkRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).DeleteSink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/DeleteSink",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).DeleteSink(ctx, req.(*DeleteSinkRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_ListExclusions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListExclusionsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).ListExclusions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/ListExclusions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).ListExclusions(ctx, req.(*ListExclusionsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_GetExclusion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetExclusionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).GetExclusion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/GetExclusion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).GetExclusion(ctx, req.(*GetExclusionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_CreateExclusion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateExclusionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).CreateExclusion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/CreateExclusion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).CreateExclusion(ctx, req.(*CreateExclusionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_UpdateExclusion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateExclusionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).UpdateExclusion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/UpdateExclusion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).UpdateExclusion(ctx, req.(*UpdateExclusionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_DeleteExclusion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteExclusionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).DeleteExclusion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/DeleteExclusion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).DeleteExclusion(ctx, req.(*DeleteExclusionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_GetCmekSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCmekSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).GetCmekSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/GetCmekSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).GetCmekSettings(ctx, req.(*GetCmekSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_UpdateCmekSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCmekSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).UpdateCmekSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/UpdateCmekSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).UpdateCmekSettings(ctx, req.(*UpdateCmekSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_GetSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).GetSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/GetSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).GetSettings(ctx, req.(*GetSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_UpdateSettings_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSettingsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).UpdateSettings(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/UpdateSettings",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).UpdateSettings(ctx, req.(*UpdateSettingsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConfigServiceV2_CopyLogEntries_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CopyLogEntriesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConfigServiceV2Server).CopyLogEntries(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/google.logging.v2.ConfigServiceV2/CopyLogEntries",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConfigServiceV2Server).CopyLogEntries(ctx, req.(*CopyLogEntriesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ConfigServiceV2_serviceDesc = grpc.ServiceDesc{
	ServiceName: "google.logging.v2.ConfigServiceV2",
	HandlerType: (*ConfigServiceV2Server)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListBuckets",
			Handler:    _ConfigServiceV2_ListBuckets_Handler,
		},
		{
			MethodName: "GetBucket",
			Handler:    _ConfigServiceV2_GetBucket_Handler,
		},
		{
			MethodName: "CreateBucket",
			Handler:    _ConfigServiceV2_CreateBucket_Handler,
		},
		{
			MethodName: "UpdateBucket",
			Handler:    _ConfigServiceV2_UpdateBucket_Handler,
		},
		{
			MethodName: "DeleteBucket",
			Handler:    _ConfigServiceV2_DeleteBucket_Handler,
		},
		{
			MethodName: "UndeleteBucket",
			Handler:    _ConfigServiceV2_UndeleteBucket_Handler,
		},
		{
			MethodName: "ListViews",
			Handler:    _ConfigServiceV2_ListViews_Handler,
		},
		{
			MethodName: "GetView",
			Handler:    _ConfigServiceV2_GetView_Handler,
		},
		{
			MethodName: "CreateView",
			Handler:    _ConfigServiceV2_CreateView_Handler,
		},
		{
			MethodName: "UpdateView",
			Handler:    _ConfigServiceV2_UpdateView_Handler,
		},
		{
			MethodName: "DeleteView",
			Handler:    _ConfigServiceV2_DeleteView_Handler,
		},
		{
			MethodName: "ListSinks",
			Handler:    _ConfigServiceV2_ListSinks_Handler,
		},
		{
			MethodName: "GetSink",
			Handler:    _ConfigServiceV2_GetSink_Handler,
		},
		{
			MethodName: "CreateSink",
			Handler:    _ConfigServiceV2_CreateSink_Handler,
		},
		{
			MethodName: "UpdateSink",
			Handler:    _ConfigServiceV2_UpdateSink_Handler,
		},
		{
			MethodName: "DeleteSink",
			Handler:    _ConfigServiceV2_DeleteSink_Handler,
		},
		{
			MethodName: "ListExclusions",
			Handler:    _ConfigServiceV2_ListExclusions_Handler,
		},
		{
			MethodName: "GetExclusion",
			Handler:    _ConfigServiceV2_GetExclusion_Handler,
		},
		{
			MethodName: "CreateExclusion",
			Handler:    _ConfigServiceV2_CreateExclusion_Handler,
		},
		{
			MethodName: "UpdateExclusion",
			Handler:    _ConfigServiceV2_UpdateExclusion_Handler,
		},
		{
			MethodName: "DeleteExclusion",
			Handler:    _ConfigServiceV2_DeleteExclusion_Handler,
		},
		{
			MethodName: "GetCmekSettings",
			Handler:    _ConfigServiceV2_GetCmekSettings_Handler,
		},
		{
			MethodName: "UpdateCmekSettings",
			Handler:    _ConfigServiceV2_UpdateCmekSettings_Handler,
		},
		{
			MethodName: "GetSettings",
			Handler:    _ConfigServiceV2_GetSettings_Handler,
		},
		{
			MethodName: "UpdateSettings",
			Handler:    _ConfigServiceV2_UpdateSettings_Handler,
		},
		{
			MethodName: "CopyLogEntries",
			Handler:    _ConfigServiceV2_CopyLogEntries_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "google/logging/v2/logging_config.proto",
}
