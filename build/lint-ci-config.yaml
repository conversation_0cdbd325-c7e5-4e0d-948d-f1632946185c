linters-settings:
  gocyclo:
    min-complexity: 20
  revive:
    rules:
      - name: exported
        arguments:
          - disableStutteringCheck
  govet:
    settings:
      printf:
        funcs:
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Infof
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Warnf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Errorf
          - (github.com/golangci/golangci-lint/pkg/logutils.Log).Fatalf

linters:
  disable-all: true
  enable:
    - unused        # replaces structcheck, varcheck, and deadcode
    - revive        # replaces golint
    - gosimple
    - govet
    - errcheck
    - staticcheck
    - ineffassign
    - bodyclose
    - whitespace
    - goimports
    - gofmt
    - dogsled
    - goconst
    - gocyclo
    - stylecheck
    - typecheck

  # don't enable:
  # - gochecknoglobals
  # - gocognit
  # - godox
  # - maligned
  # - prealloc
  # - gomnd
  # - lll
  # - misspell
  # - gochecknoinits
  # - funlen
  # - depguard
  # - dupl
  # - goconst
  # - gocritic
  # - goprintffuncname
  # - gosec
  # - ineffassign
  # - interfacer
  # - nakedret
  # - rowserrcheck
  # - scopelint
  # - staticcheck
  # - structcheck
  # - unconvert
  # - unparam

issues:
# Excluding configuration per-path, per-linter, per-text and per-source
  exclude-files:
    - '.*_test\.go$'
    - '.*_mock\.go$'
    - '.*_generated\.go$'
  exclude-rules:
    - path: _test\.go
      linters:
        - gomnd
        - gosec
        - dupl

run:
  timeout: 5m
  skip-dirs:
  #Add any directories you want skipped here
  tests: true

# golangci.com configuration
# https://github.com/golangci/golangci/wiki/Configuration
service:
  golangci-lint-version: 1.64.x # use the fixed version to not introduce new linters unexpectedly