steps:
# Regenerate the postman collection for this service using mscli
- id: 'regen-postman-collection'
  name: 'gcr.io/repcore-prod/postman-collection-gen:latest'
  entrypoint: 'mscli'
  args: ['app', 'sdk', '-l', 'postman']

# Upload the postman collection to the centralized cloud storage bucket.
# You can download postman collections here: 
# https://console.cloud.google.com/storage/browser/vendastaapis-proto2postman
- id: 'upload-postman-collections'
  name: gcr.io/cloud-builders/gsutil
  args: ['cp', './sdks/postman/*', "gs://vendastaapis-proto2postman/"]
  waitFor: ['regen-postman-collection']

tags:
- NotDeployable
- postman-collection
timeout: 1200s
options:
  machineType: N1_HIGHCPU_8
