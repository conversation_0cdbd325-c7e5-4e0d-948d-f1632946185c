// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package navbardata is a generated GoMock package.
package navbardata

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	partner_v1 "github.com/vendasta/generated-protos-go/partner/v1"
	grpc "google.golang.org/grpc"
)

// MockWhitelabelClient is a mock of WhitelabelClient interface.
type MockWhitelabelClient struct {
	ctrl     *gomock.Controller
	recorder *MockWhitelabelClientMockRecorder
}

// MockWhitelabelClientMockRecorder is the mock recorder for MockWhitelabelClient.
type MockWhitelabelClientMockRecorder struct {
	mock *MockWhitelabelClient
}

// NewMockWhitelabelClient creates a new mock instance.
func NewMockWhitelabelClient(ctrl *gomock.Controller) *MockWhitelabelClient {
	mock := &MockWhitelabelClient{ctrl: ctrl}
	mock.recorder = &MockWhitelabelClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockWhitelabelClient) EXPECT() *MockWhitelabelClientMockRecorder {
	return m.recorder
}

// GetBranding mocks base method.
func (m *MockWhitelabelClient) GetBranding(ctx context.Context, in *partner_v1.GetBrandingRequest, opts ...grpc.CallOption) (*partner_v1.GetBrandingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBranding", varargs...)
	ret0, _ := ret[0].(*partner_v1.GetBrandingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBranding indicates an expected call of GetBranding.
func (mr *MockWhitelabelClientMockRecorder) GetBranding(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBranding", reflect.TypeOf((*MockWhitelabelClient)(nil).GetBranding), varargs...)
}

// GetConfiguration mocks base method.
func (m *MockWhitelabelClient) GetConfiguration(ctx context.Context, in *partner_v1.GetConfigurationRequest, opts ...grpc.CallOption) (*partner_v1.GetConfigurationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConfiguration", varargs...)
	ret0, _ := ret[0].(*partner_v1.GetConfigurationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfiguration indicates an expected call of GetConfiguration.
func (mr *MockWhitelabelClientMockRecorder) GetConfiguration(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfiguration", reflect.TypeOf((*MockWhitelabelClient)(nil).GetConfiguration), varargs...)
}
