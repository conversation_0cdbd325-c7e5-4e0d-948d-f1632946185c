package navbardata

import (
	"context"
	iam "github.com/vendasta/IAM/sdks/go/v1"
	"github.com/vendasta/gosdks/verrors"
	"testing"

	iam_attributes "github.com/vendasta/generated-protos-go/iam/attributes"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	iammock "github.com/vendasta/IAM/sdks/go/v1/mocks"
	"github.com/vendasta/IAM/sdks/go/v1/subject"
	"github.com/vendasta/IAM/sdks/go/v1/subjectcontext"
)

type getSubjectStub struct {
	personas []subject.Subject
	cursor   string
	hasMore  bool
	err      error
}

func TestGetSubjects(t *testing.T) {
	partnerStub := &iammock.Subject{}
	partnerStub.On("Context").Return(&subjectcontext.Context{Type: "partner"})
	partnerStub.On("Attributes").Return(map[string]*iam_attributes.Attribute{
		"partner_id": {Kind: &iam_attributes.Attribute_StringAttribute{StringAttribute: "ABC"}},
	})
	superAdminStub := &iammock.Subject{}
	superAdminStub.On("Context").Return(&subjectcontext.Context{Type: "partner"})
	superAdminStub.On("Attributes").Return(map[string]*iam_attributes.Attribute{
		"partner_id":     {Kind: &iam_attributes.Attribute_StringAttribute{StringAttribute: "VA"}},
		"is_super_admin": {Kind: &iam_attributes.Attribute_BoolAttribute{BoolAttribute: true}},
	})
	smbStub := &iammock.Subject{}
	smbStub.On("Context").Return(&subjectcontext.Context{Type: "smb", Namespace: "DEF"})
	smbStub.On("Attributes").Return(map[string]*iam_attributes.Attribute{
		"partner_id": {Kind: &iam_attributes.Attribute_StringAttribute{StringAttribute: "DEF"}},
	})

	developer := &iammock.Subject{}
	developer.On("Context").Return(&subjectcontext.Context{Type: "developer"})
	developer.On("Attributes").Return(map[string]*iam_attributes.Attribute{})

	digitalAgent := &iammock.Subject{}
	digitalAgent.On("Context").Return(&subjectcontext.Context{Type: "digital_agent", Namespace: "DEF"})
	digitalAgent.On("Attributes").Return(map[string]*iam_attributes.Attribute{
		"accessible_partner_ids": {
			Kind: &iam_attributes.Attribute_ListAttribute{
				ListAttribute: &iam_attributes.ListAttribute{
					Attributes: []*iam_attributes.Attribute{
						{Kind: &iam_attributes.Attribute_StringAttribute{StringAttribute: "ABC"}},
						{Kind: &iam_attributes.Attribute_StringAttribute{StringAttribute: "DEF"}},
					},
				},
			},
		},
	})

	cases := []struct {
		name             string
		partnerID        string
		stubData         getSubjectStub
		expectedSubjects map[string]subject.Subject
		expectedError    error
	}{
		{
			name:      "superadmin has access",
			partnerID: "ABC",
			stubData: getSubjectStub{
				personas: []subject.Subject{superAdminStub},
			},
			expectedSubjects: map[string]subject.Subject{
				"partner": superAdminStub,
			},
			expectedError: nil,
		},
		{
			name:      "partner in different pid not in list",
			partnerID: "DEF",
			stubData: getSubjectStub{
				personas: []subject.Subject{partnerStub, smbStub},
			},
			expectedSubjects: map[string]subject.Subject{
				"smb": smbStub,
			},
			expectedError: nil,
		},
		{
			name:      "super admin in list of many personas",
			partnerID: "DEF",
			stubData: getSubjectStub{
				personas: []subject.Subject{partnerStub, superAdminStub, smbStub},
			},
			expectedSubjects: map[string]subject.Subject{
				"smb":     smbStub,
				"partner": superAdminStub,
			},
			expectedError: nil,
		},
		{
			name:      "digital agent has access to associated pid",
			partnerID: "DEF",
			stubData: getSubjectStub{
				personas: []subject.Subject{digitalAgent},
			},
			expectedSubjects: map[string]subject.Subject{
				"digital_agent": digitalAgent,
			},
			expectedError: nil,
		},
		{
			name:      "digital agent does not has access to associated pid",
			partnerID: "XYZ",
			stubData: getSubjectStub{
				personas: []subject.Subject{digitalAgent},
			},
			expectedSubjects: map[string]subject.Subject{},
			expectedError:    nil,
		},
		{
			name:      "developers have access",
			partnerID: "XYZ",
			stubData: getSubjectStub{
				personas: []subject.Subject{developer},
			},
			expectedSubjects: map[string]subject.Subject{
				"developer": developer,
			},
			expectedError: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			mockIAMClient := &iammock.Interface{}
			mockIAMClient.On(
				"ListPersonasByUserID",
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.Anything,
			).Return(
				c.stubData.personas,
				c.stubData.cursor,
				c.stubData.hasMore,
				c.stubData.err,
			)

			svc := &Service{
				iamClient: mockIAMClient,
			}
			actualSubjects, err := svc.getSubjects(context.Background(), "", c.partnerID)
			assert.Equal(t, c.expectedSubjects, actualSubjects)
			assert.Equal(t, c.expectedError, err)
		})
	}
}

func TestGetSubjectsListRespErrGroup(t *testing.T) {
	superAdminStub := &iammock.Subject{}
	superAdminStub.On("Context").Return(&subjectcontext.Context{Type: "partner"})
	superAdminStub.On("Attributes").Return(map[string]*iam_attributes.Attribute{
		"partner_id":     {Kind: &iam_attributes.Attribute_StringAttribute{StringAttribute: "VA"}},
		"is_super_admin": {Kind: &iam_attributes.Attribute_BoolAttribute{BoolAttribute: true}},
	})
	smbStub := &iammock.Subject{}
	smbStub.On("Context").Return(&subjectcontext.Context{Type: "smb", Namespace: "DEF"})
	smbStub.On("Attributes").Return(map[string]*iam_attributes.Attribute{
		"partner_id": {Kind: &iam_attributes.Attribute_StringAttribute{StringAttribute: "DEF"}},
	})

	cases := []struct {
		name             string
		partnerID        string
		superStubData    getSubjectStub
		stubData         getSubjectStub
		expectedSubjects map[string]subject.Subject
		expectedError    error
	}{
		{
			name:      "superadmin has access",
			partnerID: "DEF",
			superStubData: getSubjectStub{
				personas: []subject.Subject{superAdminStub, smbStub},
			},
			stubData: getSubjectStub{
				personas: []subject.Subject{smbStub},
			},
			expectedSubjects: map[string]subject.Subject{
				"partner": superAdminStub,
				"smb":     smbStub,
			},
			expectedError: nil,
		},
		{
			name:      "not a superadmin calling, but has smb in specified pid",
			partnerID: "DEF",
			superStubData: getSubjectStub{
				err: verrors.New(verrors.PermissionDenied, "not a super admin"),
			},
			stubData: getSubjectStub{
				personas: []subject.Subject{smbStub},
			},
			expectedSubjects: map[string]subject.Subject{
				"smb": smbStub,
			},
			expectedError: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			mockIAMClient := &iammock.Interface{}
			mockIAMClient.On(
				"ListPersonasByUserID",
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.MatchedBy(func(option iam.ListPersonasOption) bool {
					conf := &iam.ListPersonasConf{}
					option(conf)
					return conf.AlwaysIncludeSuperadmin == true
				}),
			).Return(
				c.superStubData.personas,
				c.superStubData.cursor,
				c.superStubData.hasMore,
				c.superStubData.err,
			)
			mockIAMClient.On(
				"ListPersonasByUserID",
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.MatchedBy(func(option iam.ListPersonasOption) bool {
					conf := &iam.ListPersonasConf{}
					option(conf)
					return conf.AlwaysIncludeSuperadmin == false
				}),
			).Return(
				c.stubData.personas,
				c.stubData.cursor,
				c.stubData.hasMore,
				c.stubData.err,
			)

			svc := &Service{
				iamClient: mockIAMClient,
			}
			actualSubjects, err := svc.getSubjects(context.Background(), "", c.partnerID)
			assert.Equal(t, c.expectedSubjects, actualSubjects)
			assert.Equal(t, c.expectedError, err)
		})
	}
}
