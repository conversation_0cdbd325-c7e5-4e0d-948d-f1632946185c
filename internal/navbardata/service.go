package navbardata

import (
	"context"
	"fmt"
	"net/url"
	"sort"
	"strings"
	"time"

	"google.golang.org/grpc"

	//businessPartner "github.com/vendasta/partner/sdks/go/v2"

	"github.com/vendasta/IAM/sdks/go/iaminterceptor"
	iam "github.com/vendasta/IAM/sdks/go/v1"
	"github.com/vendasta/IAM/sdks/go/v1/subject"
	"github.com/vendasta/IAM/sdks/go/v1/user"
	iamv2 "github.com/vendasta/IAM/sdks/go/v2"
	"github.com/vendasta/IAM/sdks/go/v2/iamrole"
	"github.com/vendasta/IAM/sdks/go/v2/requests/listusers"
	partnerProto "github.com/vendasta/generated-protos-go/partner/v1"
	"github.com/vendasta/gosdks/config"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/verrors"
	"github.com/vendasta/sso/sdks/go/sso"
	"golang.org/x/sync/errgroup"

	"github.com/vendasta/atlas/internal/branding"
	featureflags "github.com/vendasta/atlas/internal/feature_flags"
	"github.com/vendasta/atlas/internal/lang"
	"github.com/vendasta/atlas/internal/partnermarket"
)

//go:generate mockgen -source=service.go -destination=mocks.go -package=navbardata
type WhitelabelClient interface {
	GetBranding(ctx context.Context, in *partnerProto.GetBrandingRequest, opts ...grpc.CallOption) (*partnerProto.GetBrandingResponse, error)
	GetConfiguration(ctx context.Context, in *partnerProto.GetConfigurationRequest, opts ...grpc.CallOption) (*partnerProto.GetConfigurationResponse, error)
}

// Service contains a bunch of clients
type Service struct {
	iamClient          iam.Interface
	iamV2Client        iamv2.ClientInterface
	ssoClient          sso.IdentityProviderClient
	languages          lang.Getter
	locationDataClient partnermarket.LocationDataFetcher
	partnerClient      WhitelabelClient
	brandingService    branding.ServiceInterface
	signOutURL         url.URL
	centersData        []CenterData
	defaultCenterNames map[string]string
	featureFlagService featureflags.Features
}

// NavbarData holds the list of centers
type NavbarData struct {
	Email                string
	EmailVerified        bool
	Username             string
	ImpersonateeUsername string
	SignOutURL           string
	Theme                branding.Theme
	BusinessAppTheme     branding.Theme
	Centers              []*Center
	UserItems            []*UserItem
	Language             string
	NotificationsEnabled bool
	BusinessName         string
	Address              string
	Country              string
	UserSwitcherData     []*UserSwitcherData
	PartnerName          string
}

// Center is a whitelabelled url and name
type Center struct {
	URL  string
	Name string
	ID   string
}

// UserItem controls which actions show up in the user dropdown
type UserItem struct {
	Text string
	URL  string
}

type UserSwitcherData struct {
	UserID      string
	PartnerID   string
	PartnerName string
	EntryURL    string
}

const (
	businessCenterID              = "VBC"
	partnerCenterID               = "AA"
	salesCenterID                 = "ST"
	salesPersonPartnerCenterFlag  = "teamy_pcc_salesperson_session"
	showSalesAndSuccessCenterFlag = "show_sales_and_success_center"
)

type PersonaConfiguration struct {
	Name             string
	RequiredFeatures []string // optional features required to enable the center for this persona
}

type CenterData struct {
	ID                string
	Personas          []string
	Features          []string
	SscWhitelabeled   bool
	SuperadminEnabled bool
}

func buildCentersData(yeswareID string) []CenterData {
	centersData := []CenterData{
		{
			ID:                partnerCenterID,
			Personas:          []string{"partner", "sales_person"},
			SuperadminEnabled: true,
		},
		{
			ID:                "vendor-center",
			Personas:          []string{"partner", "vendor"},
			Features:          []string{},
			SuperadminEnabled: true,
		},
		{
			ID:       "ST",
			Personas: []string{"sales_person"},
		},
		{
			ID:                "conquer-local-academy",
			Personas:          []string{"partner", "sales_person"},
			SscWhitelabeled:   true,
			SuperadminEnabled: true,
		},
		{
			ID:                yeswareID,
			Personas:          []string{"partner", "sales_person", "digital_agent"},
			Features:          []string{"my-products"},
			SscWhitelabeled:   true,
			SuperadminEnabled: true,
		},
		{
			ID:       businessCenterID,
			Personas: []string{"smb"},
		},
		{
			ID:       "ARM",
			Personas: []string{"digital_agent"},
		},
		{
			ID:       "iam",
			Personas: []string{"developer"},
		},
		{
			ID:       "vstore",
			Personas: []string{"developer"},
		},
		{
			ID:       "mission-control",
			Personas: []string{"developer"},
		},
		{
			ID:       "sre-reporting",
			Personas: []string{"developer", "success"},
		},
		{
			ID:       "office-library",
			Personas: []string{"developer"},
		},
		{
			ID:       "admin-client",
			Personas: []string{"developer"},
		},
		{
			ID:                "billing",
			SuperadminEnabled: true,
		},
		{
			ID:       "web-crawler",
			Personas: []string{"developer"},
		},
	}

	return centersData
}

func buildDefaultCenterNames(yeswareCenterID string) map[string]string {
	defaultCenterNames := map[string]string{
		// Platform Centers
		partnerCenterID:  "Partner Center",
		businessCenterID: "Business App",
		"ST":             "Sales & Success Center",
		"ARM":            "Task Manager",
		"vendor-center":  "Vendor Center",

		// Admin Centers
		"iam":             "IAM",
		"vstore":          "VStore",
		"mission-control": "Mission Control",
		"sre-reporting":   "SRE Reporting",
		"billing":         "Billing Center",
		"office-library":  "Office Library",
		"admin-client":    "Admin Center",
		"web-crawler":     "Web Crawler",

		// Conquer Local Centers
		"conquer-local-academy": "Academy",

		yeswareCenterID: "Yesware",
	}

	return defaultCenterNames
}

var logoutRedirectURLs = map[config.Env]string{
	config.Prod: "https://login-prod.apigateway.co/logout-redirect",
	config.Demo: "https://login-demo.apigateway.co/logout-redirect",
}

// NewService makes a new instance of the Client
func NewService(
	env config.Env,
	iamClient iam.Interface,
	ssoClient sso.IdentityProviderClient,
	partnerClient WhitelabelClient,
	brandingService branding.ServiceInterface,
	locationDataClient partnermarket.LocationDataFetcher,
	langFetcher lang.Getter, yeswareCenterID string,
	featureFlagService featureflags.Features,
	iamV2Client iamv2.ClientInterface,
) (*Service, error) {
	signoutURL, err := url.Parse(logoutRedirectURLs[env])
	if err != nil {
		return nil, err
	}
	centersData := buildCentersData(yeswareCenterID)
	defaultCenterNames := buildDefaultCenterNames(yeswareCenterID)
	return &Service{
		iamClient:          iamClient,
		iamV2Client:        iamV2Client,
		ssoClient:          ssoClient,
		partnerClient:      partnerClient,
		brandingService:    brandingService,
		signOutURL:         *signoutURL,
		languages:          langFetcher,
		locationDataClient: locationDataClient,
		centersData:        centersData,
		defaultCenterNames: defaultCenterNames,
		featureFlagService: featureFlagService,
	}, nil
}

// GetNavbarData gets urls and white-labelled names for accessible centers
// nolint:gocyclo
func (s *Service) GetNavbarData(ctx context.Context, partnerID, marketID, accountGroupID, groupPath, signOutNextURL, userID, impersonatee, serviceProviderID string) (*NavbarData, error) {
	var businessName, address, country string
	if accountGroupID != "" || groupPath != "" {
		locationData, err := s.locationDataClient.GetLocationData(ctx, accountGroupID, groupPath)
		if err != nil {
			return nil, verrors.WrapError(err, "error getting location data").WithSourceLocation()
		}
		partnerID = locationData.PartnerID
		marketID = locationData.MarketID
		businessName = locationData.BusinessName
		address = locationData.Address
		country = locationData.Country
	}

	newCtx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()
	eGroup, egCtx := errgroup.WithContext(newCtx)

	var err error
	var subjectMap map[string]subject.Subject
	var accessibleCenters []string
	var names, urls []string
	var theme branding.Theme
	var businessAppTheme branding.Theme
	var language string
	var u *user.User
	var impersonateeUser *user.User
	var userSwitcherData []*UserSwitcherData
	var partnerName string

	if userID != "" {
		eGroup.Go(func() (err error) {
			u, err = s.iamClient.GetUser(egCtx, iam.UserID(userID))
			if err != nil {
				return verrors.WrapError(err, "error getting user").WithSourceLocation()
			}

			userSwitcherData, err = s.buildUserSwitcherData(egCtx, u, serviceProviderID)
			if err != nil {
				return verrors.WrapError(err, "error getting user switcher data").WithSourceLocation()
			}
			return nil
		})
		eGroup.Go(func() (err error) {
			language, err = s.languages.GetLanguageForUser(egCtx, userID)
			if err != nil {
				return verrors.WrapError(err, "error getting language for user").WithSourceLocation()
			}
			return nil
		})

		// get subjects contains a nested error group
		// start non-blocking rpc calls above this in one of the two error groups
		subjectMap, err = s.getSubjects(ctx, userID, partnerID)
		if err != nil {
			return nil, verrors.WrapError(err, "error getting subjects").WithSourceLocation()
		}

		accessibleCenters, err = s.getAccessibleCenters(newCtx, partnerID, marketID, subjectMap, accountGroupID)
		if err != nil {
			return nil, verrors.WrapError(err, "error getting accessible centers").WithSourceLocation()
		}

		if len(accessibleCenters) > 0 {
			eGroup.Go(func() (err error) {
				var serviceContext *sso.PartnerContext
				if partnerID != "" {
					serviceContext = &sso.PartnerContext{PartnerID: partnerID}
				}
				urls, err = s.ssoClient.GetMultiEntryURL(egCtx, accessibleCenters, serviceContext)
				if err != nil {
					return verrors.WrapError(err, "error getting entry urls").WithSourceLocation()
				}
				return nil
			})
		}
	}

	if impersonatee != "" {
		eGroup.Go(func() (err error) {
			impersonateeUser, err = s.iamClient.GetUser(egCtx, iam.UserID(impersonatee))
			if err != nil {
				return verrors.WrapError(err, "error getting impersonatee user").WithSourceLocation()
			}
			return nil
		})
	}

	eGroup.Go(func() (err error) {
		var b *partnerProto.BrandingV2
		if partnerID != "" {
			b, err = s.brandingService.GetBranding(egCtx, partnerID, marketID)
			if err != nil {
				return verrors.WrapError(err, "error getting branding").WithSourceLocation()
			}
		}
		if b != nil {
			theme = branding.PartnerProtoToTheme(b.UiTheme)
			businessAppTheme = branding.PartnerProtoToTheme(b.BusinessAppUiTheme)
			partnerName = b.GetName()
		} else {
			theme = branding.DarkTheme
			businessAppTheme = branding.LightTheme
		}

		return nil
	})

	eGroup.Go(func() (err error) {
		var brandingData *partnerProto.GetBrandingResponse
		if partnerID != "" {
			brandingData, err = s.partnerClient.GetBranding(egCtx, &partnerProto.GetBrandingRequest{
				PartnerId: partnerID,
				MarketId:  marketID,
			})
			if err != nil {
				return verrors.WrapError(err, "error getting partner branding data").WithSourceLocation()
			}
		}
		//nolint: staticcheck
		names, err = s.getWhitelabelNamesForCenters(accessibleCenters, partnerID, brandingData.GetBranding())
		if err != nil {
			return verrors.WrapError(err, "error getting whitelabel names for centers").WithSourceLocation()
		}
		return nil
	})
	err = eGroup.Wait()
	if err != nil {
		logging.Errorf(ctx, "error in errgroup: %v", err)
		return nil, err
	}
	// Transitive check, ensure that all 3 lengths are equal
	if len(urls) != len(names) || len(urls) != len(accessibleCenters) {
		logging.Errorf(ctx, "lengths of adjacency lists: 'urls': %d and 'names': %d, must be the same", len(urls), len(names))
		return nil, verrors.New(verrors.Internal, "lengths of adjacency lists: 'urls' and 'names', must be the same").WithSourceLocation()
	}

	// Join adjacency lists urls + names
	data := &NavbarData{
		Centers:              make([]*Center, len(urls)),
		Username:             buildNameFromUser(u),
		ImpersonateeUsername: buildNameFromUser(impersonateeUser),
		Email:                u.GetEmail(),
		EmailVerified:        u.GetEmailVerified(),
		Language:             language,
		BusinessName:         businessName,
		Address:              address,
		Country:              country,

		// TODO: Allow user to override thinks like dark mode for themselves? Currently derived from resource
		Theme:            theme,
		BusinessAppTheme: businessAppTheme,

		NotificationsEnabled: notificationsEnabled(subjectMap),

		SignOutURL:       s.getSignOutURL(signOutNextURL),
		UserItems:        nil, // TODO: Properly implement the logout workflow for the UserItems, then return valid data
		UserSwitcherData: userSwitcherData,
		PartnerName:      partnerName,
	}
	for i := range accessibleCenters {
		data.Centers[i] = &Center{
			URL:  urls[i],
			Name: names[i],
			ID:   accessibleCenters[i],
		}
	}

	// TODO: Remove this check after notifications are localized to czech bperreault 2020-02-27
	if partnerID == "MTEL" || partnerID == "MTCZ" {
		data.NotificationsEnabled = false
	}

	return data, nil
}

func (s *Service) buildUserSwitcherData(ctx context.Context, u *user.User, serviceProviderID string) ([]*UserSwitcherData, error) {
	if !u.EmailVerified || serviceProviderID != "AA" {
		return nil, nil
	}
	accessibleUsers, _, _, err := s.iamV2Client.ListUsers(
		iaminterceptor.RemoveCallerIdentifierOnOutgoingContext(ctx),
		"",
		listusers.WithFilterRoleIDs([]string{
			string(iamrole.Partner),
			string(iamrole.SalesPerson),
		}),
		listusers.WithAcrossAllPartners(),
		listusers.WithFilterEmail(u.Email),
		listusers.WithFilterEmailVerified(true),
		listusers.WithPageSize(100))
	if err != nil {
		return nil, err
	}
	if len(accessibleUsers) <= 1 {
		return nil, nil
	}
	partnerBrandingReqs := []*partnerProto.GetBrandingV2Request{}
	userSwitcherData := []*UserSwitcherData{}

	for _, au := range accessibleUsers {
		//skip over the current user
		if au.UserID == u.UserID {
			continue
		}
		partnerBrandingReqs = append(partnerBrandingReqs, &partnerProto.GetBrandingV2Request{
			PartnerId: au.PartnerID,
		})
		userSwitcherData = append(userSwitcherData, &UserSwitcherData{
			UserID:    au.UserID,
			PartnerID: au.PartnerID,
		})
	}

	brandingData, err := s.brandingService.GetMultiBranding(iaminterceptor.RemoveCallerIdentifierOnOutgoingContext(ctx), partnerBrandingReqs)
	if err != nil {
		return nil, err
	}
	for i, bd := range brandingData {
		userSwitcherData[i].PartnerName = bd.Name
	}
	sort.Slice(userSwitcherData, func(i, j int) bool {
		return strings.Compare(userSwitcherData[i].PartnerName, userSwitcherData[j].PartnerName) < 0
	})
	userSwitcherData, err = getEntryUrlsForUserSwitcher(u.Email, userSwitcherData, serviceProviderID)
	if err != nil {
		return nil, err
	}
	return userSwitcherData, nil
}

func getEntryUrlsForUserSwitcher(currentEmail string, userSwitcherData []*UserSwitcherData, serviceProviderID string) ([]*UserSwitcherData, error) {
	for _, data := range userSwitcherData {
		entryURL, err := buildUserSwitcherEntryURL(serviceProviderID, data.PartnerID, currentEmail)
		if err != nil {
			return nil, err
		}
		data.EntryURL = entryURL
	}
	return userSwitcherData, nil
}

func buildUserSwitcherEntryURL(serviceProviderID, partnerID, email string) (string, error) {
	u, err := url.Parse(fmt.Sprintf("https://sso-api-%s.apigateway.co/service-gateway", config.CurEnv().Name()))
	if err != nil {
		return "", err
	}
	queryParams := u.Query()
	queryParams.Set("serviceProviderId", serviceProviderID)
	queryParams.Set("partner_id", partnerID)
	queryParams.Set("emailHint", email)
	u.RawQuery = queryParams.Encode()
	return u.String(), nil
}

func buildNameFromUser(u *user.User) string {
	if u != nil {
		if u.GetFirstName() != "" && u.GetLastName() != "" {
			return fmt.Sprintf("%s %s", u.GetFirstName(), u.GetLastName())
		}
		if u.GetFirstName() != "" {
			return u.GetFirstName()
		}
		if u.GetLastName() != "" {
			return u.GetLastName()
		}
		return u.GetEmail()
	}
	return ""
}

func (s *Service) getAccessibleCenters(ctx context.Context, partnerID, marketID string, subjectMap map[string]subject.Subject, accountGroupID string) ([]string, error) {
	var result []string

	partnerConfig, err := s.partnerClient.GetConfiguration(ctx, &partnerProto.GetConfigurationRequest{
		PartnerId: partnerID,
	})
	if err != nil {
		return nil, err
	}

	//salesPersonCanAccessPartnerCenter := false
	featureFlags, err := s.featureFlagService.GetFeaturesStatus(ctx, partnerID, marketID, []string{salesPersonPartnerCenterFlag, showSalesAndSuccessCenterFlag}, accountGroupID)
	if err != nil {
		logging.Errorf(ctx, "error getting feature flag status for %s, defaulting to off: %v", salesPersonPartnerCenterFlag, err)
	}

	for _, centerData := range s.centersData {
		if s.centerEnabled(&centerData, partnerConfig.GetConfiguration(), subjectMap, featureFlags) {
			result = append(result, centerData.ID)
		}
	}

	return result, nil
}

func (s *Service) centerEnabled(centerData *CenterData, partnerConfig *partnerProto.Configuration, subjectMap map[string]subject.Subject, featureFlags map[string]bool) bool {
	if enabledBySuperadmin(centerData, subjectMap) {
		return true
	}

	if !partnerHasAllFeatures(partnerConfig.EnabledFeatures, centerData.Features) {
		return false
	}

	if centerData.ID == salesCenterID && !featureFlags[showSalesAndSuccessCenterFlag] {
		return false
	}

	for _, persona := range centerData.Personas {
		if enabledByPersona(persona, centerData, partnerConfig, subjectMap, featureFlags) {
			return true
		}
	}

	return false
}

func enabledBySuperadmin(centerData *CenterData, subjectMap map[string]subject.Subject) bool {
	return centerData.SuperadminEnabled && checkSuperadminFromPersonas(subjectMap)
}

func enabledByPersona(persona string, centerData *CenterData, partnerConfig *partnerProto.Configuration, subjectMap map[string]subject.Subject, featureFlags map[string]bool) bool {
	_, ok := subjectMap[persona]
	if !ok {
		return false
	}

	// SSC whitelabeling is a special case for the salesperson persona
	if centerData.SscWhitelabeled && isSalesPerson(persona) && partnerConfig.GetSalesConfiguration().GetSscWhitelabeled() {
		return false
	}

	// Salesperson access to partner center is restricted by a feature flag
	if centerData.ID == partnerCenterID && isSalesPerson(persona) && !featureFlags[salesPersonPartnerCenterFlag] {
		return false
	}

	return true
}

func isSalesPerson(personaType string) bool {
	return personaType == "sales_person"
}

func partnerHasAllFeatures(enabledFeatures []string, requiredFeatures []string) bool {
	if len(requiredFeatures) <= 0 {
		return true
	}
	for _, requiredFeature := range requiredFeatures {
		if !partnerHasFeature(enabledFeatures, requiredFeature) {
			return false
		}
	}
	return true
}

func partnerHasFeature(enabledFeatures []string, requiredFeature string) bool {
	for _, enabledFeature := range enabledFeatures {
		if enabledFeature == requiredFeature {
			return true
		}
	}
	return false
}

func checkSuperadminFromPersonas(personas map[string]subject.Subject) bool {
	if partnerPersona, ok := personas["partner"]; ok {
		isSuperAdminAttr, ok := partnerPersona.Attributes()["is_super_admin"]
		if ok && isSuperAdminAttr.GetBoolAttribute() {
			return true
		}
	}
	return false
}

func notificationsEnabled(subjectMap map[string]subject.Subject) bool {
	_, partnerOk := subjectMap[subject.PartnerSubjectType]
	_, digitalAgentOk := subjectMap[subject.DigitalAgentSubjectType]
	_, smbOk := subjectMap[subject.SMBSubjectType]
	_, salesPersonOK := subjectMap[subject.SalesPersonSubjectType]
	return partnerOk || digitalAgentOk || smbOk || salesPersonOK
}

// nolint: staticcheck
func (s *Service) getWhitelabelNamesForCenters(accessibleCenters []string, partnerID string, branding *partnerProto.Branding) ([]string, error) {
	if len(accessibleCenters) == 0 {
		return nil, nil
	}
	names := make([]string, len(accessibleCenters))

	for i, id := range accessibleCenters {
		names[i] = s.defaultCenterNames[id]
	}

	if partnerID == "" {
		return names, nil
	}

	for i, center := range accessibleCenters {
		if branding == nil || branding.GetApps() == nil || branding.GetApps()[center] == nil {
			names[i] = s.defaultCenterNames[center]
			continue
		}
		names[i] = branding.GetApps()[center].Name
	}

	return names, nil
}

func (s *Service) getSignOutURL(nextURL string) string {
	if nextURL == "" {
		return ""
	}
	// this may need to call sso in the future
	signOutURL := s.signOutURL
	q := signOutURL.Query()
	q.Add("nextUrl", nextURL)
	signOutURL.RawQuery = q.Encode()
	return signOutURL.String()
}
