package navbardata

import (
	"context"

	partner "github.com/vendasta/partner/sdks/go/v1"
	"github.com/vendasta/sso/sdks/go/sso"
)

// HappyIdentityProviderClient is a stub for an identity provider
type HappyIdentityProviderClient struct {
	IDToURL map[string]string
}

// GetEntryURL stub
func (i *HappyIdentityProviderClient) GetEntryURL(ctx context.Context, serviceProviderID string, serviceContext sso.ServiceContext) (entryURL string, err error) {
	return i.IDToURL[serviceProviderID], nil
}

// GetMultiEntryURL stub
func (i *HappyIdentityProviderClient) GetMultiEntryURL(ctx context.Context, serviceProviderIDs []string, serviceContext sso.ServiceContext) (entryURLs []string, err error) {
	return nil, nil
}

// GetEntryURLWithCode stub
func (i *HappyIdentityProviderClient) GetEntryURLWithCode(ctx context.Context, serviceProviderID string, serviceContext sso.ServiceContext, sessionID string, userID string, email string, nextURL string) (entryURL string, err error) {
	return "", nil
}

// Logout stub
func (i *HappyIdentityProviderClient) Logout(ctx context.Context, sessionID string) error {
	return nil
}

// HappyPartnerInterface is a partner stub
type HappyPartnerInterface struct {
	Branding *partner.Branding
}

// GetConfiguration stub
func (p *HappyPartnerInterface) GetConfiguration(ctx context.Context, partnerID string, marketID string) (*partner.Configuration, error) {
	return nil, nil
}

// GetBranding stub
func (p *HappyPartnerInterface) GetBranding(ctx context.Context, partnerID string, marketID string) (*partner.Branding, error) {
	return p.Branding, nil
}
