package navbardata

import (
	"context"

	iam "github.com/vendasta/IAM/sdks/go/v1"

	"github.com/vendasta/IAM/sdks/go/v1/subject"
	"golang.org/x/sync/errgroup"
)

// nolint:gocyclo
func (s *Service) getSubjects(ctx context.Context, userID string, partnerID string) (map[string]subject.Subject, error) {
	var result []subject.Subject
	type listPersonasResponse struct {
		personas []subject.Subject
		cursor   string
		hasMore  bool
		err      error
	}
	var superAdminIncludedListResp *listPersonasResponse
	var listResp *listPersonasResponse

	var personas []subject.Subject
	var cursor string
	hasMore := true

	for {
		if !hasMore {
			break
		}
		eGroup, egCtx := errgroup.WithContext(ctx)
		// double up requests because we don't know if the caller is a superadmin
		// if the caller is a superadmin we include the superadmin's partner persona in our response
		eGroup.Go(func() error {
			superAdminIncludedListResp = &listPersonasResponse{}

			superAdminIncludedListResp.personas, superAdminIncludedListResp.cursor, superAdminIncludedListResp.hasMore, superAdminIncludedListResp.err = s.iamClient.ListPersonasByUserID(egCtx, userID, cursor, 100, "", "", iam.AlwaysIncludeSuperadmin(true))
			return superAdminIncludedListResp.err
		})
		eGroup.Go(func() error {
			listResp = &listPersonasResponse{}
			listResp.personas, listResp.cursor, listResp.hasMore, listResp.err = s.iamClient.ListPersonasByUserID(egCtx, userID, cursor, 100, "", "", iam.AlwaysIncludeSuperadmin(false))
			return listResp.err
		})
		_ = eGroup.Wait()
		// prioritize list response with superadmin personas
		if superAdminIncludedListResp.err == nil {
			personas = superAdminIncludedListResp.personas
			cursor = superAdminIncludedListResp.cursor
			hasMore = superAdminIncludedListResp.hasMore
		} else if listResp.err == nil {
			personas = listResp.personas
			cursor = listResp.cursor
			hasMore = listResp.hasMore
		} else {
			// return the list error from the non-persona call as it's more likely to not just be a 403, but actual error
			return nil, listResp.err
		}
		for _, p := range personas {
			namespace := p.Context().Namespace
			if namespace == partnerID {
				result = append(result, p)
				continue
			}
			attrs := p.Attributes()
			isSuperAdminAttr, ok := attrs["is_super_admin"]
			if ok && isSuperAdminAttr.GetBoolAttribute() {
				result = append(result, p)
				continue
			}
			personaPartnerIDAttr, ok := attrs["partner_id"]
			if ok && personaPartnerIDAttr.GetStringAttribute() == partnerID {
				result = append(result, p)
				continue
			}
			switch p.Context().Type {
			case "developer":
				result = append(result, p)
			case "digital_agent":
				accessiblePartnerIDsAttr, ok := attrs["accessible_partner_ids"]
				if !ok {
					continue
				}
				listAttr := accessiblePartnerIDsAttr.GetListAttribute()
				if listAttr == nil || len(listAttr.Attributes) == 0 {
					continue
				}
				for _, a := range listAttr.Attributes {
					if a.GetStringAttribute() == partnerID {
						result = append(result, p)
						break
					}
				}
			}
		}
	}
	return parseSubjects(result), nil
}

func parseSubjects(subjects []subject.Subject) map[string]subject.Subject {
	m := make(map[string]subject.Subject, len(subjects))
	for _, s := range subjects {
		m[s.Context().Type] = s
	}
	return m
}
