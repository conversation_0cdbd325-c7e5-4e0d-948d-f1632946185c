package navbardata

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	iammock "github.com/vendasta/IAM/sdks/go/v1/mocks"
	"github.com/vendasta/IAM/sdks/go/v1/subject"
	"github.com/vendasta/IAM/sdks/go/v1/user"
	iammockv2 "github.com/vendasta/IAM/sdks/go/v2/mocks"
	iamuser "github.com/vendasta/IAM/sdks/go/v2/user"
	"github.com/vendasta/atlas/internal/branding"
	iam_attributes "github.com/vendasta/generated-protos-go/iam/attributes"
	partner_v1 "github.com/vendasta/generated-protos-go/partner/v1"
	"github.com/vendasta/gosdks/config"
)

const yeswareID = "stubbed-yesware-id"

func mockSubjectWithAttrs(attrs map[string]*iam_attributes.Attribute) subject.Subject {
	m := &iammock.Subject{}

	m.On("Attributes").Return(attrs)

	return m
}

func Test_getAccessibleCenters(t *testing.T) {
	cases := []struct {
		name            string
		subjectMap      map[string]subject.Subject
		SscWhitelabeled bool
		enabledFeatures []string
		featureFlags    map[string]bool
		output          []string
	}{
		{
			name: "Partner gives access to AA, VendorCenter, And Conquer Local Academy",
			subjectMap: map[string]subject.Subject{
				"partner": mockSubjectWithAttrs(nil),
			},
			enabledFeatures: []string{},
			output:          []string{"AA", "vendor-center", "conquer-local-academy"},
		},
		{
			name: "Partner with SscWhitelabeled set to false gives access to AA, VendorCenter, And Conquer Local Academy",
			subjectMap: map[string]subject.Subject{
				"partner": mockSubjectWithAttrs(nil),
			},
			enabledFeatures: []string{},
			SscWhitelabeled: false,
			output:          []string{"AA", "vendor-center", "conquer-local-academy"},
		},
		{
			name: "SMB gives access to Business Center",
			subjectMap: map[string]subject.Subject{
				"smb": mockSubjectWithAttrs(nil),
			},
			output: []string{"VBC"},
		},
		{
			name: "SalesPerson gives access to ST, And Conquer Local Academy",
			subjectMap: map[string]subject.Subject{
				"sales_person": mockSubjectWithAttrs(nil),
			},
			featureFlags: map[string]bool{
				showSalesAndSuccessCenterFlag: true,
			},
			output: []string{"ST", "conquer-local-academy"},
		},
		{
			name: "SalesPerson with SscWhitelabeled set to false gives access to ST, and Conquer Local Academy",
			subjectMap: map[string]subject.Subject{
				"sales_person": mockSubjectWithAttrs(nil),
			},
			SscWhitelabeled: false,
			featureFlags: map[string]bool{
				showSalesAndSuccessCenterFlag: true,
			},
			output: []string{"ST", "conquer-local-academy"},
		},
		{
			name: "SalesPerson with SscWhitelabeled set to true gives access to ST",
			subjectMap: map[string]subject.Subject{
				"sales_person": mockSubjectWithAttrs(nil),
			},
			SscWhitelabeled: true,
			featureFlags: map[string]bool{
				showSalesAndSuccessCenterFlag: true,
			},
			output: []string{"ST"},
		},
		{
			name: "DigitalAgent gives access to ARM",
			subjectMap: map[string]subject.Subject{
				"digital_agent": mockSubjectWithAttrs(nil),
			},
			output: []string{"ARM"},
		},
		{
			name: "Vendor gives access to Vendor Center",
			subjectMap: map[string]subject.Subject{
				"vendor": mockSubjectWithAttrs(nil),
			},
			enabledFeatures: []string{},
			output:          []string{"vendor-center"},
		},
		{
			name: "Developer gives access to admin centers",
			subjectMap: map[string]subject.Subject{
				"developer": mockSubjectWithAttrs(nil),
			},
			output: []string{"iam", "vstore", "mission-control", "sre-reporting", "office-library", "admin-client", "web-crawler"},
		},
		{
			name: "Partner and Vendor has no duplicates",
			subjectMap: map[string]subject.Subject{
				"partner": mockSubjectWithAttrs(nil),
				"vendor":  mockSubjectWithAttrs(nil),
			},
			enabledFeatures: []string{"my-products"},
			output:          []string{"AA", "vendor-center", "conquer-local-academy", yeswareID},
		},
		{
			name: "Partner and Salesperson has no duplicates",
			subjectMap: map[string]subject.Subject{
				"partner":      mockSubjectWithAttrs(nil),
				"sales_person": mockSubjectWithAttrs(nil),
			},
			enabledFeatures: []string{"my-products"},
			featureFlags: map[string]bool{
				showSalesAndSuccessCenterFlag: true,
			},
			output: []string{"AA", "vendor-center", "ST", "conquer-local-academy", yeswareID},
		},
		{
			name: "All Personas have the right order",
			subjectMap: map[string]subject.Subject{
				"partner":       mockSubjectWithAttrs(nil),
				"smb":           mockSubjectWithAttrs(nil),
				"sales_person":  mockSubjectWithAttrs(nil),
				"digital_agent": mockSubjectWithAttrs(nil),
				"vendor":        mockSubjectWithAttrs(nil),
				"developer":     mockSubjectWithAttrs(nil),
				"success":       mockSubjectWithAttrs(nil),
			},
			enabledFeatures: []string{"my-products"},
			featureFlags: map[string]bool{
				showSalesAndSuccessCenterFlag: true,
			},
			output: []string{"AA", "vendor-center", "ST", "conquer-local-academy", yeswareID, "VBC", "ARM", "iam", "vstore", "mission-control", "sre-reporting", "office-library", "admin-client", "web-crawler"},
		},
		{
			name: "Superadmin is always returned partner center, vendor center, conquer local academy, yesware, and billing",
			subjectMap: map[string]subject.Subject{
				"partner": mockSubjectWithAttrs(map[string]*iam_attributes.Attribute{
					"is_super_admin": {Kind: &iam_attributes.Attribute_BoolAttribute{BoolAttribute: true}},
				}),
			},
			output: []string{"AA", "vendor-center", "conquer-local-academy", yeswareID, "billing"},
		},
		{
			name: "Superadmin is always returned partner center, vendor center, conquer local academy, yesware, and billing - conquer flag true",
			subjectMap: map[string]subject.Subject{
				"partner": mockSubjectWithAttrs(map[string]*iam_attributes.Attribute{
					"is_super_admin": {Kind: &iam_attributes.Attribute_BoolAttribute{BoolAttribute: true}},
				}),
			},
			output: []string{"AA", "vendor-center", "conquer-local-academy", yeswareID, "billing"},

			SscWhitelabeled: false,
		},
		{
			name: "Partner has access to Yesware if feature is present",
			subjectMap: map[string]subject.Subject{
				"partner": mockSubjectWithAttrs(nil),
			},
			enabledFeatures: []string{"my-products"},
			output:          []string{"AA", "vendor-center", "conquer-local-academy", yeswareID},
		},
		{
			name: "Partner has access to Yesware if feature is present and SSC whitelabel is set",
			subjectMap: map[string]subject.Subject{
				"partner": mockSubjectWithAttrs(nil),
			},
			enabledFeatures: []string{"my-products"},
			SscWhitelabeled: true,
			output:          []string{"AA", "vendor-center", "conquer-local-academy", yeswareID},
		},
		{
			name: "Partner does not have access to Yesware if feature is not present",
			subjectMap: map[string]subject.Subject{
				"partner": mockSubjectWithAttrs(nil),
			},
			enabledFeatures: []string{},
			output:          []string{"AA", "vendor-center", "conquer-local-academy"},
		},
		{
			name: "Salesperson has access to Yesware if feature is present",
			subjectMap: map[string]subject.Subject{
				"sales_person": mockSubjectWithAttrs(nil),
			},
			enabledFeatures: []string{"my-products"},
			featureFlags: map[string]bool{
				showSalesAndSuccessCenterFlag: true,
			},
			output: []string{"ST", "conquer-local-academy", yeswareID},
		},
		{
			name: "Salesperson does not have access to Yesware if feature is present and SSC whitelabel is set",
			subjectMap: map[string]subject.Subject{
				"sales_person": mockSubjectWithAttrs(nil),
			},
			enabledFeatures: []string{"my-products"},
			SscWhitelabeled: true,
			featureFlags: map[string]bool{
				showSalesAndSuccessCenterFlag: true,
			},
			output: []string{"ST"},
		},
		{
			name: "Salesperson does not have access to Yesware if feature is not present",
			subjectMap: map[string]subject.Subject{
				"sales_person": mockSubjectWithAttrs(nil),
			},
			enabledFeatures: []string{},
			featureFlags: map[string]bool{
				showSalesAndSuccessCenterFlag: true,
			},
			output: []string{"ST", "conquer-local-academy"},
		},
		{
			name: "SalesPerson gives access to AA if feature flag enabled",
			subjectMap: map[string]subject.Subject{
				"sales_person": mockSubjectWithAttrs(nil),
			},
			featureFlags: map[string]bool{
				salesPersonPartnerCenterFlag:  true,
				showSalesAndSuccessCenterFlag: true,
			},
			output: []string{"AA", "ST", "conquer-local-academy"},
		},
		{
			name: "Salesperson should not have access to ST if not grandfathered onto the feature flag",
			subjectMap: map[string]subject.Subject{
				"sales_person": mockSubjectWithAttrs(nil),
			},
			featureFlags: map[string]bool{
				salesPersonPartnerCenterFlag:  true,
				showSalesAndSuccessCenterFlag: false,
			},
			output: []string{"AA", "conquer-local-academy"},
		},
	}

	for _, c := range cases {
		partnerID := "partner-id"
		marketID := "market-id"
		partnerConfiguration := &partner_v1.Configuration{
			EnabledFeatures: c.enabledFeatures,
			SalesConfiguration: &partner_v1.SalesConfiguration{
				SscWhitelabeled: c.SscWhitelabeled,
			},
		}
		t.Run(c.name, func(t *testing.T) {
			partnerStub := NewMockWhitelabelClient(gomock.NewController(t))
			partnerStub.EXPECT().GetConfiguration(gomock.Any(), &partner_v1.GetConfigurationRequest{
				PartnerId: partnerID,
				MarketId:  "",
			}).
				Return(&partner_v1.GetConfigurationResponse{
					Configuration: partnerConfiguration,
				}, nil)

			featureClient := &featureFlagStub{Flags: c.featureFlags}

			client, _ := NewService(config.Demo, nil, nil, partnerStub, nil, nil, nil, yeswareID, featureClient, nil)
			actual, _ := client.getAccessibleCenters(context.Background(), partnerID, marketID, c.subjectMap, "")
			assert.Equal(t, c.output, actual)
		})
	}
}

type featureFlagStub struct {
	Flags map[string]bool
}

func (f *featureFlagStub) GetFeaturesStatus(ctx context.Context, partnerID, marketID string, featureFlags []string, accountGroupID string) (map[string]bool, error) {
	return f.Flags, nil
}

func Test_GetWhitelabelNamesForCenters(t *testing.T) {
	cases := []struct {
		name              string
		partnerID         string
		accessibleCenters []string
		marketID          string
		stubOutput        *partner_v1.Branding
		expectedOutput    []string
		error             error
	}{
		{
			name:              "Happy - names are returned in same order",
			partnerID:         "ABC",
			marketID:          "cool market bro",
			accessibleCenters: []string{"ARM", "VBC"},
			stubOutput: &partner_v1.Branding{
				PrimaryColor: "doesn't matter",
				Assets:       nil,
				Apps: map[string]*partner_v1.Branding_App{
					"ARM": {Name: "Super Concierge"},
					"VBC": {Name: "Super Duper Business Center"},
				},
				Name: "",
			},
			expectedOutput: []string{
				"Super Concierge",
				"Super Duper Business Center",
			},
			error: nil,
		},
		{
			name:              "Falls back to default if no Branding is defined",
			partnerID:         "ABC",
			marketID:          "cool market bro",
			accessibleCenters: []string{"ARM", "VBC"},
			stubOutput: &partner_v1.Branding{
				PrimaryColor: "doesn't matter",
				Assets:       nil,
				Apps: map[string]*partner_v1.Branding_App{
					"ARM": {Name: "Super Concierge"},
				},
				Name: "",
			},
			expectedOutput: []string{
				"Super Concierge",
				"Business App",
			},
			error: nil,
		},
		{
			name:              "Uses default if partner id is not specified",
			partnerID:         "",
			marketID:          "",
			accessibleCenters: []string{"ARM", "VBC"},
			stubOutput: &partner_v1.Branding{
				PrimaryColor: "doesn't matter",
				Assets:       nil,
				Apps: map[string]*partner_v1.Branding_App{
					"ARM": {Name: "Super Concierge"},
					"VBC": {Name: "Super Duper Business Center"},
				},
				Name: "",
			},
			expectedOutput: []string{
				"Task Manager",
				"Business App",
			},
			error: nil,
		},
	}

	for _, c := range cases {
		client, _ := NewService(config.Demo, nil, nil, nil, nil, nil, nil, yeswareID, nil, nil)
		t.Run(c.name, func(t *testing.T) {
			actual, err := client.getWhitelabelNamesForCenters(c.accessibleCenters, c.partnerID, c.stubOutput)
			assert.Equal(t, c.error, err)
			assert.Equal(t, c.expectedOutput, actual)
		})
	}
}

func Test_buildNameFromUser(t *testing.T) {
	cases := []struct {
		name   string
		input  *user.User
		output string
	}{
		{
			name: "Gets first and last name from user",
			input: &user.User{
				FirstName: "first",
				LastName:  "last",
			},
			output: "first last",
		},
		{
			name: "Gets first name and not last name when last name is empty",
			input: &user.User{
				FirstName: "first",
			},
			output: "first",
		},
		{
			name: "Gets last name and not first name when first name is empty",
			input: &user.User{
				LastName: "last",
			},
			output: "last",
		},
		{
			name:   "Empty string when no first or last name",
			input:  &user.User{Email: "<EMAIL>"},
			output: "<EMAIL>",
		},
		{
			name:   "No User",
			input:  nil,
			output: "",
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			actual := buildNameFromUser(c.input)
			assert.Equal(t, c.output, actual)
		})
	}
}

func Test_checkSuperadminFromPersonas(t *testing.T) {
	cases := []struct {
		name   string
		input  map[string]subject.Subject
		output bool
	}{
		{
			name: "Returns true if is_super_admin attribute is true on the partner persona",
			input: map[string]subject.Subject{
				"partner": mockSubjectWithAttrs(map[string]*iam_attributes.Attribute{
					"is_super_admin": {Kind: &iam_attributes.Attribute_BoolAttribute{BoolAttribute: true}},
				}),
			},
			output: true,
		},
		{
			name: "Returns false if is_super_admin attribute is false on the partner persona",
			input: map[string]subject.Subject{
				"partner": mockSubjectWithAttrs(map[string]*iam_attributes.Attribute{
					"is_super_admin": {Kind: &iam_attributes.Attribute_BoolAttribute{BoolAttribute: false}},
				}),
			},
			output: false,
		},
		{
			name: "Returns false if is_super_admin attribute is true on the non-partner persona",
			input: map[string]subject.Subject{
				"smb": mockSubjectWithAttrs(map[string]*iam_attributes.Attribute{
					"is_super_admin": {Kind: &iam_attributes.Attribute_BoolAttribute{BoolAttribute: true}},
				}),
			},
			output: false,
		},
		{
			name:   "Returns false for empty subject map",
			input:  map[string]subject.Subject{},
			output: false,
		},
		{
			name: "Returns false if is_super_admin attribute does not exist on partner persona",
			input: map[string]subject.Subject{
				"partner": mockSubjectWithAttrs(nil),
			},
			output: false,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			actual := checkSuperadminFromPersonas(c.input)
			assert.Equal(t, c.output, actual)
		})
	}
}

func Test_UserSwitcher_Data(t *testing.T) {
	cases := []struct {
		name              string
		currentUser       *user.User
		accessibleUsers   []*iamuser.User
		serviceProviderId string
		brandingData      []*partner_v1.BrandingV2
		expectedErr       error
		expectedData      []*UserSwitcherData
	}{
		{
			name: "Only 1 partner or salesperson user associated with the email address",
			currentUser: &user.User{
				UserID:        "123",
				Email:         "<EMAIL>",
				EmailVerified: true,
			},
			accessibleUsers: []*iamuser.User{
				{
					UserID:    "123",
					PartnerID: "ABC",
				},
			},
			serviceProviderId: "AA",
			expectedErr:       nil,
			expectedData:      nil,
		},
		{
			name: "One accessible user",
			currentUser: &user.User{
				UserID:        "123",
				Email:         "<EMAIL>",
				EmailVerified: true,
			},
			accessibleUsers: []*iamuser.User{
				{
					UserID:    "123",
					PartnerID: "ABC",
				},
				{
					UserID:    "456",
					PartnerID: "DEF",
				},
			},
			serviceProviderId: "AA",
			brandingData: []*partner_v1.BrandingV2{
				{
					Name: "DEF Partner",
				},
			},

			expectedErr: nil,
			expectedData: []*UserSwitcherData{
				{"456", "DEF", "DEF Partner", fmt.Sprintf("https://sso-api-%s.apigateway.co/service-gateway?emailHint=%s&partner_id=DEF&serviceProviderId=AA", config.CurEnv().Name(), "abc%40email.com")},
			},
		},
		{
			name: "Multiple accessible users should follow sort order",
			currentUser: &user.User{
				UserID:        "123",
				Email:         "<EMAIL>",
				EmailVerified: true,
			},
			accessibleUsers: []*iamuser.User{
				{
					UserID:    "123",
					PartnerID: "ABC",
				},
				{
					UserID:    "789",
					PartnerID: "VUNI",
				},
				{
					UserID:    "456",
					PartnerID: "DEF",
				},
				{
					UserID:    "111",
					PartnerID: "VA",
				},
				{
					UserID:    "999",
					PartnerID: "CAT",
				},
			},
			brandingData: []*partner_v1.BrandingV2{
				{
					Name: "VUNI Partner",
				},
				{
					Name: "DEF Partner",
				},
				{
					Name: "VA Partner",
				},
				{
					Name: "CAT Partner",
				},
			},
			serviceProviderId: "AA",
			expectedErr:       nil,
			expectedData: []*UserSwitcherData{
				{"999", "CAT", "CAT Partner", fmt.Sprintf("https://sso-api-%s.apigateway.co/service-gateway?emailHint=%s&partner_id=CAT&serviceProviderId=AA", config.CurEnv().Name(), "abc%40email.com")},
				{"456", "DEF", "DEF Partner", fmt.Sprintf("https://sso-api-%s.apigateway.co/service-gateway?emailHint=%s&partner_id=DEF&serviceProviderId=AA", config.CurEnv().Name(), "abc%40email.com")},
				{"111", "VA", "VA Partner", fmt.Sprintf("https://sso-api-%s.apigateway.co/service-gateway?emailHint=%s&partner_id=VA&serviceProviderId=AA", config.CurEnv().Name(), "abc%40email.com")},
				{"789", "VUNI", "VUNI Partner", fmt.Sprintf("https://sso-api-%s.apigateway.co/service-gateway?emailHint=%s&partner_id=VUNI&serviceProviderId=AA", config.CurEnv().Name(), "abc%40email.com")},
			},
		},
		{
			name: "serviceProviderID other than AA should return nil data",
			currentUser: &user.User{
				UserID:        "123",
				Email:         "<EMAIL>",
				EmailVerified: true,
			},
			accessibleUsers: []*iamuser.User{
				{
					UserID:    "123",
					PartnerID: "ABC",
				},
				{
					UserID:    "456",
					PartnerID: "DEF",
				},
				{
					UserID:    "789",
					PartnerID: "VUNI",
				},
			},
			serviceProviderId: "VBC",
			expectedErr:       nil,
			expectedData:      nil,
		},
		{
			name: "unverified user should return nil data",
			currentUser: &user.User{
				UserID:        "123",
				Email:         "<EMAIL>",
				EmailVerified: false,
			},
			accessibleUsers: []*iamuser.User{
				{
					UserID:    "123",
					PartnerID: "ABC",
				},
				{
					UserID:    "456",
					PartnerID: "DEF",
				},
				{
					UserID:    "789",
					PartnerID: "VUNI",
				},
			},
			serviceProviderId: "VBC",
			expectedErr:       nil,
			expectedData:      nil,
		},
	}
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()

			iamv2 := &iammockv2.ClientInterface{}
			iamv2.On("ListUsers",
				mock.Anything,
				"",
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.Anything,
				mock.Anything,
			).Return(c.accessibleUsers, "", false, nil)
			brandingService := branding.NewMockServiceInterface(gomock.NewController(t))
			if c.expectedData != nil {
				brandingService.EXPECT().GetMultiBranding(ctx, gomock.Any()).Return(c.brandingData, nil)
			}

			service := &Service{
				iamV2Client:     iamv2,
				brandingService: brandingService,
			}
			userSwitcherData, err := service.buildUserSwitcherData(ctx, c.currentUser, c.serviceProviderId)
			assert.NoError(t, err)
			assert.Equal(t, c.expectedData, userSwitcherData)
		})
	}
}

func Test_getEntryUrlsForUserSwitcher(t *testing.T) {
	testCases := []struct {
		Name     string
		Input    []*UserSwitcherData
		Expected []*UserSwitcherData
	}{
		{
			Name: "Builds SSO entry urls for multiple users",
			Input: []*UserSwitcherData{
				{
					PartnerID: "ABC",
				},
				{
					PartnerID: "DEF",
				},
				{
					PartnerID: "VUNI",
				},
			},
			Expected: []*UserSwitcherData{
				{
					PartnerID: "ABC",
					EntryURL:  fmt.Sprintf("https://sso-api-%s.apigateway.co/service-gateway?emailHint=%s&partner_id=ABC&serviceProviderId=AA", config.CurEnv().Name(), "example%40email.com"),
				},
				{
					PartnerID: "DEF",
					EntryURL:  fmt.Sprintf("https://sso-api-%s.apigateway.co/service-gateway?emailHint=%s&partner_id=DEF&serviceProviderId=AA", config.CurEnv().Name(), "example%40email.com"),
				},
				{
					PartnerID: "VUNI",
					EntryURL:  fmt.Sprintf("https://sso-api-%s.apigateway.co/service-gateway?emailHint=%s&partner_id=VUNI&serviceProviderId=AA", config.CurEnv().Name(), "example%40email.com"),
				},
			},
		},
		{
			Name:     "Does nothing for empty input",
			Input:    []*UserSwitcherData{},
			Expected: []*UserSwitcherData{},
		},
		{
			Name:     "Does nothing for nil input",
			Input:    nil,
			Expected: nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.Name, func(t *testing.T) {
			actual, err := getEntryUrlsForUserSwitcher("<EMAIL>", tc.Input, "AA")
			require.NoError(t, err)
			require.Equal(t, len(tc.Expected), len(actual))
			for i, result := range actual {
				assert.Equal(t, tc.Expected[i], result)
			}
		})
	}
}

func Test_buildUserSwitcherEntryURL(t *testing.T) {
	expected := fmt.Sprintf("https://sso-api-%s.apigateway.co/service-gateway?emailHint=%s&partner_id=VUNI&serviceProviderId=AA", config.CurEnv().Name(), "john%40email.com")
	actual, err := buildUserSwitcherEntryURL("AA", "VUNI", "<EMAIL>")
	require.NoError(t, err)
	assert.Equal(t, expected, actual)
}
