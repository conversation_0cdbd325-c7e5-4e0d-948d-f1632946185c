package constants

import (
	"fmt"
	"strings"
)

// Tab permissions
const (
	Fulfillment       = "fulfillment"
	Dashboard         = "dashboard"
	MyProducts        = "my_products"
	NewInbox          = "new_inbox"
	MeetingScheduler  = "meeting_scheduler"
	ContentLibrary    = "content_library"
	Recommendations   = "lmi_dashboard"
	ExecutiveReport   = "executive_report"
	CustomerList      = "customer_list"
	Store             = "store"
	BusinessProfile   = "business_profile"
	Files             = "files"
	SocialConnections = "social_connections"
	CustomerVoice     = "customer_voice"
	Invoices          = "invoices"
	Orders            = "orders"
	InviteTeam        = "bc_my_team_page"
)

type BrandTabs = string

const (
	AdvertisingTab BrandTabs = "advertising"
	DataExportTab  BrandTabs = "data_export"
	GoogleQAndATab BrandTabs = "google_q_and_a"
	MapTab         BrandTabs = "map"
	ReportTab      BrandTabs = "report"
	ReviewsTab     BrandTabs = "reviews"
	SocialTab      BrandTabs = "social"
	ListingsTab    BrandTabs = "listings"
)

// User setting Feature ids
// These ids are used to determine whether a user specifically has access to the given tab.
type UserConfigurationFeatureID = string

const (
	UserConfigRecommendationsFeature     UserConfigurationFeatureID = "recommendations"
	UserConfigExecutiveReportFeature     UserConfigurationFeatureID = "vbc-executive-report"
	UserConfigMeetingSchedulerFeature    UserConfigurationFeatureID = "meeting_scheduler_business_app"
	UserConfigContentLibraryFeature      UserConfigurationFeatureID = "content-library"
	UserConfigGuidesFeature              UserConfigurationFeatureID = "guides"
	UserConfigLocalMarketingIndexFeature UserConfigurationFeatureID = "local-marketing-index"
	UserConfigMarketplaceFeature         UserConfigurationFeatureID = "access-marketplace"
	UserConfigFulfillmentFeature         UserConfigurationFeatureID = "fulfillment"
	UserConfigInviteTeam                 UserConfigurationFeatureID = "bc_my_team_page"
	UserConfigOrderPageFeature           UserConfigurationFeatureID = "orders"
	UserConfigInvoicesFeature            UserConfigurationFeatureID = "invoices"
	UserConfigFilesFeature               UserConfigurationFeatureID = "files"
	UserConfigMyProductsFeature          UserConfigurationFeatureID = "my_products"
	UserConfigCustomersFeature           UserConfigurationFeatureID = "customer_list" // Now controls CRM Contacts
	UserConfigCRMCompaniesFeature        UserConfigurationFeatureID = "crm_companies"
	UserConfigCRMTasksFeature            UserConfigurationFeatureID = "crm_tasks"
	UserConfigCRMOpportunitiesFeature    UserConfigurationFeatureID = "crm_opportunities"
	UserConfigCRMCustomObjectsFeature    UserConfigurationFeatureID = "crm_custom_objects"
	UserConfigDynamicListsFeature        UserConfigurationFeatureID = "dynamic_lists"
	UserConfigLeaderboardFeature         UserConfigurationFeatureID = "leaderboard"
	UserConfigCustomFormsFeature         UserConfigurationFeatureID = "custom_forms"
	UserConfigDashboardFeature           UserConfigurationFeatureID = "dashboard"
	UserConfigInboxFeature               UserConfigurationFeatureID = "inbox"
	UserConfigAutomationsFeature         UserConfigurationFeatureID = "automations"
	UserConfigAIAssistantFeature         UserConfigurationFeatureID = "ai_assistant"
	UserConfigLeadScoringFeature         UserConfigurationFeatureID = "lead_scoring"
)

// Feature Flags
// These correspond to feature flags set in superadmin.
type PartnerFeatureFlagID = string

const (
	PartnerFeatureIDMeetingScheduler        PartnerFeatureFlagID = "meeting_scheduler_business_app"
	PartnerFeatureIDBusinessEmails          PartnerFeatureFlagID = "business_emails"
	PartnerFeatureIDSingleNav               PartnerFeatureFlagID = "bc_single_nav"
	PartnerFeatureIDCRMOpportunity          PartnerFeatureFlagID = "crm_opportunity_business_app"
	PartnerFeatureIDDynamicLists            PartnerFeatureFlagID = "business_app_dynamic_lists"
	PartnerFeatureIDSalesFeatures           PartnerFeatureFlagID = "sales_feature_business_app"
	PartnerFeatureIDEmbeddedListings        PartnerFeatureFlagID = "listings_embedded_tabs"
	PartnerFeatureIDEmbeddedSocialMarketing PartnerFeatureFlagID = "social_marketing_embedded_tabs"
	PartnerFeatureIDEmbeddedAdIntel         PartnerFeatureFlagID = "ad_intel_embedded_tabs"
	CampaignsSMS                            PartnerFeatureFlagID = "campaigns_sms"
	AIAssistants                            PartnerFeatureFlagID = "ai_assistants"
	RouteToNewIntegrations                  PartnerFeatureFlagID = "route_to_new_integrations"
	SMBPayments                             PartnerFeatureFlagID = "inbox_payment_link"
	NPSFeatureIDReputation                  PartnerFeatureFlagID = "multilocation_reputation_nps"
	NPSNewMLDesign                          PartnerFeatureFlagID = "ml_new_design_for_rep"
	CRMMultilocation                        PartnerFeatureFlagID = "crm_bcc_multilocation"
	KeywordTrackingOverviewFeature          PartnerFeatureFlagID = "multilocation_keyword_overview"
	CRMCustomObjectsFeatureID               PartnerFeatureFlagID = "bcc_crm_custom_objects"
	PartnerFeatureIDLisAnalytics            PartnerFeatureFlagID = "bing_insights_multi_location_busines_app"
	NPSBusinessAppFeatureIDReputation       PartnerFeatureFlagID = "nps_in_business_app"
)

type ProductSKU = string

// Service Provider ID for email configuration access.  Add new service provider IDs as necessary.
const (
	ReputationManagement ProductSKU = "RM"
	SocialMarketing      ProductSKU = "SM"
	Listings             ProductSKU = "MS"
	Advertising          ProductSKU = "MP-94072e44d5364872b672d7ab4fc7a7e8"
	CustomerVoiceDemo    ProductSKU = "MP-fba21121b71148c9bb33e11fcd92d520"
	CustomerVoiceProd    ProductSKU = "MP-c4974d390a044c28aec31e421aa662b2"
	WebsiteDemo          ProductSKU = "MP-BF4SVXX68FQ5FDV3MKMR7BMVXM63LTLQ"
	WebsiteProd          ProductSKU = "MP-ee4ea04e553a4b1780caf7aad7be07cd"
	CampaignsDemo        ProductSKU = "MP-WF5KS7FHTGV4LR5DBQ4D6XKCCDB5B6G7"
	CampaignsProd        ProductSKU = "MP-ZGJ6V4QRP77WPMDKXS6VDRNX58Q42P7P"
)

const (
	// Pages that are under development on mobile will be displayed to accounts in this market
	MobileDevMarket = "mobile-dev"
)

func InternalGetExternalProductConfigID(id string) string {
	if strings.HasPrefix(id, "product-") {
		return id
	}
	return fmt.Sprintf("product-%s", id)
}

type Country = string

const (
	CountryUS Country = "US"
)

type EditionID = string

const (
	RMPremiumProd EditionID = "EDITION-JFRPLQPN"
	RMPremiumDemo EditionID = "EDITION-BFXF8W8Q"
)

// Supported platforms
const (
	MobileMode    = "mobile"
	WebMode       = "web"
	MobileDevMode = "mobileDev"
)
