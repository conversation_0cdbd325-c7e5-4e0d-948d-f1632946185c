package internal

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestConditionData_IsJSONSerializeable(t *testing.T) {
	expected := ConditionData{
		TabPermissions: {
			"bar": {},
		},
		PartnerAllowedFeatures: {
			"baz": {},
		},
		BrandTabPermissions: {
			"qux": {},
		},
		Products: {
			"quux": {},
		},
		Country: {
			"US": {},
		},
	}

	marshalledData, err := json.<PERSON>(expected)
	assert.NoError(t, err)
	assert.NotEmpty(t, marshalledData)

	unmarshalledData := ConditionData{}
	err = json.Unmarshal(marshalledData, &unmarshalledData)

	assert.NoError(t, err)

	assert.EqualValues(t, expected, unmarshalledData)
}
