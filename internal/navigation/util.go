package navigation

import (
	//nolint:golint
	_ "embed"
	"fmt"
	"strings"

	"github.com/vendasta/atlas/internal/crmcustomobjects"

	atlas_v1 "github.com/vendasta/generated-protos-go/atlas/v1"
	"github.com/vendasta/gosdks/verrors"
	"github.com/vendasta/gosdks/vstrings"

	"github.com/vendasta/atlas/internal/navigation/condition"
	navigationconfig "github.com/vendasta/atlas/internal/navigation/config"
	"github.com/vendasta/atlas/internal/navigation/internal"
	"github.com/vendasta/atlas/internal/navigation/internal/constants"
	"github.com/vendasta/atlas/internal/navigation/metadata"
	"github.com/vendasta/atlas/internal/product"
)

var enabledVerboseLogging = []string{"VUNI", "BRDL", "NBLY"}

func VerboseLoggingEnabled(partnerID string) bool {
	return vstrings.StringInSlice(partnerID, enabledVerboseLogging)
}

// navigationLink used to build a navigation link item
type navigationLink struct {
	// Give this link a unique ID
	NavigationID string
	// URL is redirected to if the user is SSO-ing from one product to another
	URL string
	// Path is used if the user is navigating within the same product
	Path string
	// The owner of this route
	ServiceProviderID string
	// If a LogoURL is provided, it will be displayed as a product icon next to the route
	LogoURL string
	// Material Icon string to be displayed next to the route
	Icon string
	// Corresponding translation ID specified in lang/<lang-code>.go
	TranslationID string
	// Whether we should show the external icon next to the route (arrow leaving a box)
	External bool
	// Fallback label to be used in the event that we cannot identify the correct translation
	Label string
	// Controls whether the user can pin this route.
	Pinnable bool
	// Text to be displayed in a chip next to the route.
	ChipContent string
	// Should show the user required modal
	UserRequired bool
	// A flag to indicate if we should open a new tab when clicked
	OpenInNewTab bool
}

type LinkOption func(*atlas_v1.SideNavigationItem) error

// linkDynamic creates a new SideNavigationItem which represents a route that can be navigated to
func linkDynamic(link navigationLink) *atlas_v1.SideNavigationItem {
	r := &atlas_v1.SideNavigationItem{
		Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
			SideNavigationLink: &atlas_v1.SideNavigationLink{
				NavigationId:      link.NavigationID,
				Url:               link.URL,
				Path:              link.Path,
				ServiceProviderId: link.ServiceProviderID,
				LogoUrl:           link.LogoURL,
				Icon:              link.Icon,
				TranslationId:     link.TranslationID,
				External:          link.External,
				Label:             link.Label,
				ShowIcon:          link.Icon != "" || link.LogoURL != "" || strings.HasPrefix(link.NavigationID, "product-"),
				Pinnable:          link.Pinnable,
				ChipContent:       link.ChipContent,
				UserRequired:      link.UserRequired,
				OpenInNewTab:      link.OpenInNewTab,
			},
		},
	}

	return r
}

func NewNavItemGenerator(args *GetNavigationItemsArgs) (*NavItemGenerator, error) {
	r := &NavItemGenerator{
		args:  args,
		links: metadata.New(nil),
		conditionData: internal.NewConditionData(args.Features, args.TabPermissions, args.BrandTabStatuses, args.Products, args.FeatureFlags, args.MarketID, args.Country,
			args.AccountsData, args.PlatformMode),
	}

	return r, nil
}

type NavItemGenerator struct {
	args          *GetNavigationItemsArgs
	links         *metadata.Service
	conditionData internal.ConditionData
}

func (n *NavItemGenerator) GetLink(config navigationconfig.LinkConfig, linkGetter LinkGetter, opts ...LinkOption) (*atlas_v1.SideNavigationItem, error) {
	link, ok := n.links.Get(config.NavItemID)
	if !ok {
		return nil, verrors.New(verrors.NotFound, "link not found")
	}

	evalOpts := condition.EvaluationOptions{}
	if len(n.args.TabPermissions) == 0 {
		evalOpts.IgnoreResources = append(evalOpts.IgnoreResources, internal.TabPermissions)
	}

	err := link.Policy.Evaluate(n.conditionData, evalOpts)
	if err != nil {
		return nil, verrors.New(verrors.PermissionDenied, "user does not have access to link: %s, error: %s", config.NavItemID, err)
	}

	if vstrings.StringInSlice(config.NavItemID, []string{"nav-files", "nav-notification-settings", "nav-profile-settings", "nav-billing-settings"}) && n.args.SubjectID == "" {
		link.UserRequired = true
	}

	sideNavItem, err := n.metaDataToSideNavigationLink(link)
	if err != nil {
		return nil, err
	}

	if config.Path != "" {
		sideNavItem.SideNavigationLink.Url = n.hydrateURL(link.ServiceProviderID, config.Path)
		sideNavItem.SideNavigationLink.Path = n.hydratePath(config.Path)
	}

	sublinks := getSublinks(config, linkGetter)

	if len(config.Children) > 0 && len(sublinks) == 0 {
		return nil, verrors.New(verrors.PermissionDenied, "access to all sublinks denied")
	}

	sideNavItem.SideNavigationLink.SubLinks = sublinks

	i := &atlas_v1.SideNavigationItem{
		Item: sideNavItem,
	}

	for _, opt := range opts {
		if err := opt(i); err != nil {
			return nil, err
		}
	}

	return i, nil
}

func (n *NavItemGenerator) overrideCRMCustomObjectTypeConfig(
	navItem navigationconfig.LinkConfig,
	objectType *crmcustomobjects.CRMCustomObjectType,
	opts []fetchOption,
) []fetchOption {
	fetchOptionVal := func(id string) LinkOption {
		if id == fmt.Sprintf("nav-crm-custom-objects-%s", objectType.ID) {
			return n.HydrateCRMCustomObject(objectType)
		}
		return nil
	}
	opts = append(opts, fetchOptionVal)
	return opts
}

func (n *NavItemGenerator) GetContainer(config navigationconfig.LinkConfig, linkGetter LinkGetter, opts ...LinkOption) (*atlas_v1.SideNavigationItem, error) {
	c, ok := n.links.Get(config.NavItemID)
	if !ok {
		return nil, verrors.New(verrors.NotFound, "container not found")
	}

	evalOpts := condition.EvaluationOptions{}
	if len(n.args.TabPermissions) == 0 {
		evalOpts.IgnoreResources = append(evalOpts.IgnoreResources, internal.TabPermissions)
	}

	if err := c.Policy.Evaluate(n.conditionData, evalOpts); err != nil {
		return nil, verrors.WrapError(err, "container '%s' access denied", config.NavItemID)
	}

	sideNavContainer, err := n.metaDataToSideNavigationContainer(c)
	if err != nil {
		return nil, err
	}

	if config.Path != "" {
		sideNavContainer.SideNavigationContainer.Url = n.hydrateURL(c.ServiceProviderID, config.Path)
	}

	container := &atlas_v1.SideNavigationItem{
		Item: sideNavContainer,
	}

	errors := make([]error, 0)
	for _, childConfig := range config.Children {
		childOpts := []fetchOption{}

		if childConfig.NavItemID == "nav-crm-custom-objects" {
			customObjectLinks := make([]navigationconfig.LinkConfig, 0, len(n.args.CRMCustomObjectTypes))
			for _, customObjectType := range n.args.CRMCustomObjectTypes {
				childOpts = n.overrideCRMCustomObjectTypeConfig(childConfig, customObjectType, childOpts)
				customObjectLinks = append(customObjectLinks, navigationconfig.LinkConfig{
					NavItemID: fmt.Sprintf("%s-%s", childConfig.NavItemID, customObjectType.ID),
					Type:      navigationconfig.CRMCustomObjectType,
					Path:      customObjectType.Path,
				})
			}
			childConfig.Rules = &navigationconfig.SubstitutionRules{SubstituteNavConfig: &navigationconfig.NavigationConfig{
				Items: customObjectLinks,
			}}
		}
		navItem := fetchLinksForItem(childConfig, linkGetter, childOpts...)
		if navItem != nil {
			container.GetSideNavigationContainer().SideNavigationItems = append(
				container.GetSideNavigationContainer().SideNavigationItems,
				navItem...,
			)
		}
	}

	if len(container.GetSideNavigationContainer().SideNavigationItems) == 0 {
		return nil, verrors.New(verrors.PermissionDenied, "access to all child links denied: %v", errors)
	}

	for _, opt := range opts {
		if err := opt(container); err != nil {
			return nil, err
		}
	}

	if len(container.GetSideNavigationContainer().SideNavigationItems) == 1 {
		sideNavItem := container.GetSideNavigationContainer().SideNavigationItems[0]
		if sideNavItem.GetSideNavigationLink().Icon == "" {
			sideNavItem.GetSideNavigationLink().Icon = container.GetSideNavigationContainer().Icon
		}
		sideNavItem.GetSideNavigationLink().ShowIcon = true
		return sideNavItem, nil
	}

	return container, nil
}

func (n *NavItemGenerator) GetExternalProductLink(config navigationconfig.LinkConfig, linkGetter LinkGetter, opts ...LinkOption) (*atlas_v1.SideNavigationItem, error) {
	link, err := n.GetLink(config, linkGetter)
	if err != nil && !verrors.IsError(verrors.NotFound, err) {
		return nil, verrors.WrapError(err, "Error fetching NavItem %s", config.NavItemID)
	}

	for _, c := range n.args.Products {
		if config.NavItemID == constants.InternalGetExternalProductConfigID(c.ServiceProviderID) {
			if c.EntryURL == "" || c.Name == "" {
				return nil, verrors.New(verrors.Internal, "Product not found: %s", config.NavItemID)
			}

			if link.GetSideNavigationLink() == nil {
				link = linkDynamic(navigationLink{
					NavigationID:      config.NavItemID,
					URL:               c.EntryURL,
					Path:              c.Path,
					ServiceProviderID: c.ServiceProviderID,
					LogoURL:           c.LogoURL,
					Label:             c.Name,
					Pinnable:          true,
					UserRequired:      c.RequiresUser && n.args.SubjectID == "",
					OpenInNewTab:      true,
				})
			}

			sublinks := getSublinks(config, linkGetter)

			if len(config.Children) > 0 && len(sublinks) == 0 {
				return nil, verrors.New(verrors.PermissionDenied, "access to all sublinks denied")
			}

			link.GetSideNavigationLink().SubLinks = sublinks

			for _, opt := range opts {
				if err := opt(link); err != nil {
					return nil, err
				}
			}

			return link, nil
		}
	}

	return nil, verrors.New(verrors.Internal, "Product not found: %s", config.NavItemID)
}

func (n *NavItemGenerator) GetCRMCustomObjectLink(config navigationconfig.LinkConfig, linkGetter LinkGetter, opts ...LinkOption) (*atlas_v1.SideNavigationItem, error) {
	link, err := n.GetLink(config, linkGetter)
	if err != nil && !verrors.IsError(verrors.NotFound, err) {
		return nil, verrors.WrapError(err, "Error fetching NavItem %s", config.NavItemID)
	}
	for _, c := range n.args.CRMCustomObjectTypes {
		if config.NavItemID == fmt.Sprintf("nav-crm-custom-objects-%s", c.ID) {
			if link.GetSideNavigationLink() == nil {
				link = linkDynamic(navigationLink{
					NavigationID:      config.NavItemID,
					Path:              c.Path,
					ServiceProviderID: "VBC",
					Label:             c.Name,
					Pinnable:          false,
					UserRequired:      false,
					OpenInNewTab:      false,
				})
			}
			for _, opt := range opts {
				if opt == nil {
					continue
				}
				if err := opt(link); err != nil {
					return nil, err
				}
			}

			return link, nil
		}
	}
	return nil, verrors.New(verrors.Internal, "Custom object not found: %s", config.NavItemID)
}

func (n *NavItemGenerator) hydrateURL(serviceProvider, link string) string {
	if serviceProvider == "VBC" {
		link = "<whitelabelUrl>" + link
	}

	link = strings.ReplaceAll(link, "<whitelabelUrl>", n.args.WhitelabelURL)
	link = strings.ReplaceAll(link, "<subjectId>", n.args.SubjectID)
	link = strings.ReplaceAll(link, "<accountGroupId>", n.args.AccountGroupID)
	link = strings.ReplaceAll(link, "<brandPath>", n.args.GroupPath)

	return link
}

func (n *NavItemGenerator) hydratePath(link string) string {
	link = strings.ReplaceAll(link, "<subjectId>", n.args.SubjectID)
	link = strings.ReplaceAll(link, "<accountGroupId>", n.args.AccountGroupID)
	link = strings.ReplaceAll(link, "<brandPath>", n.args.GroupPath)

	return link
}

// HydrateProductInfoOption is a workaround for the fact that most of the navigation info is still
// stored externally. Until we move all the navigation info into our own
// database, we need to hydrate the URLs and paths with the correct values we
// are given by the caller, even for configs we partially store in
// navigationconfig filestore.
func (n *NavItemGenerator) HydrateProductInfoOption(p *product.Product) LinkOption {
	return func(sni *atlas_v1.SideNavigationItem) error {
		if p == nil {
			return verrors.New(verrors.NotFound, "product not found")
		}

		// currently only custom config containers have incorrect info
		c := sni.GetSideNavigationContainer()
		if c != nil {
			if p.EntryURL != "" {
				c.Url = p.EntryURL
			}
			if p.Name != "" {
				c.Label = p.Name
				c.TranslationId = ""
			}
			if p.LogoURL != "" {
				c.LogoUrl = p.LogoURL
				c.Icon = ""
				c.ShowIcon = true
			}

			return nil
		}

		l := sni.GetSideNavigationLink()
		if l != nil {
			if p.ServiceProviderID != l.ServiceProviderId {
				l.LaunchUrl = p.EntryURL
			} else if p.EntryURL != "" {
				l.Url = p.EntryURL
			}
			if p.Name != "" {
				l.Label = p.Name
				l.TranslationId = ""
			}
			if p.LogoURL != "" {
				l.LogoUrl = p.LogoURL
			}
			if l.Path == "" && p.Path != "" {
				l.Path = p.Path
			}
			if l.ServiceProviderId == "" {
				l.ServiceProviderId = p.ServiceProviderID
			}
			l.Icon = ""
			l.ShowIcon = true
			l.Pinnable = true

			p.RequiresUser = p.RequiresUser && n.args.SubjectID == ""
			return nil
		}

		return nil
	}
}

func (n *NavItemGenerator) HydrateCRMCustomObject(c *crmcustomobjects.CRMCustomObjectType) LinkOption {
	return func(sni *atlas_v1.SideNavigationItem) error {
		if c == nil {
			return verrors.New(verrors.NotFound, "CRMCustomObject not found")
		}
		link := sni.GetSideNavigationLink()
		link.Url = n.hydrateURL("VBC", c.Path)
		link.Label = c.Name
		link.Path = c.Path
		link.ShowIcon = false
		return nil
	}
}

func (n *NavItemGenerator) SetOpenInNewTabOption(value bool) LinkOption {
	return func(sni *atlas_v1.SideNavigationItem) error {
		l := sni.GetSideNavigationLink()
		if l != nil {
			l.OpenInNewTab = value
		}
		return nil
	}
}

func (n *NavItemGenerator) metaDataToSideNavigationLink(m *metadata.MetaData) (*atlas_v1.SideNavigationItem_SideNavigationLink, error) {
	if m == nil {
		return nil, verrors.New(verrors.NotFound, "metadata not found")
	}

	return &atlas_v1.SideNavigationItem_SideNavigationLink{
		SideNavigationLink: &atlas_v1.SideNavigationLink{
			NavigationId:             m.NavigationID,
			Url:                      m.URL,
			ServiceProviderId:        m.ServiceProviderID,
			LogoUrl:                  m.LogoURL,
			Icon:                     m.Icon,
			TranslationId:            m.TranslationID,
			DescriptionTranslationId: m.DescriptionTranslationID,
			External:                 m.External,
			Label:                    m.Label,
			ShowIcon:                 m.ShowIcon,
			Pinnable:                 m.Pinnable,
			ChipContent:              m.ChipContent,
			UserRequired:             m.UserRequired,
			OpenInNewTab:             m.OpenInNewTab,
		},
	}, nil
}

func (n *NavItemGenerator) metaDataToSideNavigationContainer(m *metadata.MetaData) (*atlas_v1.SideNavigationItem_SideNavigationContainer, error) {
	if m == nil {
		return nil, verrors.New(verrors.NotFound, "metadata not found")
	}

	return &atlas_v1.SideNavigationItem_SideNavigationContainer{
		SideNavigationContainer: &atlas_v1.SideNavigationContainer{
			Url:           m.URL,
			LogoUrl:       m.LogoURL,
			Label:         m.Label,
			TranslationId: m.TranslationID,
			Icon:          m.Icon,
			ShowIcon:      m.ShowIcon,
			Pinnable:      m.Pinnable,
		},
	}, nil
}

// Sublinks are pages that are subrouted through a parent page, the parent page
// is expected to have some on-page method for navigating to each sublink. The
// difference between this and the Container type is that the Container type
// children are expected to be rendered in the sidenav somehow (usually as a
// dropdown). A container can contain a link with sublinks, but a link cannot
// contain a container with children.
func getSublinks(config navigationconfig.LinkConfig, linkGetter LinkGetter) []*atlas_v1.SideNavigationLink {
	if len(config.Children) == 0 {
		return nil
	}

	sublinks := make([]*atlas_v1.SideNavigationLink, 0, len(config.Children))
	for _, childConfig := range config.Children {
		childItems := fetchLinksForItem(childConfig, linkGetter)
		for _, childItem := range childItems {
			sublinks = append(sublinks, childItem.GetSideNavigationLink())
		}
	}

	return sublinks
}
