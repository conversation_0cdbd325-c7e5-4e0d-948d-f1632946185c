package metadata

import (
	"encoding/json"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestFilestore_IsSerializeable(t *testing.T) {
	marshalled, err := json.<PERSON>(allMetaData)
	assert.NoError(t, err)
	assert.NotEmpty(t, marshalled)

	unmarshalled := map[string]MetaData{}
	err = json.Unmarshal(marshalled, &unmarshalled)
	assert.NoError(t, err)

	assert.EqualValues(t, allMetaData, unmarshalled)
}
