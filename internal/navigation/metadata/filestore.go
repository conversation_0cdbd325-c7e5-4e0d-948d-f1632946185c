package metadata

import (
	"github.com/vendasta/gosdks/verrors"

	featureflags "github.com/vendasta/atlas/internal/feature_flags"
	"github.com/vendasta/atlas/internal/navigation/condition"
	"github.com/vendasta/atlas/internal/navigation/internal"
	"github.com/vendasta/atlas/internal/navigation/internal/constants"
)

type Store struct {
	metaData map[string]MetaData
}

func newStore() *Store {
	s := &Store{
		metaData: allMetaData,
	}
	return s
}

func (s *Store) Get(id string) (*MetaData, error) {
	if metaData, ok := s.metaData[id]; ok {
		return &metaData, nil
	}
	return nil, verrors.New(verrors.NotFound, "metadata not found for id: %s", id)
}

func (s *Store) GetMulti(ids []string) ([]*MetaData, error) {
	metaData := []*MetaData{}
	for _, id := range ids {
		if data, ok := s.metaData[id]; ok {
			metaData = append(metaData, &data)
		}
	}
	return metaData, nil
}

var allMetaData = map[string]MetaData{
	"nav-billing-settings": {
		NavigationID:             "nav-billing-settings",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.BILLING_SETTINGS",
		DescriptionTranslationID: "NAVIGATION.TABS.BILLING_SETTINGS_DESCRIPTION",
		Label:                    "Billing Settings",
		Icon:                     "receipt",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.Invoices,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigInvoicesFeature,
					},
				},
			},
		},
	},
	"nav-brand-advertising": {
		NavigationID:      "nav-brand-advertising",
		ServiceProviderID: "VBC",
		Icon:              "public",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.ADVERTISING",
		Label:             "Advertising",
		ShowIcon:          true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.BrandTabPermissions,
					Select: []string{
						constants.AdvertisingTab,
					},
				},
			},
			Conditions: []condition.Condition{
				{
					Operator: condition.Not,
					Queries: []condition.Query{
						{
							From: internal.PlatformMode,
							Select: []string{
								constants.MobileMode,
							},
						},
					},
				},
			},
		},
	},
	"nav-advertising-overview": {
		NavigationID:      "nav-brand-advertising",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
		Label:             "Overview",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.Products,
					Select: []string{
						constants.Advertising,
					},
				},
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.MyProducts,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigMyProductsFeature,
					},
				},
			},
			Conditions: []condition.Condition{
				{
					Operator: condition.Not,
					Queries: []condition.Query{
						{
							From: internal.PlatformMode,
							Select: []string{
								constants.MobileMode,
							},
						},
					},
				},
			},
		},
	},
	"nav-brand-home": {
		NavigationID:      "nav-brand-home",
		ServiceProviderID: "VBC",
		Icon:              "home",
		TranslationID:     "NAVIGATION.TABS.HOME",
		Label:             "Home",
		ShowIcon:          true,
	},
	"nav-brand-data-export": {
		NavigationID:      "nav-brand-data-export",
		ServiceProviderID: "VBC",
		Icon:              "unarchive",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.DATA_EXPORT",
		Label:             "Data Exporter",
		ShowIcon:          true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.BrandTabPermissions,
					Select: []string{
						constants.DataExportTab,
					},
				},
			},
			Conditions: []condition.Condition{
				{
					Operator: condition.Not,
					Queries: []condition.Query{
						{
							From: internal.PlatformMode,
							Select: []string{
								constants.MobileMode,
							},
						},
					},
				},
			},
		},
	},
	"nav-data-export": {
		NavigationID:             "nav-brand-data-export",
		ServiceProviderID:        "VBC",
		Icon:                     "unarchive",
		TranslationID:            "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.DATA_EXPORT",
		DescriptionTranslationID: "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.DATA_EXPORT_DESCRIPTION",
		Label:                    "Data Exporter",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.PartnerFeatureIDSingleNav,
					},
				},
			},
			Conditions: []condition.Condition{
				{
					Operator: condition.Not,
					Queries: []condition.Query{
						{
							From: internal.PlatformMode,
							Select: []string{
								constants.MobileMode,
							},
						},
					},
				},
			},
		},
	},
	"nav-brand-google-my-business-overview": {
		NavigationID:      "nav-brand-google-my-business",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
		Label:             "Overview",
	},
	"nav-brand-google-my-business": {
		NavigationID:      "nav-brand-google-my-business",
		ServiceProviderID: "VBC",
		Icon:              "store",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_MY_BUSINESS",
		Label:             "Google My Business",
		ShowIcon:          true,
	},
	"nav-google-my-business": {
		NavigationID:      "nav-brand-google-my-business",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
		Label:             "Overview",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.PartnerFeatureIDSingleNav,
					},
				},
			},
			Conditions: []condition.Condition{
				{
					Operator: condition.Not,
					Queries: []condition.Query{
						{
							From: internal.PlatformMode,
							Select: []string{
								constants.MobileMode,
							},
						},
					},
				},
			},
		},
	},
	"nav-brand-analytics": {
		NavigationID:      "nav-brand-analytics",
		ServiceProviderID: "VBC",
		Icon:              "store",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GMBBING",
		Label:             "Analytics",
	},
	"nav-brand-analytics-google": {
		NavigationID:      "nav-brand-analytics-google",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
		Label:             "Google",
	},
	"nav-brand-analytics-bing": {
		NavigationID:      "nav-brand-analytics-bing",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.BING_ANALYTICS",
		Label:             "Bing",
		Policy:            embeddedAnalyticsPolicy,
	},
	"nav-brand-google-my-business-google-q-and-a": {
		NavigationID:      "nav-brand-google-my-business-google-q-and-a",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
		Label:             "Google Q\u0026A",
	},
	"nav-brand-google-my-business-google-q-and-a-sublink": {
		NavigationID:      "nav-brand-google-my-business-google-q-and-a",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
		Label:             "Google Q\u0026A",
		Policy: &condition.Condition{
			Operator: condition.And,
			Queries: []condition.Query{
				{
					From: internal.BrandTabPermissions,
					Select: []string{
						constants.ReviewsTab,
					},
				},
			},
		},
	},
	"nav-google-my-business-google-q-and-a": {
		NavigationID:      "nav-google-my-business-google-q-and-a",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
		Label:             "Google Q\u0026A",
		Policy:            MLRMPolicy,
	},
	"nav-google-my-business-google-q-and-a-sublink": {
		NavigationID:      "nav-google-my-business-google-q-and-a",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
		Label:             "Google Q\u0026A",
		Policy:            embeddedRMPolicy,
	},
	"nav-embedded-listings": {
		NavigationID:      "nav-embedded-listings",
		ServiceProviderID: "VBC",
		Icon:              "store",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.ALL_LISTINGS",
		Label:             "All Listings",
		ShowIcon:          true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.PartnerFeatureIDEmbeddedListings,
					},
				},
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.MyProducts,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigMyProductsFeature,
					},
				},
				{
					From: internal.BrandTabPermissions,
					Select: []string{
						constants.ListingsTab,
					},
				},
			},
		},
	},
	"nav-embedded-listing": {
		NavigationID:      "nav-embedded-listings",
		ServiceProviderID: "VBC",
		Icon:              "store",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.LISTINGS",
		Label:             "Listings",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.PartnerFeatureIDEmbeddedListings,
					},
				},
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.MyProducts,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigMyProductsFeature,
					},
				},
			},
		},
	},
	"nav-brand-listings-manage": {
		NavigationID:      "nav-brand-listings-manage",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.MANAGE",
		Label:             "Manage",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.BrandTabPermissions,
					Select: []string{
						constants.ListingsTab,
					},
				},
			},
		},
	},
	"nav-listings-keyword-tracking": {
		NavigationID:      "nav-brand-listings-keyword-tracking",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.KEYWORD_TRACKING",
		Label:             "Keyword Tracking",
		Policy:            embeddedListingsPolicy,
	},
	"nav-embedded-keyword-tracking": {
		NavigationID:      "nav-embedded-keyword-tracking",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.KEYWORD_TRACKING",
		Label:             "Keyword Tracking",
		Icon:              "trending_up",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.PartnerFeatureIDEmbeddedListings,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigMyProductsFeature,
					},
				},
			},
		},
	},
	"nav-brand-keyword-tracking-overview": {
		NavigationID:      "nav-brand-keyword-tracking-overview",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.KEYWORD_TRACKING_OVERVIEW",
		Label:             "Overview",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.KeywordTrackingOverviewFeature,
					},
				},
			},
		},
	},
	"nav-brand-keyword-tracking-keyword": {
		NavigationID:      "nav-brand-keyword-tracking-keyword",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.KEYWORD_TRACKING_KEYWORD",
		Label:             "Keywords",
	},
	"nav-listings-listing-sync": {
		NavigationID:      "nav-brand-listing-sync",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.LISTING_SYNC",
		Label:             "Listing Sync",
		Policy:            embeddedListingsPolicy,
	},
	"nav-brand-listings-overview": {
		NavigationID:      "nav-brand-listings-overview",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
		Label:             "Overview",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.BrandTabPermissions,
					Select: []string{
						constants.ListingsTab,
					},
				},
			},
		},
	},
	"nav-brand-report": {
		NavigationID:      "nav-brand-report",
		ServiceProviderID: "VBC",
		Icon:              "timeline",
		TranslationID:     "NAVIGATION.TABS.MULTI_LOCATION_REPORT",
		Label:             "Executive Report",
		ShowIcon:          true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.BrandTabPermissions,
					Select: []string{
						constants.ReportTab,
					},
				},
			},
		},
	},
	"nav-brand-reputation-insights": {
		NavigationID:      "nav-brand-reputation-insights",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS",
		Label:             "Insights",
		Policy:            reviewsTabEnabledPolicy,
	},
	"nav-reputation-insights": {
		NavigationID:      "nav-reputation-insights",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS",
		Label:             "Insights",
		Policy:            embeddedRMPolicy,
	},
	"nav-reputation-nps": {
		NavigationID:      "nav-reputation-nps",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.NPS",
		Label:             "NPS",
		Policy:            embeddedNPSBusinessAppPolicy,
	},
	"nav-reputation": {
		NavigationID:      constants.InternalGetExternalProductConfigID("RM"),
		Label:             "Reputation",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REPUTATION",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.Products,
					Select: []string{
						"RM",
					},
				},
			},
		},
	},
	"nav-brand-reputation-manage": {
		NavigationID:      "nav-brand-reputation-manage",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REVIEWS",
		Label:             "Reviews",
		Policy:            reviewsTabEnabledPolicy,
	},
	"nav-brand-crm-tasks": {
		NavigationID:      "nav-brand-crm-tasks",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
		Label:             "Tasks",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.CRMMultilocation,
					},
				},
			},
		},
	},
	"nav-brand-crm-contacts": {
		NavigationID:      "nav-brand-crm-contacts",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
		Label:             "Contacts",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.CRMMultilocation,
					},
				},
			},
		},
	},
	"nav-brand-reputation-reviews": {
		NavigationID:      "nav-brand-reputation-reviews",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REVIEWS",
		Label:             "Reviews",
		Policy:            reviewsTabEnabledPolicy,
	},
	"nav-reputation-manage": {
		NavigationID:      "nav-reputation-manage",
		ServiceProviderID: "VBC",
		TranslationID:     "NAVIGATION.TABS.REVIEWS",
		Label:             "Reviews",
		Policy:            embeddedRMPolicy,
	},
	"nav-standalone-reputation-manage": {
		NavigationID:      "nav-brand-reputation-manage",
		ServiceProviderID: "VBC",
		TranslationID:     "NAVIGATION.TABS.REVIEWS",
		Label:             "Reviews",
		Icon:              "reviews",
		Policy:            MLRMPolicy,
	},
	"nav-brand-reputation-nps": {
		NavigationID:      "nav-brand-reputation-nps",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.NPS",
		Label:             "NPS",
		Policy:            npsTabEnabledPolicy,
	},
	"nav-brand-reputation-nps-overview": {
		NavigationID:      "nav-brand-reputation-nps-overview",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
		Label:             "Overview",
		Policy:            npsTabEnabledPolicy,
	},
	"nav-brand-reputation-net_promoter_score": {
		NavigationID:      "nav-brand-reputation-net_promoter_score",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.NET_PROMOTER_SCORE",
		Label:             "Net Promoter Score",
		Policy:            npsTabEnabledPolicy,
	},
	"nav-brand-reputation-nps-team": {
		NavigationID:      "nav-brand-reputation-nps-team",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TEAM",
		Label:             "Team",
	},
	"nav-brand-reputation-requests": {
		NavigationID:      "nav-brand-reputation-requests",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REQUESTS",
		Label:             "Requests",
	},
	"nav-brand-reputation-requests-overview": {
		NavigationID:      "nav-brand-reputation-requests-overview",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
		Label:             "Overview",
	},
	"nav-brand-reputation-overview": {
		NavigationID:      "nav-brand-reputation-overview",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
		Label:             "Overview",
		Policy:            reviewsTabEnabledPolicy,
	},
	"nav-reputation-overview": {
		NavigationID:      "nav-brand-reputation-overview",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
		Label:             "Overview",
		Policy:            MLRMPolicy,
	},
	"nav-brand-social": {
		NavigationID:      "nav-brand-social",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
		Label:             "Overview",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.BrandTabPermissions,
					Select: []string{
						constants.SocialTab,
					},
				},
			},
		},
	},
	"nav-social": {
		NavigationID:      "nav-brand-social",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
		Label:             "Overview",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.Products,
					Select: []string{
						"SM",
					},
				},
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.MyProducts,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigMyProductsFeature,
					},
				},
			},
		},
	},
	"nav-social-marketing": {
		NavigationID:      constants.InternalGetExternalProductConfigID("SM"),
		Label:             "Social Marketing",
		Pinnable:          true,
		ShowIcon:          true,
		ServiceProviderID: "VBC",
		Policy: &condition.Condition{
			Conditions: []condition.Condition{
				*embeddedSocialPolicy,
			},
		},
	},
	"nav-brand-social-manage-posts": {
		NavigationID:      "nav-brand-social-manage-posts",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.MANAGE_POSTS",
		Label:             "Manage Posts",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.BrandTabPermissions,
					Select: []string{
						constants.SocialTab,
					},
				},
			},
		},
	},
	"nav-brand-crm": {
		NavigationID:      "nav-brand-crm",
		ServiceProviderID: "VBC",
		Label:             "Contacts",
		Icon:              "contacts",
		TranslationID:     "NAVIGATION.TABS.CRM.CRM",
		ShowIcon:          true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.CRMMultilocation,
					},
				},
			},
		},
	},
	"crm": {
		NavigationID:  "crm",
		Label:         "Contacts",
		Icon:          "contacts",
		TranslationID: "NAVIGATION.TABS.CRM.CRM",
		ShowIcon:      true,
	},
	"nav-crm-companies": {
		NavigationID:      "nav-crm-companies",
		ServiceProviderID: "VBC",
		Icon:              "location_city",
		TranslationID:     "NAVIGATION.TABS.CRM.COMPANY",
		Label:             "Companies",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						// TODO - determine the correct tab permission
						constants.CustomerList,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigCRMCompaniesFeature,
					},
				},
			},
		},
	},
	"nav-crm-activities": {
		NavigationID:      "nav-crm-activities",
		ServiceProviderID: "VBC",
		Icon:              "feed",
		TranslationID:     "NAVIGATION.TABS.CRM.ACTIVITY_FEED",
		Label:             "Activity feed",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						// TODO - determine the correct tab permission
						constants.CustomerList,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigCustomersFeature,
					},
				},
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.PartnerFeatureIDSalesFeatures,
					},
				},
			},
		},
	},
	"nav-crm": {
		NavigationID:      "nav-crm",
		ServiceProviderID: "VBC",
		Icon:              "person",
		TranslationID:     "NAVIGATION.TABS.CRM.CONTACT",
		Label:             "Contacts",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.CustomerList,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigCustomersFeature,
					},
				},
			},
		},
	},
	"nav-crm-tasks": {
		NavigationID:      "nav-crm-tasks",
		ServiceProviderID: "VBC",
		Icon:              "assignment_turned_in",
		TranslationID:     "NAVIGATION.TABS.CRM.SALES_TASKS",
		Label:             "Sales tasks",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						// TODO - determine the correct tab permission
						constants.CustomerList,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigCRMTasksFeature,
					},
				},
			},
		},
	},
	"nav-crm-opportunities": {
		NavigationID:      "nav-crm-opportunities",
		ServiceProviderID: "VBC",
		Icon:              "monetization_on",
		TranslationID:     "NAVIGATION.TABS.CRM.OPPORTUNITIES",
		Label:             "Opportunities",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						// TODO - determine the correct tab permission
						constants.CustomerList,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigCRMOpportunitiesFeature,
					},
				},
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.PartnerFeatureIDCRMOpportunity,
					},
				},
			},
		},
	},
	"nav-dynamic-lists": {
		NavigationID:      "nav-dynamic-lists",
		ServiceProviderID: "VBC",
		Icon:              "reorder",
		TranslationID:     "NAVIGATION.TABS.DYNAMIC_LISTS",
		Label:             "Lists",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						// TODO - determine the correct tab permission
						constants.CustomerList,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigDynamicListsFeature,
					},
				},
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.PartnerFeatureIDDynamicLists,
					},
				},
			},
		},
	},
	"nav-leaderboard": {
		NavigationID:      "nav-leaderboard",
		ServiceProviderID: "VBC",
		Icon:              "leaderboard",
		TranslationID:     "NAVIGATION.TABS.LEADERBOARD",
		Label:             "Leaderboard",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.CustomerList,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigLeaderboardFeature,
					},
				},
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.PartnerFeatureIDSalesFeatures,
					},
				},
			},
		},
	},
	"nav-custom-forms": {
		NavigationID:      "nav-custom-forms",
		ServiceProviderID: "VBC",
		Icon:              "description",
		TranslationID:     "NAVIGATION.TABS.CUSTOM_FORMS",
		Label:             "Forms",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						//TODO - use the custom forms tab permission
						constants.CustomerList,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigCustomFormsFeature,
					},
				},
			},
		},
	},
	"nav-crm-meeting-scheduler": {
		NavigationID:      "nav-meeting-scheduler",
		ServiceProviderID: "VBC",
		Icon:              "event",
		TranslationID:     "NAVIGATION.TABS.MEETING_SCHEDULER",
		Label:             "My Meetings",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.MeetingScheduler,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigMeetingSchedulerFeature,
					},
				},
			},
		},
	},
	"nav-home": {
		NavigationID:      "nav-home",
		ServiceProviderID: "VBC",
		Icon:              "home",
		TranslationID:     "NAVIGATION.TABS.HOME",
		Label:             "Home",
		ShowIcon:          true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.Dashboard,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigDashboardFeature,
					},
				},
			},
		},
	},
	"nav-all-campaigns": {
		NavigationID:      "nav-all-campaigns",
		ServiceProviderID: "VBC",
		Label:             "Campaigns",
		Icon:              "mark_email_read",
		TranslationID:     "NAVIGATION.TABS.CAMPAIGNS",
		ShowIcon:          true,
		Policy: &condition.Condition{
			Conditions: []condition.Condition{
				{
					Operator: condition.Or,
					Queries: []condition.Query{
						{
							From: internal.Products,
							Select: []string{
								constants.CampaignsDemo,
							},
						},
						{
							From: internal.Products,
							Select: []string{
								constants.CampaignsProd,
							},
						},
					},
				},
			},
		},
	},
	"nav-email-configuration": {
		NavigationID:             "nav-email-configuration",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.EMAIL_CONFIGURATION",
		DescriptionTranslationID: "NAVIGATION.TABS.EMAIL_CONFIGURATION_DESCRIPTION",
		Label:                    "Email Configuration",
		Icon:                     "email",
	},
	"nav-sms-configuration": {
		NavigationID:             "nav-sms-configuration",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.SMS_CONFIGURATION",
		DescriptionTranslationID: "NAVIGATION.TABS.SMS_CONFIGURATION_DESCRIPTION",
		Label:                    "SMS Configuration",
		Icon:                     "phone_iphone",
		Policy: &condition.Condition{
			Operator: condition.And,
			Conditions: []condition.Condition{
				{
					Operator: condition.And,
					Queries: []condition.Query{
						{
							From: internal.FeatureFlags,
							Select: []string{
								constants.CampaignsSMS,
							},
						},
						{
							From: internal.Country,
							Select: []string{
								constants.CountryUS,
							},
						},
					},
				}, {
					Operator: condition.Or,
					Queries: []condition.Query{
						{
							From: internal.Products,
							Select: []string{
								constants.CampaignsDemo,
							},
						},
						{
							From: internal.Products,
							Select: []string{
								constants.CampaignsProd,
							},
						},
						{
							From: internal.Editions,
							Select: []string{
								constants.RMPremiumDemo,
							},
						},
						{
							From: internal.Editions,
							Select: []string{
								constants.RMPremiumProd,
							},
						},
					},
				},
			},
		},
	},
	"nav-emails": {
		NavigationID:             "nav-emails",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.EMAIL_HISTORY",
		DescriptionTranslationID: "NAVIGATION.TABS.EMAIL_HISTORY_DESCRIPTION",
		Label:                    "Email History",
		Icon:                     "history",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.PartnerFeatureIDBusinessEmails,
					},
				},
			},
			Conditions: []condition.Condition{
				{
					Operator: condition.Or,
					Queries: []condition.Query{
						{
							From: internal.Products,
							Select: []string{
								constants.CustomerVoiceDemo,
							},
						},
						{
							From: internal.Products,
							Select: []string{
								constants.CustomerVoiceProd,
							},
						},
						{
							From: internal.Products,
							Select: []string{
								constants.WebsiteDemo,
							},
						},
						{
							From: internal.Products,
							Select: []string{
								constants.WebsiteProd,
							},
						},
					},
				},
			},
		},
	},
	"nav-executive-report": {
		NavigationID:      "nav-executive-report",
		ServiceProviderID: "VBC",
		Icon:              "timeline",
		TranslationID:     "NAVIGATION.TABS.EXECUTIVE_REPORT",
		Label:             "Executive Report",
		ShowIcon:          true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.ExecutiveReport,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigExecutiveReportFeature,
					},
				},
			},
		},
	},
	"nav-files": {
		NavigationID:             "nav-files",
		ServiceProviderID:        "VBC",
		Icon:                     "insert_drive_file",
		TranslationID:            "NAVIGATION.TABS.FILES",
		DescriptionTranslationID: "NAVIGATION.TABS.FILES_DESCRIPTION",
		Label:                    "Files",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.Files,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigFilesFeature,
					},
				},
			},
		},
	},
	"automations": {
		NavigationID:  "automations",
		Label:         "Automations",
		Icon:          "offline_bolt",
		TranslationID: "NAVIGATION.TABS.AUTOMATIONS",
		ShowIcon:      true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigAutomationsFeature,
					},
				},
			},
		},
	},
	"nav-automations": {
		NavigationID:      "nav-automations",
		ServiceProviderID: "VBC",
		TranslationID:     "NAVIGATION.TABS.SYSTEM_AUTOMATIONS",
		Label:             "System Automations",
	},
	"nav-automations-mine": {
		NavigationID:             "nav-automations-mine",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.AUTOMATIONS",
		DescriptionTranslationID: "NAVIGATION.TABS.AUTOMATIONS_DESCRIPTION",
		Label:                    "Automations",
		Icon:                     "offline_bolt",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigAutomationsFeature,
					},
				},
			},
		},
	},
	"nav-automations-mine-admin": {
		NavigationID:             "nav-automations-mine",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.AUTOMATIONS",
		DescriptionTranslationID: "NAVIGATION.TABS.AUTOMATIONS_DESCRIPTION",
		Label:                    "Automations",
		Icon:                     "offline_bolt",
		ShowIcon:                 true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigAutomationsFeature,
					},
				},
			},
		},
	},
	"nav-automations-templates": {
		NavigationID:      "nav-automations-templates",
		ServiceProviderID: "VBC",
		TranslationID:     "NAVIGATION.TABS.AUTOMATION_TEMPLATES",
		Label:             "Templates",
	},
	"nav-fulfillment": {
		NavigationID:             "nav-fulfillment",
		ServiceProviderID:        "VBC",
		Icon:                     "timelapse",
		TranslationID:            "NAVIGATION.TABS.FULFILLMENT",
		DescriptionTranslationID: "NAVIGATION.TABS.FULFILLMENT_DESCRIPTION",
		Label:                    "Projects",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.Fulfillment,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigFulfillmentFeature,
					},
				},
			},
		},
	},
	"nav-guides": {
		NavigationID:             "nav-guides",
		ServiceProviderID:        "VBC",
		Icon:                     "library_books",
		TranslationID:            "NAVIGATION.TABS.GUIDES",
		DescriptionTranslationID: "NAVIGATION.TABS.GUIDES_DESCRIPTION",
		Label:                    "Guides",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.ContentLibrary,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigGuidesFeature,
					},
				},
			},
		},
	},
	"nav-payment-settings": {
		NavigationID:             "nav-payment-settings",
		ServiceProviderID:        "VBC",
		Icon:                     "credit_card",
		TranslationID:            "NAVIGATION.TABS.PAYMENT_SETTINGS",
		DescriptionTranslationID: "NAVIGATION.TABS.PAYMENT_SETTINGS_DESCRIPTION",
		Label:                    "Payment Settings",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.SMBPayments,
					},
				},
			},
		},
	},
	"payments": {
		NavigationID:  "payments",
		Label:         "Payments",
		Icon:          "wallet",
		TranslationID: "NAVIGATION.TABS.PAYMENTS",
		ShowIcon:      true,
	},
	"nav-payments-invoices": {
		NavigationID:      "nav-payments-invoices",
		ServiceProviderID: "VBC",
		TranslationID:     "NAVIGATION.TABS.PAYMENTS.INVOICES",
		Label:             "Invoices",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.SMBPayments,
					},
				},
			},
		},
	},
	"nav-payments-payments": {
		NavigationID:      "nav-payments-payments",
		ServiceProviderID: "VBC",
		TranslationID:     "NAVIGATION.TABS.PAYMENTS.PAYMENTS",
		Label:             "Payments",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.SMBPayments,
					},
				},
			},
		},
	},
	"nav-payments-payouts": {
		NavigationID:      "nav-payments-payouts",
		ServiceProviderID: "VBC",
		TranslationID:     "NAVIGATION.TABS.PAYMENTS.PAYOUTS",
		Label:             "Payouts",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.SMBPayments,
					},
				},
			},
		},
	},
	"nav-inbox": {
		NavigationID:      "nav-inbox",
		ServiceProviderID: "VBC",
		Icon:              "question_answer",
		TranslationID:     "NAVIGATION.TABS.INBOX",
		Label:             "Inbox Messages",
		ShowIcon:          true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.NewInbox,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigInboxFeature,
					},
				},
			},
		},
	},
	"nav-brand-inbox": {
		NavigationID:      "nav-brand-inbox",
		ServiceProviderID: "VBC",
		Icon:              "question_answer",
		TranslationID:     "NAVIGATION.TABS.INBOX",
		Label:             "Inbox Messages",
		ShowIcon:          true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigInboxFeature,
					},
				},
			},
		},
	},
	"nav-inbox-settings": {
		NavigationID:             "nav-inbox-settings",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.INBOX_SETTINGS",
		DescriptionTranslationID: "NAVIGATION.TABS.INBOX_SETTINGS_DESCRIPTION",
		Label:                    "Inbox",
		Icon:                     "question_answer",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.NewInbox,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigInboxFeature,
					},
				},
			},
		},
	},
	"nav-ai-knowledge-settings": {
		NavigationID:             "nav-ai-knowledge-settings",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS",
		DescriptionTranslationID: "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS_DESCRIPTION",
		Label:                    "AI knowledge base",
		Icon:                     "menu_book",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.NewInbox,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigInboxFeature,
					},
				},
			},
		},
	},
	"ai": {
		NavigationID:  "ai",
		Label:         "AI",
		Icon:          "auto_awesome",
		TranslationID: "NAVIGATION.TABS.AI",
		ShowIcon:      true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigAIAssistantFeature,
					},
				},
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.AIAssistants,
					},
				},
				{
					From: internal.FeatureFlags,
					Select: []string{
						featureflags.GoalsForGroupFeatureID,
					},
				},
			},
		},
	},
	"nav-ai-assistants": {
		NavigationID:      "nav-ai-assistants",
		ServiceProviderID: "VBC",
		TranslationID:     "NAVIGATION.TABS.AI_ASSISTANTS",
		Label:             "AI workforce",
		Icon:              "auto_awesome",
	},
	"nav-ai-assistants-knowledge": {
		NavigationID:      "nav-ai-assistants-knowledge",
		ServiceProviderID: "VBC",
		TranslationID:     "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS",
		Label:             "AI knowledge base",
		Icon:              "menu_book",
	},
	"nav-ai-assistants-settings": {
		NavigationID:             "nav-ai-assistants-settings",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.AI_ASSISTANTS",
		DescriptionTranslationID: "NAVIGATION.TABS.AI_ASSISTANTS_SETTINGS_DESCRIPTION",
		Label:                    "AI workforce",
		Icon:                     "smart_toy",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigAIAssistantFeature,
					},
				},
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.AIAssistants,
					},
				},
				{
					From: internal.FeatureFlags,
					Select: []string{
						featureflags.GoalsForGroupFeatureID,
					},
				},
			},
		},
	},
	"nav-lead-scoring": {
		NavigationID:             "nav-lead-scoring",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.LEAD_SCORING",
		DescriptionTranslationID: "NAVIGATION.TABS.LEAD_SCORING_DESCRIPTION",
		Label:                    "Score",
		Icon:                     "calculate",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						// TODO - determine the correct tab permission
						constants.CustomerList,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigLeadScoringFeature,
					},
				},
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.PartnerFeatureIDSalesFeatures,
					},
				},
			},
		},
	},
	"nav-field-management-settings": {
		NavigationID:             "nav-field-management-settings",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.FIELD_MANAGEMENT_SETTINGS",
		DescriptionTranslationID: "NAVIGATION.TABS.FIELD_MANAGEMENT_SETTINGS_DESCRIPTION",
		Label:                    "CRM fields",
		Icon:                     "wysiwyg",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigCustomersFeature,
					},
				},
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.PartnerFeatureIDSalesFeatures,
					},
				},
			},
		},
		ChipContent: "Beta",
	},
	"nav-invoices": {
		NavigationID:             "nav-invoices",
		ServiceProviderID:        "VBC",
		Icon:                     "attach_money",
		TranslationID:            "NAVIGATION.TABS.INVOICES",
		DescriptionTranslationID: "NAVIGATION.TABS.INVOICES_DESCRIPTION",
		Label:                    "Invoices",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.Invoices,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigInvoicesFeature,
					},
				},
			},
		},
	},
	"nav-meeting-scheduler": {
		NavigationID:      "nav-meeting-scheduler",
		ServiceProviderID: "VBC",
		Icon:              "event",
		TranslationID:     "NAVIGATION.TABS.MEETING_SCHEDULER",
		Label:             "My Meetings",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.MeetingScheduler,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigMeetingSchedulerFeature,
					},
				},
			},
		},
	},
	"nav-notification-settings": {
		NavigationID:             "nav-notification-settings",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
		DescriptionTranslationID: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
		Label:                    "Notification Settings",
		Icon:                     "notifications",
	},
	"nav-profile-settings": {
		NavigationID:             "nav-profile-settings",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
		DescriptionTranslationID: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
		Label:                    "Notification Settings",
		Icon:                     "notifications",
	},
	"nav-orders": {
		NavigationID:             "nav-orders",
		ServiceProviderID:        "VBC",
		Icon:                     "list_alt",
		TranslationID:            "NAVIGATION.TABS.ORDERS",
		DescriptionTranslationID: "NAVIGATION.TABS.ORDERS_DESCRIPTION",
		Label:                    "Orders",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.Orders,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigOrderPageFeature,
					},
				},
			},
		},
	},
	"nav-products": {
		NavigationID:      "nav-products",
		ServiceProviderID: "VBC",
		Icon:              "apps",
		TranslationID:     "NAVIGATION.TABS.MY_PRODUCTS",
		Label:             "My Products",
		ShowIcon:          true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.MyProducts,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigMyProductsFeature,
					},
				},
			},
		},
	},
	"nav-settings": {
		NavigationID:      "nav-settings",
		ServiceProviderID: "VBC",
		Icon:              "store_outlined",
		TranslationID:     "NAVIGATION.TABS.BUSINESS_PROFILE",
		Label:             "Business Profile",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.BusinessProfile,
					},
				},
			},
		},
	},
	"nav-business-profile": {
		NavigationID:             "nav-settings",
		ServiceProviderID:        "VBC",
		Icon:                     "store_outlined",
		TranslationID:            "NAVIGATION.TABS.BUSINESS_PROFILE",
		DescriptionTranslationID: "NAVIGATION.TABS.BUSINESS_PROFILE_DESCRIPTION",
		Label:                    "Business Profile",
		ShowIcon:                 true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.BusinessProfile,
					},
				},
			},
		},
	},
	"nav-social-connections": {
		NavigationID:             "nav-social-connections",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
		DescriptionTranslationID: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
		Label:                    "Social Connections",
		Icon:                     "settings_input_component",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.SocialConnections,
					},
				},
			},
		},
	},
	"nav-integrations": {
		NavigationID:             "nav-integrations",
		ServiceProviderID:        "VBC",
		TranslationID:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
		DescriptionTranslationID: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
		Label:                    "Social Connections",
		Icon:                     "settings_input_component",
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.RouteToNewIntegrations,
					},
				},
			},
		},
	},
	"nav-store": {
		NavigationID:      "nav-store",
		ServiceProviderID: "VBC",
		Icon:              "shopping_basket",
		TranslationID:     "NAVIGATION.TABS.STORE",
		Label:             "Store",
		ShowIcon:          true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.TabPermissions,
					Select: []string{
						constants.Store,
					},
				},
				{
					From: internal.PartnerAllowedFeatures,
					Select: []string{
						constants.UserConfigMarketplaceFeature,
					},
				},
			},
		},
	},
	"nav-listings": {
		NavigationID:      constants.InternalGetExternalProductConfigID("MS"),
		ServiceProviderID: "VBC",
		Label:             "Listings",
		Icon:              "location_on",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.LISTINGS",
		ShowIcon:          true,
		Policy:            embeddedListingsPolicy,
	},
	"nav-adintel": {
		ServiceProviderID: "VBC",
		NavigationID:      constants.InternalGetExternalProductConfigID(constants.Advertising),
		Label:             "Advertising Intelligence",
		ShowIcon:          true,
		Policy:            embeddedAdvertisingPolicy,
	},
	constants.InternalGetExternalProductConfigID(constants.CustomerVoiceProd): {
		ServiceProviderID: constants.CustomerVoiceProd,
		NavigationID:      constants.InternalGetExternalProductConfigID(constants.CustomerVoiceProd),
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.Products,
					Select: []string{
						constants.CustomerVoiceProd,
					},
				},
			},
		},
	},
	constants.InternalGetExternalProductConfigID(constants.WebsiteProd): {
		ServiceProviderID: constants.WebsiteProd,
		NavigationID:      constants.InternalGetExternalProductConfigID(constants.WebsiteProd),
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.Products,
					Select: []string{
						constants.WebsiteProd,
					},
				},
			},
		},
	},
	constants.InternalGetExternalProductConfigID(constants.WebsiteDemo): {
		ServiceProviderID: constants.WebsiteDemo,
		NavigationID:      constants.InternalGetExternalProductConfigID(constants.WebsiteDemo),
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.Products,
					Select: []string{
						constants.WebsiteDemo,
					},
				},
			},
		},
	},
	"reputation": {
		NavigationID:  "reputation",
		Label:         "Reputation",
		Icon:          "star",
		TranslationID: "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REPUTATION",
		ShowIcon:      true,
		Policy:        MLRMPolicy,
	},
	"nav-embedded-reputation": {
		NavigationID:      "nav-reputation",
		ServiceProviderID: "VBC",
		Label:             "Reputation",
		Icon:              "star",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REPUTATION",
		ShowIcon:          true,
	},
	"social": {
		NavigationID:  "social",
		Label:         "Social",
		Icon:          "question_answer",
		TranslationID: "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.SOCIAL",
		ShowIcon:      true,
	},
	// This is for the multi-location social page with embedded tabs
	"nav-embedded-social": {
		NavigationID:      "nav-embedded-social",
		Label:             "Social",
		Icon:              "question_answer",
		ServiceProviderID: "VBC",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.SOCIAL",
		ShowIcon:          true,
		Policy: &condition.Condition{
			Queries: []condition.Query{
				{
					From: internal.FeatureFlags,
					Select: []string{
						constants.PartnerFeatureIDEmbeddedSocialMarketing,
					},
				},
				{
					From: internal.BrandTabPermissions,
					Select: []string{
						constants.SocialTab,
					},
				},
			},
		},
	},
	"listings": {
		NavigationID:  "listings",
		Label:         "Listings",
		Icon:          "location_on",
		TranslationID: "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.LISTINGS",
		ShowIcon:      true,
	},
	"listings-keyword": {
		NavigationID:  "listings-keyword",
		Label:         "Keyword Tracking",
		Icon:          "trending_up",
		TranslationID: "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.KEYWORD_TRACKING",
		ShowIcon:      true,
	},
	"google-my-business": {
		NavigationID:      "nav-embedded-google-my-business",
		ServiceProviderID: "VBC",
		Label:             "Google My Business",
		Icon:              "store",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_MY_BUSINESS",
		ShowIcon:          true,
	},
	"nav-embedded-analytics": {
		NavigationID:      "nav-embedded-analytics",
		ServiceProviderID: "VBC",
		Label:             "Analytics",
		Icon:              "store",
		TranslationID:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GMBBING",
	},
	"nav-administration-link": {
		NavigationID:      "nav-settings-link",
		ServiceProviderID: "VBC",
		Label:             "Administration",
		Icon:              "settings",
		TranslationID:     "NAVIGATION.TABS.ADMINISTRATION",
		ShowIcon:          true,
	},
	"nav-marketing": {
		NavigationID:      "nav-marketing",
		ServiceProviderID: "VBC",
		Label:             "Marketing",
		Icon:              "campaigns",
		TranslationID:     "NAVIGATION.TABS.MARKETING",
		ShowIcon:          true,
	},
}

var embeddedNPSBusinessAppPolicy = &condition.Condition{
	Queries: []condition.Query{
		{
			From: internal.Products,
			Select: []string{
				"RM",
			},
		},
		{
			From: internal.FeatureFlags,
			Select: []string{
				constants.NPSBusinessAppFeatureIDReputation,
			},
		},
	},
}

var embeddedRMPolicy = &condition.Condition{
	Queries: []condition.Query{
		{
			From: internal.Products,
			Select: []string{
				"RM",
			},
		},
	},
}

var MLRMPolicy = &condition.Condition{
	Operator: condition.And,
	Queries: []condition.Query{
		{
			From: internal.FeatureFlags,
			Select: []string{
				constants.NPSNewMLDesign,
			},
		},
		{
			From: internal.BrandTabPermissions,
			Select: []string{
				constants.ReviewsTab,
			},
		},
	},
}

var embeddedListingsPolicy = &condition.Condition{
	Queries: []condition.Query{
		{
			From: internal.Products,
			Select: []string{
				"MS",
			},
		},
		{
			From: internal.FeatureFlags,
			Select: []string{
				constants.PartnerFeatureIDEmbeddedListings,
			},
		},
	},
	Conditions: []condition.Condition{
		{
			Operator: condition.Or,
			Queries: []condition.Query{
				{
					From: internal.PlatformMode,
					Select: []string{
						constants.WebMode,
					},
				},
				{
					From: internal.PlatformMode,
					Select: []string{
						constants.MobileDevMode,
					},
				},
			},
		},
	},
}

var embeddedSocialPolicy = &condition.Condition{
	Queries: []condition.Query{
		{
			From: internal.Products,
			Select: []string{
				"SM",
			},
		},
		{
			From: internal.FeatureFlags,
			Select: []string{
				constants.PartnerFeatureIDEmbeddedSocialMarketing,
			},
		},
	},
}

var embeddedAdvertisingPolicy = &condition.Condition{
	Queries: []condition.Query{
		{
			From: internal.Products,
			Select: []string{
				constants.Advertising,
			},
		},
		{
			From: internal.FeatureFlags,
			Select: []string{
				constants.PartnerFeatureIDEmbeddedAdIntel,
			},
		},
	},
}

var reviewsTabEnabledPolicy = &condition.Condition{
	Queries: []condition.Query{
		{
			From: internal.BrandTabPermissions,
			Select: []string{
				constants.ReviewsTab,
			},
		},
	},
}

var npsTabEnabledPolicy = &condition.Condition{
	Operator: condition.And,
	Queries: []condition.Query{
		{
			From: internal.FeatureFlags,
			Select: []string{
				constants.NPSFeatureIDReputation,
			},
		},
		{
			From: internal.BrandTabPermissions,
			Select: []string{
				constants.ReviewsTab,
			},
		},
	},
}

var embeddedAnalyticsPolicy = &condition.Condition{
	Queries: []condition.Query{
		{
			From: internal.FeatureFlags,
			Select: []string{
				constants.PartnerFeatureIDLisAnalytics,
			},
		},
	},
}
