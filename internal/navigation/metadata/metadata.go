package metadata

import "github.com/vendasta/atlas/internal/navigation/condition"

type MetaData struct {
	// Give this link a unique ID
	NavigationID string `json:"navigation_id"`
	// URL is redirected to if the user is SSOing from one product to another
	URL string `json:"url"`
	// The owner of this route
	ServiceProviderID string `json:"service_provider_id"`
	// If a LogoURL is provided, it will be displayed as a product icon next to the route
	LogoURL string `json:"logo_url"`
	// Material Icon string to be displayed next to the route
	Icon string `json:"icon"`
	// Whether the icon should be shown
	ShowIcon bool `json:"show_icon"`
	// Corresponding translation ID specified in lang/<lang-code>.go
	TranslationID            string `json:"translation_id"`
	DescriptionTranslationID string `json:"description_translation_id"`
	// Whether we should show the external icon next to the route (arrow leaving a box)
	External bool `json:"external"`
	// Fallback label to be used in the event that we cannot identify the correct translation
	Label string `json:"label"`
	// Controls whether the user can pin this route.
	Pinnable bool `json:"pinnable"`
	// Text to be displayed in a chip next to the route.
	ChipContent string `json:"chip_content"`
	// Should show the user required modal
	UserRequired bool `json:"user_required"`
	// A flag to indicate if we should open a new tab when clicked
	OpenInNewTab bool `json:"open_in_new_tab"`
	// Condition to be evaluated to determine if the link should be shown
	Policy *condition.Condition `json:"policy,omitempty"`
}
