package metadata

//go:generate mockgen -source=service.go -destination=mocks/store.go -package=mocks
type store interface {
	Get(id string) (*MetaData, error)
	GetMulti(ids []string) ([]*MetaData, error)
}

type Service struct {
	store store
}

func New(store store) *Service {
	if store == nil {
		store = newStore()
	}

	return &Service{
		store: store,
	}
}

func (s *Service) Get(id string) (*MetaData, bool) {
	metaData, err := s.store.Get(id)
	if err != nil {
		return nil, false
	}

	return metaData, true
}

func (s *Service) GetMulti(ids []string) map[string]*MetaData {
	metaData, err := s.store.GetMulti(ids)
	if err != nil {
		return nil
	}

	metaDataMap := make(map[string]*MetaData, len(metaData))
	for _, m := range metaData {
		metaDataMap[m.NavigationID] = m
	}

	return metaDataMap
}
