package config

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNavigationConfig_Validate(t *testing.T) {
	testCases := []struct {
		name          string
		config        *NavigationConfig
		expectedError string
	}{
		{
			name: "valid config",
			config: &NavigationConfig{
				ConfigID: "testConfig",
				Items: []LinkConfig{
					{NavItemID: "testID1", Type: Link},
					{NavItemID: "testParentID1", Type: Container, Children: []LinkConfig{
						{NavItemID: "testID2", Type: Link},
					}},
				},
			},
			expectedError: ""},
		{
			name:          "config is invalid because it is nil",
			config:        nil,
			expectedError: "config is nil"},
		{
			name: "config is invalid because there are no items",
			config: &NavigationConfig{
				ConfigID: "testConfig",
			},
			expectedError: "has no items"},
		{
			name: "config is invalid because the ConfigID is not set",
			config: &NavigationConfig{
				ConfigID: "",
				Items: []LinkConfig{
					{NavItemID: "testID1", Type: Link},
				},
			},
			expectedError: "ConfigID is unset"},
		{
			name: "config is invalid because a child item is invalid",
			config: &NavigationConfig{
				ConfigID: "testConfig",
				Items: []LinkConfig{
					{}, // invalid because nothing is set
				},
			},
			expectedError: "NavItemID is empty"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.config.Validate()
			if tc.expectedError == "" {
				assert.NoError(t, err)

				return
			}
			assert.Contains(t, err.Error(), tc.expectedError)
		})
	}
}

func TestLinkConfig_Validate(t *testing.T) {
	testCases := []struct {
		name          string
		config        LinkConfig
		expectedError []string
	}{
		{
			name:          "valid config",
			config:        LinkConfig{NavItemID: "testID1", Type: Link},
			expectedError: nil},
		{
			name:          "config is invalid because the NavItemID is empty",
			config:        LinkConfig{NavItemID: "", Type: Link},
			expectedError: []string{"NavItemID is empty"}},
		{
			name:          "config is invalid because the Type is empty",
			config:        LinkConfig{NavItemID: "testID1", Type: ""},
			expectedError: []string{"has no type"}},
		{
			name: "container config is invalid because a child item is invalid",
			config: LinkConfig{
				NavItemID: "testParentID1",
				Type:      Container,
				Children: []LinkConfig{
					{NavItemID: "", Type: Link},
				},
			},
			expectedError: []string{"NavItemID is empty"}},
		{
			name: "container config is invalid because its SubstitutionRules are invalid",
			config: LinkConfig{
				NavItemID: "testParentID1",
				Type:      Link,
				Rules: &SubstitutionRules{
					SubstituteNavConfig: &NavigationConfig{}, // invalid because empty
				},
			},
			expectedError: []string{substitutionRulesErrPrefix, "ConfigID is unset"}},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.config.Validate()
			if tc.expectedError != nil {
				for _, expectedError := range tc.expectedError {
					assert.Contains(t, err.Error(), expectedError)
				}

				return
			}
			assert.NoError(t, err)
		})
	}
}

func TestSubstitutionRules_Validate(t *testing.T) {
	testCases := []struct {
		name          string
		config        *SubstitutionRules
		expectedError []string
	}{
		{
			name:          "valid config",
			config:        &SubstitutionRules{SubstituteNavConfig: &NavigationConfig{ConfigID: "sub", Items: []LinkConfig{{NavItemID: "testID1", Type: Link}}}},
			expectedError: nil},
		{
			name:          "nil is also a valid SubstitutionRule",
			config:        nil,
			expectedError: nil},
		{
			name:          "config is invalid because the SubstituteNavConfig is empty",
			config:        &SubstitutionRules{SubstituteNavConfig: &NavigationConfig{ConfigID: "sub"}},
			expectedError: []string{"config has no items"}},
		{
			name:          "config is invalid because the SubstitutionRules are empty",
			config:        &SubstitutionRules{},
			expectedError: []string{navigationConfigErrPrefix, substitutionRulesErrPrefix}},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := tc.config.Validate()
			if tc.expectedError != nil {
				for _, expectedError := range tc.expectedError {
					assert.Contains(t, err.Error(), expectedError)
				}

				return
			}
			assert.NoError(t, err)
		})
	}
}
