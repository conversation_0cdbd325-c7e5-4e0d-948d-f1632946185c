package config

import (
	"fmt"
	"slices"

	"github.com/vendasta/gosdks/verrors"

	"github.com/vendasta/atlas/internal/navigation/internal/constants"
)

const (
	SingleLocationID = "SingleLocation"
	MultiLocationID  = "MultiLocation"

	// Partner-specific overrides
	rainmakerPID       = "ZA5U"
	indigodigitalPID   = "ZERF"
	cerberusDigitalPID = "9TD8"
	vitazaDigitalPID   = "0W8W"
	herstMediaPID      = "HMSI"
	comporiumPID       = "MIL"

	SingleLocationMobileID = "SingleMobile"
)

const substitutionID = "substite"

var reputationConfigID = constants.InternalGetExternalProductConfigID("RM")
var listingsConfigID = constants.InternalGetExternalProductConfigID("MS")
var socialMarketingConfigID = constants.InternalGetExternalProductConfigID("SM")
var adIntelConfigID = constants.InternalGetExternalProductConfigID("MP-94072e44d5364872b672d7ab4fc7a7e8")
var hotelManagerConfigID = constants.InternalGetExternalProductConfigID("MP-KFTFV6RK6NBX6PLVPXCSRLN5PKNJCPHV")
var calendarHeroConfigID = constants.InternalGetExternalProductConfigID("MP-58G85F84DZBND7CK228578MWLD38SS4S")
var websiteProConfigID = constants.InternalGetExternalProductConfigID("MP-ee4ea04e553a4b1780caf7aad7be07cd")
var customerVoiceConfigID = constants.InternalGetExternalProductConfigID("MP-c4974d390a044c28aec31e421aa662b2")
var contentStudioConfigID = constants.InternalGetExternalProductConfigID("MP-6MGWWPM3X88ZGGHVSJHT7JDJ3P3QZQSG")
var googleAdsStudioConfigID = constants.InternalGetExternalProductConfigID("MP-B77R7GG4QHGKF5RMVSLVZPD3D5JSTZ4K")
var campaignsConfigID = constants.InternalGetExternalProductConfigID("MP-ZGJ6V4QRP77WPMDKXS6VDRNX58Q42P7P")
var myComporiumConfigID = constants.InternalGetExternalProductConfigID("MP-FQJJ7V7B4PXCLF3722775S3WLZHGZG38")

// SEOAlphaConfigID is for 9TD8 specific skus
var SEOAlphaConfigID = constants.InternalGetExternalProductConfigID("MP-XBRSXQSPSC4H4TSGMMMKHR3GQZMHL2LH")
var SEOHikeConfigID = constants.InternalGetExternalProductConfigID("MP-NTQXLQ2V3VRHKRJ3C2JGFT78TRDRQFPW")
var GoogleSearchAdsSmallBizConfigID = constants.InternalGetExternalProductConfigID("MP-X7FXPRFVB3N6S5K8CHLJKMDG232GRM78")
var GoogleDisplayAdsSmallBizConfigID = constants.InternalGetExternalProductConfigID("MP-647TKQGPVKXF5CHQCPZ4RK78CFTWHWFH")

type navFileStore struct{}

func newFileStore() *navFileStore {
	return &navFileStore{}
}

func (s *navFileStore) Get(id string) (*NavigationConfig, error) {
	if config, ok := configMap[id]; ok {
		return config, nil
	}

	return nil, verrors.New(verrors.NotFound, "Navigation config not found for id: %s", id)
}

func (s *navFileStore) GetMulti(ids []string) ([]*NavigationConfig, error) {
	var configs []*NavigationConfig
	for _, id := range ids {
		config, err := s.Get(id)
		if err != nil {
			if verrors.IsError(verrors.NotFound, err) {
				configs = append(configs, nil)
				continue
			}

			return nil, err
		}

		configs = append(configs, config)
	}

	return configs, nil
}

func (s *navFileStore) List() []string {
	var ids []string
	for k := range configMap {
		ids = append(ids, k)
	}

	return ids
}

var configMap = map[string]*NavigationConfig{
	SingleLocationID: {
		ConfigID: SingleLocationID, Items: singleLocationNavTabs, Featureset: *NewFeatureset([]string{
			reputationConfigID,
			listingsConfigID,
			socialMarketingConfigID,
			adIntelConfigID,
		}),
	},
	SingleLocationMobileID: {
		ConfigID: SingleLocationMobileID, Items: mobileTabsConfig, Featureset: *NewFeatureset([]string{
			reputationConfigID,
		}),
	},

	comporiumPID: {
		ConfigID: comporiumPID, Items: comporiumConfig, Featureset: *NewFeatureset([]string{
			myComporiumConfigID,
			reputationConfigID,
			listingsConfigID,
			socialMarketingConfigID,
			adIntelConfigID,
		}),
	},
	GetMultilocationConfigID(comporiumPID): {
		ConfigID: GetMultilocationConfigID(comporiumPID),
		Items:    multiLocationNavTabs,
	},

	rainmakerPID: {
		ConfigID: SingleLocationID, Items: rainmakerConfig, Featureset: *NewFeatureset([]string{
			adIntelConfigID,
			calendarHeroConfigID,
			customerVoiceConfigID,
			hotelManagerConfigID,
			listingsConfigID,
			reputationConfigID,
			socialMarketingConfigID,
			contentStudioConfigID,
			googleAdsStudioConfigID,
			websiteProConfigID,
			campaignsConfigID,
		}),
	},
	GetMultilocationConfigID(rainmakerPID): {
		ConfigID: GetMultilocationConfigID(rainmakerPID),
		Items:    multiLocationNavTabs,
	},
	indigodigitalPID: {
		ConfigID: SingleLocationID, Items: indigodigitalConfig, Featureset: *NewFeatureset([]string{
			adIntelConfigID,
			customerVoiceConfigID,
			listingsConfigID,
			reputationConfigID,
			socialMarketingConfigID,
			websiteProConfigID,
		}),
	},
	GetMultilocationConfigID(indigodigitalPID): {
		ConfigID: GetMultilocationConfigID(indigodigitalPID),
		Items:    multiLocationNavTabs,
	},
	herstMediaPID: {
		ConfigID: SingleLocationID, Items: herstMediaConfig, Featureset: *NewFeatureset([]string{
			campaignsConfigID,
			reputationConfigID,
			listingsConfigID,
			socialMarketingConfigID,
			adIntelConfigID,
		}),
	},
	GetMultilocationConfigID(herstMediaPID): {
		ConfigID: GetMultilocationConfigID(herstMediaPID),
		Items:    multiLocationNavTabs,
	},
	cerberusDigitalPID: {
		ConfigID: SingleLocationID, Items: cerberusDigitalConfig, Featureset: *NewFeatureset([]string{
			calendarHeroConfigID,
			listingsConfigID,
			reputationConfigID,
			socialMarketingConfigID,
			websiteProConfigID,
			adIntelConfigID,
			customerVoiceConfigID,
			SEOAlphaConfigID,
			SEOHikeConfigID,
			GoogleSearchAdsSmallBizConfigID,
			GoogleDisplayAdsSmallBizConfigID,
		}),
	},
	GetMultilocationConfigID(cerberusDigitalPID): {
		ConfigID: GetMultilocationConfigID(cerberusDigitalPID),
		Items:    multiLocationNavTabs,
	},
	vitazaDigitalPID: {
		ConfigID: SingleLocationID, Items: vitazaDigitalConfig, Featureset: *NewFeatureset([]string{
			calendarHeroConfigID,
			listingsConfigID,
			reputationConfigID,
			socialMarketingConfigID,
			websiteProConfigID,
			adIntelConfigID,
			customerVoiceConfigID,
			SEOHikeConfigID,
			websiteProConfigID,
			customerVoiceConfigID,
			"MP-6L742WPPBGZCV5PVTXKP3KWRZ4HTQJ28",
			"MP-5XQXZP2NX676LX74SMKBGM3HQPT2NLT5",
			"MP-X44RWDQGHC2NGDN63TC5BRQMBKQP4TPQ",
			"MP-3RSR8MSSL3GJT8BH4K62CNHNH66H3QJH",
			"MP-********************************",
			"MP-KNDN6KPHQ4K2DGB57C552DVN5JNNFJCB",
			"MP-QVF65PL6HGFPRMKXJSBKPZNWXDWNCVCK",
			"MP-5MKQ4X8NWK8FJ7ZLRX5KD7DWG45CDS6T",
			"MP-65RLZC2FKG3MM4GHCCV6FZWC3L57652K",
			"MP-TWG2D7DWPXZRWRRDB37BBLLKS2MBC4CL",
			"MP-4HZKKK4DKXB6QFQ8QGNL2FRCSTG3RSTR",
			"MP-16d5e227e7b647afaf233595b21bef7a",
			"MP-T2RPGQHKWCMTHBMVCF3NHV6WSPNGPJPW",
			"MP-492e1467b897434aa528bd8e74dcde0d",
			"MP-03fc7fb5e3b848cd88523515cd66ea9c",
			"MP-6GLFKDNCV3LTHTRZ6DDXLJN7MLVBBWGW",
			"MP-FQ264D5N2822PT7ST6BVPFST6QHSKZC8",
			"MP-GB6TPVDDJVK3CK7MQW86XG75TMRCXNJK",
			"MP-KJSKWHP4PTRZXNTT2GKKRJRWXVH4PDHT",
			"MP-XQF5VKFP2PLW87RSVBF6TWNKRJCJQKSQ",
			"MP-ZDG5JR684VX5XDHBKQHRH6NDT5S258ZD",
			"MP-7SVRPZ7X8BNXHVL2SXTRZDJCMRK4XLDM",
			"MP-87X8H7C8KZKW6LGQ2XP7JS7W72Q6F427",
			"MP-HC57CZ6CXNKTVXMLTCCMBFZVRNDXF4SL",
			"MP-Q5R52HBZ2Q88Z232H77R2KRKZLT2JT7D",
			"MP-HZBQQCFV6WMVFB2JNMFFWN5658L23LRP",
			"MP-XB8JLCWPBF7Z22ZGCH3MZBHCF3L6KPML",
			"MP-C778MX455BLPBM8BXSZWHDHKDZ336K86",
			"MP-WM65PB2J5PL6BCNLB5JJN58DDQWXZVPN",
			"MP-2JB3LB2H8N77R325X84KBNP4XDTTMRGC",
			"MP-GF3D2CNFXDBTBRVCXLDPMB57TMBF73TW",
			"MP-********************************",
			"MP-4M8GCJ8T2N46KNTTV3XQ3FDC7LGC2QL4",
			"A-FZ4XVS2WWF",
			"MP-2da7414a9f604f518d6ae0127080f9e2",
			"MP-CQB668KS4XSRDHFGRGQ4358K2VCWHV3M",
			"MP-FVGG6TS6J5V5ZJQPS7GGP25838QSXCVD",
		}),
	},
	GetMultilocationConfigID(vitazaDigitalPID): {
		ConfigID: GetMultilocationConfigID(vitazaDigitalPID),
		Items:    multiLocationNavTabs,
	},
	MultiLocationID:         {ConfigID: MultiLocationID, Items: multiLocationNavTabs},
	reputationConfigID:      {ConfigID: reputationConfigID, Items: reputationConfig},
	listingsConfigID:        {ConfigID: listingsConfigID, Items: listingsConfig},
	socialMarketingConfigID: {ConfigID: socialMarketingConfigID, Items: socialMarketingConfig},
	adIntelConfigID:         {ConfigID: adIntelConfigID, Items: adIntelConfig},
}

var singleLocationNavTabs = appendConfigs(
	[]LinkConfig{
		// { NavItemID, Type, Path, Children, SubstitutionRules }
		{"nav-home", Link, "/account/location/<accountGroupId>/home", nil, nil},
		{"nav-inbox", Link, "/account/location/<accountGroupId>/inbox", nil, nil},
	},

	crmConfig,

	PaymentsConfig,

	aiAssistantsConfig,

	[]LinkConfig{
		{"nav-customer-list", Link, "/account/location/<accountGroupId>/customer-list", nil, nil},
	},
	campaignsConfig,
	[]LinkConfig{
		{"nav-executive-report", Link, "/account/location/<accountGroupId>/executive-report/monthly", nil, nil},
		{"marketplace-products", Placeholder, "", nil, nil},
	},

	// Configured products
	listingsConfig,
	reputationConfig,
	socialMarketingConfig,
	adIntelConfig,

	[]LinkConfig{
		{"nav-products", Link, "/account/location/<accountGroupId>/products", nil, nil},
		{"nav-store", Link, "/account/location/<accountGroupId>/store", nil, nil},
		{"nav-automations-mine-admin", Link, "/account/location/<accountGroupId>/automations/all", nil, nil},
	},

	administrationLinkConfig,
)

var multiLocationNavTabs = []LinkConfig{
	// { NavItemID, Type, Path, Children, SubstitutionRules }
	{"nav-brand-home", Link, "/account/brands/<brandPath>/home", nil, nil},
	{"nav-brand-inbox", Link, "/account/brands/<brandPath>/inbox", nil, nil},

	{
		"nav-brand-crm", Container, "", []LinkConfig{
			{"nav-brand-crm-contacts", Link, "/account/brands/<brandPath>/contacts", []LinkConfig{
				{"nav-brand-crm-contacts", Link, "/account/brands/<brandPath>/contacts", nil, nil},
			}, nil},
			{"nav-brand-crm-tasks", Link, "/account/brands/<brandPath>/tasks", []LinkConfig{
				{"nav-brand-crm-tasks", Link, "/account/brands/<brandPath>/tasks", nil, nil},
			}, nil},
		}, nil,
	},

	{"nav-brand-report", Link, "/account/brands/<brandPath>/report", nil, nil},

	{
		"reputation", Container, "", []LinkConfig{
			{"nav-brand-reputation-reviews", Link, "/account/brands/<brandPath>/reviews", []LinkConfig{
				{"nav-brand-reputation-overview", Link, "/account/brands/<brandPath>/reviews/overview", nil, nil},
				{"nav-brand-reputation-manage", Link, "/account/brands/<brandPath>/reviews/manage-reviews", nil, nil},
				{"nav-brand-reputation-insights", Link, "/account/brands/<brandPath>/reviews/insights", nil, nil},
				{"nav-brand-google-my-business-google-q-and-a", Link, "/account/brands/<brandPath>/reviews/google-q-and-a", nil, nil},
			}, nil},
			{"nav-brand-reputation-net_promoter_score", Link, "/account/brands/<brandPath>/netpromoterscore", []LinkConfig{
				{"nav-brand-reputation-nps-overview", Link, "/account/brands/<brandPath>/netpromoterscore/overview", nil, nil},
				{"nav-brand-reputation-nps", Link, "/account/brands/<brandPath>/netpromoterscore/nps", nil, nil},
				{"nav-brand-reputation-nps-team", Link, "/account/brands/<brandPath>/netpromoterscore/team", nil, nil},
			}, nil},
			{"nav-brand-reputation-requests", Link, "/account/brands/<brandPath>/requests", []LinkConfig{
				{"nav-brand-reputation-requests-overview", Link, "/account/brands/<brandPath>/requests/overview", nil, nil},
			}, nil},
		},
		&SubstitutionRules{
			SubstituteNavConfig: &NavigationConfig{
				ConfigID: substitutionID, Items: []LinkConfig{
					{
						"nav-embedded-reputation", Link, "/account/brands/<brandPath>/reputation", []LinkConfig{
							{"nav-brand-reputation-overview", Link, "/account/brands/<brandPath>/reputation/reviews", nil, nil},
							{"nav-brand-reputation-manage", Link, "/account/brands/<brandPath>/reputation/manage-reviews", nil, nil},
							{"nav-brand-reputation-nps", Link, "/account/brands/<brandPath>/reputation/nps", nil, nil},
							{"nav-brand-reputation-insights", Link, "/account/brands/<brandPath>/reputation/insights", nil, nil},
							{
								"nav-brand-google-my-business-google-q-and-a-sublink", Link,
								"/account/brands/<brandPath>/reputation/google-q-and-a", nil, nil,
							},
						}, nil,
					},
				},
			},
		},
	},

	{
		"nav-embedded-listings", Container, "", []LinkConfig{
			{"nav-embedded-listing", Link, "/account/brands/<brandPath>/listings", []LinkConfig{
				{"nav-brand-listings-overview", Link, "/account/brands/<brandPath>/listings/overview", nil, nil},
				{"nav-brand-listings-manage", Link, "/account/brands/<brandPath>/listings/manage", nil, nil},
			}, nil},
			{"nav-embedded-keyword-tracking", Link, "/account/brands/<brandPath>/keyword-tracking", []LinkConfig{
				{"nav-brand-keyword-tracking-overview", Link, "/account/brands/<brandPath>/keyword-tracking/overview", nil, nil},
				{"nav-brand-keyword-tracking-keyword", Link, "/account/brands/<brandPath>/keyword-tracking/keywords", nil, nil},
			}, nil},
			{"nav-embedded-analytics", Link, "/account/brands/<brandPath>/analytics", []LinkConfig{
				{"nav-brand-analytics-google", Link, "/account/brands/<brandPath>/analytics/google", nil, nil},
				{"nav-brand-analytics-bing", Link, "/account/brands/<brandPath>/analytics/bing", nil, nil},
			}, nil},
		},
		&SubstitutionRules{
			SubstituteNavConfig: &NavigationConfig{
				ConfigID: substitutionID, Items: []LinkConfig{
					{"nav-embedded-listing", Link, "/account/brands/<brandPath>/listings", []LinkConfig{
						{"nav-brand-listings-overview", Link, "/account/brands/<brandPath>/listings/overview", nil, nil},
						{"nav-brand-listings-manage", Link, "/account/brands/<brandPath>/listings/manage", nil, nil},
					}, nil},
				},
			},
		},
	},

	{
		"nav-embedded-social", Link, "/account/brands/<brandPath>/social", []LinkConfig{
			{"nav-brand-social", Link, "/account/brands/<brandPath>/social/overview", nil, nil},
			{"nav-brand-social-manage-posts", Link, "/account/brands/<brandPath>/social/manage-posts", nil, nil},
		}, &SubstitutionRules{
			SubstituteNavConfig: &NavigationConfig{
				ConfigID: substitutionID, Items: []LinkConfig{
					{
						"social", Container, "", []LinkConfig{
							{"nav-brand-social", Link, "/account/brands/<brandPath>/social-overview", nil, nil},
							{
								"nav-brand-social-manage-posts", Link, "/account/brands/<brandPath>/manage-social-posts", nil,
								nil,
							},
						}, nil,
					},
				},
			},
		},
	},

	{"nav-brand-advertising", Link, "/account/brands/<brandPath>/advertising-overview", nil, nil},
	{"nav-brand-data-export", Link, "/account/brands/<brandPath>/data-export", nil, nil},
}

var comporiumConfig = appendConfigs(
	[]LinkConfig{
		// { NavItemID, Type, Path, Children, SubstitutionRules }
		{"nav-home", Link, "/account/location/<accountGroupId>/home", nil, nil},
		{myComporiumConfigID, ExternalProduct, "", nil, nil},
		{"nav-inbox", Link, "/account/location/<accountGroupId>/inbox", nil, nil},
	},

	crmConfig,

	[]LinkConfig{
		{"nav-customer-list", Link, "/account/location/<accountGroupId>/customer-list", nil, nil},
	},
	campaignsConfig,
	[]LinkConfig{
		{"nav-executive-report", Link, "/account/location/<accountGroupId>/executive-report/monthly", nil, nil},
		{"marketplace-products", Placeholder, "", nil, nil},
	},

	// Configured products
	listingsConfig,
	reputationConfig,
	socialMarketingConfig,
	adIntelConfig,

	[]LinkConfig{
		{"nav-products", Link, "/account/location/<accountGroupId>/products", nil, nil},
		{"nav-store", Link, "/account/location/<accountGroupId>/store", nil, nil},
	},

	administrationLinkConfig,
)

var rainmakerConfig = appendConfigs(
	[]LinkConfig{
		// { NavItemID, Type, Path, Children, SubstitutionRules }
		{"nav-home", Link, "/account/location/<accountGroupId>/home", nil, nil},
		{"nav-inbox", Link, "/account/location/<accountGroupId>/inbox", nil, nil},
	},

	crmConfig,
	aiAssistantsConfig,

	[]LinkConfig{
		{"nav-customer-list", Link, "/account/location/<accountGroupId>/customer-list", nil, nil},
	},
	campaignsConfig,
	[]LinkConfig{
		{"nav-executive-report", Link, "/account/location/<accountGroupId>/executive-report/weekly", nil, nil},
		{"marketplace-products", Placeholder, "", nil, nil},
	},

	// Configured products
	listingsConfig,
	[]LinkConfig{
		{websiteProConfigID, ExternalProduct, "", nil, nil},
		{customerVoiceConfigID, ExternalProduct, "", nil, nil},
	},
	reputationConfig,
	socialMarketingConfig,
	[]LinkConfig{
		GetExternalLinkForProduct(contentStudioConfigID),
		GetExternalLinkForProduct(googleAdsStudioConfigID),
	},
	adIntelConfig,
	[]LinkConfig{
		{hotelManagerConfigID, ExternalProduct, "", nil, nil},
		{calendarHeroConfigID, ExternalProduct, "", nil, nil},
	},

	[]LinkConfig{
		{"nav-products", Link, "/account/location/<accountGroupId>/products", nil, nil},
		{"nav-store", Link, "/account/location/<accountGroupId>/store", nil, nil},
	},
	administrationLinkConfig,
)

var reputationConfig = []LinkConfig{
	{
		"nav-reputation", Link, "/account/location/<accountGroupId>/reputation", []LinkConfig{
			{"nav-reputation-manage", Link, "/account/location/<accountGroupId>/reputation/manage-reviews", nil, nil},
			{"nav-reputation-nps", Link, "/account/location/<accountGroupId>/reputation/netpromoterscore", nil, nil},
			{"nav-reputation-insights", Link, "/account/location/<accountGroupId>/reputation/insights", nil, nil},
			{
				"nav-google-my-business-google-q-and-a-sublink", Link,
				"/account/location/<accountGroupId>/reputation/google-q-and-a", nil, nil,
			},
		}, &SubstitutionRules{
			SubstituteNavConfig: &NavigationConfig{
				ConfigID: substitutionID, Items: []LinkConfig{
					{reputationConfigID, ExternalProduct, "", nil, nil},
				},
			},
		},
	},
}

var listingsConfig = []LinkConfig{
	{
		"nav-listings", Link, "/account/location/<accountGroupId>/listings", []LinkConfig{
			{
				"nav-listings-keyword-tracking", Link, "/account/location/<accountGroupId>/listings/keyword-tracking", nil,
				nil,
			},
			{
				"nav-listings-listing-sync", Link, "/account/location/<accountGroupId>/listings/listing-sync", nil,
				nil,
			},
		}, &SubstitutionRules{
			SubstituteNavConfig: &NavigationConfig{
				ConfigID: substitutionID, Items: []LinkConfig{
					{listingsConfigID, ExternalProduct, "", nil, nil},
				},
			},
		},
	},
}

var adminMobileConfig = []LinkConfig{
	{
		"nav-administration-link", Link, "/account/location/<accountGroupId>/settings", []LinkConfig{
			{"nav-social-connections", Link, "/account/location/<accountGroupId>/settings/connections", nil, nil},
			{"nav-integrations", Link, "/account/location/<accountGroupId>/settings/integrations", nil, nil},
			{
				"nav-profile-settings", Link,
				"/account/location/<accountGroupId>/settings/notifications/<subjectId>/settings", nil, nil,
			},
			{
				"nav-sms-configuration", Link, "/account/location/<accountGroupId>/settings/sms-configuration", nil,
				nil,
			},
			{"nav-inbox-settings", Link, "/account/location/<accountGroupId>/settings/inbox", nil, nil},
			{"nav-ai-knowledge-settings", Link, "/account/location/<accountGroupId>/settings/ai-knowledge", nil, nil},
			{"nav-field-management-settings", Link, "/account/location/<accountGroupId>/settings/field-management", nil, nil},
			{"nav-lead-scoring", Link, "/account/location/<accountGroupId>/score", nil, nil},
		}, nil,
	},
}

var administrationLinkConfig = []LinkConfig{
	// changing the order of the links here can change the order of the sections on the administration page
	{"nav-administration-link", Link, "/account/location/<accountGroupId>/administration", []LinkConfig{
		// Account
		{"nav-business-profile", Link, "/account/location/<accountGroupId>/administration/profile", nil, nil},
		{"nav-notification-settings", Link, "/account/location/<accountGroupId>/administration/notifications/<subjectId>/settings", nil, nil},
		{"nav-billing-settings", Link, "/account/location/<accountGroupId>/administration/billing/<subjectId>/settings", nil, nil},

		// Payments
		{"nav-payment-settings", Link, "/account/location/<accountGroupId>/administration/payments", nil, nil},

		// App Settings
		{"nav-automations-mine", Link, "/account/location/<accountGroupId>/automations/all", nil, nil},
		{"nav-integrations", Link, "/account/location/<accountGroupId>/administration/integrations", nil, nil},
		{"nav-social-connections", Link, "/account/location/<accountGroupId>/administration/connections", nil, nil},
		{"nav-field-management-settings", Link, "/account/location/<accountGroupId>/administration/field-management", nil, nil},
		{"nav-lead-scoring", Link, "/account/location/<accountGroupId>/score", nil, nil},

		// AI Settings
		{"nav-ai-knowledge-settings", Link, "/account/location/<accountGroupId>/administration/ai-knowledge", nil, nil},
		{"nav-ai-assistants-settings", Link, "/account/location/<accountGroupId>/ai/assistants", nil, nil},

		// Communication Settings
		{"nav-inbox-settings", Link, "/account/location/<accountGroupId>/administration/inbox", nil, nil},
		{"nav-email-configuration", Link, "/account/location/<accountGroupId>/administration/email-configuration", nil, nil},
		{"nav-sms-configuration", Link, "/account/location/<accountGroupId>/administration/sms-configuration", nil, nil},
		{"nav-emails", Link, "/account/location/<accountGroupId>/administration/email-history", nil, nil},

		// From Partner
		{"nav-guides", Link, "/account/location/<accountGroupId>/administration/guides", nil, nil},
		{"nav-files", Link, "/account/location/<accountGroupId>/administration/files", nil, nil},
		{"nav-fulfillment", Link, "/account/location/<accountGroupId>/administration/projects", nil, nil},
		{"nav-invoices", Link, "/account/location/<accountGroupId>/administration/invoices", nil, nil},
		{"nav-orders", Link, "/account/location/<accountGroupId>/administration/orders", nil, nil},
	}, nil,
	},
}

var adIntelConfig = []LinkConfig{
	{
		"nav-adintel", Link, "/account/location/<accountGroupId>/advertising", []LinkConfig{
			{"nav-advertising-overview", Link, "/account/location/<accountGroupId>/advertising/overview", nil, nil},
		}, &SubstitutionRules{
			SubstituteNavConfig: &NavigationConfig{
				ConfigID: substitutionID, Items: []LinkConfig{
					{adIntelConfigID, ExternalProduct, "", nil, nil},
				},
			},
		},
	},
}

var socialMarketingConfig = []LinkConfig{
	{
		"nav-social-marketing", Link, "/account/location/<accountGroupId>/social", []LinkConfig{
			{"nav-social", Link, "/account/location/<accountGroupId>/social/overview", nil, nil},
		}, &SubstitutionRules{
			SubstituteNavConfig: &NavigationConfig{
				ConfigID: substitutionID, Items: []LinkConfig{
					{socialMarketingConfigID, ExternalProduct, "", nil, nil},
				},
			},
		},
	},
}

var indigodigitalConfig = appendConfigs(
	[]LinkConfig{
		// { NavItemID, Type, Path, Children, SubstitutionRules }
		{"nav-home", Link, "/account/location/<accountGroupId>/home", nil, nil},
		{"nav-inbox", Link, "/account/location/<accountGroupId>/inbox", nil, nil},
	},

	crmConfig,

	[]LinkConfig{
		{"nav-customer-list", Link, "/account/location/<accountGroupId>/customer-list", nil, nil},
	},
	campaignsConfig,
	[]LinkConfig{
		{"nav-executive-report", Link, "/account/location/<accountGroupId>/executive-report/weekly", nil, nil},
		{"marketplace-products", Placeholder, "", nil, nil},
	},

	// Configured products
	adIntelConfig,
	listingsConfig,
	reputationConfig,
	[]LinkConfig{
		{websiteProConfigID, ExternalProduct, "", nil, nil},
		{customerVoiceConfigID, ExternalProduct, "", nil, nil},
	},
	socialMarketingConfig,

	[]LinkConfig{
		{"nav-products", Link, "/account/location/<accountGroupId>/products", nil, nil},
		{"nav-store", Link, "/account/location/<accountGroupId>/store", nil, nil},
	},
	administrationLinkConfig,
)

var herstMediaConfig = func() []LinkConfig {
	herstConfig := make([]LinkConfig, len(singleLocationNavTabs))
	copy(herstConfig, singleLocationNavTabs)
	return slices.DeleteFunc(herstConfig, func(c LinkConfig) bool { return c.NavItemID == "nav-products" })
}()

var cerberusDigitalConfig = appendConfigs(
	[]LinkConfig{
		// { NavItemID, Type, Path, Children, SubstitutionRules }
		{"nav-home", Link, "/account/location/<accountGroupId>/home", nil, nil},
		{"nav-inbox", Link, "/account/location/<accountGroupId>/inbox", nil, nil},
	},

	crmConfig,
	aiAssistantsConfig,

	[]LinkConfig{
		{"nav-customer-list", Link, "/account/location/<accountGroupId>/customer-list", nil, nil},
	},
	campaignsConfig,
	[]LinkConfig{
		{"nav-executive-report", Link, "/account/location/<accountGroupId>/executive-report/weekly", nil, nil},
	},

	// Configured products
	listingsConfig,
	reputationConfig,
	socialMarketingConfig,
	[]LinkConfig{
		{websiteProConfigID, ExternalProduct, "", nil, nil},

		{SEOAlphaConfigID, ExternalProduct, "", nil, nil},
		{SEOHikeConfigID, ExternalProduct, "", nil, nil},

		{GoogleSearchAdsSmallBizConfigID, ExternalProduct, "", nil, nil},
		{GoogleDisplayAdsSmallBizConfigID, ExternalProduct, "", nil, nil},
	},
	adIntelConfig,

	[]LinkConfig{
		{customerVoiceConfigID, ExternalProduct, "", nil, nil},
		{calendarHeroConfigID, ExternalProduct, "", nil, nil},

		{"marketplace-products", Placeholder, "", nil, nil},

		{"nav-products", Link, "/account/location/<accountGroupId>/products", nil, nil},

		{"nav-store", Link, "/account/location/<accountGroupId>/store", nil, nil},
	},
	administrationLinkConfig,
)

var vitazaDigitalConfig = appendConfigs(
	[]LinkConfig{
		// { NavItemID, Type, Path, Children, SubstitutionRules }
		{"nav-home", Link, "/account/location/<accountGroupId>/home", nil, nil},
		{"nav-inbox", Link, "/account/location/<accountGroupId>/inbox", nil, nil},
	},

	crmConfig,
	aiAssistantsConfig,

	[]LinkConfig{
		{"nav-custom-forms", Link, "/account/location/custom-forms", nil, nil},
		{"nav-customer-list", Link, "/account/location/<accountGroupId>/customer-list", nil, nil},
		{"nav-executive-report", Link, "/account/location/<accountGroupId>/executive-report/weekly", nil, nil},
	},
	campaignsConfig,
	[]LinkConfig{},

	// Configured products
	[]LinkConfig{
		GetExternalLinkForProduct("MP-6L742WPPBGZCV5PVTXKP3KWRZ4HTQJ28"),
		GetExternalLinkForProduct("MP-5XQXZP2NX676LX74SMKBGM3HQPT2NLT5"),
		GetExternalLinkForProduct("MP-X44RWDQGHC2NGDN63TC5BRQMBKQP4TPQ"),
		GetExternalLinkForProduct("MP-3RSR8MSSL3GJT8BH4K62CNHNH66H3QJH"),
		GetExternalLinkForProduct("MP-********************************"),
		GetExternalLinkForProduct("MP-KNDN6KPHQ4K2DGB57C552DVN5JNNFJCB"),
		GetExternalLinkForProduct("MP-QVF65PL6HGFPRMKXJSBKPZNWXDWNCVCK"),
		GetExternalLinkForProduct("MP-5MKQ4X8NWK8FJ7ZLRX5KD7DWG45CDS6T"),
		GetExternalLinkForProduct("MP-65RLZC2FKG3MM4GHCCV6FZWC3L57652K"),
		GetExternalLinkForProduct("MP-TWG2D7DWPXZRWRRDB37BBLLKS2MBC4CL"),
		GetExternalLinkForProduct("MP-4HZKKK4DKXB6QFQ8QGNL2FRCSTG3RSTR"),
		GetExternalLinkForProduct(websiteProConfigID),
		GetExternalLinkForProduct("MP-16d5e227e7b647afaf233595b21bef7a"),
		GetExternalLinkForProduct("MP-T2RPGQHKWCMTHBMVCF3NHV6WSPNGPJPW"),
		GetExternalLinkForProduct("MP-492e1467b897434aa528bd8e74dcde0d"),
		GetExternalLinkForProduct("MP-03fc7fb5e3b848cd88523515cd66ea9c"),
		GetExternalLinkForProduct(customerVoiceConfigID),
		GetExternalLinkForProduct(calendarHeroConfigID),
	},

	listingsConfig,

	[]LinkConfig{
		GetExternalLinkForProduct("MP-6GLFKDNCV3LTHTRZ6DDXLJN7MLVBBWGW"),
		GetExternalLinkForProduct("MP-FQ264D5N2822PT7ST6BVPFST6QHSKZC8"),
	},

	reputationConfig,
	socialMarketingConfig,

	[]LinkConfig{
		GetExternalLinkForProduct("MP-GB6TPVDDJVK3CK7MQW86XG75TMRCXNJK"),
		GetExternalLinkForProduct("MP-KJSKWHP4PTRZXNTT2GKKRJRWXVH4PDHT"),
		GetExternalLinkForProduct("MP-XQF5VKFP2PLW87RSVBF6TWNKRJCJQKSQ"),
		GetExternalLinkForProduct("MP-ZDG5JR684VX5XDHBKQHRH6NDT5S258ZD"),
		GetExternalLinkForProduct("MP-7SVRPZ7X8BNXHVL2SXTRZDJCMRK4XLDM"),
		GetExternalLinkForProduct("MP-87X8H7C8KZKW6LGQ2XP7JS7W72Q6F427"),
		GetExternalLinkForProduct("MP-HC57CZ6CXNKTVXMLTCCMBFZVRNDXF4SL"),
		GetExternalLinkForProduct("MP-Q5R52HBZ2Q88Z232H77R2KRKZLT2JT7D"),
		GetExternalLinkForProduct("MP-HZBQQCFV6WMVFB2JNMFFWN5658L23LRP"),
		GetExternalLinkForProduct(SEOHikeConfigID),
		GetExternalLinkForProduct("MP-XB8JLCWPBF7Z22ZGCH3MZBHCF3L6KPML"),
		GetExternalLinkForProduct("MP-C778MX455BLPBM8BXSZWHDHKDZ336K86"),
		GetExternalLinkForProduct("MP-WM65PB2J5PL6BCNLB5JJN58DDQWXZVPN"),
		GetExternalLinkForProduct("MP-2JB3LB2H8N77R325X84KBNP4XDTTMRGC"),
		GetExternalLinkForProduct("MP-GF3D2CNFXDBTBRVCXLDPMB57TMBF73TW"),
	},

	adIntelConfig,

	[]LinkConfig{
		GetExternalLinkForProduct("MP-********************************"),
		GetExternalLinkForProduct("MP-4M8GCJ8T2N46KNTTV3XQ3FDC7LGC2QL4"),
		GetExternalLinkForProduct("A-FZ4XVS2WWF"),
		GetExternalLinkForProduct("MP-2da7414a9f604f518d6ae0127080f9e2"),
		GetExternalLinkForProduct("MP-CQB668KS4XSRDHFGRGQ4358K2VCWHV3M"),
		GetExternalLinkForProduct("MP-FVGG6TS6J5V5ZJQPS7GGP25838QSXCVD"),
	},

	[]LinkConfig{
		{"marketplace-products", Placeholder, "", nil, nil},
		{"nav-products", Link, "/account/location/<accountGroupId>/products", nil, nil},
		{"nav-store", Link, "/account/location/<accountGroupId>/store", nil, nil},
	},
	administrationLinkConfig,
)

var PaymentsConfig = []LinkConfig{
	{
		"payments", Container, "", []LinkConfig{
			{"nav-payments-invoices", Link, "/account/location/<accountGroupId>/invoices", nil, nil},
			{"nav-payments-payments", Link, "/account/location/<accountGroupId>/payments", nil, nil},
			{"nav-payments-payouts", Link, "/account/location/<accountGroupId>/payouts", nil, nil},
		}, nil,
	},
}

var crmConfig = []LinkConfig{
	{
		"crm", Container, "", []LinkConfig{
			{"nav-crm", Link, "/account/location/<accountGroupId>/crm/contact", nil, nil},
			{"nav-crm-companies", Link, "/account/location/<accountGroupId>/crm/company", nil, nil},
			{"nav-crm-activities", Link, "/account/location/<accountGroupId>/crm/activities/feed", nil, nil},
			{"nav-crm-opportunities", Link, "/account/location/<accountGroupId>/crm/opportunity", nil, nil},
			{"nav-crm-tasks", Link, "/account/location/<accountGroupId>/crm/task", nil, nil},
			{"nav-crm-custom-objects", Placeholder, "", nil, nil},
			{"nav-dynamic-lists", Link, "/account/location/<accountGroupId>/lists", nil, nil},
			{"nav-leaderboard", Link, "/account/location/<accountGroupId>/leaderboard", nil, nil},
			{"nav-custom-forms", Link, "/account/location/<accountGroupId>/custom-forms", nil, nil},
			{"nav-crm-meeting-scheduler", Link, "/account/location/<accountGroupId>/events-and-meetings/schedule", nil, nil},
		}, nil,
	},
}

var crmMobileConfig = []LinkConfig{
	{
		"crm", Container, "", []LinkConfig{
			{"nav-crm", Link, "/account/location/<accountGroupId>/crm/contact", nil, nil},
			{"nav-crm-companies", Link, "/account/location/<accountGroupId>/crm/company", nil, nil},
			{"nav-crm-activities", Link, "/account/location/<accountGroupId>/crm/activities/feed", nil, nil},
			{"nav-crm-opportunities", Link, "/account/location/<accountGroupId>/crm/opportunity", nil, nil},
			{"nav-crm-tasks", Link, "/account/location/<accountGroupId>/crm/task", nil, nil},
			{"nav-crm-custom-objects", Placeholder, "", nil, nil},
			{"nav-dynamic-lists", Link, "/account/location/<accountGroupId>/lists", nil, nil},
			{"nav-leaderboard", Link, "/account/location/<accountGroupId>/leaderboard", nil, nil},
		}, nil,
	},
}

var aiAssistantsConfig = []LinkConfig{
	{"ai", Container, "", []LinkConfig{
		{"nav-ai-assistants", Link, "/account/location/<accountGroupId>/ai/assistants", nil, nil},
		{"nav-ai-assistants-knowledge", Link, "/account/location/<accountGroupId>/ai/knowledge-base", nil, nil},
	}, nil},
}

var campaignsConfig = []LinkConfig{
	{"nav-all-campaigns", Link, "/account/location/<accountGroupId>/campaigns", nil, nil},
}

var mobileTabsConfig = appendConfigs(
	[]LinkConfig{
		{"nav-home", Link, "/account/location/<accountGroupId>/home", nil, nil},
		{"nav-inbox", Link, "/account/location/<accountGroupId>/inbox", nil, nil},
	},
	crmMobileConfig,
	aiAssistantsConfig,
	[]LinkConfig{
		{"nav-executive-report", Link, "/account/location/<accountGroupId>/executive-report/monthly", nil, nil},
	},
	reputationConfig,
	listingsConfig,
	adminMobileConfig,
)

func appendConfigs(configs ...[]LinkConfig) []LinkConfig {
	var result []LinkConfig
	for _, config := range configs {
		result = append(result, config...)
	}
	return result
}

func GetMultilocationConfigID(pid string) string {
	return fmt.Sprintf("multilocation-%s", pid)
}

func GetExternalLinkForProduct(productID string) LinkConfig {
	return LinkConfig{
		NavItemID: constants.InternalGetExternalProductConfigID(productID),
		Type:      ExternalProduct,
	}
}
