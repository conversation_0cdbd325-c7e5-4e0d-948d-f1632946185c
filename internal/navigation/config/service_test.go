package config_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/vendasta/atlas/internal/navigation/config"
	"github.com/vendasta/atlas/internal/navigation/config/mocks"
	"github.com/vendasta/gosdks/verrors"
)

const testConfig = "testConfig"
const testID1, testID2, testID3 = "testID1", "testID2", "testID3"
const testParentID1 = "testParentID1"

func TestService_mapsStoredConfigs_toNavItems(t *testing.T) {
	mockStore := mocks.NewMockstore(gomock.NewController(t))
	mockStore.EXPECT().Get(testConfig).Return(&config.NavigationConfig{
		ConfigID: testConfig,
		Items: []config.LinkConfig{
			{NavItemID: testID1, Type: config.Link},
			{NavItemID: testParentID1, Type: config.Container, Children: []config.LinkConfig{
				{NavItemID: testID2, Type: config.Link},
			}},
		},
	}, nil)

	expectedNavItems := []config.LinkConfig{
		{NavItemID: testID1, Type: config.Link},
		{NavItemID: testParentID1, Type: config.Container, Children: []config.LinkConfig{
			{NavItemID: testID2, Type: config.Link},
		}},
	}

	service := config.New(mockStore)
	config, err := service.GetLinkConfig(testConfig)

	assert.NoError(t, err)
	assert.EqualValues(t, expectedNavItems, config.Items)
}

func TestService_returnsError_whenStoreFails(t *testing.T) {
	mockStore := mocks.NewMockstore(gomock.NewController(t))
	mockStore.EXPECT().Get(testConfig).Return(nil, verrors.New(verrors.Internal, "test error"))

	service := config.New(mockStore)
	_, err := service.GetLinkConfig(testConfig)

	assert.Error(t, err)
}

func TestService_configContainerWithManyItems_orderIsPreserved(t *testing.T) {
	testID4, testID5 := "testID4", "testID5"

	mockStore := mocks.NewMockstore(gomock.NewController(t))
	mockStore.EXPECT().Get(testConfig).Return(&config.NavigationConfig{
		ConfigID: testConfig,
		Items: []config.LinkConfig{
			{NavItemID: testID1, Type: config.Link},
			{NavItemID: testParentID1, Type: config.Container, Children: []config.LinkConfig{

				{NavItemID: testID2, Type: config.Link},
				{NavItemID: testID3, Type: config.Link},
				{NavItemID: testID5, Type: config.Link},
			}},
			{NavItemID: testID4, Type: config.Link},
		},
	}, nil)

	expectedNavItems := []config.LinkConfig{
		{NavItemID: testID1, Type: config.Link},
		{NavItemID: testParentID1, Type: config.Container, Children: []config.LinkConfig{
			{NavItemID: testID2, Type: config.Link},
			{NavItemID: testID3, Type: config.Link},
			{NavItemID: testID5, Type: config.Link},
		}},
		{NavItemID: testID4, Type: config.Link},
	}

	service := config.New(mockStore)
	config, err := service.GetLinkConfig(testConfig)

	assert.NoError(t, err)
	assert.EqualValues(t, expectedNavItems, config.Items)
}

func TestService_BuildsCorrectSubstitutionRules(t *testing.T) {
	mockStore := mocks.NewMockstore(gomock.NewController(t))

	substituteID := "sub1"

	testCases := []struct {
		desc               string
		config             []config.LinkConfig
		expectedSubstitute config.LinkConfig
	}{
		{
			desc: "Can substitute a link for a link",
			config: []config.LinkConfig{
				{
					NavItemID: testID1,
					Type:      config.Link,
					Rules: &config.SubstitutionRules{&config.NavigationConfig{Items: []config.LinkConfig{{
						NavItemID: substituteID,
						Type:      config.Link,
					}}}},
				}},
			expectedSubstitute: config.LinkConfig{
				NavItemID: substituteID,
				Type:      config.Link,
			}},
		{
			desc: "Can substitute a container for a link",
			config: []config.LinkConfig{
				{
					NavItemID: testID1,
					Type:      config.Container,
					Rules: &config.SubstitutionRules{&config.NavigationConfig{Items: []config.LinkConfig{{
						NavItemID: substituteID,
						Type:      config.Link,
					}}}},
					Children: []config.LinkConfig{
						{
							NavItemID: testID2,
							Type:      config.Link,
						},
					},
				},
			},
			expectedSubstitute: config.LinkConfig{
				NavItemID: substituteID,
				Type:      config.Link,
			}},
		{
			desc: "Can substitute a link in a container for a link in the same container",
			config: []config.LinkConfig{
				{
					NavItemID: testID1,
					Type:      config.Container,
					Children: []config.LinkConfig{
						{
							NavItemID: testID2,
							Type:      config.Link,
							Rules: &config.SubstitutionRules{&config.NavigationConfig{Items: []config.LinkConfig{{
								NavItemID: substituteID,
								Type:      config.Link,
							}}}},
						},
					},
				},
			},
			expectedSubstitute: config.LinkConfig{
				NavItemID: substituteID,
				Type:      config.Link,
			}},
	}

	for _, tt := range testCases {
		t.Run(tt.desc, func(t *testing.T) {
			mockStore.EXPECT().Get(testConfig).Return(&config.NavigationConfig{
				ConfigID: testConfig,
				Items:    tt.config,
			}, nil)

			service := config.New(mockStore)
			config, err := service.GetLinkConfig(testConfig)

			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}

			c := config.Items[0]
			if c.Rules != nil {
				sub := c.Substitute()
				assert.EqualValues(t, tt.expectedSubstitute, sub.Items[0])
			}

			if len(c.Children) > 0 {
				for _, child := range c.Children {
					if child.Rules != nil {
						sub := child.Substitute()
						assert.EqualValues(t, tt.expectedSubstitute, sub.Items[0])
					}
				}
			}
		})
	}
}

func TestGenerateConfigIfNilOption(t *testing.T) {
	mockStore := mocks.NewMockstore(gomock.NewController(t))
	mockStore.EXPECT().GetMulti(gomock.Any()).AnyTimes().Return(nil, nil)

	errConfig := "errConfig"

	testCases := []struct {
		desc        string
		configID    string
		generator   config.ConfigGeneratorFn
		expectedErr error
	}{
		{
			desc:     "Returns error if generator returns error",
			configID: errConfig,
			generator: func(id string) (*config.NavigationConfig, error) {
				return &config.NavigationConfig{}, verrors.New(verrors.InvalidArgument, "error generating config")

			},
			expectedErr: verrors.New(verrors.InvalidArgument, "error generating config"),
		},
		{
			desc:     "Returns generated config if generator returns config",
			configID: testConfig,
			generator: func(id string) (*config.NavigationConfig, error) {
				return &config.NavigationConfig{
					ConfigID: id,
				}, nil
			},
		},
	}

	service := config.New(mockStore)

	for _, tt := range testCases {
		t.Run(tt.desc, func(t *testing.T) {
			_, err := service.GetMultiLinkConfig([]string{tt.configID}, config.GenerateConfigIfNilOption(tt.generator))
			if err != nil {
				assert.EqualValues(t, tt.expectedErr, err)
			}
		})
	}
}
