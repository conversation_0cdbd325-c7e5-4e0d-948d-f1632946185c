package config

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestFilestore_ReturnsErrorIfNoConfigFound(t *testing.T) {
	fs := newFileStore()
	_, err := fs.Get("test")
	assert.Error(t, err)
}

func TestFilestore_ConfigIfConfigFound(t *testing.T) {
	fs := newFileStore()
	items, err := fs.Get(SingleLocationID)
	assert.NoError(t, err)

	assert.Equal(t, SingleLocationID, items.ConfigID)
	assert.NotEmpty(t, items.Items)
}

func TestSanity_AllConfigsAreValid(t *testing.T) {
	store := newFileStore()

	for _, config := range store.List() {
		navConfig, err := store.Get(config)
		assert.NoError(t, err)
		assert.NoError(t, navConfig.Validate())
	}
}

func TestFilestore_RegressionTest_IncludesCalendarHero_ForVitazaDigital(t *testing.T) {
	calHeroNavID := "product-MP-58G85F84DZBND7CK228578MWLD38SS4S"
	fs := newFileStore()

	config, err := fs.Get(vitazaDigitalPID)
	assert.NoError(t, err)

	got := LinkConfig{}
	for _, item := range config.Items {
		if item.NavItemID == calHeroNavID {
			got = item
			break
		}
	}
	want := LinkConfig{NavItemID: calHeroNavID, Type: ExternalProduct}
	assert.Equal(t, want, got, "Calendar Hero Navigation Link Config is not included in partner 0W8W configuration")
	assert.True(t, config.HasFeature(calHeroNavID), "Calendar Hero is not included in partner 0W8W feature set")
}
