package config_test

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/vendasta/atlas/internal/navigation/config"
)

func TestFeatureset(t *testing.T) {
	testCases := []struct {
		name     string
		features []string
		feature  string
		expected bool
	}{
		{
			name:     "empty Featureset",
			features: []string{},
			feature:  "foo",
			expected: false,
		},
		{
			name: "feature not in Featureset",

			features: []string{"bar"},
			feature:  "foo",
			expected: false,
		},
		{
			name:     "feature in Featureset",
			features: []string{"foo"},
			feature:  "foo",
			expected: true,
		},
		{
			name:     "feature in Featureset with other features",
			features: []string{"foo", "bar"},
			feature:  "foo",
			expected: true,
		},
		{
			name:     "feature in Featureset with other features",
			features: []string{"foo", "bar"},
			feature:  "bar",
			expected: true,
		},
		{
			name:     "feature not in Featureset with other features, and feature is subset of other feature",
			features: []string{"foo", "bar"},
			feature:  "ba",
			expected: false,
		},
		{
			name:     "nil features list in Featureset returns false",
			features: nil,
			feature:  "foo",
			expected: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			fs := config.NewFeatureset(tc.features)
			actual := fs.HasFeature(tc.feature)
			assert.Equal(t, tc.expected, actual)
		})
	}
}

func TestFeatureset_DoesNotPanicForNilFeatureset(t *testing.T) {
	var fs *config.Featureset
	fs.HasFeature("foo")
}

func TestFeatureset_FixesProductIdsAutomatically(t *testing.T) {
	features := []string{"MP-foo", "A-bar"}

	fs := config.NewFeatureset(features)
	assert.True(t, fs.HasFeature("product-MP-foo"))
	assert.True(t, fs.HasFeature("product-A-bar"))
}

func TestFeatureset_DoesNotApplyPrefixToPrefixedItems(t *testing.T) {
	features := []string{"product-MP-foo", "product-A-bar", "nav-item-1"}

	fs := config.NewFeatureset(features)
	assert.True(t, fs.HasFeature("product-MP-foo"))
	assert.True(t, fs.HasFeature("product-A-bar"))
	assert.True(t, fs.HasFeature("nav-item-1"))
}
