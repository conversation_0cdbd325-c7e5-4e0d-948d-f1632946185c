package config

import (
	"strings"

	"github.com/vendasta/atlas/internal/navigation/internal/constants"
)

type Featureset struct {
	features []string
}

func NewFeatureset(features []string) *Featureset {
	for i, f := range features {
		if strings.HasPrefix(f, "MP-") || strings.HasPrefix(f, "A-") {
			features[i] = constants.InternalGetExternalProductConfigID(f)
		}
	}

	return &Featureset{features: features}
}

func (fs *Featureset) HasFeature(feature string) bool {
	if fs == nil {
		return false
	}

	for _, f := range fs.features {
		if f == feature {
			return true
		}
	}
	return false
}
