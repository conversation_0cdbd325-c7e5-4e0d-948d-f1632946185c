package config

import (
	"github.com/vendasta/gosdks/verrors"
)

//go:generate mockgen -source=service.go -destination=mocks/store.go -package=mocks
type store interface {
	// Get returns the navigation config by ID
	Get(id string) (*NavigationConfig, error)
	// GetMulti returns multiple navigation configs by ID
	GetMulti(ids []string) ([]*NavigationConfig, error)
}

func New(s store) *NavigationConfigService {
	if s == nil {
		s = newFileStore()
	}

	return &NavigationConfigService{
		store: s,
	}
}

type NavigationConfigService struct {
	store store
}

// ConfigOptions modify the bahavior of the GetLinkConfig and GetMultiLinkConfig methods
// to handle cases where a stored config cannot encapsulate the desired behavior.
// nolint:golint
type ConfigOption func(string, **NavigationConfig) error

// nolint:golint
type ConfigGeneratorFn func(id string) (*NavigationConfig, error)

// If a config is not found for the given ID, a new config is built using the builder function.
func GenerateConfigIfNilOption(g ConfigGeneratorFn) ConfigOption {
	return func(id string, config **NavigationConfig) error {
		if *config != nil {
			return nil
		}

		if g == nil {
			return verrors.New(verrors.Internal, "no builder function provided")
		}

		newConfig, err := g(id)
		if err != nil {
			return err
		}

		*config = newConfig

		return nil
	}
}

func (s *NavigationConfigService) GetLinkConfig(id string, opts ...ConfigOption) (*NavigationConfig, error) {
	config, err := s.store.Get(id)
	if err != nil {
		return nil, err
	}

	if err := applyOptions(id, &config, opts...); err != nil {
		return nil, err
	}

	return config, nil
}

func (s *NavigationConfigService) GetMultiLinkConfig(ids []string, opts ...ConfigOption) ([]*NavigationConfig, error) {
	configs, err := s.store.GetMulti(ids)
	if err != nil {
		return nil, err
	}

	for i := range configs {
		if err := applyOptions(ids[i], &configs[i], opts...); err != nil {
			return nil, err
		}
	}

	return configs, nil
}

func applyOptions(id string, config **NavigationConfig, opts ...ConfigOption) error {
	for _, opt := range opts {
		if err := opt(id, config); err != nil {
			return err
		}
	}

	return nil
}
