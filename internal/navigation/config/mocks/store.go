// Code generated by MockGen. DO NOT EDIT.
// Source: service.go

// Package mocks is a generated GoMock package.
package mocks

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	navigationconfig "github.com/vendasta/atlas/internal/navigation/config"
)

// Mockstore is a mock of store interface.
type Mockstore struct {
	ctrl     *gomock.Controller
	recorder *MockstoreMockRecorder
}

// MockstoreMockRecorder is the mock recorder for Mockstore.
type MockstoreMockRecorder struct {
	mock *Mockstore
}

// NewMockstore creates a new mock instance.
func NewMockstore(ctrl *gomock.Controller) *Mockstore {
	mock := &Mockstore{ctrl: ctrl}
	mock.recorder = &MockstoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *Mockstore) EXPECT() *MockstoreMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *Mockstore) Get(id string) (*navigationconfig.NavigationConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", id)
	ret0, _ := ret[0].(*navigationconfig.NavigationConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockstoreMockRecorder) Get(id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*Mockstore)(nil).Get), id)
}

// GetMulti mocks base method.
func (m *Mockstore) GetMulti(ids []string) ([]*navigationconfig.NavigationConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMulti", ids)
	ret0, _ := ret[0].([]*navigationconfig.NavigationConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMulti indicates an expected call of GetMulti.
func (mr *MockstoreMockRecorder) GetMulti(ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMulti", reflect.TypeOf((*Mockstore)(nil).GetMulti), ids)
}
