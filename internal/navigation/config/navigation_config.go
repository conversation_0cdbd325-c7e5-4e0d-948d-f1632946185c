package config

import "github.com/vendasta/gosdks/verrors"

const (
	navigationConfigErrPrefix  = "invalid NavigationConfig"
	linkConfigErrPrefix        = "invalid LinkConfig"
	substitutionRulesErrPrefix = "invalid SubstitutionRules"
)

func errorPrefixer(prefix string) func(msg string, args ...interface{}) error {
	return func(msg string, args ...interface{}) error {
		err := verrors.New(verrors.InvalidArgument, msg, args...)

		return verrors.WrapError(err, prefix)
	}
}

type NavItemType string

const (
	Link NavItemType = "link"
	// A placeholder is replaced by a dynamically fetched link or set of links
	Placeholder         NavItemType = "placeholder"
	Container           NavItemType = "container"
	ExternalProduct     NavItemType = "external_product"
	CRMCustomObjectType NavItemType = "crm_custom_object_type"
)

type NavigationConfig struct {
	ConfigID string

	// Items are ordered by the order they are in the slice.
	Items []LinkConfig

	// Featureset is a set of features configured by this NavigationConfig.
	// This is used to determine if a feature needs to be injected into the
	// NavigationConfig. This allows us to include new or non-configured
	// features in the NavigationConfig.
	Featureset
}

func (n *NavigationConfig) Validate() (err error) {
	configErr := errorPrefixer(navigationConfigErrPrefix)
	if n == nil {
		return configErr("config is nil")
	}

	if n.ConfigID == "" {
		return configErr("ConfigID is unset")
	}

	if len(n.Items) == 0 {
		return configErr("config has no items")
	}

	for _, item := range n.Items {
		if err := item.Validate(); err != nil {
			return verrors.WrapError(err, navigationConfigErrPrefix)
		}
	}

	return nil
}

type LinkConfig struct {
	NavItemID string

	Type NavItemType
	// Frontend route. Overrides the URL field on the metadata.
	Path     string
	Children []LinkConfig
	Rules    *SubstitutionRules
}

func (l LinkConfig) Substitute() *NavigationConfig {
	if l.Rules.IsEmpty() {
		return nil
	}

	return l.Rules.SubstituteNavConfig
}

func (l LinkConfig) Validate() error {
	configErr := errorPrefixer(linkConfigErrPrefix)
	if l.NavItemID == "" {
		return configErr("NavItemID is empty")
	}

	if l.Type == "" {
		return configErr("`%s` has no type", l.NavItemID)
	}

	if l.Type == Container && len(l.Children) == 0 {
		return configErr("`%s` is a container with no children", l.NavItemID)
	}

	if l.Rules != nil {
		if err := l.Rules.Validate(); err != nil {
			return verrors.WrapError(err, linkConfigErrPrefix)
		}
	}

	for _, child := range l.Children {
		if err := child.Validate(); err != nil {
			return verrors.WrapError(err, "`%s` has invalid Child", l.NavItemID)
		}
	}

	return nil
}

type SubstitutionRules struct {
	// If an item is not accessible, such as in at least these two cases:
	// 1. The user does not have access to the item
	// 2. The item is a container and all of its children are not accessible
	// Then the item will be replaced with the item specified by this field.
	//
	// Note: while this rule supports recursive substitutions,
	// the rule processor may not.
	SubstituteNavConfig *NavigationConfig
}

func (s *SubstitutionRules) IsEmpty() bool {
	return s == nil
}

func (s *SubstitutionRules) Validate() error {
	if s == nil {
		return nil
	}

	if err := s.SubstituteNavConfig.Validate(); err != nil {
		return verrors.WrapError(err, substitutionRulesErrPrefix)
	}

	return nil
}
