package navigation

import (
	"context"
	"fmt"
	"os"
	"reflect"
	"strings"
	"testing"

	"github.com/vendasta/atlas/internal/crmcustomobjects"

	navigationconfig "github.com/vendasta/atlas/internal/navigation/config"

	"github.com/google/go-cmp/cmp"
	"github.com/stretchr/testify/assert"
	atlas_v1 "github.com/vendasta/generated-protos-go/atlas/v1"

	featureflags "github.com/vendasta/atlas/internal/feature_flags"
	"github.com/vendasta/atlas/internal/location"
	"github.com/vendasta/atlas/internal/navigation/internal/constants"
	"github.com/vendasta/atlas/internal/product"
	"github.com/vendasta/atlas/internal/sidenavigation"
)

var protoDiffer = cmp.FilterPath(
	func(p cmp.Path) bool {
		return strings.Contains(p.String(), "state") || strings.Contains(p.String(), "sizeCache") || strings.Contains(p.String(), "unknownFields")
	},
	cmp.Ignore(),
)

func Test_buildDropdownItems(t *testing.T) {
	os.Setenv("ENVIRONMENT", "test")

	tests := []struct {
		name string
		args *GetNavigationItemsArgs
		want []*atlas_v1.DropdownItem
	}{
		{
			name: "partner (no SMB subject ID) gets a sign out dropdown item",
			args: &GetNavigationItemsArgs{
				WhitelabelURL: "https://abc.smblogin.com",
			},
			want: []*atlas_v1.DropdownItem{
				{
					Url:           fmt.Sprintf("%s/logout/", "https://abc.smblogin.com"),
					Path:          "/logout/",
					TranslationId: "NAVIGATION.MENU.SIGN_OUT",
					Label:         "Sign Out",
				},
			},
		},
		{
			name: "impersonating and partner (no SMB subject ID) gets stop impersonating and sign out dropdown item",
			args: &GetNavigationItemsArgs{
				WhitelabelURL:   "https://abc.smblogin.com",
				PartnerID:       "ABC",
				IsImpersonating: true,
			},
			want: []*atlas_v1.DropdownItem{
				{
					Url:           "https://sso-api-demo.apigateway.co/ABC/stop-impersonation",
					Path:          "",
					TranslationId: "NAVIGATION.MENU.STOP_IMPERSONATING",
					Label:         "Stop Impersonating",
				},
				{
					Url:           fmt.Sprintf("%s/logout/", "https://abc.smblogin.com"),
					Path:          "/logout/",
					TranslationId: "NAVIGATION.MENU.SIGN_OUT",
					Label:         "Sign Out",
				},
			},
		},
		{
			name: "should return edit profile",
			args: &GetNavigationItemsArgs{
				WhitelabelURL:      "https://abc.smblogin.com",
				SubjectID:          "U-123",
				EditProfileEnabled: true,
			},
			want: []*atlas_v1.DropdownItem{
				{
					Url:           fmt.Sprintf("%s/settings/user-profile/U-123", "https://abc.smblogin.com"),
					Path:          "/settings/user-profile/U-123",
					TranslationId: "NAVIGATION.MENU.EDIT_PROFILE",
					Label:         "Edit Profile",
				},
				{
					Url:           fmt.Sprintf("%s/logout/", "https://abc.smblogin.com"),
					Path:          "/logout/",
					TranslationId: "NAVIGATION.MENU.SIGN_OUT",
					Label:         "Sign Out",
				},
			},
		},
		{
			name: "should return edit profile with AG set",
			args: &GetNavigationItemsArgs{
				WhitelabelURL:      "https://abc.smblogin.com",
				SubjectID:          "U-123",
				AccountGroupID:     "AG-123",
				EditProfileEnabled: true,
			},
			want: []*atlas_v1.DropdownItem{
				{
					Url:           fmt.Sprintf("%s/account/location/AG-123/settings/notifications/U-123/settings", "https://abc.smblogin.com"),
					Path:          "/account/location/AG-123/settings/notifications/U-123/settings",
					TranslationId: "NAVIGATION.MENU.EDIT_PROFILE",
					Label:         "Edit Profile",
				},
				{
					Url:           fmt.Sprintf("%s/logout/", "https://abc.smblogin.com"),
					Path:          "/logout/",
					TranslationId: "NAVIGATION.MENU.SIGN_OUT",
					Label:         "Sign Out",
				},
			},
		},
		{
			name: "should return edit profile with group path set",
			args: &GetNavigationItemsArgs{
				WhitelabelURL:      "https://abc.smblogin.com",
				SubjectID:          "U-123",
				GroupPath:          "G-123",
				EditProfileEnabled: true,
			},
			want: []*atlas_v1.DropdownItem{
				{
					Url:           "https://abc.smblogin.com/account/brands/G-123/settings/user-profile/U-123",
					Path:          "/account/brands/G-123/settings/user-profile/U-123",
					TranslationId: "NAVIGATION.MENU.EDIT_PROFILE",
					Label:         "Edit Profile",
				},
				{
					Url:           "https://abc.smblogin.com/logout/",
					Path:          "/logout/",
					TranslationId: "NAVIGATION.MENU.SIGN_OUT",
					Label:         "Sign Out",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := buildDropdownItems(tt.args); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("buildDropdownItems() = %s", cmp.Diff(got, tt.want, protoDiffer))
			}
		})
	}
}

func Test_buildAdministrationItems(t *testing.T) {
	tests := []struct {
		name string
		args *GetNavigationItemsArgs
		want []*atlas_v1.SideNavigationItem
	}{
		{
			name: "regression test (BC-3157): returns most settings in settings container",
			args: &GetNavigationItemsArgs{
				PartnerID:      "ABC",
				WhitelabelURL:  "https://abc.smblogin.com",
				AccountGroupID: "AG-123",
				SubjectID:      "U-123",
				TabPermissions: []string{"invoices", "social_connections"},
				Features:       &sidenavigation.Features{ShowInvoices: true},
				NavConfig: []navigationconfig.LinkConfig{
					{
						NavItemID: "nav-administration-link",
						Type:      navigationconfig.Container,
						Children: []navigationconfig.LinkConfig{
							{
								NavItemID: "nav-social-connections",
								Type:      navigationconfig.Link,
								Path:      "/account/location/<accountGroupId>/settings/connections",
							},
							{
								NavItemID: "nav-notification-settings",
								Type:      navigationconfig.Link,
								Path:      "/account/location/<accountGroupId>/settings/notifications/<subjectId>/settings",
							},
							{
								NavItemID: "nav-billing-settings",
								Type:      navigationconfig.Link,
								Path:      "/account/location/<accountGroupId>/settings/billing/<subjectId>/settings",
							},
						},
					},
				},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							TranslationId: "NAVIGATION.TABS.ADMINISTRATION",
							Icon:          "settings",
							Label:         "Administration",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:             "nav-social-connections",
											Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/connections",
											Path:                     "/account/location/AG-123/settings/connections",
											ServiceProviderId:        "VBC",
											TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
											DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
											Label:                    "Social Connections",
											Icon:                     "settings_input_component",
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:             "nav-notification-settings",
											Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/notifications/U-123/settings",
											Path:                     "/account/location/AG-123/settings/notifications/U-123/settings",
											ServiceProviderId:        "VBC",
											TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
											DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
											Label:                    "Notification Settings",
											Icon:                     "notifications",
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:             "nav-billing-settings",
											Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/billing/U-123/settings",
											Path:                     "/account/location/AG-123/settings/billing/U-123/settings",
											ServiceProviderId:        "VBC",
											TranslationId:            "NAVIGATION.TABS.BILLING_SETTINGS",
											DescriptionTranslationId: "NAVIGATION.TABS.BILLING_SETTINGS_DESCRIPTION",
											Label:                    "Billing Settings",
											Icon:                     "receipt",
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewNavItemGenerator(tt.args)
			if err != nil {
				t.Fatal(err)
			}

			if got, _, err := GetNavigationItems(context.Background(), tt.args); !reflect.DeepEqual(got, tt.want) {
				assert.NoError(t, err)
				assert.Equal(t, len(tt.want), len(got))
				for i, item := range got {
					if !reflect.DeepEqual(item, tt.want[i]) {
						t.Fatalf(cmp.Diff(item, tt.want[i], protoDiffer))
					}
				}
			}
		})
	}
}

func Test_buildAccountGroupItems(t *testing.T) {
	tests := []struct {
		name string
		args *GetNavigationItemsArgs
		want []*atlas_v1.SideNavigationItem
	}{
		{
			name: "should return all items",
			args: &GetNavigationItemsArgs{
				PartnerID:      "ABC",
				SubjectID:      "U-123",
				WhitelabelURL:  "https://abc.smblogin.com",
				GroupPath:      "G-123",
				AccountGroupID: "AG-123",
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{
						"recommendations",
						"vbc-executive-report",
						"meeting_scheduler_business_app",
						"content-library",
						"guides",
						"local-marketing-index",
						"access-marketplace",
						"fulfillment",
						"bc_my_team_page",
						"orders",
						"invoices",
						"files",
						"my_products",
						"customer_list",
						"home",
						"inbox",
						"crm_companies",
						"crm_activities",
						"crm_tasks",
						"crm_opportunities",
						"dynamic_lists",
						"leaderboard",
						"custom_forms",
						"inbox_payment_link",
						"ai_assistants",
					},
					ShowContentLibrary:    true,
					ShowLmiDashboard:      true,
					ShowExecutiveReport:   true,
					ShowStore:             true,
					ShowFulfillment:       true,
					ShowMarketplace:       true,
					ShowInviteTeam:        true,
					ShowInvoices:          true,
					ShowOrderPage:         true,
					ShowMeetingScheduler:  true,
					ShowFiles:             true,
					ShowMyProducts:        true,
					ShowCustomers:         true,
					ShowCRMCompanies:      true,
					ShowCRMTasks:          true,
					ShowCRMOpportunities:  true,
					ShowDynamicLists:      true,
					ShowLeaderboard:       true,
					ShowCustomForms:       true,
					ShowAutomations:       true,
					ShowHome:              true,
					ShowInboxMessage:      true,
					HasBrandsEnabled:      true,
					HasProductMarketplace: true,
					HasSMPostPerformance:  true,
					ShowAIAssistant:       true,
					ShowLeadScoring:       true,
				},
				FeatureFlags: map[string]bool{
					"meeting_scheduler_business_app":    true,
					"business_emails":                   true,
					"bc_brands_manage_social":           true,
					"crm_company_business_app":          true,
					"sales_feature_business_app":        true,
					"crm_opportunity_business_app":      true,
					"business_app_dynamic_lists":        true,
					"custom_forms_business_app":         true,
					"ai_assistants":                     true,
					"inbox_payment_link":                true,
					featureflags.GoalsForGroupFeatureID: true,
				},
				PlatformMode: constants.WebMode,
				Products: []*product.Product{
					{
						ServiceProviderID: "MP-c4974d390a044c28aec31e421aa662b2",
					},
					{
						ServiceProviderID: "MP-WF5KS7FHTGV4LR5DBQ4D6XKCCDB5B6G7",
					},
				},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-home",
							Url:               "https://abc.smblogin.com/account/location/AG-123/home",
							Path:              "/account/location/AG-123/home",
							ServiceProviderId: "VBC",
							Icon:              "home",
							TranslationId:     "NAVIGATION.TABS.HOME",
							Label:             "Home",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-inbox",
							Url:               "https://abc.smblogin.com/account/location/AG-123/inbox",
							Path:              "/account/location/AG-123/inbox",
							ServiceProviderId: "VBC",
							Icon:              "question_answer",
							TranslationId:     "NAVIGATION.TABS.INBOX",
							Label:             "Inbox Messages",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							Label:         "Contacts",
							TranslationId: "NAVIGATION.TABS.CRM.CRM",
							Icon:          "contacts",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-crm",
											Url:               "https://abc.smblogin.com/account/location/AG-123/crm/contact",
											Path:              "/account/location/AG-123/crm/contact",
											ServiceProviderId: "VBC",
											Icon:              "person",
											TranslationId:     "NAVIGATION.TABS.CRM.CONTACT",
											Label:             "Contacts",
											ShowIcon:          false,
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-crm-companies",
											Url:               "https://abc.smblogin.com/account/location/AG-123/crm/company",
											Path:              "/account/location/AG-123/crm/company",
											ServiceProviderId: "VBC",
											Icon:              "location_city",
											TranslationId:     "NAVIGATION.TABS.CRM.COMPANY",
											Label:             "Companies",
											ShowIcon:          false,
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-crm-activities",
											Url:               "https://abc.smblogin.com/account/location/AG-123/crm/activities/feed",
											Path:              "/account/location/AG-123/crm/activities/feed",
											ServiceProviderId: "VBC",
											Icon:              "feed",
											TranslationId:     "NAVIGATION.TABS.CRM.ACTIVITY_FEED",
											Label:             "Activity feed",
											ShowIcon:          false,
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-crm-opportunities",
											Url:               "https://abc.smblogin.com/account/location/AG-123/crm/opportunity",
											Path:              "/account/location/AG-123/crm/opportunity",
											ServiceProviderId: "VBC",
											Icon:              "monetization_on",
											TranslationId:     "NAVIGATION.TABS.CRM.OPPORTUNITIES",
											Label:             "Opportunities",
											ShowIcon:          false,
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-crm-tasks",
											Url:               "https://abc.smblogin.com/account/location/AG-123/crm/task",
											Path:              "/account/location/AG-123/crm/task",
											ServiceProviderId: "VBC",
											Icon:              "assignment_turned_in",
											TranslationId:     "NAVIGATION.TABS.CRM.SALES_TASKS",
											Label:             "Sales tasks",
											ShowIcon:          false,
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-dynamic-lists",
											Url:               "https://abc.smblogin.com/account/location/AG-123/lists",
											Path:              "/account/location/AG-123/lists",
											ServiceProviderId: "VBC",
											Icon:              "reorder",
											TranslationId:     "NAVIGATION.TABS.DYNAMIC_LISTS",
											Label:             "Lists",
											ShowIcon:          false,
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-leaderboard",
											Url:               "https://abc.smblogin.com/account/location/AG-123/leaderboard",
											Path:              "/account/location/AG-123/leaderboard",
											ServiceProviderId: "VBC",
											Icon:              "leaderboard",
											TranslationId:     "NAVIGATION.TABS.LEADERBOARD",
											Label:             "Leaderboard",
											ShowIcon:          false,
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-custom-forms",
											Url:               "https://abc.smblogin.com/account/location/AG-123/custom-forms",
											Path:              "/account/location/AG-123/custom-forms",
											ServiceProviderId: "VBC",
											Icon:              "description",
											TranslationId:     "NAVIGATION.TABS.CUSTOM_FORMS",
											Label:             "Forms",
											ShowIcon:          false,
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-meeting-scheduler",
											Url:               "https://abc.smblogin.com/account/location/AG-123/events-and-meetings/schedule",
											Path:              "/account/location/AG-123/events-and-meetings/schedule",
											ServiceProviderId: "VBC",
											Icon:              "event",
											TranslationId:     "NAVIGATION.TABS.MEETING_SCHEDULER",
											Label:             "My Meetings",
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							Label:         "Payments",
							TranslationId: "NAVIGATION.TABS.PAYMENTS",
							Icon:          "wallet",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-payments-invoices",
											Url:               "https://abc.smblogin.com/account/location/AG-123/invoices",
											Path:              "/account/location/AG-123/invoices",
											ServiceProviderId: "VBC",
											TranslationId:     "NAVIGATION.TABS.PAYMENTS.INVOICES",
											Label:             "Invoices",
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-payments-payments",
											Url:               "https://abc.smblogin.com/account/location/AG-123/payments",
											Path:              "/account/location/AG-123/payments",
											ServiceProviderId: "VBC",
											TranslationId:     "NAVIGATION.TABS.PAYMENTS.PAYMENTS",
											Label:             "Payments",
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-payments-payouts",
											Url:               "https://abc.smblogin.com/account/location/AG-123/payouts",
											Path:              "/account/location/AG-123/payouts",
											ServiceProviderId: "VBC",
											TranslationId:     "NAVIGATION.TABS.PAYMENTS.PAYOUTS",
											Label:             "Payouts",
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							NavigationId:  "",
							Label:         "AI",
							TranslationId: "NAVIGATION.TABS.AI",
							Icon:          "auto_awesome",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-ai-assistants",
											Url:               "https://abc.smblogin.com/account/location/AG-123/ai/assistants",
											Path:              "/account/location/AG-123/ai/assistants",
											ServiceProviderId: "VBC",
											Icon:              "auto_awesome",
											TranslationId:     "NAVIGATION.TABS.AI_ASSISTANTS",
											Label:             "AI workforce",
											ShowIcon:          false,
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-ai-assistants-knowledge",
											Url:               "https://abc.smblogin.com/account/location/AG-123/ai/knowledge-base",
											Path:              "/account/location/AG-123/ai/knowledge-base",
											ServiceProviderId: "VBC",
											Icon:              "menu_book",
											TranslationId:     "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS",
											Label:             "AI knowledge base",
											ShowIcon:          false,
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-all-campaigns",
							Url:               "https://abc.smblogin.com/account/location/AG-123/campaigns",
							Path:              "/account/location/AG-123/campaigns",
							ServiceProviderId: "VBC",
							Icon:              "mark_email_read",
							TranslationId:     "NAVIGATION.TABS.CAMPAIGNS",
							Label:             "Campaigns",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-executive-report",
							Url:               "https://abc.smblogin.com/account/location/AG-123/executive-report/monthly",
							Path:              "/account/location/AG-123/executive-report/monthly",
							ServiceProviderId: "VBC",
							Icon:              "timeline",
							TranslationId:     "NAVIGATION.TABS.EXECUTIVE_REPORT",
							Label:             "Executive Report",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-products",
							Url:               "https://abc.smblogin.com/account/location/AG-123/products",
							Path:              "/account/location/AG-123/products",
							ServiceProviderId: "VBC",
							Icon:              "apps",
							TranslationId:     "NAVIGATION.TABS.MY_PRODUCTS",
							Label:             "My Products",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-store",
							Url:               "https://abc.smblogin.com/account/location/AG-123/store",
							Path:              "/account/location/AG-123/store",
							ServiceProviderId: "VBC",
							Icon:              "shopping_basket",
							TranslationId:     "NAVIGATION.TABS.STORE",
							Label:             "Store",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:             "nav-automations-mine",
							Url:                      "https://abc.smblogin.com/account/location/AG-123/automations/all",
							Path:                     "/account/location/AG-123/automations/all",
							ServiceProviderId:        "VBC",
							Icon:                     "offline_bolt",
							TranslationId:            "NAVIGATION.TABS.AUTOMATIONS",
							Label:                    "Automations",
							ShowIcon:                 true,
							DescriptionTranslationId: "NAVIGATION.TABS.AUTOMATIONS_DESCRIPTION",
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-settings-link",
							Url:               "https://abc.smblogin.com/account/location/AG-123/administration",
							Path:              "/account/location/AG-123/administration",
							ServiceProviderId: "VBC",
							Icon:              "settings",
							TranslationId:     "NAVIGATION.TABS.ADMINISTRATION",
							Label:             "Administration",
							ShowIcon:          true,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:             "nav-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/profile",
									Path:                     "/account/location/AG-123/administration/profile",
									ServiceProviderId:        "VBC",
									Icon:                     "store_outlined",
									TranslationId:            "NAVIGATION.TABS.BUSINESS_PROFILE",
									Label:                    "Business Profile",
									ShowIcon:                 true,
									DescriptionTranslationId: "NAVIGATION.TABS.BUSINESS_PROFILE_DESCRIPTION",
								},
								{
									NavigationId:             "nav-notification-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/notifications/U-123/settings",
									Path:                     "/account/location/AG-123/administration/notifications/U-123/settings",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
									DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
									Label:                    "Notification Settings",
									Icon:                     "notifications",
								},
								{
									NavigationId:             "nav-billing-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/billing/U-123/settings",
									Path:                     "/account/location/AG-123/administration/billing/U-123/settings",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.BILLING_SETTINGS",
									DescriptionTranslationId: "NAVIGATION.TABS.BILLING_SETTINGS_DESCRIPTION",
									Label:                    "Billing Settings",
									Icon:                     "receipt",
								},
								{
									NavigationId:             "nav-payment-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/payments",
									Path:                     "/account/location/AG-123/administration/payments",
									ServiceProviderId:        "VBC",
									Icon:                     "credit_card",
									TranslationId:            "NAVIGATION.TABS.PAYMENT_SETTINGS",
									Label:                    "Payment Settings",
									DescriptionTranslationId: "NAVIGATION.TABS.PAYMENT_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-automations-mine",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/automations/all",
									Path:                     "/account/location/AG-123/automations/all",
									ServiceProviderId:        "VBC",
									Icon:                     "offline_bolt",
									TranslationId:            "NAVIGATION.TABS.AUTOMATIONS",
									Label:                    "Automations",
									DescriptionTranslationId: "NAVIGATION.TABS.AUTOMATIONS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-social-connections",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/connections",
									Path:                     "/account/location/AG-123/administration/connections",
									ServiceProviderId:        "VBC",
									Icon:                     "settings_input_component",
									TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
									Label:                    "Social Connections",
									DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-field-management-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/field-management",
									Path:                     "/account/location/AG-123/administration/field-management",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.FIELD_MANAGEMENT_SETTINGS",
									DescriptionTranslationId: "NAVIGATION.TABS.FIELD_MANAGEMENT_SETTINGS_DESCRIPTION",
									Label:                    "CRM fields",
									Icon:                     "wysiwyg",
									ChipContent:              "Beta",
								},
								{
									NavigationId:             "nav-lead-scoring",
									ServiceProviderId:        "VBC",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/score",
									Path:                     "/account/location/AG-123/score",
									TranslationId:            "NAVIGATION.TABS.LEAD_SCORING",
									DescriptionTranslationId: "NAVIGATION.TABS.LEAD_SCORING_DESCRIPTION",
									Label:                    "Score",
									Icon:                     "calculate",
								},
								{
									NavigationId:             "nav-ai-knowledge-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/ai-knowledge",
									Path:                     "/account/location/AG-123/administration/ai-knowledge",
									ServiceProviderId:        "VBC",
									Icon:                     "menu_book",
									TranslationId:            "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS",
									Label:                    "AI knowledge base",
									DescriptionTranslationId: "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-ai-assistants-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/ai/assistants",
									Path:                     "/account/location/AG-123/ai/assistants",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.AI_ASSISTANTS",
									DescriptionTranslationId: "NAVIGATION.TABS.AI_ASSISTANTS_SETTINGS_DESCRIPTION",
									Label:                    "AI workforce",
									Icon:                     "smart_toy",
								},
								{
									NavigationId:             "nav-inbox-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/inbox",
									Path:                     "/account/location/AG-123/administration/inbox",
									ServiceProviderId:        "VBC",
									Icon:                     "question_answer",
									TranslationId:            "NAVIGATION.TABS.INBOX_SETTINGS",
									Label:                    "Inbox",
									DescriptionTranslationId: "NAVIGATION.TABS.INBOX_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-email-configuration",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/email-configuration",
									Path:                     "/account/location/AG-123/administration/email-configuration",
									ServiceProviderId:        "VBC",
									Icon:                     "email",
									TranslationId:            "NAVIGATION.TABS.EMAIL_CONFIGURATION",
									Label:                    "Email Configuration",
									DescriptionTranslationId: "NAVIGATION.TABS.EMAIL_CONFIGURATION_DESCRIPTION",
								},
								{
									NavigationId:             "nav-emails",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/email-history",
									Path:                     "/account/location/AG-123/administration/email-history",
									ServiceProviderId:        "VBC",
									Icon:                     "history",
									TranslationId:            "NAVIGATION.TABS.EMAIL_HISTORY",
									Label:                    "Email History",
									DescriptionTranslationId: "NAVIGATION.TABS.EMAIL_HISTORY_DESCRIPTION",
								},
								{
									NavigationId:             "nav-guides",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/guides",
									Path:                     "/account/location/AG-123/administration/guides",
									ServiceProviderId:        "VBC",
									Icon:                     "library_books",
									TranslationId:            "NAVIGATION.TABS.GUIDES",
									Label:                    "Guides",
									DescriptionTranslationId: "NAVIGATION.TABS.GUIDES_DESCRIPTION",
								},
								{
									NavigationId:             "nav-files",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/files",
									Path:                     "/account/location/AG-123/administration/files",
									ServiceProviderId:        "VBC",
									Icon:                     "insert_drive_file",
									TranslationId:            "NAVIGATION.TABS.FILES",
									Label:                    "Files",
									DescriptionTranslationId: "NAVIGATION.TABS.FILES_DESCRIPTION",
								},
								{
									NavigationId:             "nav-fulfillment",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/projects",
									Path:                     "/account/location/AG-123/administration/projects",
									ServiceProviderId:        "VBC",
									Icon:                     "timelapse",
									TranslationId:            "NAVIGATION.TABS.FULFILLMENT",
									Label:                    "Projects",
									DescriptionTranslationId: "NAVIGATION.TABS.FULFILLMENT_DESCRIPTION",
								},
								{
									NavigationId:             "nav-invoices",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/invoices",
									Path:                     "/account/location/AG-123/administration/invoices",
									ServiceProviderId:        "VBC",
									Icon:                     "attach_money",
									TranslationId:            "NAVIGATION.TABS.INVOICES",
									Label:                    "Invoices",
									DescriptionTranslationId: "NAVIGATION.TABS.INVOICES_DESCRIPTION",
								},
								{
									NavigationId:             "nav-orders",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/orders",
									Path:                     "/account/location/AG-123/administration/orders",
									ServiceProviderId:        "VBC",
									Icon:                     "list_alt",
									TranslationId:            "NAVIGATION.TABS.ORDERS",
									Label:                    "Orders",
									DescriptionTranslationId: "NAVIGATION.TABS.ORDERS_DESCRIPTION",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "should return only settings tab - access to none tab",
			args: &GetNavigationItemsArgs{
				AccountGroupID: "AG-123",
				WhitelabelURL:  "https://abc.smblogin.com",
				Features: &sidenavigation.Features{
					ShowFulfillment:      true,
					ShowHome:             true,
					ShowContentLibrary:   true,
					ShowExecutiveReport:  true,
					ShowMyProducts:       true,
					ShowStore:            true,
					ShowMeetingScheduler: true,
					ShowCustomers:        true,
					ShowFiles:            true,
					ShowOrderPage:        true,
					ShowInvoices:         true,
					ShowInboxMessage:     true,
				},
				TabPermissions: []string{""},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-settings-link",
							Url:               "https://abc.smblogin.com/account/location/AG-123/administration",
							Path:              "/account/location/AG-123/administration",
							ServiceProviderId: "VBC",
							Icon:              "settings",
							TranslationId:     "NAVIGATION.TABS.ADMINISTRATION",
							Label:             "Administration",
							ShowIcon:          true,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:             "nav-notification-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/notifications//settings",
									Path:                     "/account/location/AG-123/administration/notifications//settings",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
									DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
									Label:                    "Notification Settings",
									Icon:                     "notifications",
									UserRequired:             true,
								},
								{
									NavigationId:             "nav-email-configuration",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/email-configuration",
									Path:                     "/account/location/AG-123/administration/email-configuration",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.EMAIL_CONFIGURATION",
									DescriptionTranslationId: "NAVIGATION.TABS.EMAIL_CONFIGURATION_DESCRIPTION",
									Label:                    "Email Configuration",
									Icon:                     "email",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "should return the new single tabs that came from multi location features",
			args: &GetNavigationItemsArgs{
				PartnerID:      "VUNI",
				AccountGroupID: "AG-123",
				WhitelabelURL:  "https://abc.smblogin.com",
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{"my_products"},
					ShowMyProducts:  true,
				},
				FeatureFlags: map[string]bool{
					constants.PartnerFeatureIDSingleNav:               true,
					constants.PartnerFeatureIDEmbeddedAdIntel:         true,
					constants.AIAssistants:                            false,
					constants.PartnerFeatureIDEmbeddedSocialMarketing: false,
					constants.PartnerFeatureIDEmbeddedListings:        false,
					constants.NPSNewMLDesign:                          true,
					constants.PartnerFeatureIDLisAnalytics:            true,
					constants.NPSBusinessAppFeatureIDReputation:       true,
				},
				Products: []*product.Product{
					{
						ServiceProviderID: "RM",
						EntryURL:          "https://repman.appspot.com/cv/entry/AG-XJS5MTKWJN/",
						Name:              "Reputation Management",
					},
					{
						ServiceProviderID: "SM",
						Name:              "Social Marketing",
						EntryURL:          "https://socialmarketing.appspot.com/cv/entry/AG-XJS5MTKWJN/",
					},
					{
						ServiceProviderID: "MP-94072e44d5364872b672d7ab4fc7a7e8",
						Name:              "Advertising Intelligence",
						EntryURL:          "https://advertisingintelligence.appspot.com/cv/entry/AG-XJS5MTKWJN/",
					},
				},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      constants.InternalGetExternalProductConfigID("RM"),
							ServiceProviderId: "VBC",
							LaunchUrl:         "https://repman.appspot.com/cv/entry/AG-XJS5MTKWJN/",
							Url:               "https://abc.smblogin.com/account/location/AG-123/reputation",
							Path:              "/account/location/AG-123/reputation",
							Label:             "Reputation Management",
							ShowIcon:          true,
							Pinnable:          true,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:      "nav-reputation-manage",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/manage-reviews",
									Path:              "/account/location/AG-123/reputation/manage-reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "NAVIGATION.TABS.REVIEWS",
									Label:             "Reviews",
								},
								{
									NavigationId:      "nav-reputation-nps",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/netpromoterscore",
									Path:              "/account/location/AG-123/reputation/netpromoterscore",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.NPS",
									Label:             "NPS",
								},
								{
									NavigationId:      "nav-reputation-insights",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/insights",
									Path:              "/account/location/AG-123/reputation/insights",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS",
									Label:             "Insights",
								},
								{
									NavigationId:      "nav-google-my-business-google-q-and-a",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/google-q-and-a",
									Path:              "/account/location/AG-123/reputation/google-q-and-a",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
									Label:             "Google Q&A",
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      constants.InternalGetExternalProductConfigID("SM"),
							ServiceProviderId: "SM",
							Label:             "Social Marketing",
							Url:               "https://socialmarketing.appspot.com/cv/entry/AG-XJS5MTKWJN/",
							ShowIcon:          true,
							Pinnable:          true,
							OpenInNewTab:      true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      constants.InternalGetExternalProductConfigID("MP-94072e44d5364872b672d7ab4fc7a7e8"),
							Label:             "Advertising Intelligence",
							LaunchUrl:         "https://advertisingintelligence.appspot.com/cv/entry/AG-XJS5MTKWJN/",
							Path:              "/account/location/AG-123/advertising",
							Url:               "https://abc.smblogin.com/account/location/AG-123/advertising",
							ServiceProviderId: "VBC",
							ShowIcon:          true,
							Pinnable:          true,
							External:          false,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:      "nav-brand-advertising",
									Url:               "https://abc.smblogin.com/account/location/AG-123/advertising/overview",
									Path:              "/account/location/AG-123/advertising/overview",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
									Label:             "Overview",
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-products",
							Url:               "https://abc.smblogin.com/account/location/AG-123/products",
							Path:              "/account/location/AG-123/products",
							ServiceProviderId: "VBC",
							Icon:              "apps",
							TranslationId:     "NAVIGATION.TABS.MY_PRODUCTS",
							Label:             "My Products",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-settings-link",
							Url:               "https://abc.smblogin.com/account/location/AG-123/administration",
							Path:              "/account/location/AG-123/administration",
							ServiceProviderId: "VBC",
							Icon:              "settings",
							TranslationId:     "NAVIGATION.TABS.ADMINISTRATION",
							Label:             "Administration",
							ShowIcon:          true,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:             "nav-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/profile",
									Path:                     "/account/location/AG-123/administration/profile",
									ServiceProviderId:        "VBC",
									Icon:                     "store_outlined",
									TranslationId:            "NAVIGATION.TABS.BUSINESS_PROFILE",
									Label:                    "Business Profile",
									ShowIcon:                 true,
									DescriptionTranslationId: "NAVIGATION.TABS.BUSINESS_PROFILE_DESCRIPTION",
								},
								{
									NavigationId:             "nav-notification-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/notifications//settings",
									Path:                     "/account/location/AG-123/administration/notifications//settings",
									ServiceProviderId:        "VBC",
									Icon:                     "notifications",
									TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
									Label:                    "Notification Settings",
									UserRequired:             true,
									DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-social-connections",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/connections",
									Path:                     "/account/location/AG-123/administration/connections",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
									DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
									Label:                    "Social Connections",
									Icon:                     "settings_input_component",
								},
								{
									NavigationId:             "nav-email-configuration",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/email-configuration",
									Path:                     "/account/location/AG-123/administration/email-configuration",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.EMAIL_CONFIGURATION",
									DescriptionTranslationId: "NAVIGATION.TABS.EMAIL_CONFIGURATION_DESCRIPTION",
									Label:                    "Email Configuration",
									Icon:                     "email",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "should return the old single tabs for social and listings from multi location features",
			args: &GetNavigationItemsArgs{
				PartnerID:      "VUNI",
				AccountGroupID: "AG-123",
				WhitelabelURL:  "https://abc.smblogin.com",
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{"my_products"},
					ShowMyProducts:  true,
				},
				FeatureFlags: map[string]bool{
					constants.PartnerFeatureIDSingleNav:               true,
					constants.PartnerFeatureIDEmbeddedSocialMarketing: true,
					constants.PartnerFeatureIDEmbeddedListings:        true,
					constants.AIAssistants:                            false,
				},
				Products: []*product.Product{
					{
						ServiceProviderID: "SM",
						Name:              "Social Marketing",
					},
					{
						ServiceProviderID: "MS",
						Name:              "Listing Builder",
					},
				},
				// WARP-1061 Platform mode is enabled to support Local SEO pages in mobile app development,
				// but it shouldn't be displayed in the submitted app. Once shipped, PlatformMode can be removed from this test.
				PlatformMode: PlatformModeWeb,
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      constants.InternalGetExternalProductConfigID("MS"),
							ServiceProviderId: "VBC",
							Label:             "Listing Builder",
							Url:               "https://abc.smblogin.com/account/location/AG-123/listings",
							Path:              "/account/location/AG-123/listings",
							ShowIcon:          true,
							Pinnable:          true,
							OpenInNewTab:      false,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:      "nav-brand-listings-keyword-tracking",
									Url:               "https://abc.smblogin.com/account/location/AG-123/listings/keyword-tracking",
									ServiceProviderId: "VBC",
									Path:              "/account/location/AG-123/listings/keyword-tracking",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.KEYWORD_TRACKING",
									Label:             "Keyword Tracking",
								},
								{
									NavigationId:      "nav-brand-listing-sync",
									Url:               "https://abc.smblogin.com/account/location/AG-123/listings/listing-sync",
									ServiceProviderId: "VBC",
									Path:              "/account/location/AG-123/listings/listing-sync",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.LISTING_SYNC",
									Label:             "Listing Sync",
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      constants.InternalGetExternalProductConfigID("SM"),
							ServiceProviderId: "VBC",
							Label:             "Social Marketing",
							Url:               "https://abc.smblogin.com/account/location/AG-123/social",
							Path:              "/account/location/AG-123/social",
							ShowIcon:          true,
							Pinnable:          true,
							OpenInNewTab:      false,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:      "nav-brand-social",
									Url:               "https://abc.smblogin.com/account/location/AG-123/social/overview",
									Path:              "/account/location/AG-123/social/overview",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
									Label:             "Overview",
									ServiceProviderId: "VBC",
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-products",
							Url:               "https://abc.smblogin.com/account/location/AG-123/products",
							Path:              "/account/location/AG-123/products",
							ServiceProviderId: "VBC",
							Icon:              "apps",
							TranslationId:     "NAVIGATION.TABS.MY_PRODUCTS",
							Label:             "My Products",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-settings-link",
							Url:               "https://abc.smblogin.com/account/location/AG-123/administration",
							Path:              "/account/location/AG-123/administration",
							ServiceProviderId: "VBC",
							Icon:              "settings",
							TranslationId:     "NAVIGATION.TABS.ADMINISTRATION",
							Label:             "Administration",
							ShowIcon:          true,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:             "nav-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/profile",
									Path:                     "/account/location/AG-123/administration/profile",
									ServiceProviderId:        "VBC",
									Icon:                     "store_outlined",
									TranslationId:            "NAVIGATION.TABS.BUSINESS_PROFILE",
									Label:                    "Business Profile",
									ShowIcon:                 true,
									DescriptionTranslationId: "NAVIGATION.TABS.BUSINESS_PROFILE_DESCRIPTION",
								},
								{
									NavigationId:             "nav-notification-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/notifications//settings",
									Path:                     "/account/location/AG-123/administration/notifications//settings",
									ServiceProviderId:        "VBC",
									Icon:                     "notifications",
									TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
									Label:                    "Notification Settings",
									UserRequired:             true,
									DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-social-connections",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/connections",
									Path:                     "/account/location/AG-123/administration/connections",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
									DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
									Label:                    "Social Connections",
									Icon:                     "settings_input_component",
								},
								{
									NavigationId:             "nav-email-configuration",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/email-configuration",
									Path:                     "/account/location/AG-123/administration/email-configuration",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.EMAIL_CONFIGURATION",
									DescriptionTranslationId: "NAVIGATION.TABS.EMAIL_CONFIGURATION_DESCRIPTION",
									Label:                    "Email Configuration",
									Icon:                     "email",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "if config for invoices is off should not return invoices tab and billing settings sub tab",
			args: &GetNavigationItemsArgs{
				AccountGroupID: "AG-123",
				WhitelabelURL:  "https://abc.smblogin.com",
				Features:       &sidenavigation.Features{},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-settings-link",
							Url:               "https://abc.smblogin.com/account/location/AG-123/administration",
							Path:              "/account/location/AG-123/administration",
							ServiceProviderId: "VBC",
							Icon:              "settings",
							TranslationId:     "NAVIGATION.TABS.ADMINISTRATION",
							Label:             "Administration",
							ShowIcon:          true,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:             "nav-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/profile",
									Path:                     "/account/location/AG-123/administration/profile",
									ServiceProviderId:        "VBC",
									Icon:                     "store_outlined",
									TranslationId:            "NAVIGATION.TABS.BUSINESS_PROFILE",
									Label:                    "Business Profile",
									ShowIcon:                 true,
									DescriptionTranslationId: "NAVIGATION.TABS.BUSINESS_PROFILE_DESCRIPTION",
								},
								{
									NavigationId:             "nav-notification-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/notifications//settings",
									Path:                     "/account/location/AG-123/administration/notifications//settings",
									ServiceProviderId:        "VBC",
									Icon:                     "notifications",
									TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
									Label:                    "Notification Settings",
									UserRequired:             true,
									DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-social-connections",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/connections",
									Path:                     "/account/location/AG-123/administration/connections",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
									DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
									Label:                    "Social Connections",
									Icon:                     "settings_input_component",
								},
								{
									NavigationId:             "nav-email-configuration",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/email-configuration",
									Path:                     "/account/location/AG-123/administration/email-configuration",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.EMAIL_CONFIGURATION",
									DescriptionTranslationId: "NAVIGATION.TABS.EMAIL_CONFIGURATION_DESCRIPTION",
									Label:                    "Email Configuration",
									Icon:                     "email",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "should not return the payment settings sublink if feature flag is off",
			args: &GetNavigationItemsArgs{
				PartnerID:      "VUNI",
				AccountGroupID: "AG-123",
				SubjectID:      "U-123",
				WhitelabelURL:  "https://abc.smblogin.com",
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{},
				},
				FeatureFlags: map[string]bool{
					constants.SMBPayments: false,
				},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-settings-link",
							Url:               "https://abc.smblogin.com/account/location/AG-123/administration",
							Path:              "/account/location/AG-123/administration",
							ServiceProviderId: "VBC",
							Icon:              "settings",
							TranslationId:     "NAVIGATION.TABS.ADMINISTRATION",
							Label:             "Administration",
							ShowIcon:          true,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:             "nav-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/profile",
									Path:                     "/account/location/AG-123/administration/profile",
									ServiceProviderId:        "VBC",
									Icon:                     "store_outlined",
									TranslationId:            "NAVIGATION.TABS.BUSINESS_PROFILE",
									Label:                    "Business Profile",
									ShowIcon:                 true,
									DescriptionTranslationId: "NAVIGATION.TABS.BUSINESS_PROFILE_DESCRIPTION",
								},
								{
									NavigationId:             "nav-notification-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/notifications/U-123/settings",
									Path:                     "/account/location/AG-123/administration/notifications/U-123/settings",
									ServiceProviderId:        "VBC",
									Icon:                     "notifications",
									TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
									Label:                    "Notification Settings",
									DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-social-connections",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/connections",
									Path:                     "/account/location/AG-123/administration/connections",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
									DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
									Label:                    "Social Connections",
									Icon:                     "settings_input_component",
								},
								{
									NavigationId:             "nav-email-configuration",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/email-configuration",
									Path:                     "/account/location/AG-123/administration/email-configuration",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.EMAIL_CONFIGURATION",
									DescriptionTranslationId: "NAVIGATION.TABS.EMAIL_CONFIGURATION_DESCRIPTION",
									Label:                    "Email Configuration",
									Icon:                     "email",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "should return nav-integrations in sublink if feature flag is true",
			args: &GetNavigationItemsArgs{
				PartnerID:      "ABC",
				SubjectID:      "U-123",
				WhitelabelURL:  "https://abc.smblogin.com",
				AccountGroupID: "AG-123",
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{},
				},
				FeatureFlags: map[string]bool{
					constants.RouteToNewIntegrations: true,
				},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-settings-link",
							Url:               "https://abc.smblogin.com/account/location/AG-123/administration",
							Path:              "/account/location/AG-123/administration",
							ServiceProviderId: "VBC",
							Icon:              "settings",
							TranslationId:     "NAVIGATION.TABS.ADMINISTRATION",
							Label:             "Administration",
							ShowIcon:          true,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:             "nav-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/profile",
									Path:                     "/account/location/AG-123/administration/profile",
									ServiceProviderId:        "VBC",
									Icon:                     "store_outlined",
									TranslationId:            "NAVIGATION.TABS.BUSINESS_PROFILE",
									Label:                    "Business Profile",
									ShowIcon:                 true,
									DescriptionTranslationId: "NAVIGATION.TABS.BUSINESS_PROFILE_DESCRIPTION",
								},
								{
									NavigationId:             "nav-notification-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/notifications/U-123/settings",
									Path:                     "/account/location/AG-123/administration/notifications/U-123/settings",
									ServiceProviderId:        "VBC",
									Icon:                     "notifications",
									TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
									Label:                    "Notification Settings",
									DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-integrations",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/integrations",
									Path:                     "/account/location/AG-123/administration/integrations",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
									DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
									Label:                    "Social Connections",
									Icon:                     "settings_input_component",
								},
								{
									NavigationId:             "nav-social-connections",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/connections",
									Path:                     "/account/location/AG-123/administration/connections",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
									DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
									Label:                    "Social Connections",
									Icon:                     "settings_input_component",
								},
								{
									NavigationId:             "nav-email-configuration",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/email-configuration",
									Path:                     "/account/location/AG-123/administration/email-configuration",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.EMAIL_CONFIGURATION",
									DescriptionTranslationId: "NAVIGATION.TABS.EMAIL_CONFIGURATION_DESCRIPTION",
									Label:                    "Email Configuration",
									Icon:                     "email",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "should not return nav-integrations in sublink if feature flag is false",
			args: &GetNavigationItemsArgs{
				PartnerID:      "ABC",
				SubjectID:      "U-123",
				WhitelabelURL:  "https://abc.smblogin.com",
				AccountGroupID: "AG-123",
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{},
				},
				FeatureFlags: map[string]bool{
					constants.RouteToNewIntegrations: false,
				},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-settings-link",
							Url:               "https://abc.smblogin.com/account/location/AG-123/administration",
							Path:              "/account/location/AG-123/administration",
							ServiceProviderId: "VBC",
							Icon:              "settings",
							TranslationId:     "NAVIGATION.TABS.ADMINISTRATION",
							Label:             "Administration",
							ShowIcon:          true,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:             "nav-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/profile",
									Path:                     "/account/location/AG-123/administration/profile",
									ServiceProviderId:        "VBC",
									Icon:                     "store_outlined",
									TranslationId:            "NAVIGATION.TABS.BUSINESS_PROFILE",
									Label:                    "Business Profile",
									ShowIcon:                 true,
									DescriptionTranslationId: "NAVIGATION.TABS.BUSINESS_PROFILE_DESCRIPTION",
								},
								{
									NavigationId:             "nav-notification-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/notifications/U-123/settings",
									Path:                     "/account/location/AG-123/administration/notifications/U-123/settings",
									ServiceProviderId:        "VBC",
									Icon:                     "notifications",
									TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
									Label:                    "Notification Settings",
									DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-social-connections",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/connections",
									Path:                     "/account/location/AG-123/administration/connections",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
									DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
									Label:                    "Social Connections",
									Icon:                     "settings_input_component",
								},
								{
									NavigationId:             "nav-email-configuration",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/email-configuration",
									Path:                     "/account/location/AG-123/administration/email-configuration",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.EMAIL_CONFIGURATION",
									DescriptionTranslationId: "NAVIGATION.TABS.EMAIL_CONFIGURATION_DESCRIPTION",
									Label:                    "Email Configuration",
									Icon:                     "email",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "should return all SMB-payment related sublinks if feature flag is on",
			args: &GetNavigationItemsArgs{
				PartnerID:      "VUNI",
				AccountGroupID: "AG-123",
				SubjectID:      "U-123",
				WhitelabelURL:  "https://abc.smblogin.com",
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{},
				},
				FeatureFlags: map[string]bool{
					constants.SMBPayments: true,
				},
			},

			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							Label:         "Payments",
							TranslationId: "NAVIGATION.TABS.PAYMENTS",
							Icon:          "wallet",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-payments-invoices",
											Url:               "https://abc.smblogin.com/account/location/AG-123/invoices",
											Path:              "/account/location/AG-123/invoices",
											ServiceProviderId: "VBC",
											TranslationId:     "NAVIGATION.TABS.PAYMENTS.INVOICES",
											Label:             "Invoices",
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-payments-payments",
											Url:               "https://abc.smblogin.com/account/location/AG-123/payments",
											Path:              "/account/location/AG-123/payments",
											ServiceProviderId: "VBC",
											TranslationId:     "NAVIGATION.TABS.PAYMENTS.PAYMENTS",
											Label:             "Payments",
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-payments-payouts",
											Url:               "https://abc.smblogin.com/account/location/AG-123/payouts",
											Path:              "/account/location/AG-123/payouts",
											ServiceProviderId: "VBC",
											TranslationId:     "NAVIGATION.TABS.PAYMENTS.PAYOUTS",
											Label:             "Payouts",
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-settings-link",
							Url:               "https://abc.smblogin.com/account/location/AG-123/administration",
							Path:              "/account/location/AG-123/administration",
							ServiceProviderId: "VBC",
							Icon:              "settings",
							TranslationId:     "NAVIGATION.TABS.ADMINISTRATION",
							Label:             "Administration",
							ShowIcon:          true,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:             "nav-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/profile",
									Path:                     "/account/location/AG-123/administration/profile",
									ServiceProviderId:        "VBC",
									Icon:                     "store_outlined",
									TranslationId:            "NAVIGATION.TABS.BUSINESS_PROFILE",
									Label:                    "Business Profile",
									ShowIcon:                 true,
									DescriptionTranslationId: "NAVIGATION.TABS.BUSINESS_PROFILE_DESCRIPTION",
								},
								{
									NavigationId:             "nav-notification-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/notifications/U-123/settings",
									Path:                     "/account/location/AG-123/administration/notifications/U-123/settings",
									ServiceProviderId:        "VBC",
									Icon:                     "notifications",
									TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
									Label:                    "Notification Settings",
									DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-payment-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/payments",
									Path:                     "/account/location/AG-123/administration/payments",
									ServiceProviderId:        "VBC",
									Icon:                     "credit_card",
									TranslationId:            "NAVIGATION.TABS.PAYMENT_SETTINGS",
									Label:                    "Payment Settings",
									DescriptionTranslationId: "NAVIGATION.TABS.PAYMENT_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-social-connections",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/connections",
									Path:                     "/account/location/AG-123/administration/connections",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
									DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
									Label:                    "Social Connections",
									Icon:                     "settings_input_component",
								},
								{
									NavigationId:             "nav-email-configuration",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/email-configuration",
									Path:                     "/account/location/AG-123/administration/email-configuration",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.EMAIL_CONFIGURATION",
									DescriptionTranslationId: "NAVIGATION.TABS.EMAIL_CONFIGURATION_DESCRIPTION",
									Label:                    "Email Configuration",
									Icon:                     "email",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "Should not return SMB's payments tab when feature flag is off",
			args: &GetNavigationItemsArgs{
				PartnerID:      "VUNI",
				AccountGroupID: "AG-123",
				SubjectID:      "U-123",
				WhitelabelURL:  "https://abc.smblogin.com",
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{},
				},
				FeatureFlags: map[string]bool{},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-settings-link",
							Url:               "https://abc.smblogin.com/account/location/AG-123/administration",
							Path:              "/account/location/AG-123/administration",
							ServiceProviderId: "VBC",
							Icon:              "settings",
							TranslationId:     "NAVIGATION.TABS.ADMINISTRATION",
							Label:             "Administration",
							ShowIcon:          true,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:             "nav-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/profile",
									Path:                     "/account/location/AG-123/administration/profile",
									ServiceProviderId:        "VBC",
									Icon:                     "store_outlined",
									TranslationId:            "NAVIGATION.TABS.BUSINESS_PROFILE",
									Label:                    "Business Profile",
									ShowIcon:                 true,
									DescriptionTranslationId: "NAVIGATION.TABS.BUSINESS_PROFILE_DESCRIPTION",
								},
								{
									NavigationId:             "nav-notification-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/notifications/U-123/settings",
									Path:                     "/account/location/AG-123/administration/notifications/U-123/settings",
									ServiceProviderId:        "VBC",
									Icon:                     "notifications",
									TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
									Label:                    "Notification Settings",
									DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-social-connections",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/connections",
									Path:                     "/account/location/AG-123/administration/connections",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
									DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
									Label:                    "Social Connections",
									Icon:                     "settings_input_component",
								},
								{
									NavigationId:             "nav-email-configuration",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/administration/email-configuration",
									Path:                     "/account/location/AG-123/administration/email-configuration",
									ServiceProviderId:        "VBC",
									TranslationId:            "NAVIGATION.TABS.EMAIL_CONFIGURATION",
									DescriptionTranslationId: "NAVIGATION.TABS.EMAIL_CONFIGURATION_DESCRIPTION",
									Label:                    "Email Configuration",
									Icon:                     "email",
								},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewNavItemGenerator(tt.args)
			if err != nil {
				t.Fatal(err)
			}

			s := navigationconfig.New(nil)
			config, err := s.GetLinkConfig(navigationconfig.SingleLocationID)
			if err != nil {
				t.Fatal(err)
			}
			tt.args.NavConfig = config.Items

			productIDs := make([]string, 0, len(tt.args.Products))
			for _, item := range tt.args.Products {
				navItemID := constants.InternalGetExternalProductConfigID(item.ServiceProviderID)
				if !config.HasFeature(navItemID) {
					productIDs = append(productIDs, navItemID)
				}
			}

			b := GetDefaultProductNavConfigBuilder(tt.args.Products)
			productNavConfigs, err := s.GetMultiLinkConfig(productIDs, navigationconfig.GenerateConfigIfNilOption(
				b,
			))
			if err != nil {
				t.Fatal(err)
			}
			tt.args.ProductNavConfigs = productNavConfigs
			got, _, err := GetNavigationItems(context.Background(), tt.args)
			assert.NoError(t, err)
			assertNoMissingLinks(t, tt.want, got)
		})
	}
}

func Test_buildAccountGroupMobileItems(t *testing.T) {
	tests := []struct {
		name string
		args *GetNavigationItemsArgs
		want []*atlas_v1.SideNavigationItem
	}{
		{
			name: "build mobile tab items",
			args: &GetNavigationItemsArgs{
				PartnerID:      "VUNI",
				AccountGroupID: "AG-123",
				WhitelabelURL:  "https://abc.smblogin.com",
				SubjectID:      "U-123",
				Features: &sidenavigation.Features{
					ShowHome:            true,
					ShowInboxMessage:    true,
					ShowCustomers:       true,
					ShowExecutiveReport: true,
					ShowCRMCompanies:    true,
					ShowMyProducts:      true,
					ShowAIAssistant:     true,
				},
				FeatureFlags: map[string]bool{
					constants.ReputationManagement:      true,
					constants.AIAssistants:              true,
					constants.NPSNewMLDesign:            true,
					featureflags.GoalsForGroupFeatureID: true,
				},
				Products: []*product.Product{
					{
						ServiceProviderID: "RM",
						EntryURL:          "https://repman.appspot.com/cv/entry/AG-123/",
						Name:              "Reputation Management",
					},
					{
						ServiceProviderID: "SM",
						EntryURL:          "https://social.appspot.com/entry/AG-123/",
						Name:              "Social",
					},
				},
				PlatformMode: constants.MobileMode,
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-home",
							Url:               "https://abc.smblogin.com/account/location/AG-123/home",
							Path:              "/account/location/AG-123/home",
							ServiceProviderId: "VBC",
							Icon:              "home",
							TranslationId:     "NAVIGATION.TABS.HOME",
							Label:             "Home",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-inbox",
							Url:               "https://abc.smblogin.com/account/location/AG-123/inbox",
							Path:              "/account/location/AG-123/inbox",
							ServiceProviderId: "VBC",
							Icon:              "question_answer",
							TranslationId:     "NAVIGATION.TABS.INBOX",
							Label:             "Inbox Messages",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							Label:         "Contacts",
							TranslationId: "NAVIGATION.TABS.CRM.CRM",
							Icon:          "contacts",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-crm",
											Url:               "https://abc.smblogin.com/account/location/AG-123/crm/contact",
											Path:              "/account/location/AG-123/crm/contact",
											ServiceProviderId: "VBC",
											Icon:              "person",
											TranslationId:     "NAVIGATION.TABS.CRM.CONTACT",
											Label:             "Contacts",
											ShowIcon:          false,
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-crm-companies",
											Url:               "https://abc.smblogin.com/account/location/AG-123/crm/company",
											Path:              "/account/location/AG-123/crm/company",
											ServiceProviderId: "VBC",
											Icon:              "location_city",
											TranslationId:     "NAVIGATION.TABS.CRM.COMPANY",
											Label:             "Companies",
											ShowIcon:          false,
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							NavigationId:  "",
							Label:         "AI",
							TranslationId: "NAVIGATION.TABS.AI",
							Icon:          "auto_awesome",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-ai-assistants",
											Url:               "https://abc.smblogin.com/account/location/AG-123/ai/assistants",
											Path:              "/account/location/AG-123/ai/assistants",
											ServiceProviderId: "VBC",
											Icon:              "auto_awesome",
											TranslationId:     "NAVIGATION.TABS.AI_ASSISTANTS",
											Label:             "AI workforce",
											ShowIcon:          false,
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-ai-assistants-knowledge",
											Url:               "https://abc.smblogin.com/account/location/AG-123/ai/knowledge-base",
											Path:              "/account/location/AG-123/ai/knowledge-base",
											ServiceProviderId: "VBC",
											Icon:              "menu_book",
											TranslationId:     "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS",
											Label:             "AI knowledge base",
											ShowIcon:          false,
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-executive-report",
							Url:               "https://abc.smblogin.com/account/location/AG-123/executive-report/monthly",
							Path:              "/account/location/AG-123/executive-report/monthly",
							ServiceProviderId: "VBC",
							Icon:              "timeline",
							TranslationId:     "NAVIGATION.TABS.EXECUTIVE_REPORT",
							Label:             "Executive Report",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "product-RM",
							Icon:              "",
							ShowIcon:          true,
							Label:             "Reputation Management",
							Url:               "https://abc.smblogin.com/account/location/AG-123/reputation",
							Path:              "/account/location/AG-123/reputation",
							ServiceProviderId: "VBC",
							Pinnable:          true,
							LaunchUrl:         "https://repman.appspot.com/cv/entry/AG-123/",
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:      "nav-reputation-manage",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/manage-reviews",
									Path:              "/account/location/AG-123/reputation/manage-reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "NAVIGATION.TABS.REVIEWS",
									Label:             "Reviews",
								},
								{
									NavigationId:      "nav-reputation-insights",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/insights",
									Path:              "/account/location/AG-123/reputation/insights",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS",
									Label:             "Insights",
								},
								{
									NavigationId:      "nav-google-my-business-google-q-and-a",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/google-q-and-a",
									Path:              "/account/location/AG-123/reputation/google-q-and-a",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
									Label:             "Google Q\u0026A",
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-settings-link",
							Url:               "https://abc.smblogin.com/account/location/AG-123/settings",
							Path:              "/account/location/AG-123/settings",
							ServiceProviderId: "VBC",
							TranslationId:     "NAVIGATION.TABS.ADMINISTRATION",
							Label:             "Administration",
							Icon:              "settings",
							ShowIcon:          true,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:             "nav-social-connections",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/connections",
									Path:                     "/account/location/AG-123/settings/connections",
									ServiceProviderId:        "VBC",
									Icon:                     "settings_input_component",
									TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
									DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
									Label:                    "Social Connections",
								},
								{
									NavigationId:             "nav-profile-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/notifications/U-123/settings",
									Path:                     "/account/location/AG-123/settings/notifications/U-123/settings",
									ServiceProviderId:        "VBC",
									Icon:                     "notifications",
									TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
									Label:                    "Notification Settings",
									DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-inbox-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/inbox",
									Path:                     "/account/location/AG-123/settings/inbox",
									ServiceProviderId:        "VBC",
									Icon:                     "question_answer",
									TranslationId:            "NAVIGATION.TABS.INBOX_SETTINGS",
									DescriptionTranslationId: "NAVIGATION.TABS.INBOX_SETTINGS_DESCRIPTION",
									Label:                    "Inbox",
								},
								{
									NavigationId:             "nav-ai-knowledge-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/ai-knowledge",
									Path:                     "/account/location/AG-123/settings/ai-knowledge",
									ServiceProviderId:        "VBC",
									Icon:                     "menu_book",
									Label:                    "AI knowledge base",
									TranslationId:            "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS",
									DescriptionTranslationId: "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS_DESCRIPTION",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "build mobile tab items no feature flag for new dashboard",
			args: &GetNavigationItemsArgs{
				PartnerID:      "VUNI",
				AccountGroupID: "AG-123",
				WhitelabelURL:  "https://abc.smblogin.com",
				Features: &sidenavigation.Features{
					ShowInboxMessage: true,
					ShowCustomers:    true,
					ShowCRMCompanies: true,
					ShowMyProducts:   true,
				},
				Products: []*product.Product{
					{
						ServiceProviderID: "RM",
						EntryURL:          "https://repman.appspot.com/cv/entry/AG-123/",
						Name:              "Reputation Management",
					},
					{
						ServiceProviderID: "SM",
						EntryURL:          "https://social.appspot.com/entry/AG-123/",
						Name:              "Social",
					},
				},
				PlatformMode: constants.MobileMode,
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-inbox",
							Url:               "https://abc.smblogin.com/account/location/AG-123/inbox",
							Path:              "/account/location/AG-123/inbox",
							ServiceProviderId: "VBC",
							Icon:              "question_answer",
							TranslationId:     "NAVIGATION.TABS.INBOX",
							Label:             "Inbox Messages",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							Label:         "Contacts",
							TranslationId: "NAVIGATION.TABS.CRM.CRM",
							Icon:          "contacts",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-crm",
											Url:               "https://abc.smblogin.com/account/location/AG-123/crm/contact",
											Path:              "/account/location/AG-123/crm/contact",
											ServiceProviderId: "VBC",
											Icon:              "person",
											TranslationId:     "NAVIGATION.TABS.CRM.CONTACT",
											Label:             "Contacts",
											ShowIcon:          false,
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-crm-companies",
											Url:               "https://abc.smblogin.com/account/location/AG-123/crm/company",
											Path:              "/account/location/AG-123/crm/company",
											ServiceProviderId: "VBC",
											Icon:              "location_city",
											TranslationId:     "NAVIGATION.TABS.CRM.COMPANY",
											Label:             "Companies",
											ShowIcon:          false,
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "product-RM",
							Icon:              "",
							ShowIcon:          true,
							Label:             "Reputation Management",
							Url:               "https://abc.smblogin.com/account/location/AG-123/reputation",
							Path:              "/account/location/AG-123/reputation",
							ServiceProviderId: "VBC",
							Pinnable:          true,
							LaunchUrl:         "https://repman.appspot.com/cv/entry/AG-123/",
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:      "nav-reputation-manage",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/manage-reviews",
									Path:              "/account/location/AG-123/reputation/manage-reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "NAVIGATION.TABS.REVIEWS",
									Label:             "Reviews",
								},
								{
									NavigationId:      "nav-reputation-insights",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/insights",
									Path:              "/account/location/AG-123/reputation/insights",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS",
									Label:             "Insights",
								},
								{
									NavigationId:      "nav-google-my-business-google-q-and-a",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/google-q-and-a",
									Path:              "/account/location/AG-123/reputation/google-q-and-a",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
									Label:             "Google Q\u0026A",
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-settings-link",
							Url:               "https://abc.smblogin.com/account/location/AG-123/settings",
							Path:              "/account/location/AG-123/settings",
							ServiceProviderId: "VBC",
							TranslationId:     "NAVIGATION.TABS.ADMINISTRATION",
							Label:             "Administration",
							Icon:              "settings",
							ShowIcon:          true,
							Pinnable:          false,
							OpenInNewTab:      false,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:             "nav-social-connections",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/connections",
									Path:                     "/account/location/AG-123/settings/connections",
									ServiceProviderId:        "VBC",
									Icon:                     "settings_input_component",
									TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
									Label:                    "Social Connections",
									DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-profile-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/notifications//settings",
									Path:                     "/account/location/AG-123/settings/notifications//settings",
									ServiceProviderId:        "VBC",
									Icon:                     "notifications",
									TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
									Label:                    "Notification Settings",
									UserRequired:             true,
									DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-inbox-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/inbox",
									Path:                     "/account/location/AG-123/settings/inbox",
									ServiceProviderId:        "VBC",
									Icon:                     "question_answer",
									TranslationId:            "NAVIGATION.TABS.INBOX_SETTINGS",
									Label:                    "Inbox",
									DescriptionTranslationId: "NAVIGATION.TABS.INBOX_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-ai-knowledge-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/ai-knowledge",
									Path:                     "/account/location/AG-123/settings/ai-knowledge",
									ServiceProviderId:        "VBC",
									Icon:                     "menu_book",
									TranslationId:            "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS",
									Label:                    "AI knowledge base",
									DescriptionTranslationId: "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS_DESCRIPTION",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "build mobile tab items for mobile dev market",
			args: &GetNavigationItemsArgs{
				PartnerID:      "VUNI",
				AccountGroupID: "AG-123",
				WhitelabelURL:  "https://abc.smblogin.com",
				SubjectID:      "U-123",
				Features: &sidenavigation.Features{
					ShowHome:            true,
					ShowInboxMessage:    true,
					ShowCustomers:       true,
					ShowCRMCompanies:    true,
					ShowMyProducts:      true,
					ShowExecutiveReport: true,
				},
				FeatureFlags: map[string]bool{
					constants.ReputationManagement:      true,
					constants.AIAssistants:              false,
					constants.NPSNewMLDesign:            true,
					featureflags.GoalsForGroupFeatureID: true,
				},
				Products: []*product.Product{
					{
						ServiceProviderID: "RM",
						EntryURL:          "https://repman.appspot.com/cv/entry/AG-123/",
						Name:              "Reputation Management",
					},
					{
						ServiceProviderID: "SM",
						EntryURL:          "https://social.appspot.com/entry/AG-123/",
						Name:              "Social",
					},
				},
				PlatformMode: constants.MobileMode,
				MarketID:     constants.MobileDevMarket,
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-home",
							Url:               "https://abc.smblogin.com/account/location/AG-123/home",
							Path:              "/account/location/AG-123/home",
							ServiceProviderId: "VBC",
							Icon:              "home",
							TranslationId:     "NAVIGATION.TABS.HOME",
							Label:             "Home",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-inbox",
							Url:               "https://abc.smblogin.com/account/location/AG-123/inbox",
							Path:              "/account/location/AG-123/inbox",
							ServiceProviderId: "VBC",
							Icon:              "question_answer",
							TranslationId:     "NAVIGATION.TABS.INBOX",
							Label:             "Inbox Messages",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							Label:         "Contacts",
							TranslationId: "NAVIGATION.TABS.CRM.CRM",
							Icon:          "contacts",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-crm",
											Url:               "https://abc.smblogin.com/account/location/AG-123/crm/contact",
											Path:              "/account/location/AG-123/crm/contact",
											ServiceProviderId: "VBC",
											Icon:              "person",
											TranslationId:     "NAVIGATION.TABS.CRM.CONTACT",
											Label:             "Contacts",
											ShowIcon:          false,
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-crm-companies",
											Url:               "https://abc.smblogin.com/account/location/AG-123/crm/company",
											Path:              "/account/location/AG-123/crm/company",
											ServiceProviderId: "VBC",
											Icon:              "location_city",
											TranslationId:     "NAVIGATION.TABS.CRM.COMPANY",
											Label:             "Companies",
											ShowIcon:          false,
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-executive-report",
							Url:               "https://abc.smblogin.com/account/location/AG-123/executive-report/monthly",
							Path:              "/account/location/AG-123/executive-report/monthly",
							ServiceProviderId: "VBC",
							Icon:              "timeline",
							TranslationId:     "NAVIGATION.TABS.EXECUTIVE_REPORT",
							Label:             "Executive Report",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "product-RM",
							Icon:              "",
							ShowIcon:          true,
							Label:             "Reputation Management",
							Url:               "https://abc.smblogin.com/account/location/AG-123/reputation",
							Path:              "/account/location/AG-123/reputation",
							ServiceProviderId: "VBC",
							Pinnable:          true,
							LaunchUrl:         "https://repman.appspot.com/cv/entry/AG-123/",
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:      "nav-reputation-manage",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/manage-reviews",
									Path:              "/account/location/AG-123/reputation/manage-reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "NAVIGATION.TABS.REVIEWS",
									Label:             "Reviews",
								},
								{
									NavigationId:      "nav-reputation-insights",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/insights",
									Path:              "/account/location/AG-123/reputation/insights",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS",
									Label:             "Insights",
								},
								{
									NavigationId:      "nav-google-my-business-google-q-and-a",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/google-q-and-a",
									Path:              "/account/location/AG-123/reputation/google-q-and-a",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
									Label:             "Google Q\u0026A",
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-settings-link",
							Url:               "https://abc.smblogin.com/account/location/AG-123/settings",
							Path:              "/account/location/AG-123/settings",
							ServiceProviderId: "VBC",
							TranslationId:     "NAVIGATION.TABS.ADMINISTRATION",
							Label:             "Administration",
							Icon:              "settings",
							ShowIcon:          true,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:             "nav-social-connections",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/connections",
									Path:                     "/account/location/AG-123/settings/connections",
									ServiceProviderId:        "VBC",
									Icon:                     "settings_input_component",
									TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
									DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
									Label:                    "Social Connections",
								},
								{
									NavigationId:             "nav-profile-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/notifications/U-123/settings",
									Path:                     "/account/location/AG-123/settings/notifications/U-123/settings",
									ServiceProviderId:        "VBC",
									Icon:                     "notifications",
									TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
									Label:                    "Notification Settings",
									DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-inbox-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/inbox",
									Path:                     "/account/location/AG-123/settings/inbox",
									ServiceProviderId:        "VBC",
									Icon:                     "question_answer",
									TranslationId:            "NAVIGATION.TABS.INBOX_SETTINGS",
									DescriptionTranslationId: "NAVIGATION.TABS.INBOX_SETTINGS_DESCRIPTION",
									Label:                    "Inbox",
								},
								{
									NavigationId:             "nav-ai-knowledge-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/ai-knowledge",
									Path:                     "/account/location/AG-123/settings/ai-knowledge",
									ServiceProviderId:        "VBC",
									Icon:                     "menu_book",
									Label:                    "AI knowledge base",
									TranslationId:            "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS",
									DescriptionTranslationId: "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS_DESCRIPTION",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "build mobile tab items with exec report enabled but market is not mobile dev",
			args: &GetNavigationItemsArgs{
				PartnerID:      "VUNI",
				AccountGroupID: "AG-123",
				WhitelabelURL:  "https://abc.smblogin.com",
				SubjectID:      "U-123",
				Features: &sidenavigation.Features{
					ShowInboxMessage:    true,
					ShowCustomers:       true,
					ShowCRMCompanies:    true,
					ShowMyProducts:      true,
					ShowExecutiveReport: true,
				},
				FeatureFlags: map[string]bool{
					constants.ReputationManagement:      true,
					constants.AIAssistants:              false,
					constants.NPSNewMLDesign:            true,
					featureflags.GoalsForGroupFeatureID: true,
				},
				Products: []*product.Product{
					{
						ServiceProviderID: "RM",
						EntryURL:          "https://repman.appspot.com/cv/entry/AG-123/",
						Name:              "Reputation Management",
					},
					{
						ServiceProviderID: "SM",
						EntryURL:          "https://social.appspot.com/entry/AG-123/",
						Name:              "Social",
					},
				},
				PlatformMode: constants.MobileMode,
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-inbox",
							Url:               "https://abc.smblogin.com/account/location/AG-123/inbox",
							Path:              "/account/location/AG-123/inbox",
							ServiceProviderId: "VBC",
							Icon:              "question_answer",
							TranslationId:     "NAVIGATION.TABS.INBOX",
							Label:             "Inbox Messages",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							Label:         "Contacts",
							TranslationId: "NAVIGATION.TABS.CRM.CRM",
							Icon:          "contacts",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-crm",
											Url:               "https://abc.smblogin.com/account/location/AG-123/crm/contact",
											Path:              "/account/location/AG-123/crm/contact",
											ServiceProviderId: "VBC",
											Icon:              "person",
											TranslationId:     "NAVIGATION.TABS.CRM.CONTACT",
											Label:             "Contacts",
											ShowIcon:          false,
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-crm-companies",
											Url:               "https://abc.smblogin.com/account/location/AG-123/crm/company",
											Path:              "/account/location/AG-123/crm/company",
											ServiceProviderId: "VBC",
											Icon:              "location_city",
											TranslationId:     "NAVIGATION.TABS.CRM.COMPANY",
											Label:             "Companies",
											ShowIcon:          false,
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-executive-report",
							Url:               "https://abc.smblogin.com/account/location/AG-123/executive-report/monthly",
							Path:              "/account/location/AG-123/executive-report/monthly",
							ServiceProviderId: "VBC",
							Icon:              "timeline",
							TranslationId:     "NAVIGATION.TABS.EXECUTIVE_REPORT",
							Label:             "Executive Report",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "product-RM",
							Icon:              "",
							ShowIcon:          true,
							Label:             "Reputation Management",
							Url:               "https://abc.smblogin.com/account/location/AG-123/reputation",
							Path:              "/account/location/AG-123/reputation",
							ServiceProviderId: "VBC",
							Pinnable:          true,
							LaunchUrl:         "https://repman.appspot.com/cv/entry/AG-123/",
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:      "nav-reputation-manage",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/manage-reviews",
									Path:              "/account/location/AG-123/reputation/manage-reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "NAVIGATION.TABS.REVIEWS",
									Label:             "Reviews",
								},
								{
									NavigationId:      "nav-reputation-insights",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/insights",
									Path:              "/account/location/AG-123/reputation/insights",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS",
									Label:             "Insights",
								},
								{
									NavigationId:      "nav-google-my-business-google-q-and-a",
									Url:               "https://abc.smblogin.com/account/location/AG-123/reputation/google-q-and-a",
									Path:              "/account/location/AG-123/reputation/google-q-and-a",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
									Label:             "Google Q\u0026A",
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-settings-link",
							Url:               "https://abc.smblogin.com/account/location/AG-123/settings",
							Path:              "/account/location/AG-123/settings",
							ServiceProviderId: "VBC",
							TranslationId:     "NAVIGATION.TABS.ADMINISTRATION",
							Label:             "Administration",
							Icon:              "settings",
							ShowIcon:          true,
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:             "nav-social-connections",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/connections",
									Path:                     "/account/location/AG-123/settings/connections",
									ServiceProviderId:        "VBC",
									Icon:                     "settings_input_component",
									TranslationId:            "NAVIGATION.TABS.SOCIAL_CONNECTIONS",
									DescriptionTranslationId: "NAVIGATION.TABS.SOCIAL_CONNECTIONS_DESCRIPTION",
									Label:                    "Social Connections",
								},
								{
									NavigationId:             "nav-profile-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/notifications/U-123/settings",
									Path:                     "/account/location/AG-123/settings/notifications/U-123/settings",
									ServiceProviderId:        "VBC",
									Icon:                     "notifications",
									TranslationId:            "NAVIGATION.TABS.NOTIFICATION_SETTINGS",
									Label:                    "Notification Settings",
									DescriptionTranslationId: "NAVIGATION.TABS.NOTIFICATION_SETTINGS_DESCRIPTION",
								},
								{
									NavigationId:             "nav-inbox-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/inbox",
									Path:                     "/account/location/AG-123/settings/inbox",
									ServiceProviderId:        "VBC",
									Icon:                     "question_answer",
									TranslationId:            "NAVIGATION.TABS.INBOX_SETTINGS",
									DescriptionTranslationId: "NAVIGATION.TABS.INBOX_SETTINGS_DESCRIPTION",
									Label:                    "Inbox",
								},
								{
									NavigationId:             "nav-ai-knowledge-settings",
									Url:                      "https://abc.smblogin.com/account/location/AG-123/settings/ai-knowledge",
									Path:                     "/account/location/AG-123/settings/ai-knowledge",
									ServiceProviderId:        "VBC",
									Icon:                     "menu_book",
									Label:                    "AI knowledge base",
									TranslationId:            "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS",
									DescriptionTranslationId: "NAVIGATION.TABS.AI_KNOWLEDGE_SETTINGS_DESCRIPTION",
								},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewNavItemGenerator(tt.args)
			if err != nil {
				t.Fatal(err)
			}

			s := navigationconfig.New(nil)
			config, err := s.GetLinkConfig(navigationconfig.SingleLocationMobileID)
			if err != nil {
				t.Fatal(err)
			}
			tt.args.NavConfig = config.Items

			productIDs := make([]string, 0, len(tt.args.Products))
			for _, item := range tt.args.Products {
				navItemID := constants.InternalGetExternalProductConfigID(item.ServiceProviderID)
				if !config.HasFeature(navItemID) {
					productIDs = append(productIDs, navItemID)
				}
			}
			b := GetDefaultProductNavConfigBuilder(tt.args.Products)
			productNavConfigs, err := s.GetMultiLinkConfig(productIDs, navigationconfig.GenerateConfigIfNilOption(
				b,
			))
			if err != nil {
				t.Fatal(err)
			}
			tt.args.ProductNavConfigs = productNavConfigs
			got, _, _ := GetNavigationItems(context.Background(), tt.args)
			assertNoMissingLinks(t, tt.want, got)
		})
	}
}

func Test_buildBrandsItems(t *testing.T) {
	tests := []struct {
		name string
		args *GetNavigationItemsArgs
		want []*atlas_v1.SideNavigationItem
	}{
		{
			name: "all items",
			args: &GetNavigationItemsArgs{
				PartnerID:     "ABC",
				GroupPath:     "G-123",
				WhitelabelURL: "https://abc.smblogin.com",
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{
						"recommendations",
						"vbc-executive-report",
						"meeting_scheduler_business_app",
						"content-library",
						"guides",
						"local-marketing-index",
						"access-marketplace",
						"fulfillment",
						"bc_my_team_page",
						"orders",
						"invoices",
						"files",
						"my_products",
						"customer_list",
						"home",
						"inbox",
					},
					ShowInboxMessage: true,
				},
				FeatureFlags: map[string]bool{
					"meeting_scheduler_business_app":         true,
					"business_emails":                        true,
					"bc_brands_manage_social":                true,
					constants.NPSNewMLDesign:                 false,
					constants.CRMMultilocation:               true,
					constants.KeywordTrackingOverviewFeature: true,
					featureflags.GoalsForGroupFeatureID:      true,
					constants.PartnerFeatureIDLisAnalytics:   true,
				},
				BrandTabStatuses: &location.BrandTabStatuses{
					ReviewsTabEnabled:     true,
					ListingsTabEnabled:    true,
					SocialTabEnabled:      true,
					MapTabEnabled:         true,
					AdvertisingTabEnabled: true,
					ReportTabEnabled:      true,
					DataExportTabEnabled:  true,
					GoogleQAndATabEnabled: true,
				},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-home",
							Url:               "https://abc.smblogin.com/account/brands/G-123/home",
							Path:              "/account/brands/G-123/home",
							ServiceProviderId: "VBC",
							Icon:              "home",
							TranslationId:     "NAVIGATION.TABS.HOME",
							Label:             "Home",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-inbox",
							Url:               "https://abc.smblogin.com/account/brands/G-123/inbox",
							Path:              "/account/brands/G-123/inbox",
							ServiceProviderId: "VBC",
							Icon:              "question_answer",
							TranslationId:     "NAVIGATION.TABS.INBOX",
							Label:             "Inbox Messages",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							Label:         "Contacts",
							TranslationId: "NAVIGATION.TABS.CRM.CRM",
							Icon:          "contacts",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-crm-contacts",
											Url:               "https://abc.smblogin.com/account/brands/G-123/contacts",
											Path:              "/account/brands/G-123/contacts",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
											Label:             "Contacts",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-crm-contacts",
													Url:               "https://abc.smblogin.com/account/brands/G-123/contacts",
													Path:              "/account/brands/G-123/contacts",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
													Label:             "Contacts",
												},
											},
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-crm-tasks",
											Url:               "https://abc.smblogin.com/account/brands/G-123/tasks",
											Path:              "/account/brands/G-123/tasks",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
											Label:             "Tasks",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-crm-tasks",
													Url:               "https://abc.smblogin.com/account/brands/G-123/tasks",
													Path:              "/account/brands/G-123/tasks",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
													Label:             "Tasks",
												},
											},
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-report",
							Url:               "https://abc.smblogin.com/account/brands/G-123/report",
							Path:              "/account/brands/G-123/report",
							ServiceProviderId: "VBC",
							Icon:              "timeline",
							TranslationId:     "NAVIGATION.TABS.MULTI_LOCATION_REPORT",
							Label:             "Executive Report",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-reputation",
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REPUTATION",
							Icon:              "star",
							ShowIcon:          true,
							Label:             "Reputation",
							Url:               "https://abc.smblogin.com/account/brands/G-123/reputation",
							Path:              "/account/brands/G-123/reputation",
							ServiceProviderId: "VBC",
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:      "nav-brand-reputation-overview",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/reviews",
									Path:              "/account/brands/G-123/reputation/reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
									Label:             "Overview",
								},
								{
									NavigationId:      "nav-brand-reputation-manage",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/manage-reviews",
									Path:              "/account/brands/G-123/reputation/manage-reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REVIEWS",
									Label:             "Reviews",
								},
								{
									NavigationId:      "nav-brand-reputation-insights",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/insights",
									Path:              "/account/brands/G-123/reputation/insights",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS",
									Label:             "Insights",
								},
								{
									NavigationId:      "nav-brand-google-my-business-google-q-and-a",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/google-q-and-a",
									Path:              "/account/brands/G-123/reputation/google-q-and-a",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
									Label:             "Google Q\u0026A",
								},
							},
						},
					},
				},

				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							TranslationId: "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.SOCIAL",
							Icon:          "question_answer",
							ShowIcon:      true,
							Label:         "Social",
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-social",
											Url:               "https://abc.smblogin.com/account/brands/G-123/social-overview",
											Path:              "/account/brands/G-123/social-overview",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
											Label:             "Overview",
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-social-manage-posts",
											Url:               "https://abc.smblogin.com/account/brands/G-123/manage-social-posts",
											Path:              "/account/brands/G-123/manage-social-posts",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.MANAGE_POSTS",
											Label:             "Manage Posts",
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-advertising",
							Url:               "https://abc.smblogin.com/account/brands/G-123/advertising-overview",
							Path:              "/account/brands/G-123/advertising-overview",
							ServiceProviderId: "VBC",
							Icon:              "public",
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.ADVERTISING",
							Label:             "Advertising",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-data-export",
							Url:               "https://abc.smblogin.com/account/brands/G-123/data-export",
							Path:              "/account/brands/G-123/data-export",
							ServiceProviderId: "VBC",
							Icon:              "unarchive",
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.DATA_EXPORT",
							Label:             "Data Exporter",
							ShowIcon:          true,
						},
					},
				},
			},
		},
		{
			name: "google q and a disabled",
			args: &GetNavigationItemsArgs{
				GroupPath:     "G-123",
				WhitelabelURL: "https://abc.smblogin.com",
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{
						"recommendations",
						"vbc-executive-report",
						"meeting_scheduler_business_app",
						"content-library",
						"guides",
						"local-marketing-index",
						"access-marketplace",
						"fulfillment",
						"bc_my_team_page",
						"orders",
						"invoices",
						"files",
						"my_products",
						"customer_list",
						"home",
						"inbox",
					},
					ShowContentLibrary:    true,
					ShowLmiDashboard:      true,
					ShowExecutiveReport:   true,
					ShowStore:             true,
					ShowFulfillment:       true,
					ShowMarketplace:       true,
					ShowInviteTeam:        true,
					ShowInvoices:          true,
					ShowOrderPage:         true,
					ShowMeetingScheduler:  true,
					ShowFiles:             true,
					ShowMyProducts:        true,
					ShowCustomers:         true,
					ShowHome:              true,
					ShowInboxMessage:      true,
					HasBrandsEnabled:      true,
					HasProductMarketplace: true,
					HasSMPostPerformance:  true,
				},
				FeatureFlags: map[string]bool{
					"meeting_scheduler_business_app":         true,
					"business_emails":                        true,
					"bc_brands_manage_social":                true,
					constants.NPSNewMLDesign:                 false,
					constants.CRMMultilocation:               true,
					constants.KeywordTrackingOverviewFeature: true,
					featureflags.GoalsForGroupFeatureID:      true,
				},
				BrandTabStatuses: &location.BrandTabStatuses{
					ReviewsTabEnabled:     true,
					ListingsTabEnabled:    true,
					SocialTabEnabled:      true,
					MapTabEnabled:         true,
					AdvertisingTabEnabled: true,
					ReportTabEnabled:      true,
					DataExportTabEnabled:  true,
				},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-home",
							Url:               "https://abc.smblogin.com/account/brands/G-123/home",
							Path:              "/account/brands/G-123/home",
							ServiceProviderId: "VBC",
							Icon:              "home",
							TranslationId:     "NAVIGATION.TABS.HOME",
							Label:             "Home",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-inbox",
							Url:               "https://abc.smblogin.com/account/brands/G-123/inbox",
							Path:              "/account/brands/G-123/inbox",
							ServiceProviderId: "VBC",
							Icon:              "question_answer",
							TranslationId:     "NAVIGATION.TABS.INBOX",
							Label:             "Inbox Messages",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							Label:         "Contacts",
							TranslationId: "NAVIGATION.TABS.CRM.CRM",
							Icon:          "contacts",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-crm-contacts",
											Url:               "https://abc.smblogin.com/account/brands/G-123/contacts",
											Path:              "/account/brands/G-123/contacts",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
											Label:             "Contacts",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-crm-contacts",
													Url:               "https://abc.smblogin.com/account/brands/G-123/contacts",
													Path:              "/account/brands/G-123/contacts",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
													Label:             "Contacts",
												},
											},
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-crm-tasks",
											Url:               "https://abc.smblogin.com/account/brands/G-123/tasks",
											Path:              "/account/brands/G-123/tasks",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
											Label:             "Tasks",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-crm-tasks",
													Url:               "https://abc.smblogin.com/account/brands/G-123/tasks",
													Path:              "/account/brands/G-123/tasks",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
													Label:             "Tasks",
												},
											},
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-report",
							Url:               "https://abc.smblogin.com/account/brands/G-123/report",
							Path:              "/account/brands/G-123/report",
							ServiceProviderId: "VBC",
							Icon:              "timeline",
							TranslationId:     "NAVIGATION.TABS.MULTI_LOCATION_REPORT",
							Label:             "Executive Report",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REPUTATION",
							Icon:              "star",
							ShowIcon:          true,
							Label:             "Reputation",
							NavigationId:      "nav-reputation",
							Url:               "https://abc.smblogin.com/account/brands/G-123/reputation",
							Path:              "/account/brands/G-123/reputation",
							ServiceProviderId: "VBC",
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:      "nav-brand-reputation-overview",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/reviews",
									Path:              "/account/brands/G-123/reputation/reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
									Label:             "Overview",
								},
								{
									NavigationId:      "nav-brand-reputation-manage",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/manage-reviews",
									Path:              "/account/brands/G-123/reputation/manage-reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REVIEWS",
									Label:             "Reviews",
								},
								{
									NavigationId:      "nav-brand-reputation-insights",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/insights",
									Path:              "/account/brands/G-123/reputation/insights",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS",
									Label:             "Insights",
								},
								{
									NavigationId:      "nav-brand-google-my-business-google-q-and-a",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/google-q-and-a",
									Path:              "/account/brands/G-123/reputation/google-q-and-a",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
									Label:             "Google Q&A",
								},
							},
						},
					},
				},

				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							TranslationId: "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.SOCIAL",
							Icon:          "question_answer",
							ShowIcon:      true,
							Label:         "Social",
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-social",
											Url:               "https://abc.smblogin.com/account/brands/G-123/social-overview",
											Path:              "/account/brands/G-123/social-overview",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
											Label:             "Overview",
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-social-manage-posts",
											Url:               "https://abc.smblogin.com/account/brands/G-123/manage-social-posts",
											Path:              "/account/brands/G-123/manage-social-posts",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.MANAGE_POSTS",
											Label:             "Manage Posts",
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-advertising",
							Url:               "https://abc.smblogin.com/account/brands/G-123/advertising-overview",
							Path:              "/account/brands/G-123/advertising-overview",
							ServiceProviderId: "VBC",
							Icon:              "public",
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.ADVERTISING",
							Label:             "Advertising",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-data-export",
							Url:               "https://abc.smblogin.com/account/brands/G-123/data-export",
							Path:              "/account/brands/G-123/data-export",
							ServiceProviderId: "VBC",
							Icon:              "unarchive",
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.DATA_EXPORT",
							Label:             "Data Exporter",
							ShowIcon:          true,
						},
					},
				},
			},
		},
		{
			name: "Disable listing and data tab",
			args: &GetNavigationItemsArgs{
				GroupPath:     "G-123",
				WhitelabelURL: "https://abc.smblogin.com",
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{
						"recommendations",
						"vbc-executive-report",
						"meeting_scheduler_business_app",
						"content-library",
						"guides",
						"local-marketing-index",
						"access-marketplace",
						"fulfillment",
						"bc_my_team_page",
						"orders",
						"invoices",
						"files",
						"my_products",
						"customer_list",
						"home",
						"inbox",
					},
				},
				FeatureFlags: map[string]bool{
					"meeting_scheduler_business_app":         true,
					"business_emails":                        true,
					"bc_brands_manage_social":                true,
					"ai-assistants":                          true,
					constants.NPSNewMLDesign:                 false,
					constants.CRMMultilocation:               true,
					constants.KeywordTrackingOverviewFeature: true,
					featureflags.GoalsForGroupFeatureID:      true,
					constants.PartnerFeatureIDLisAnalytics:   true,
				},
				BrandTabStatuses: &location.BrandTabStatuses{
					ReviewsTabEnabled:     true,
					ListingsTabEnabled:    false,
					SocialTabEnabled:      true,
					MapTabEnabled:         true,
					AdvertisingTabEnabled: true,
					ReportTabEnabled:      true,
					DataExportTabEnabled:  false,
					GoogleQAndATabEnabled: true,
				},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-home",
							Url:               "https://abc.smblogin.com/account/brands/G-123/home",
							Path:              "/account/brands/G-123/home",
							ServiceProviderId: "VBC",
							Icon:              "home",
							TranslationId:     "NAVIGATION.TABS.HOME",
							Label:             "Home",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							Label:         "Contacts",
							TranslationId: "NAVIGATION.TABS.CRM.CRM",
							Icon:          "contacts",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-crm-contacts",
											Url:               "https://abc.smblogin.com/account/brands/G-123/contacts",
											Path:              "/account/brands/G-123/contacts",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
											Label:             "Contacts",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-crm-contacts",
													Url:               "https://abc.smblogin.com/account/brands/G-123/contacts",
													Path:              "/account/brands/G-123/contacts",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
													Label:             "Contacts",
												},
											},
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-crm-tasks",
											Url:               "https://abc.smblogin.com/account/brands/G-123/tasks",
											Path:              "/account/brands/G-123/tasks",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
											Label:             "Tasks",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-crm-tasks",
													Url:               "https://abc.smblogin.com/account/brands/G-123/tasks",
													Path:              "/account/brands/G-123/tasks",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
													Label:             "Tasks",
												},
											},
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-report",
							Url:               "https://abc.smblogin.com/account/brands/G-123/report",
							Path:              "/account/brands/G-123/report",
							ServiceProviderId: "VBC",
							Icon:              "timeline",
							TranslationId:     "NAVIGATION.TABS.MULTI_LOCATION_REPORT",
							Label:             "Executive Report",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REPUTATION",
							Icon:              "star",
							ShowIcon:          true,
							Label:             "Reputation",
							NavigationId:      "nav-reputation",
							Url:               "https://abc.smblogin.com/account/brands/G-123/reputation",
							Path:              "/account/brands/G-123/reputation",
							ServiceProviderId: "VBC",
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:      "nav-brand-reputation-overview",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/reviews",
									Path:              "/account/brands/G-123/reputation/reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
									Label:             "Overview",
								},
								{
									NavigationId:      "nav-brand-reputation-manage",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/manage-reviews",
									Path:              "/account/brands/G-123/reputation/manage-reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REVIEWS",
									Label:             "Reviews",
								},
								{
									NavigationId:      "nav-brand-reputation-insights",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/insights",
									Path:              "/account/brands/G-123/reputation/insights",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS",
									Label:             "Insights",
								},
								{
									NavigationId:      "nav-brand-google-my-business-google-q-and-a",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/google-q-and-a",
									Path:              "/account/brands/G-123/reputation/google-q-and-a",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
									Label:             "Google Q&A",
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							TranslationId: "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.SOCIAL",
							Icon:          "question_answer",
							ShowIcon:      true,
							Label:         "Social",
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-social",
											Url:               "https://abc.smblogin.com/account/brands/G-123/social-overview",
											Path:              "/account/brands/G-123/social-overview",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
											Label:             "Overview",
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-social-manage-posts",
											Url:               "https://abc.smblogin.com/account/brands/G-123/manage-social-posts",
											Path:              "/account/brands/G-123/manage-social-posts",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.MANAGE_POSTS",
											Label:             "Manage Posts",
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-advertising",
							Url:               "https://abc.smblogin.com/account/brands/G-123/advertising-overview",
							Path:              "/account/brands/G-123/advertising-overview",
							ServiceProviderId: "VBC",
							Icon:              "public",
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.ADVERTISING",
							Label:             "Advertising",
							ShowIcon:          true,
						},
					},
				},
			},
		},
		{
			name: "Disable NPS tab Reputation with feature flag",
			args: &GetNavigationItemsArgs{
				GroupPath:     "G-123",
				WhitelabelURL: "https://abc.smblogin.com",
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{
						"recommendations",
						"vbc-executive-report",
						"meeting_scheduler_business_app",
						"content-library",
						"guides",
						"local-marketing-index",
						"access-marketplace",
						"fulfillment",
						"bc_my_team_page",
						"orders",
						"invoices",
						"files",
						"my_products",
						"customer_list",
						"home",
						"inbox",
					},
				},
				FeatureFlags: map[string]bool{
					"meeting_scheduler_business_app":         true,
					"business_emails":                        true,
					"bc_brands_manage_social":                true,
					"ai-assistants":                          true,
					constants.NPSFeatureIDReputation:         false,
					constants.NPSNewMLDesign:                 false,
					constants.CRMMultilocation:               true,
					constants.KeywordTrackingOverviewFeature: true,
					featureflags.GoalsForGroupFeatureID:      true,
					constants.PartnerFeatureIDLisAnalytics:   true,
				},
				BrandTabStatuses: &location.BrandTabStatuses{
					ReviewsTabEnabled:     true,
					ListingsTabEnabled:    false,
					SocialTabEnabled:      true,
					MapTabEnabled:         true,
					AdvertisingTabEnabled: true,
					ReportTabEnabled:      true,
					DataExportTabEnabled:  false,
					GoogleQAndATabEnabled: true,
				},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-home",
							Url:               "https://abc.smblogin.com/account/brands/G-123/home",
							Path:              "/account/brands/G-123/home",
							ServiceProviderId: "VBC",
							Icon:              "home",
							TranslationId:     "NAVIGATION.TABS.HOME",
							Label:             "Home",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							Label:         "Contacts",
							TranslationId: "NAVIGATION.TABS.CRM.CRM",
							Icon:          "contacts",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-crm-contacts",
											Url:               "https://abc.smblogin.com/account/brands/G-123/contacts",
											Path:              "/account/brands/G-123/contacts",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
											Label:             "Contacts",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-crm-contacts",
													Url:               "https://abc.smblogin.com/account/brands/G-123/contacts",
													Path:              "/account/brands/G-123/contacts",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
													Label:             "Contacts",
												},
											},
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-crm-tasks",
											Url:               "https://abc.smblogin.com/account/brands/G-123/tasks",
											Path:              "/account/brands/G-123/tasks",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
											Label:             "Tasks",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-crm-tasks",
													Url:               "https://abc.smblogin.com/account/brands/G-123/tasks",
													Path:              "/account/brands/G-123/tasks",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
													Label:             "Tasks",
												},
											},
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-report",
							Url:               "https://abc.smblogin.com/account/brands/G-123/report",
							Path:              "/account/brands/G-123/report",
							ServiceProviderId: "VBC",
							Icon:              "timeline",
							TranslationId:     "NAVIGATION.TABS.MULTI_LOCATION_REPORT",
							Label:             "Executive Report",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REPUTATION",
							Icon:              "star",
							ShowIcon:          true,
							Label:             "Reputation",
							NavigationId:      "nav-reputation",
							Url:               "https://abc.smblogin.com/account/brands/G-123/reputation",
							Path:              "/account/brands/G-123/reputation",
							ServiceProviderId: "VBC",
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:      "nav-brand-reputation-overview",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/reviews",
									Path:              "/account/brands/G-123/reputation/reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
									Label:             "Overview",
								},
								{
									NavigationId:      "nav-brand-reputation-manage",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/manage-reviews",
									Path:              "/account/brands/G-123/reputation/manage-reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REVIEWS",
									Label:             "Reviews",
								},
								{
									NavigationId:      "nav-brand-reputation-insights",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/insights",
									Path:              "/account/brands/G-123/reputation/insights",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS",
									Label:             "Insights",
								},
								{
									NavigationId:      "nav-brand-google-my-business-google-q-and-a",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/google-q-and-a",
									Path:              "/account/brands/G-123/reputation/google-q-and-a",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
									Label:             "Google Q&A",
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							TranslationId: "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.SOCIAL",
							Icon:          "question_answer",
							ShowIcon:      true,
							Label:         "Social",
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-social",
											Url:               "https://abc.smblogin.com/account/brands/G-123/social-overview",
											Path:              "/account/brands/G-123/social-overview",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
											Label:             "Overview",
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-social-manage-posts",
											Url:               "https://abc.smblogin.com/account/brands/G-123/manage-social-posts",
											Path:              "/account/brands/G-123/manage-social-posts",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.MANAGE_POSTS",
											Label:             "Manage Posts",
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-advertising",
							Url:               "https://abc.smblogin.com/account/brands/G-123/advertising-overview",
							Path:              "/account/brands/G-123/advertising-overview",
							ServiceProviderId: "VBC",
							Icon:              "public",
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.ADVERTISING",
							Label:             "Advertising",
							ShowIcon:          true,
						},
					},
				},
			},
		},
		{
			name: "Hide nps tabs when reputation is disabled",
			args: &GetNavigationItemsArgs{
				GroupPath:     "G-123",
				WhitelabelURL: "https://abc.smblogin.com",
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{
						"recommendations",
						"vbc-executive-report",
						"meeting_scheduler_business_app",
						"content-library",
						"guides",
						"local-marketing-index",
						"access-marketplace",
						"fulfillment",
						"bc_my_team_page",
						"orders",
						"invoices",
						"files",
						"my_products",
						"customer_list",
						"home",
						"inbox",
					},
				},
				FeatureFlags: map[string]bool{
					"meeting_scheduler_business_app":         true,
					"business_emails":                        true,
					"bc_brands_manage_social":                true,
					"ai-assistants":                          true,
					constants.NPSFeatureIDReputation:         true,
					constants.NPSNewMLDesign:                 true,
					constants.CRMMultilocation:               true,
					constants.KeywordTrackingOverviewFeature: true,
					featureflags.GoalsForGroupFeatureID:      true,
					constants.PartnerFeatureIDLisAnalytics:   true,
				},
				BrandTabStatuses: &location.BrandTabStatuses{
					ReviewsTabEnabled:     false,
					ListingsTabEnabled:    false,
					SocialTabEnabled:      true,
					MapTabEnabled:         true,
					AdvertisingTabEnabled: true,
					ReportTabEnabled:      true,
					DataExportTabEnabled:  false,
					GoogleQAndATabEnabled: true,
				},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-home",
							Url:               "https://abc.smblogin.com/account/brands/G-123/home",
							Path:              "/account/brands/G-123/home",
							ServiceProviderId: "VBC",
							Icon:              "home",
							TranslationId:     "NAVIGATION.TABS.HOME",
							Label:             "Home",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							Label:         "Contacts",
							TranslationId: "NAVIGATION.TABS.CRM.CRM",
							Icon:          "contacts",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-crm-contacts",
											Url:               "https://abc.smblogin.com/account/brands/G-123/contacts",
											Path:              "/account/brands/G-123/contacts",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
											Label:             "Contacts",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-crm-contacts",
													Url:               "https://abc.smblogin.com/account/brands/G-123/contacts",
													Path:              "/account/brands/G-123/contacts",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
													Label:             "Contacts",
												},
											},
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-crm-tasks",
											Url:               "https://abc.smblogin.com/account/brands/G-123/tasks",
											Path:              "/account/brands/G-123/tasks",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
											Label:             "Tasks",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-crm-tasks",
													Url:               "https://abc.smblogin.com/account/brands/G-123/tasks",
													Path:              "/account/brands/G-123/tasks",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
													Label:             "Tasks",
												},
											},
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-report",
							Url:               "https://abc.smblogin.com/account/brands/G-123/report",
							Path:              "/account/brands/G-123/report",
							ServiceProviderId: "VBC",
							Icon:              "timeline",
							TranslationId:     "NAVIGATION.TABS.MULTI_LOCATION_REPORT",
							Label:             "Executive Report",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							TranslationId: "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.SOCIAL",
							Icon:          "question_answer",
							ShowIcon:      true,
							Label:         "Social",
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-social",
											Url:               "https://abc.smblogin.com/account/brands/G-123/social-overview",
											Path:              "/account/brands/G-123/social-overview",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
											Label:             "Overview",
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-social-manage-posts",
											Url:               "https://abc.smblogin.com/account/brands/G-123/manage-social-posts",
											Path:              "/account/brands/G-123/manage-social-posts",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.MANAGE_POSTS",
											Label:             "Manage Posts",
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-advertising",
							Url:               "https://abc.smblogin.com/account/brands/G-123/advertising-overview",
							Path:              "/account/brands/G-123/advertising-overview",
							ServiceProviderId: "VBC",
							Icon:              "public",
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.ADVERTISING",
							Label:             "Advertising",
							ShowIcon:          true,
						},
					},
				},
			},
		},
		{
			name: "Show NPS tab Reputation",
			args: &GetNavigationItemsArgs{
				GroupPath:     "G-123",
				WhitelabelURL: "https://abc.smblogin.com",
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{
						"recommendations",
						"vbc-executive-report",
						"meeting_scheduler_business_app",
						"content-library",
						"guides",
						"local-marketing-index",
						"access-marketplace",
						"fulfillment",
						"bc_my_team_page",
						"orders",
						"invoices",
						"files",
						"my_products",
						"customer_list",
						"home",
						"inbox",
					},
				},
				FeatureFlags: map[string]bool{
					"meeting_scheduler_business_app":         true,
					"business_emails":                        true,
					"bc_brands_manage_social":                true,
					"ai-assistants":                          true,
					constants.NPSFeatureIDReputation:         true,
					constants.NPSNewMLDesign:                 true,
					constants.CRMMultilocation:               true,
					constants.KeywordTrackingOverviewFeature: true,
					constants.PartnerFeatureIDLisAnalytics:   true,
				},
				BrandTabStatuses: &location.BrandTabStatuses{
					ReviewsTabEnabled:     true,
					ListingsTabEnabled:    false,
					SocialTabEnabled:      true,
					MapTabEnabled:         true,
					AdvertisingTabEnabled: true,
					ReportTabEnabled:      true,
					DataExportTabEnabled:  false,
					GoogleQAndATabEnabled: true,
				},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-home",
							Url:               "https://abc.smblogin.com/account/brands/G-123/home",
							Path:              "/account/brands/G-123/home",
							ServiceProviderId: "VBC",
							Icon:              "home",
							TranslationId:     "NAVIGATION.TABS.HOME",
							Label:             "Home",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							Label:         "Contacts",
							TranslationId: "NAVIGATION.TABS.CRM.CRM",
							Icon:          "contacts",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-crm-contacts",
											Url:               "https://abc.smblogin.com/account/brands/G-123/contacts",
											Path:              "/account/brands/G-123/contacts",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
											Label:             "Contacts",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-crm-contacts",
													Url:               "https://abc.smblogin.com/account/brands/G-123/contacts",
													Path:              "/account/brands/G-123/contacts",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
													Label:             "Contacts",
												},
											},
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-crm-tasks",
											Url:               "https://abc.smblogin.com/account/brands/G-123/tasks",
											Path:              "/account/brands/G-123/tasks",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
											Label:             "Tasks",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-crm-tasks",
													Url:               "https://abc.smblogin.com/account/brands/G-123/tasks",
													Path:              "/account/brands/G-123/tasks",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
													Label:             "Tasks",
												},
											},
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-report",
							Url:               "https://abc.smblogin.com/account/brands/G-123/report",
							Path:              "/account/brands/G-123/report",
							ServiceProviderId: "VBC",
							Icon:              "timeline",
							TranslationId:     "NAVIGATION.TABS.MULTI_LOCATION_REPORT",
							Label:             "Executive Report",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							TranslationId: "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REPUTATION",
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-reputation-reviews",
											Url:               "https://abc.smblogin.com/account/brands/G-123/reviews",
											Path:              "/account/brands/G-123/reviews",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REVIEWS",
											Label:             "Reviews",
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-reputation-overview",
													Url:               "https://abc.smblogin.com/account/brands/G-123/reviews/overview",
													Path:              "/account/brands/G-123/reviews/overview",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
													Label:             "Overview",
												},
												{
													NavigationId:      "nav-brand-reputation-manage",
													Url:               "https://abc.smblogin.com/account/brands/G-123/reviews/manage-reviews",
													Path:              "/account/brands/G-123/reviews/manage-reviews",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REVIEWS",
													Label:             "Reviews",
												},
												{
													NavigationId:      "nav-brand-reputation-insights",
													Url:               "https://abc.smblogin.com/account/brands/G-123/reviews/insights",
													Path:              "/account/brands/G-123/reviews/insights",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS",
													Label:             "Insights",
												},
												{
													NavigationId:      "nav-brand-google-my-business-google-q-and-a",
													Url:               "https://abc.smblogin.com/account/brands/G-123/reviews/google-q-and-a",
													Path:              "/account/brands/G-123/reviews/google-q-and-a",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
													Label:             "Google Q&A",
												},
											},
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-reputation-net_promoter_score",
											Url:               "https://abc.smblogin.com/account/brands/G-123/netpromoterscore",
											Path:              "/account/brands/G-123/netpromoterscore",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.NET_PROMOTER_SCORE",
											Label:             "Net Promoter Score",
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-reputation-nps-overview",
													Url:               "https://abc.smblogin.com/account/brands/G-123/netpromoterscore/overview",
													Path:              "/account/brands/G-123/netpromoterscore/overview",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
													Label:             "Overview",
												},
												{
													NavigationId:      "nav-brand-reputation-nps",
													Url:               "https://abc.smblogin.com/account/brands/G-123/netpromoterscore/nps",
													Path:              "/account/brands/G-123/netpromoterscore/nps",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.NPS",
													Label:             "NPS",
												},
												{
													NavigationId:      "nav-brand-reputation-nps-team",
													Url:               "https://abc.smblogin.com/account/brands/G-123/netpromoterscore/team",
													Path:              "/account/brands/G-123/netpromoterscore/team",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TEAM",
													Label:             "Team",
												},
											},
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-reputation-requests",
											Url:               "https://abc.smblogin.com/account/brands/G-123/requests",
											Path:              "/account/brands/G-123/requests",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REQUESTS",
											Label:             "Requests",
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-reputation-requests-overview",
													Url:               "https://abc.smblogin.com/account/brands/G-123/requests/overview",
													Path:              "/account/brands/G-123/requests/overview",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
													Label:             "Overview",
												},
											},
										},
									},
								},
							},
							Icon:     "star",
							Label:    "Reputation",
							ShowIcon: true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							TranslationId: "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.SOCIAL",
							Icon:          "question_answer",
							ShowIcon:      true,
							Label:         "Social",
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-social",
											Url:               "https://abc.smblogin.com/account/brands/G-123/social-overview",
											Path:              "/account/brands/G-123/social-overview",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
											Label:             "Overview",
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-social-manage-posts",
											Url:               "https://abc.smblogin.com/account/brands/G-123/manage-social-posts",
											Path:              "/account/brands/G-123/manage-social-posts",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.MANAGE_POSTS",
											Label:             "Manage Posts",
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-advertising",
							Url:               "https://abc.smblogin.com/account/brands/G-123/advertising-overview",
							Path:              "/account/brands/G-123/advertising-overview",
							ServiceProviderId: "VBC",
							Icon:              "public",
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.ADVERTISING",
							Label:             "Advertising",
							ShowIcon:          true,
						},
					},
				},
			},
		},
		{
			name: "listings tab enabled",
			args: &GetNavigationItemsArgs{
				PartnerID:     "ABC",
				GroupPath:     "G-123",
				WhitelabelURL: "https://abc.smblogin.com",
				Products: []*product.Product{
					{
						ServiceProviderID: "MS",
						Name:              "Listings",
					},
				},
				Features: &sidenavigation.Features{
					EnabledFeatures: []string{
						"recommendations",
						"vbc-executive-report",
						"meeting_scheduler_business_app",
						"content-library",
						"guides",
						"local-marketing-index",
						"access-marketplace",
						"fulfillment",
						"bc_my_team_page",
						"orders",
						"invoices",
						"files",
						"my_products",
						"customer_list",
						"home",
						"inbox",
					},
					ShowInboxMessage: true,
					ShowMyProducts:   true,
				},
				FeatureFlags: map[string]bool{
					"meeting_scheduler_business_app":           true,
					"business_emails":                          true,
					"bc_brands_manage_social":                  true,
					constants.NPSNewMLDesign:                   false,
					constants.CRMMultilocation:                 true,
					constants.KeywordTrackingOverviewFeature:   true,
					featureflags.GoalsForGroupFeatureID:        true,
					constants.PartnerFeatureIDLisAnalytics:     true,
					constants.PartnerFeatureIDEmbeddedListings: true,
				},
				BrandTabStatuses: &location.BrandTabStatuses{
					ReviewsTabEnabled:     true,
					ListingsTabEnabled:    true,
					SocialTabEnabled:      true,
					MapTabEnabled:         true,
					AdvertisingTabEnabled: true,
					ReportTabEnabled:      true,
					DataExportTabEnabled:  true,
					GoogleQAndATabEnabled: true,
				},
			},
			want: []*atlas_v1.SideNavigationItem{
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-home",
							Url:               "https://abc.smblogin.com/account/brands/G-123/home",
							Path:              "/account/brands/G-123/home",
							ServiceProviderId: "VBC",
							Icon:              "home",
							TranslationId:     "NAVIGATION.TABS.HOME",
							Label:             "Home",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-inbox",
							Url:               "https://abc.smblogin.com/account/brands/G-123/inbox",
							Path:              "/account/brands/G-123/inbox",
							ServiceProviderId: "VBC",
							Icon:              "question_answer",
							TranslationId:     "NAVIGATION.TABS.INBOX",
							Label:             "Inbox Messages",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							Label:         "Contacts",
							TranslationId: "NAVIGATION.TABS.CRM.CRM",
							Icon:          "contacts",
							ShowIcon:      true,
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-crm-contacts",
											Url:               "https://abc.smblogin.com/account/brands/G-123/contacts",
											Path:              "/account/brands/G-123/contacts",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
											Label:             "Contacts",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-crm-contacts",
													Url:               "https://abc.smblogin.com/account/brands/G-123/contacts",
													Path:              "/account/brands/G-123/contacts",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.CONTACTS",
													Label:             "Contacts",
												},
											},
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-crm-tasks",
											Url:               "https://abc.smblogin.com/account/brands/G-123/tasks",
											Path:              "/account/brands/G-123/tasks",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
											Label:             "Tasks",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-crm-tasks",
													Url:               "https://abc.smblogin.com/account/brands/G-123/tasks",
													Path:              "/account/brands/G-123/tasks",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.TASKS",
													Label:             "Tasks",
												},
											},
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-report",
							Url:               "https://abc.smblogin.com/account/brands/G-123/report",
							Path:              "/account/brands/G-123/report",
							ServiceProviderId: "VBC",
							Icon:              "timeline",
							TranslationId:     "NAVIGATION.TABS.MULTI_LOCATION_REPORT",
							Label:             "Executive Report",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-reputation",
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REPUTATION",
							Icon:              "star",
							ShowIcon:          true,
							Label:             "Reputation",
							Url:               "https://abc.smblogin.com/account/brands/G-123/reputation",
							Path:              "/account/brands/G-123/reputation",
							ServiceProviderId: "VBC",
							SubLinks: []*atlas_v1.SideNavigationLink{
								{
									NavigationId:      "nav-brand-reputation-overview",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/reviews",
									Path:              "/account/brands/G-123/reputation/reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
									Label:             "Overview",
								},
								{
									NavigationId:      "nav-brand-reputation-manage",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/manage-reviews",
									Path:              "/account/brands/G-123/reputation/manage-reviews",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.REVIEWS",
									Label:             "Reviews",
								},
								{
									NavigationId:      "nav-brand-reputation-insights",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/insights",
									Path:              "/account/brands/G-123/reputation/insights",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.INSIGHTS",
									Label:             "Insights",
								},
								{
									NavigationId:      "nav-brand-google-my-business-google-q-and-a",
									Url:               "https://abc.smblogin.com/account/brands/G-123/reputation/google-q-and-a",
									Path:              "/account/brands/G-123/reputation/google-q-and-a",
									ServiceProviderId: "VBC",
									TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.GOOGLE_Q_AND_A",
									Label:             "Google Q\u0026A",
								},
							},
						},
					},
				},

				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							TranslationId: "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.ALL_LISTINGS",
							Icon:          "store",
							ShowIcon:      true,
							Label:         "All Listings",
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-embedded-listings",
											Url:               "https://abc.smblogin.com/account/brands/G-123/listings",
											Path:              "/account/brands/G-123/listings",
											ServiceProviderId: "VBC",
											Icon:              "store",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.LISTINGS",
											Label:             "Listings",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-listings-overview",
													Url:               "https://abc.smblogin.com/account/brands/G-123/listings/overview",
													Path:              "/account/brands/G-123/listings/overview",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
													Label:             "Overview",
												},
												{
													NavigationId:      "nav-brand-listings-manage",
													Url:               "https://abc.smblogin.com/account/brands/G-123/listings/manage",
													Path:              "/account/brands/G-123/listings/manage",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.MANAGE",
													Label:             "Manage",
												},
											},
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-embedded-keyword-tracking",
											Url:               "https://abc.smblogin.com/account/brands/G-123/keyword-tracking",
											Path:              "/account/brands/G-123/keyword-tracking",
											ServiceProviderId: "VBC",
											Icon:              "trending_up",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.KEYWORD_TRACKING",
											Label:             "Keyword Tracking",
											ShowIcon:          false,
											SubLinks: []*atlas_v1.SideNavigationLink{
												{
													NavigationId:      "nav-brand-keyword-tracking-overview",
													Url:               "https://abc.smblogin.com/account/brands/G-123/keyword-tracking/overview",
													Path:              "/account/brands/G-123/keyword-tracking/overview",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.KEYWORD_TRACKING_OVERVIEW",
													Label:             "Overview",
												},
												{
													NavigationId:      "nav-brand-keyword-tracking-keyword",
													Url:               "https://abc.smblogin.com/account/brands/G-123/keyword-tracking/keywords",
													Path:              "/account/brands/G-123/keyword-tracking/keywords",
													ServiceProviderId: "VBC",
													TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.KEYWORD_TRACKING_KEYWORD",
													Label:             "Keywords",
												},
											},
										},
									},
								},
							},
						},
					},
				},

				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
						SideNavigationContainer: &atlas_v1.SideNavigationContainer{
							TranslationId: "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.SOCIAL",
							Icon:          "question_answer",
							ShowIcon:      true,
							Label:         "Social",
							SideNavigationItems: []*atlas_v1.SideNavigationItem{
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-social",
											Url:               "https://abc.smblogin.com/account/brands/G-123/social-overview",
											Path:              "/account/brands/G-123/social-overview",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.OVERVIEW",
											Label:             "Overview",
										},
									},
								},
								{
									Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
										SideNavigationLink: &atlas_v1.SideNavigationLink{
											NavigationId:      "nav-brand-social-manage-posts",
											Url:               "https://abc.smblogin.com/account/brands/G-123/manage-social-posts",
											Path:              "/account/brands/G-123/manage-social-posts",
											ServiceProviderId: "VBC",
											TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.MANAGE_POSTS",
											Label:             "Manage Posts",
										},
									},
								},
							},
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-advertising",
							Url:               "https://abc.smblogin.com/account/brands/G-123/advertising-overview",
							Path:              "/account/brands/G-123/advertising-overview",
							ServiceProviderId: "VBC",
							Icon:              "public",
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.ADVERTISING",
							Label:             "Advertising",
							ShowIcon:          true,
						},
					},
				},
				{
					Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
						SideNavigationLink: &atlas_v1.SideNavigationLink{
							NavigationId:      "nav-brand-data-export",
							Url:               "https://abc.smblogin.com/account/brands/G-123/data-export",
							Path:              "/account/brands/G-123/data-export",
							ServiceProviderId: "VBC",
							Icon:              "unarchive",
							TranslationId:     "PERFORMANCE.MULTI_LOCATION.PAGE_TITLES.DATA_EXPORT",
							Label:             "Data Exporter",
							ShowIcon:          true,
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewNavItemGenerator(tt.args)
			if err != nil {
				t.Fatal(err)
			}

			s := navigationconfig.New(nil)
			config, err := s.GetLinkConfig(navigationconfig.MultiLocationID)
			if err != nil {
				t.Fatal(err)
			}
			tt.args.NavConfig = config.Items

			got, _, err := GetNavigationItems(context.Background(), tt.args)
			assert.NoError(t, err)
			assertNoMissingLinks(t, tt.want, got)
		})
	}
}

func TestGetNavigationItems_ReturnsExpectedProducts(t *testing.T) {
	products := []*product.Product{
		{
			ServiceProviderID: "serviceProvider1",
			Name:              "p1",
			EntryURL:          "https://p1.com/entry",
			Path:              "/entry",
			Host:              "p1.com",
			LogoURL:           "p1.com/logo",
			RequiresUser:      false,
		},
		{
			ServiceProviderID: "serviceProvider2",
			Name:              "p2",
			EntryURL:          "https://p2.com/entry",
			Path:              "/entry",
			Host:              "p2.com",
			LogoURL:           "p2.com/logo",
			RequiresUser:      true,
		},
	}

	navArgs := &GetNavigationItemsArgs{
		Products: products,
		NavConfig: []navigationconfig.LinkConfig{
			{
				NavItemID: "marketplace-products",
				Type:      navigationconfig.Placeholder,
				Children:  nil,
				Rules:     nil,
			},
		},
		ProductNavConfigs: []*navigationconfig.NavigationConfig{
			{
				ConfigID: "product-serviceProvider1",
				Items: []navigationconfig.LinkConfig{
					{
						NavItemID: "product-serviceProvider1",
						Type:      navigationconfig.ExternalProduct,
						Children:  nil,
						Rules:     nil,
					},
				},
			},
			{
				ConfigID: "product-serviceProvider2",
				Items: []navigationconfig.LinkConfig{
					{
						NavItemID: "product-serviceProvider2",
						Type:      navigationconfig.ExternalProduct,
						Children:  nil,
						Rules:     nil,
					},
				},
			},
		},
	}

	got, _, err := GetNavigationItems(context.Background(), navArgs)
	if err != nil {
		t.Fatal(err)
	}

	want := []*atlas_v1.SideNavigationItem{
		{
			Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
				SideNavigationLink: &atlas_v1.SideNavigationLink{
					NavigationId:      "product-serviceProvider1",
					ServiceProviderId: "serviceProvider1",
					Path:              "/entry",
					Url:               "https://p1.com/entry",
					LogoUrl:           "p1.com/logo",
					Label:             "p1",
					Pinnable:          true,
					ShowIcon:          true,
					UserRequired:      false,
					OpenInNewTab:      true,
				},
			},
		},
		{
			Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
				SideNavigationLink: &atlas_v1.SideNavigationLink{
					NavigationId:      "product-serviceProvider2",
					ServiceProviderId: "serviceProvider2",
					Path:              "/entry",
					Url:               "https://p2.com/entry",
					LogoUrl:           "p2.com/logo",
					Label:             "p2",
					Pinnable:          true,
					ShowIcon:          true,
					UserRequired:      true,
					OpenInNewTab:      true,
				},
			},
		},
	}

	assert.EqualValues(t, want, got)
}

func TestGetNavigationItems_ReturnsExpectedCRMCustomObjects(t *testing.T) {
	crmCustomObjectTypes := []*crmcustomobjects.CRMCustomObjectType{
		{
			ID:   "CustomObjectType-1",
			Name: "Vehicles",
			Path: "/account/location/AG-123/crm/custom-object/CustomObjectType-1/list",
		},
		{
			ID:   "CustomObjectType-2",
			Name: "Hotels",
			Path: "/account/location/AG-123/crm/custom-object/CustomObjectType-2/list",
		},
	}

	navArgs := &GetNavigationItemsArgs{
		WhitelabelURL:        "https://vbc-demo.com",
		CRMCustomObjectTypes: crmCustomObjectTypes,
		NavConfig: []navigationconfig.LinkConfig{
			{
				"crm", navigationconfig.Container, "", []navigationconfig.LinkConfig{
					{
						NavItemID: "nav-crm-custom-objects",
						Type:      navigationconfig.Placeholder,
						Children:  nil,
						Rules:     nil,
					},
				},
				nil,
			},
		},
	}
	got, _, err := GetNavigationItems(context.Background(), navArgs)
	if err != nil {
		t.Fatal(err)
	}

	want := []*atlas_v1.SideNavigationItem{
		{
			Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
				SideNavigationContainer: &atlas_v1.SideNavigationContainer{
					Label:         "Contacts",
					Icon:          "contacts",
					TranslationId: "NAVIGATION.TABS.CRM.CRM",
					ShowIcon:      true,
					SideNavigationItems: []*atlas_v1.SideNavigationItem{
						{
							Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
								SideNavigationLink: &atlas_v1.SideNavigationLink{
									NavigationId:      "nav-crm-custom-objects-CustomObjectType-1",
									ServiceProviderId: "VBC",
									Url:               "https://vbc-demo.com/account/location/AG-123/crm/custom-object/CustomObjectType-1/list",
									Path:              "/account/location/AG-123/crm/custom-object/CustomObjectType-1/list",
									Label:             "Vehicles",
									TranslationId:     "",
									Pinnable:          false,
									ShowIcon:          false,
									UserRequired:      false,
									OpenInNewTab:      false,
								},
							},
						},
						{
							Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
								SideNavigationLink: &atlas_v1.SideNavigationLink{
									NavigationId:      "nav-crm-custom-objects-CustomObjectType-2",
									ServiceProviderId: "VBC",
									Url:               "https://vbc-demo.com/account/location/AG-123/crm/custom-object/CustomObjectType-2/list",
									Path:              "/account/location/AG-123/crm/custom-object/CustomObjectType-2/list",
									Label:             "Hotels",
									TranslationId:     "",
									Pinnable:          false,
									ShowIcon:          false,
									UserRequired:      false,
									OpenInNewTab:      false,
								},
							},
						},
					},
				},
			},
		},
	}

	assert.EqualValues(t, want, got)
}

func TestGetNavigationItems_ReturnsExpectedProductsWhenNestedInContainer(t *testing.T) {
	products := []*product.Product{
		{
			ServiceProviderID: "serviceProvider1",
			Name:              "p1",
			EntryURL:          "https://p1.com/entry",
			Path:              "/entry",
			Host:              "p1.com",
			LogoURL:           "p1.com/logo",
			RequiresUser:      false,
		},
		{
			ServiceProviderID: "serviceProvider2",
			Name:              "p2",
			EntryURL:          "https://p2.com/entry",
			Path:              "/entry",
			Host:              "p2.com",
			LogoURL:           "p2.com/logo",
			RequiresUser:      false,
		},
	}

	navArgs := &GetNavigationItemsArgs{
		Products: products,
		NavConfig: []navigationconfig.LinkConfig{
			{
				NavItemID: "nav-marketing",
				Type:      navigationconfig.Container,
				Children: []navigationconfig.LinkConfig{
					{
						NavItemID: "product-serviceProvider1",
						Type:      navigationconfig.ExternalProduct,
						Children:  nil,
						Rules:     nil,
					},
					{
						NavItemID: "product-serviceProvider2",
						Type:      navigationconfig.ExternalProduct,
						Children:  nil,
						Rules:     nil,
					},
				},
				Rules: nil,
			},
		},
		ProductNavConfigs: []*navigationconfig.NavigationConfig{
			{
				ConfigID: "product-serviceProvider1",
				Items: []navigationconfig.LinkConfig{
					{
						NavItemID: "product-serviceProvider1",
						Type:      navigationconfig.ExternalProduct,
						Children:  nil,
						Rules:     nil,
					},
				},
			},
			{
				ConfigID: "product-serviceProvider2",
				Items: []navigationconfig.LinkConfig{
					{
						NavItemID: "product-serviceProvider2",
						Type:      navigationconfig.ExternalProduct,
						Children:  nil,
						Rules:     nil,
					},
				},
			},
		},
	}

	got, _, err := GetNavigationItems(context.Background(), navArgs)
	if err != nil {
		t.Fatal(err)
	}

	want := []*atlas_v1.SideNavigationItem{
		{
			Item: &atlas_v1.SideNavigationItem_SideNavigationContainer{
				SideNavigationContainer: &atlas_v1.SideNavigationContainer{
					Label:         "Marketing",
					TranslationId: "NAVIGATION.TABS.MARKETING",
					Icon:          "campaigns",
					ShowIcon:      true,
					SideNavigationItems: []*atlas_v1.SideNavigationItem{
						{
							Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
								SideNavigationLink: &atlas_v1.SideNavigationLink{
									NavigationId:      "product-serviceProvider1",
									ServiceProviderId: "serviceProvider1",
									Path:              "/entry",
									Url:               "https://p1.com/entry",
									LogoUrl:           "p1.com/logo",
									Label:             "p1",
									Pinnable:          true,
									ShowIcon:          true,
									UserRequired:      false,
									OpenInNewTab:      true,
								},
							},
						},
						{
							Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
								SideNavigationLink: &atlas_v1.SideNavigationLink{
									NavigationId:      "product-serviceProvider2",
									ServiceProviderId: "serviceProvider2",
									Path:              "/entry",
									Url:               "https://p2.com/entry",
									LogoUrl:           "p2.com/logo",
									Label:             "p2",
									Pinnable:          true,
									ShowIcon:          true,
									UserRequired:      false,
									OpenInNewTab:      true,
								},
							},
						},
					},
				},
			},
		},
	}

	assert.EqualValues(t, want, got)
}

func TestGetNavigationItems_DoesNotReturnIncorrectProductNavItem_WhenServiceProviderIDSubsetOfAnotherServiceProvider(t *testing.T) {
	products := []*product.Product{
		{
			ServiceProviderID: constants.Listings,
			Name:              "p1",
			EntryURL:          "https://p1.com/entry",
			Path:              "/entry",
			Host:              "p1.com",
			LogoURL:           "p1.com/logo",
			RequiresUser:      false,
		},
		{
			ServiceProviderID: "MP-BBG2CWQD76ZQZLMSJPTSMWQSWGQXQVHZ",
			Name:              "p2",
			EntryURL:          "https://p2.com/entry",
			Path:              "/entry",
			Host:              "p2.com",
			LogoURL:           "p2.com/logo",
			RequiresUser:      true,
		},
	}

	navArgs := &GetNavigationItemsArgs{
		Products: products,
		NavConfig: []navigationconfig.LinkConfig{
			{
				NavItemID: "marketplace-products",
				Type:      navigationconfig.Placeholder,
				Children:  nil,
				Rules:     nil,
			},
		},
		ProductNavConfigs: []*navigationconfig.NavigationConfig{
			{
				ConfigID: constants.InternalGetExternalProductConfigID(constants.Listings),
				Items: []navigationconfig.LinkConfig{
					{
						NavItemID: constants.InternalGetExternalProductConfigID(constants.Listings),
						Type:      navigationconfig.ExternalProduct,
						Children:  nil,
						Rules:     nil,
					},
				},
			},
			{
				ConfigID: "product-MP-BBG2CWQD76ZQZLMSJPTSMWQSWGQXQVHZ",
				Items: []navigationconfig.LinkConfig{
					{
						NavItemID: "product-MP-BBG2CWQD76ZQZLMSJPTSMWQSWGQXQVHZ",
						Type:      navigationconfig.ExternalProduct,
						Children:  nil,
						Rules:     nil,
					},
				},
			},
		},
		FeatureFlags: map[string]bool{
			constants.PartnerFeatureIDEmbeddedSocialMarketing: true,
			constants.PartnerFeatureIDEmbeddedAdIntel:         true,
			constants.PartnerFeatureIDEmbeddedListings:        true,
			featureflags.GoalsForGroupFeatureID:               true,
		},
		Features: &sidenavigation.Features{

			EnabledFeatures: []string{
				"recommendations",
				"vbc-executive-report",
				"meeting_scheduler_business_app",
				"content-library",
				"guides",
				"local-marketing-index",
				"access-marketplace",
				"fulfillment",
				"bc_my_team_page",
				"orders",
				"invoices",
				"files",
				"my_products",
				"customer_list",
				"dashboard",
				"inbox",
			},
			ShowMyProducts: true,
		},
	}

	got, _, err := GetNavigationItems(context.Background(), navArgs)
	if err != nil {
		t.Fatal(err)
	}

	want := []*atlas_v1.SideNavigationItem{
		{
			Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
				SideNavigationLink: &atlas_v1.SideNavigationLink{
					NavigationId:      "product-MS",
					ServiceProviderId: constants.Listings,
					Path:              "/entry",
					Url:               "https://p1.com/entry",
					LogoUrl:           "p1.com/logo",
					Label:             "p1",
					Pinnable:          true,
					ShowIcon:          true,
					UserRequired:      false,
					OpenInNewTab:      false,
				},
			},
		},
		{
			Item: &atlas_v1.SideNavigationItem_SideNavigationLink{
				SideNavigationLink: &atlas_v1.SideNavigationLink{
					NavigationId:      "product-MP-BBG2CWQD76ZQZLMSJPTSMWQSWGQXQVHZ",
					ServiceProviderId: "MP-BBG2CWQD76ZQZLMSJPTSMWQSWGQXQVHZ",
					Path:              "/entry",
					Url:               "https://p2.com/entry",
					LogoUrl:           "p2.com/logo",
					Label:             "p2",
					Pinnable:          true,
					ShowIcon:          true,
					UserRequired:      true,
					OpenInNewTab:      true,
				},
			},
		},
	}

	assert.EqualValues(t, want, got)
}

func TestGetDefaultProductNavConfigBuilder(t *testing.T) {
	testCases := []struct {
		name         string
		products     []*product.Product
		configIDs    []string
		want         *navigationconfig.NavigationConfig
		errorMessage string
	}{
		{
			name:         "returns error if config id is not included in list of products",
			products:     []*product.Product{},
			configIDs:    []string{"product-MS"},
			errorMessage: "unknown product: product-MS",
		},
		{
			name:      "returns correct config when products are present",
			configIDs: []string{"product-MS"},
			products: []*product.Product{
				{
					ServiceProviderID: constants.Listings,
					Name:              "p1",
					EntryURL:          "https://p1.com/entry",
					Path:              "/entry",
					Host:              "p1.com",
					LogoURL:           "p1.com/logo",
					RequiresUser:      false,
				},
			},
			want: &navigationconfig.NavigationConfig{
				ConfigID: "product-MS",
				Items: []navigationconfig.LinkConfig{
					{
						NavItemID: "product-MS",
						Type:      navigationconfig.ExternalProduct,
						Children:  nil,
						Rules:     nil,
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			for _, config := range tc.configIDs {
				got, err := GetDefaultProductNavConfigBuilder(tc.products)(config)
				if tc.errorMessage != "" {
					assert.EqualError(t, err, tc.errorMessage)
					return
				}
				assert.NoError(t, err)
				assert.EqualValues(t, tc.want, got)
			}
		})
	}

}

type missingLink struct {
	want        *atlas_v1.SideNavigationItem
	wantPriorID string
	got         *atlas_v1.SideNavigationItem
	gotPriorID  string
	diff        string
}

func assertNoMissingLinks(t *testing.T, want, got []*atlas_v1.SideNavigationItem) {
	if mae := getMissingLinks(want, got); len(mae.missing) > 0 || len(mae.extra) > 0 {
		missing := mae.missing
		for _, m := range missing {
			if m.got != nil {
				if m.wantPriorID != m.gotPriorID {
					t.Errorf("expected item %s to appear after %s, but it appeared after %s instead\n", getRelevantID(m.want), m.wantPriorID, m.gotPriorID)
				}
				if m.diff != "" {
					t.Errorf("\n---- Legend ----\n`+` (expected)\n`-` (actual)\n\nitem with id %s is different:\n %s\n", getRelevantID(m.want), m.diff)
				}
			} else {
				t.Errorf("missing item with id %s, expected to appear after %s", getRelevantID(m.want), m.wantPriorID)
			}
		}

		extra := mae.extra
		for _, e := range extra {
			t.Errorf("extra item with id %s", e)
		}
	}
}

type missingAndExtraLinks struct {
	missing []missingLink
	extra   []string
}

func getMissingLinks(want, got []*atlas_v1.SideNavigationItem) missingAndExtraLinks {
	var missing []missingLink
	extra := map[string]struct{}{}
	// check if all items in want are in got
	// if an item is not in got, add it to missing, along with the id of its prior sibling
	for i, item := range want {
		var gotItem *atlas_v1.SideNavigationItem
		found := false
		j := 0
		for j, gotItem = range got {
			if isSameNavItemID(item, gotItem) {
				extra[getRelevantID(gotItem)] = struct{}{}
				found = true
				break
			}

		}
		wantPriorID := "no prior item"
		gotPriorID := "no prior item"
		if i > 0 {
			wantPriorID = getRelevantID(want[i-1])
		}
		if j > 0 {
			gotPriorID = getRelevantID(got[j-1])
		}

		if found && (!reflect.DeepEqual(item, gotItem) || wantPriorID != gotPriorID) {
			missing = append(missing, missingLink{
				want:        item,
				gotPriorID:  gotPriorID,
				diff:        calcProtoDiff(item, gotItem),
				wantPriorID: wantPriorID,
				got:         gotItem,
			})
		} else if !found {
			wantPriorID := "no prior item"
			if i > 0 {
				wantPriorID = getRelevantID(want[i-1])
			}
			missing = append(missing, missingLink{
				want:        item,
				wantPriorID: wantPriorID,
			})
		}
	}

	var extraIDs []string
	if len(extra) != len(got) {
		for _, item := range got {
			if _, ok := extra[getRelevantID(item)]; !ok {
				extraIDs = append(extraIDs, getRelevantID(item))
			}
		}
	}

	return missingAndExtraLinks{
		missing: missing,
		extra:   extraIDs,
	}
}

func getRelevantID(item *atlas_v1.SideNavigationItem) string {
	if item == nil {
		return "(*nil)"
	}

	if c := item.GetSideNavigationContainer(); c != nil {
		if c.Label != "" {
			return c.Label
		}

		return c.TranslationId
	}

	return item.GetSideNavigationLink().NavigationId
}

func isSameNavItemID(a, b *atlas_v1.SideNavigationItem) bool {
	return getRelevantID(a) == getRelevantID(b)
}

func calcProtoDiff(a, b *atlas_v1.SideNavigationItem) string {
	wCont, gCont, wLink, gLink := a.GetSideNavigationContainer(), b.GetSideNavigationContainer(), a.GetSideNavigationLink(), b.GetSideNavigationLink()
	// if the non-nil types aren't the same, use a simple message
	if (wCont == nil && gCont != nil) || (wCont != nil && gCont == nil) {
		return fmt.Sprintf("want type: %T, got type: %T", wCont, gCont)
	} else if (wLink == nil && gLink != nil) || (wLink != nil && gLink == nil) {
		return fmt.Sprintf("want type: %T, got type: %T", wLink, gLink)
	} else if wCont != nil && gCont != nil {
		// if the types are the same, but the translations are different, use a simple message
		if wCont.TranslationId != gCont.TranslationId {
			return fmt.Sprintf("want translationId: %s, got translationId: %s", wCont.TranslationId, gCont.TranslationId)
		}
	} else if wLink != nil && gLink != nil {
		// if the types are the same, but the navigationIds are different, use a simple message
		if wLink.NavigationId != gLink.NavigationId {
			return fmt.Sprintf("want navigationId: %s, got navigationId: %s", wLink.NavigationId, gLink.NavigationId)
		}
	}

	// if the types are the same, and the translations are the same, use a detailed message
	return cmp.Diff(a, b, protoDiffer)
}
