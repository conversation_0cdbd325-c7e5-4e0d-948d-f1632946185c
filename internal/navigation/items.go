package navigation

import (
	"context"
	"fmt"

	"github.com/vendasta/atlas/internal/crmcustomobjects"

	atlas_v1 "github.com/vendasta/generated-protos-go/atlas/v1"
	"github.com/vendasta/gosdks/config"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/verrors"

	"github.com/vendasta/atlas/internal/accountsdata"
	"github.com/vendasta/atlas/internal/location"
	navigationconfig "github.com/vendasta/atlas/internal/navigation/config"
	"github.com/vendasta/atlas/internal/navigation/internal/constants"
	"github.com/vendasta/atlas/internal/product"
	"github.com/vendasta/atlas/internal/sidenavigation"
)

// GetNavigationItemsArgs are arguments for navigation items
type GetNavigationItemsArgs struct {
	SubjectID            string
	PartnerID            string
	MarketID             string
	WhitelabelURL        string
	TabPermissions       []string
	Features             *sidenavigation.Features
	FeatureFlags         map[string]bool
	AccountGroupID       string
	GroupPath            string
	Products             []*product.Product
	IncludeBrandItems    bool
	AccountsData         []*accountsdata.Account
	Environment          string
	BrandTabStatuses     *location.BrandTabStatuses
	PinnedItems          []string
	ReferralLink         *sidenavigation.ReferralLink
	Country              string
	IsImpersonating      bool
	NavConfig            []navigationconfig.LinkConfig
	ProductNavConfigs    []*navigationconfig.NavigationConfig
	EditProfileEnabled   bool
	PlatformMode         string
	CRMCustomObjectTypes []*crmcustomobjects.CRMCustomObjectType
}

func overrideInternalProductConfig(
	navItem navigationconfig.LinkConfig,
	product *product.Product,
	links *NavItemGenerator,
	opts []LinkOption,
) []LinkOption {
	switch product.ServiceProviderID {
	case constants.Advertising:
		if navItem.NavItemID == "nav-adintel" {
			opts = append(opts, links.HydrateProductInfoOption(product))
			break
		}
	case constants.SocialMarketing:
		if navItem.NavItemID == "nav-social-marketing" || navItem.NavItemID == "product-SM" {
			opts = append(opts, links.HydrateProductInfoOption(product))
			break
		}
	case constants.ReputationManagement:
		if navItem.NavItemID == "nav-reputation" {
			opts = append(opts, links.HydrateProductInfoOption(product))
			break
		}
	case constants.Listings:
		if navItem.NavItemID == "nav-listings" || navItem.NavItemID == "product-MS" {
			opts = append(opts, links.HydrateProductInfoOption(product), links.SetOpenInNewTabOption(false))
		}
	}

	return opts
}

// GetNavigationItems returns a list of navigation items
func GetNavigationItems(ctx context.Context, args *GetNavigationItemsArgs) (
	[]*atlas_v1.SideNavigationItem,
	[]*atlas_v1.DropdownItem,
	error,
) {
	accessErrors := make([]error, 0)
	defer func() {
		if !VerboseLoggingEnabled(args.PartnerID) {
			return
		}

		logging.Tag(ctx, "GetNavigationItems.ConfigID", args.PartnerID)
		if len(accessErrors) > 0 {
			logging.Infof(ctx, "Access errors: %v", accessErrors)
		}
	}()

	links, err := NewNavItemGenerator(args)
	if err != nil {
		return nil, nil, err
	}
	var lg LinkGetter
	lg = func(navItem navigationconfig.LinkConfig, opts ...LinkOption) *atlas_v1.SideNavigationItem {
		var item *atlas_v1.SideNavigationItem
		var err error

		for _, product := range args.Products {
			if constants.InternalGetExternalProductConfigID(product.ServiceProviderID) == navItem.NavItemID {
				opts = append(opts, links.HydrateProductInfoOption(product))
			}
			opts = overrideInternalProductConfig(navItem, product, links, opts)
		}

		switch navItem.Type {
		case navigationconfig.Container:
			item, err = links.GetContainer(navItem, lg, opts...)
		case navigationconfig.Link:
			item, err = links.GetLink(navItem, lg, opts...)
		case navigationconfig.Placeholder:
			return nil
		case navigationconfig.ExternalProduct:
			item, err = links.GetExternalProductLink(navItem, lg, opts...)
		case navigationconfig.CRMCustomObjectType:
			item, err = links.GetCRMCustomObjectLink(navItem, lg, opts...)
		default:
			err = verrors.New(verrors.Internal, "unknown item type: %s", navItem.Type)
		}

		if err != nil {
			accessErrors = append(accessErrors, verrors.WrapError(err, "Could not build %s %s", navItem.Type, navItem.NavItemID))
		}

		return item
	}

	protoItems := make([]*atlas_v1.SideNavigationItem, 0, len(args.NavConfig))
	for _, navItem := range args.NavConfig {
		opts := []fetchOption{}

		if navItem.NavItemID == "marketplace-products" {
			productConfigs := make([]navigationconfig.LinkConfig, 0, len(args.ProductNavConfigs))

			for _, product := range args.ProductNavConfigs {
				productConfigs = append(productConfigs, product.Items...)
			}

			navItem.Rules = &navigationconfig.SubstitutionRules{SubstituteNavConfig: &navigationconfig.NavigationConfig{
				Items: productConfigs,
			}}
		}

		items := fetchLinksForItem(navItem, lg, opts...)

		if items != nil {
			protoItems = append(protoItems, items...)
		}
	}

	dropDownItems := buildDropdownItems(args)

	return protoItems, dropDownItems, nil
}

type LinkGetter func(navigationconfig.LinkConfig, ...LinkOption) *atlas_v1.SideNavigationItem

type fetchOption func(id string) LinkOption

func fetchLinksForItem(c navigationconfig.LinkConfig, l LinkGetter, opts ...fetchOption) []*atlas_v1.SideNavigationItem {
	lr := make([]LinkOption, 0, len(opts))
	for _, opt := range opts {
		lr = append(lr, opt(c.NavItemID))
	}

	link := l(c, lr...)
	if link == nil {
		sub := c.Substitute()
		var links []*atlas_v1.SideNavigationItem
		if sub != nil {
			for _, item := range sub.Items {
				links = append(links, fetchLinksForItem(item, l, opts...)...)
			}
		}

		return links
	}

	return []*atlas_v1.SideNavigationItem{link}
}

func GetDefaultProductNavConfigBuilder(ps []*product.Product) navigationconfig.ConfigGeneratorFn {
	pm := make(map[string]*product.Product, len(ps))

	for _, p := range ps {
		pm[constants.InternalGetExternalProductConfigID(p.ServiceProviderID)] = p
	}

	return func(id string) (*navigationconfig.NavigationConfig, error) {
		_, ok := pm[id]
		if !ok {
			return nil, verrors.New(verrors.Internal, "unknown product: %s", id)
		}

		return &navigationconfig.NavigationConfig{
			ConfigID: id,
			Items: []navigationconfig.LinkConfig{
				{
					NavItemID: id,
					Type:      navigationconfig.ExternalProduct,
				},
			},
		}, nil
	}
}

func buildDropdownItems(args *GetNavigationItemsArgs) []*atlas_v1.DropdownItem {
	var dropDownItems []*atlas_v1.DropdownItem
	if args.IsImpersonating {
		dropDownItems = append(dropDownItems, buildStopImpersonatingItem(args.PartnerID))
	}
	dropDownItems = append(dropDownItems, &atlas_v1.DropdownItem{
		Url:           fmt.Sprintf("%s/logout/", args.WhitelabelURL),
		Path:          "/logout/",
		TranslationId: "NAVIGATION.MENU.SIGN_OUT",
		Label:         "Sign Out",
	})
	if args.SubjectID != "" {
		dropDownItems = append(buildSMBDropdownItems(args), dropDownItems...)
	}

	return dropDownItems
}

func buildSMBDropdownItems(args *GetNavigationItemsArgs) []*atlas_v1.DropdownItem {
	if args == nil {
		return nil
	}

	var items []*atlas_v1.DropdownItem

	if profileItem := buildEditProfileItem(args); profileItem != nil {
		items = append(items, profileItem)
	}

	if args.AccountGroupID != "" && args.ReferralLink != nil && args.ReferralLink.LongURL != "" {
		items = append(items, &atlas_v1.DropdownItem{
			Url:           fmt.Sprintf("%s/account/location/%s/invite", args.WhitelabelURL, args.AccountGroupID),
			Path:          fmt.Sprintf("/account/location/%s/invite", args.AccountGroupID),
			TranslationId: "NAVIGATION.MENU.SMB_INVITE",
			Label:         "Invite a Business",
		})
	}

	return items
}

func buildEditProfileItem(args *GetNavigationItemsArgs) *atlas_v1.DropdownItem {
	if !args.EditProfileEnabled {
		return nil
	}

	var buttonURL string
	label := "Edit Profile"
	buttonPath := fmt.Sprintf("/settings/user-profile/%s", args.SubjectID)
	if args.AccountGroupID != "" {
		buttonPath = fmt.Sprintf("/account/location/%s/settings/notifications/%s/settings", args.AccountGroupID, args.SubjectID)
	} else if args.GroupPath != "" {
		buttonPath = fmt.Sprintf("/account/brands/%s/settings/user-profile/%s", args.GroupPath, args.SubjectID)
	}

	buttonURL = fmt.Sprintf("%s%s", args.WhitelabelURL, buttonPath)

	return &atlas_v1.DropdownItem{
		Url:           buttonURL,
		Path:          buttonPath,
		TranslationId: "NAVIGATION.MENU.EDIT_PROFILE",
		Label:         label,
	}
}

func buildStopImpersonatingItem(partnerID string) *atlas_v1.DropdownItem {
	ssoGreyLabelDomain := getSsoGreyLabelDomain()

	return &atlas_v1.DropdownItem{
		Url:           fmt.Sprintf("%s/%s/stop-impersonation", ssoGreyLabelDomain, partnerID),
		Path:          "",
		TranslationId: "NAVIGATION.MENU.STOP_IMPERSONATING",
		Label:         "Stop Impersonating",
	}
}

func getSsoGreyLabelDomain() interface{} {
	if config.IsProd() {
		return "https://sso-api-prod.apigateway.co"
	}

	return "https://sso-api-demo.apigateway.co"
}
