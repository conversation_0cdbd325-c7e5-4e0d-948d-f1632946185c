package condition_test

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/vendasta/atlas/internal/navigation/condition"
	"github.com/vendasta/atlas/internal/navigation/internal"
)

func TestQuery(t *testing.T) {
	testCases := []struct {
		name          string
		query         condition.Query
		conditionData internal.ConditionData
		passes        bool
	}{
		{
			name: "empty query",
			query: condition.Query{
				From:   "",
				Select: nil,
			},
			conditionData: nil,
			passes:        false,
		},
		{
			name: "empty condition data",
			query: condition.Query{
				From:   "foo",
				Select: []string{"bar"},
			},
			conditionData: nil,
			passes:        false,
		},
		{
			name: "empty condition data map",
			query: condition.Query{
				From:   "foo",
				Select: []string{"bar"},
			},
			conditionData: internal.ConditionData{
				"": nil,
			},
			passes: false,
		},
		{
			name: "empty select",
			query: condition.Query{
				From:   "foo",
				Select: []string{},
			},
			conditionData: internal.ConditionData{
				"foo": nil,
			},
			passes: false,
		},
		{
			name: "empty from",
			query: condition.Query{
				From:   "",
				Select: []string{"bar"},
			},
			conditionData: internal.ConditionData{
				"foo": nil,
			},
			passes: false,
		},
		{
			name: "empty select and from",
			query: condition.Query{
				From:   "",
				Select: []string{},
			},
			conditionData: internal.ConditionData{
				"foo": nil,
			},
			passes: false,
		},
		{
			name: "select not in condition data",
			query: condition.Query{
				From:   "foo",
				Select: []string{"bar"},
			},
			conditionData: internal.ConditionData{
				"foo": nil,
			},
			passes: false,
		},
		{
			name: "select in condition data",
			query: condition.Query{
				From:   "foo",
				Select: []string{"bar"},
			},
			conditionData: internal.ConditionData{
				"foo": {
					"bar": {},
				},
			},
			passes: true,
		},
		{
			name: "select in condition data with multiple selects",
			query: condition.Query{
				From:   "foo",
				Select: []string{"bar", "baz"},
			},
			conditionData: internal.ConditionData{
				"foo": {
					"bar": {},
					"baz": {},
				},
			},
			passes: true,
		},
		{
			name: "select not in condition data with multiple selects",
			query: condition.Query{
				From:   "foo",
				Select: []string{"bar", "baz"},
			},
			conditionData: internal.ConditionData{
				"foo": {
					"bar": {},
				},
			},
			passes: false,
		},
		{
			name: "select in condition data with multiple selects and from",
			query: condition.Query{
				From:   "foo",
				Select: []string{"bar", "baz"},
			},
			conditionData: internal.ConditionData{
				"foo": {
					"bar": {},
					"baz": {},
				},
				"bar": {
					"foo": {},
				},
			},
			passes: true,
		},
		{
			name: "select not in condition data with multiple selects and from",
			query: condition.Query{
				From:   "foo",
				Select: []string{"bar", "baz"},
			},
			conditionData: internal.ConditionData{
				"foo": {
					"bar": {},
				},
				"bar": {
					"foo": {},
				},
			},
			passes: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.query.Evaluate(tc.conditionData, condition.EvaluationOptions{})
			if (result == nil) != tc.passes {
				t.Errorf("expected result: %t, got: %t", tc.passes, result == nil)
			}
		})
	}
}

func TestCondition_Evaluate_And(t *testing.T) {
	testCases := []struct {
		name          string
		condition     condition.Condition
		conditionData internal.ConditionData
		passes        bool
	}{
		{
			name: "empty condition",
			condition: condition.Condition{
				Operator: condition.And,
			},
			conditionData: nil,
			passes:        true,
		},
		{
			name: "empty condition data",
			condition: condition.Condition{
				Operator: condition.And,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar"},
					},
				},
			},
			conditionData: nil,
			passes:        false,
		},
		{
			name: "empty condition data map",
			condition: condition.Condition{
				Operator: condition.And,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar"},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo": {},
			},
			passes: false,
		},
		{
			name: "Returns true if the query is true",
			condition: condition.Condition{
				Operator: condition.And,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar"},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo": {
					"bar": {},
				},
			},
			passes: true,
		},
		{
			name: "Returns true if all of multiple queries are true",
			condition: condition.Condition{
				Operator: condition.And,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar", "baz"},
					},
					{
						From:   "other",
						Select: []string{"snack"},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo": {
					"bar": {},
					"baz": {},
				},
				"other": {
					"snack": {},
				},
			},
			passes: true,
		},
		{
			name: "Returns false if any of multiple queries are false",
			condition: condition.Condition{
				Operator: condition.And,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar", "baz"},
					},
					{
						From:   "other",
						Select: []string{"snack"},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo": {
					"bar": {},
					"baz": {},
				},
				"other": {},
			},
			passes: false,
		},
		{
			name: "Returns true if all of multiple conditions are true",
			condition: condition.Condition{
				Operator: condition.And,
				Conditions: []condition.Condition{
					{
						Operator: condition.And,
						Queries: []condition.Query{
							{
								From:   "foo",
								Select: []string{"bar", "baz"},
							},
						},
					},
					{
						Operator: condition.And,
						Queries: []condition.Query{
							{
								From:   "other",
								Select: []string{"snack"},
							},
						},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo": {
					"bar": {},
					"baz": {},
				},
				"other": {
					"snack": {},
				},
			},
			passes: true,
		},
		{
			name: "Returns false if any of multiple conditions are false",
			condition: condition.Condition{
				Operator: condition.And,
				Conditions: []condition.Condition{
					{
						Operator: condition.And,
						Queries: []condition.Query{
							{
								From:   "foo",
								Select: []string{"bar", "baz"},
							},
						},
					},
					{
						Operator: condition.And,
						Queries: []condition.Query{
							{
								From:   "other",
								Select: []string{"snack"},
							},
						},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo": {
					"bar": {},
					"baz": {},
				},
				"other": {},
			},
			passes: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.condition.Evaluate(tc.conditionData, condition.EvaluationOptions{})
			if (result == nil) != tc.passes {
				t.Errorf("Expected %v, got %v", tc.passes, result == nil)
			}
		})
	}
}

func TestCondition_Evaluate_Or(t *testing.T) {
	testCases := []struct {
		name          string
		condition     condition.Condition
		conditionData internal.ConditionData
		passes        bool
	}{
		{
			name: "empty condition",
			condition: condition.Condition{
				Operator: condition.Or,
			},
			conditionData: nil,
			passes:        true,
		},
		{
			name: "empty condition data",
			condition: condition.Condition{
				Operator: condition.Or,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar"},
					},
				},
			},
			conditionData: nil,
			passes:        false,
		},
		{
			name: "empty condition data map",
			condition: condition.Condition{
				Operator: condition.Or,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar"},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo": {},
			},
			passes: false,
		},
		{
			name: "Returns true if the query is true",
			condition: condition.Condition{
				Operator: condition.Or,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar"},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo": {
					"bar": {},
				},
			},
			passes: true,
		},
		{
			name: "Returns true if any of multiple queries are true",
			condition: condition.Condition{
				Operator: condition.Or,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar", "baz"},
					},
					{
						From:   "other",
						Select: []string{"snack"},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo": {
					"bar": {},
					"baz": {},
				},
				"other": {},
			},
			passes: true,
		},
		{
			name: "Returns false if all of multiple queries are false",
			condition: condition.Condition{
				Operator: condition.Or,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar", "baz"},
					},
					{
						From:   "other",
						Select: []string{"snack"},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo":   {},
				"other": {},
			},
			passes: false,
		},
		{
			name: "Returns true if any of multiple conditions are true",
			condition: condition.Condition{
				Operator: condition.Or,
				Conditions: []condition.Condition{
					{
						Operator: condition.Or,
						Queries: []condition.Query{
							{
								From:   "foo",
								Select: []string{"bar", "baz"},
							},
						},
					},
					{
						Operator: condition.Or,
						Queries: []condition.Query{
							{
								From:   "other",
								Select: []string{"snack"},
							},
						},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo": {},
				"other": {
					"snack": {},
				},
			},
			passes: true,
		},
		{
			name: "Returns false if all of multiple conditions are false",
			condition: condition.Condition{
				Operator: condition.Or,
				Conditions: []condition.Condition{
					{
						Operator: condition.Or,
						Queries: []condition.Query{
							{
								From:   "foo",
								Select: []string{"bar", "baz"},
							},
						},
					},
					{
						Operator: condition.Or,
						Queries: []condition.Query{
							{
								From:   "other",
								Select: []string{"snack"},
							},
						},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo":   {},
				"other": {},
			},
			passes: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.condition.Evaluate(tc.conditionData, condition.EvaluationOptions{})
			if (result == nil) != tc.passes {
				t.Errorf("Expected %v, got %v", tc.passes, result == nil)
			}
		})
	}
}

func TestCondition_Evaluate_Not(t *testing.T) {
	testCases := []struct {
		name          string
		condition     condition.Condition
		conditionData internal.ConditionData
		passes        bool
	}{
		{
			name: "empty condition",
			condition: condition.Condition{
				Operator: condition.Not,
			},
			conditionData: nil,
			passes:        true,
		},
		{
			name: "empty condition data",
			condition: condition.Condition{
				Operator: condition.Not,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar"},
					},
				},
			},
			conditionData: nil,
			passes:        true,
		},
		{
			name: "empty condition data map",
			condition: condition.Condition{
				Operator: condition.Not,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar"},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo": {},
			},
			passes: true,
		},
		{
			name: "Returns false if the query is true",
			condition: condition.Condition{
				Operator: condition.Not,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar"},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo": {
					"bar": {},
				},
			},
			passes: false,
		},
		{
			name: "Returns false if any of multiple queries are true",
			condition: condition.Condition{
				Operator: condition.Not,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar", "baz"},
					},
					{
						From:   "other",
						Select: []string{"snack"},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo": {
					"bar": {},
					"baz": {},
				},
				"other": {},
			},
			passes: false,
		},
		{
			name: "Returns true if all of multiple queries are false",
			condition: condition.Condition{
				Operator: condition.Not,
				Queries: []condition.Query{
					{
						From:   "foo",
						Select: []string{"bar", "baz"},
					},
					{
						From:   "other",
						Select: []string{"snack"},
					},
				},
			},
			conditionData: internal.ConditionData{
				"foo":   {},
				"other": {},
			},
			passes: true,
		},
		{
			"Returns false if any of multiple conditions are true",
			condition.Condition{
				Operator: condition.Not,
				Conditions: []condition.Condition{
					{
						Operator: condition.Or,
						Queries: []condition.Query{
							{
								From:   "foo",
								Select: []string{"bar", "baz"},
							},
						},
					},
					{
						Operator: condition.Or,
						Queries: []condition.Query{
							{
								From:   "other",
								Select: []string{"snack"},
							},
						},
					},
				},
			},
			internal.ConditionData{
				"foo": {},
				"other": {
					"snack": {},
				},
			},
			false,
		},
		{
			"Returns true if all of multiple conditions are false",
			condition.Condition{
				Operator: condition.Not,
				Conditions: []condition.Condition{
					{
						Operator: condition.Or,
						Queries: []condition.Query{
							{
								From:   "foo",
								Select: []string{"bar", "baz"},
							},
						},
					},
					{
						Operator: condition.Or,
						Queries: []condition.Query{
							{
								From:   "other",
								Select: []string{"snack"},
							},
						},
					},
				},
			},
			internal.ConditionData{
				"foo":   {},
				"other": {},
			},
			true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := tc.condition.Evaluate(tc.conditionData, condition.EvaluationOptions{})
			if (result == nil) != tc.passes {
				t.Errorf("Expected %v, got %v", tc.passes, result == nil)
			}
		})
	}
}

func TestConditionOptions_IgnoreResource(t *testing.T) {
	// test that if a resource is ignored, all queries to that resource are ignored
	// and the condition is considered to be true
	c := condition.Condition{
		Queries: []condition.Query{
			{
				From:   "foo",
				Select: []string{"bar"},
			},
		},
	}

	// no condition data means all queries will fail unless they are ignored
	conditionData := internal.ConditionData{}

	result := c.Evaluate(conditionData, condition.EvaluationOptions{
		IgnoreResources: []string{
			"foo",
		},
	})
	assert.NoError(t, result)
}
