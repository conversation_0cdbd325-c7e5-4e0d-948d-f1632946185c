package condition

import (
	"errors"
	"fmt"

	"github.com/vendasta/atlas/internal/navigation/internal"
)

type Operators = string

const (
	And Operators = "and"
	Or  Operators = "or"
	// for multiple conditions and queries this is a NAND
	Not Operators = "not"
)

// Conditions are evaluated to determine if a navigation item should be shown.
type Condition struct {
	// Operator is the operator used to evaluate the condition. If the operator is empty, the condition will be evaluated
	// using an AND operator.
	Operator Operators `json:"operator,omitempty"`
	// Queries are simple select queries applied against the condition data.
	Queries []Query `json:"queries,omitempty"`
	// Conditions are nested conditions.
	Conditions []Condition `json:"conditions,omitempty"`
}

func (c Condition) IsEmpty() bool {
	return len(c.Queries) == 0 && len(c.Conditions) == 0
}

func (c *Condition) IsEmptyOrNil() bool {
	return c == nil || c.IsEmpty()
}

type EvaluationOptions struct {
	// IgnoreResources is a list of resources that will pass regardless of policy conditions.
	IgnoreResources []string
}

func (c *Condition) Evaluate(conditionData internal.ConditionData, options EvaluationOptions) error {
	if c.IsEmptyOrNil() {
		return nil
	}

	switch c.Operator {
	case And:
		return c.evaluateAnd(conditionData, options)
	case Or:
		return c.evaluateOr(conditionData, options)
	case Not:
		return c.evaluateNot(conditionData, options)
	default:
		return c.evaluateAnd(conditionData, options)
	}
}

func (c Condition) evaluateAnd(conditionData internal.ConditionData, options EvaluationOptions) error {
	for _, query := range c.Queries {
		if err := query.Evaluate(conditionData, options); err != nil {
			return fmt.Errorf("and Condition: %s ", err)
		}
	}
	for _, condition := range c.Conditions {
		if err := condition.Evaluate(conditionData, options); err != nil {
			return fmt.Errorf("and Condition: %s ", err)
		}
	}
	return nil
}

func (c Condition) evaluateOr(conditionData internal.ConditionData, options EvaluationOptions) error {
	for _, query := range c.Queries {
		if err := query.Evaluate(conditionData, options); err == nil {
			return nil
		}
	}
	for _, condition := range c.Conditions {
		if err := condition.Evaluate(conditionData, options); err == nil {
			return nil
		}
	}
	return errors.New("or condition: all queries & conditions failed")
}

func (c Condition) evaluateNot(conditionData internal.ConditionData, options EvaluationOptions) error {
	for _, query := range c.Queries {
		if err := query.Evaluate(conditionData, options); err == nil {
			return fmt.Errorf("not condition: query succeeded for: %s.%v", query.From, query.Select)
		}
	}
	for _, condition := range c.Conditions {
		if err := condition.Evaluate(conditionData, options); err == nil {
			return fmt.Errorf("not condition: nested %s condition succeeded", condition.Operator)
		}
	}
	return nil
}

type Query struct {
	// Name of the conditiondata map we want to select from
	From internal.ConditionEntry `json:"from"`
	// Fields to select from the conditiondata map
	Select []string `json:"select"`
}

func (q Query) Evaluate(conditionData internal.ConditionData, options EvaluationOptions) error {
	if len(q.Select) == 0 {
		return errors.New("query: no fields to select")
	}
	if q.From == "" {
		return errors.New("query: no map to select from")
	}
	if conditionData == nil {
		return errors.New("query: no condition data")
	}

	// if the resource is in the ignore list, return nil
	for _, resource := range options.IgnoreResources {
		if resource == q.From {
			return nil
		}
	}

	if conditionData[q.From] == nil {
		return fmt.Errorf("query: no condition data for %s", q.From)
	}

	for _, field := range q.Select {
		if _, ok := conditionData[q.From][field]; !ok {
			return fmt.Errorf("query: no condition data for %s.%s", q.From, field)
		}
	}
	return nil
}
