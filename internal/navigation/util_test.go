package navigation

import (
	_ "embed"
	atlas_v1 "github.com/vendasta/generated-protos-go/atlas/v1"
	"testing"

	navigationconfig "github.com/vendasta/atlas/internal/navigation/config"
	"github.com/vendasta/atlas/internal/sidenavigation"
)

var allFeaturesEnabled = &sidenavigation.Features{
	EnabledFeatures: []string{
		"recommendations",
		"vbc-executive-report",
		"meeting_scheduler_business_app",
		"content-library",
		"guides",
		"local-marketing-index",
		"access-marketplace",
		"fulfillment",
		"bc_my_team_page",
		"orders",
		"invoices",
		"files",
		"my_products",
		"customer_list",
		"dashboard",
		"inbox",
	},
	ShowContentLibrary:    true,
	ShowLmiDashboard:      true,
	ShowExecutiveReport:   true,
	ShowStore:             true,
	ShowFulfillment:       true,
	ShowMarketplace:       true,
	ShowInviteTeam:        true,
	ShowInvoices:          true,
	ShowOrderPage:         true,
	ShowMeetingScheduler:  true,
	ShowFiles:             true,
	ShowMyProducts:        true,
	ShowCustomers:         true,
	ShowHome:              true,
	ShowInboxMessage:      true,
	HasBrandsEnabled:      true,
	HasProductMarketplace: true,
	HasSMPostPerformance:  true,
}

func TestNavItemGenerator_GetLink_UserRequired(t *testing.T) {
	tests := []struct {
		name string
		args *GetNavigationItemsArgs
		id   string
		want bool
	}{
		{
			name: "nav-files requires user when subject id is provided",
			args: &GetNavigationItemsArgs{
				SubjectID: "U-123",
				Features:  allFeaturesEnabled,
			},
			id: "nav-files",
		},
		{
			name: "nav-files requires user when subject id is not provided",
			args: &GetNavigationItemsArgs{
				Features: allFeaturesEnabled,
			},
			id:   "nav-files",
			want: true,
		},
		{
			name: "nav-notification-settings requires user when subject id is provided",
			args: &GetNavigationItemsArgs{
				SubjectID: "U-123",
				Features:  allFeaturesEnabled,
			},
			id: "nav-notification-settings",
		},
		{
			name: "nav-notification-settings requires user when subject id is not provided",
			args: &GetNavigationItemsArgs{
				Features: allFeaturesEnabled,
			},
			id:   "nav-notification-settings",
			want: true,
		},
		{
			name: "nav-billing-settings requires user when subject id is provided",
			args: &GetNavigationItemsArgs{
				SubjectID: "U-123",
				Features:  allFeaturesEnabled,
			},
			id: "nav-billing-settings",
		},
		{
			name: "nav-billing-settings requires user when subject id is not provided",
			args: &GetNavigationItemsArgs{
				Features: allFeaturesEnabled,
			},
			id:   "nav-billing-settings",
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			n, err := NewNavItemGenerator(tt.args)
			if err != nil {
				t.Fatalf("NavItemGenerator.GetLink() error = %v", err)
			}

			config := navigationconfig.LinkConfig{NavItemID: tt.id, Type: navigationconfig.Link}
			var lg LinkGetter
			lg = func(navItem navigationconfig.LinkConfig, opts ...LinkOption) *atlas_v1.SideNavigationItem {
				item, _ := n.GetLink(navItem, lg, opts...)
				return item
			}
			if got, _ := n.GetLink(config, lg); got.GetSideNavigationLink().GetUserRequired() != tt.want {
				t.Errorf("NavItemGenerator.GetLink() = %v, want %v", got.GetSideNavigationLink().GetUserRequired(), tt.want)
			}
		})
	}
}
