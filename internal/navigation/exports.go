package navigation

import "github.com/vendasta/atlas/internal/navigation/internal/constants"

// GetExternalProductConfigID is exported to support the current implementation, but will be unexported in the future.
var GetExternalProductConfigID = constants.InternalGetExternalProductConfigID

// These constants are exported to support the current implementation, but will be unexported in the future.
const PartnerFeatureIDBusinessEmails = constants.PartnerFeatureIDBusinessEmails
const PartnerFeatureIDMeetingScheduler = constants.PartnerFeatureIDMeetingScheduler
const PartnerFeatureIDSingleNav = constants.PartnerFeatureIDSingleNav
const PartnerFeatureIDCRMOpportunity = constants.PartnerFeatureIDCRMOpportunity
const PartnerFeatureIDDynamicLists = constants.PartnerFeatureIDDynamicLists
const PartnerFeatureIDSalesFeatures = constants.PartnerFeatureIDSalesFeatures
const PartnerFeatureIDEmbeddedListings = constants.PartnerFeatureIDEmbeddedListings
const PartnerFeatureIDEmbeddedSocialMarketing = constants.PartnerFeatureIDEmbeddedSocialMarketing
const PartnerFeatureIDEmbeddedAdIntel = constants.PartnerFeatureIDEmbeddedAdIntel
const PartnerFeatureIDCampaignsSMS = constants.CampaignsSMS
const PartnerFeatureIDAIAssistants = constants.AIAssistants
const PartnerFeatureIDRouteToNewIntegrations = constants.RouteToNewIntegrations
const PartnerFeatureSMBPayments = constants.SMBPayments
const PartnerFeatureIDRepmanNPS = constants.NPSFeatureIDReputation
const PartnerFeatureIDRepmanNewMLDesign = constants.NPSNewMLDesign
const PartnerFeatureIDCRMMultilocation = constants.CRMMultilocation
const PartnerKeywordTrackingOverviewFeature = constants.KeywordTrackingOverviewFeature
const PlatformModeMobile = constants.MobileMode
const PlatformModeWeb = constants.WebMode
const CRMCustomObjectsFeatureID = constants.CRMCustomObjectsFeatureID
const PartnerFeatureIDLisAnalytics = constants.PartnerFeatureIDLisAnalytics
const PartnerFeatureIDRepmanBusinessAppNPS = constants.NPSBusinessAppFeatureIDReputation
