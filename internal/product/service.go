package product

import (
	"context"
	"fmt"
	"net/url"
	"strings"
	"time"

	accounts "github.com/vendasta/accounts/sdks/go/v1"
	"github.com/vendasta/sso/sdks/go/sso/getmultientryurl"

	"golang.org/x/sync/errgroup"

	marketplaceapps_v2 "github.com/vendasta/generated-protos-go/marketplace_apps/v2"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/verrors"
	marketplaceapps "github.com/vendasta/marketplace-apps/sdks/go/v2"
	"github.com/vendasta/sso/sdks/go/sso"
)

// Service handles getting white labeled product data
type Service struct {
	sso         sso.IdentityProviderClient
	accounts    accounts.Interface
	marketplace marketplaceapps.PartnerClientInterface
}

// NewService returns a new product service
func NewService(ssoClient sso.IdentityProviderClient, accountClient accounts.Interface, marketplaceClient marketplaceapps.PartnerClientInterface) *Service {
	return &Service{
		sso:         ssoClient,
		accounts:    accountClient,
		marketplace: marketplaceClient,
	}
}

// GetProducts returns a list of products
func (s *Service) GetProducts(ctx context.Context, partnerID string, marketID string, accountGroupID string, serviceProviderIDs []string, impersonateeUserID string) ([]*Product, error) {
	if len(serviceProviderIDs) == 0 {
		return nil, nil
	}

	eg, eCtx := errgroup.WithContext(ctx)
	var entryURLs []string
	eg.Go(func() error {
		var opts []getmultientryurl.Option
		if impersonateeUserID != "" {
			opts = []getmultientryurl.Option{getmultientryurl.Impersonatee(impersonateeUserID)}
		}
		var err error
		entryURLs, err = s.sso.GetMultiEntryURL(eCtx, serviceProviderIDs, &sso.AccountContext{AccountID: accountGroupID}, opts...)
		if err != nil {
			logging.Errorf(eCtx, "error calling sso get multi entry url %v", err)
		}
		return err
	})
	var activeProducts []*accounts.Account
	eg.Go(func() error {
		var err error
		activeProducts, err = s.accounts.List(eCtx, accountGroupID, partnerID)
		if err != nil {
			logging.Errorf(eCtx, "error listing accounts: %v", err)
		}
		return err
	})
	apps := make([]*marketplaceapps_v2.App, len(serviceProviderIDs))
	eg.Go(func() error {
		var err error
		appKeys := make([]*marketplaceapps_v2.AppKey, len(serviceProviderIDs))
		for i, id := range serviceProviderIDs {
			appKeys[i] = &marketplaceapps_v2.AppKey{
				AppId:     id,
				EditionId: "",
			}
		}
		resp, err := s.marketplace.GetMultiApp(eCtx, &marketplaceapps_v2.GetMultiAppRequest{
			AppKeys:           appKeys,
			PartnerId:         partnerID,
			MarketId:          marketID,
			IncludeNotEnabled: false,
		})
		if err != nil {
			logging.Errorf(eCtx, "error getting multi app: %v", err)
			return err
		}
		apps = resp.Apps
		return nil
	})
	if err := eg.Wait(); err != nil {
		logging.Errorf(ctx, "error getting products data: %v", err)
		return nil, err
	}
	return buildProducts(ctx, entryURLs, activeProducts, apps, accountGroupID)
}

func buildProducts(ctx context.Context, entryURLs []string, activeProducts []*accounts.Account, apps []*marketplaceapps_v2.App, accountGroupID string) ([]*Product, error) {
	if len(entryURLs) != len(apps) {
		return nil, verrors.New(verrors.Internal, "length mismatch")
	}
	appIDToActiveProductMapping := make(map[string]*accounts.Account)
	for _, activeProduct := range activeProducts {
		if activeProduct == nil {
			continue
		}
		productID := activeProduct.MarketplaceAppID
		appIDToActiveProductMapping[productID] = activeProduct
	}
	products := make([]*Product, len(apps))
	for i, a := range apps {
		if a == nil || a.GetKey() == nil {
			continue
		}
		appID := a.GetKey().GetAppId()
		// if the app exists within the manually set inaccessible list, skip it
		if _, ok := inaccessibleProduct[appID]; ok {
			continue
		}
		// if the app is hidden from the client dashboard, skip it
		if a.GetBasicIntegration().GetHideFromClientDashboard() {
			continue
		}
		u, err := url.Parse(entryURLs[i])
		if err != nil {
			return nil, err
		}
		host := ""
		if u.Scheme != "" && u.Host != "" {
			host = fmt.Sprintf("%s://%s", u.Scheme, u.Host)
		}

		url := entryURLs[i]
		// if no entryURL, check the appURL
		if url == "" {
			url = a.GetBasicIntegration().GetApplicationUrl()
		}

		isExpiredTrial := false
		if activeProductForApp, ok := appIDToActiveProductMapping[appID]; ok {
			// if no appURL, search for a customURL on the active app
			if url == "" {
				url = activeProductForApp.CustomEntryURL
			}
			isExpiredTrial = activeProductForApp.IsTrial() && activeProductForApp.Expiry.Before(time.Now().UTC())
		}
		// Non-SSO links need their params replaced by atlas or the frontend
		url = strings.ReplaceAll(url, "<accountId>", accountGroupID)

		products[i] = &Product{
			ServiceProviderID: appID,
			Name:              a.GetSharedMarketingInformation().GetName(),
			EntryURL:          url,
			Path:              u.Path,
			Host:              host,
			LogoURL:           a.GetSharedMarketingInformation().GetIconUrl(),
			RequiresUser:      productRequiresUser(appID),
			IsExpiredTrial:    isExpiredTrial,
		}
	}
	return products, nil
}

func productRequiresUser(appID string) bool {
	_, ok := userRequiredProduct[strings.ToUpper(appID)]
	return ok
}

var inaccessibleProduct = map[string]struct{}{
	// gsuite apps
	"MP-6XDHVMQ84K4THNNP2Z7W2GC28VLHRC4Q": {},
	"MP-XBHPSLDBHZ8Q8F57P43DPKL6SPHKHHMS": {},
	"MP-GNWSMJS4BJB4Z8RZWZ8SXW6J4HP32KJ3": {},
	"MP-********************************": {},
	"MP-7TMH5K8N7KNNRZ5NC4RNNS2JFK64HMNC": {},
	"MP-LMVNT4HKZCXTVDFMVT62Q6VB3ZJGNQC6": {},
	// concierge
	"MP-R7G3NP55T725DM5VGDBHTWVT8L73MKXN": {},
	"MP-QQF46WQ8W4TXTD8CBX8P6X7XBV4KBNW4": {},
	// ms-office apps
	"MP-GXWJBHH6PQ5HDCDXFQ2ZP4D276JCJLKH": {},
	"MP-MWTXLV4WDG2QCHSF2DR4JRSC4WK32DDG": {},
}

// List of third party legacy sso apps that require impersonation
var userRequiredProduct = map[string]struct{}{
	"MP-B77R7GG4QHGKF5RMVSLVZPD3D5JSTZ4K": {},
	"MP-QCF8BN6ZLCF8VG44ZBFKK4WC8ZLHMPGB": {},
	"MP-MLR5D46VS7LD435B3GJCQTBD5DKRXC6H": {},
	"MP-MLXNLFH72ZZKHTLDKQ784PCZPJPTPV63": {},
	"MP-GB6TPVDDJVK3CK7MQW86XG75TMRCXNJK": {},
	"MP-DWZ34CZNP45MK4N4X4GH76QQF448P3TD": {},
	"MP-SS2FMDDSML3TG3BGFVDVT8ZTF7TDZ8SF": {},
	"MP-2da7414a9f604f518d6ae0127080f9e2": {},
	"MP-11b0a570be284547a9ea1ad6fc26a261": {},
	"MP-LHNDH2N6X5H6JHMWRQ4P7D6WNWRRSCH5": {},
	"MP-8RQ7CTD5LBDFF2V2HQG3JF26N48PSNGR": {},
	"MP-7KCQR6C2M2LBSXQXLPM3GG3FHQLXSBMF": {},
	"MP-W74STW72G4WVKL52LT45NFJC3XH7M5J2": {},
	"MP-aa5151cf1bb840819dbdf85a55e717bf": {},
	"MP-CM77Q8QDQVD2FL5X6J5CG5M4QQBC6ZTB": {},
	"MP-3QXRWPFX4BTVGZ3NBPSWPS8L3S7QS66W": {},
	"MP-5H2HL8GGBZKMX8XNHSKT5SRH8Q2L3ZCN": {},
	"MP-FVGG6TS6J5V5ZJQPS7GGP25838QSXCVD": {},
	"MP-f72c489e967649b082a84f42760eaa1b": {},
	"MP-SGBV7TJ5TZN557NFCGPXBSTHC5STM784": {},
	"MP-L3R7WQ5QGQ8H5WWFX6NXSSNXJMKNPLDG": {},
	"MP-F6BPK4L5B5CLCXHWLF84MJQHQX63W8X5": {},
	"MP-8B4M6FR6MZLQSRNW37CHQ4QSFX2PG5XR": {},
	"MP-JSLWMC6KWSVCBFNC5J846W5CK4K4FRWP": {},
	"MP-7P6HQ6FV3W767MH8BHXN2TT458QNM4WZ": {},
	"MP-RC8VV8K5DV368F6WJ3ZCSKWG2Q62QSDC": {},
	"MP-LMMGS3SJ4H6RHS6RVZTCJP43KZRLQWQR": {},
	"MP-JFW4X5KMBK44GT4SBDZL72KVP2DPZQZS": {},
	"MP-G222MCLJMR86JCFZWDDVZDXZ7NXZWNKR": {},
	"MP-HPSWQNKQRPF6STQDV6FMFPXHHXJ58XLN": {},
	"MP-647TKQGPVKXF5CHQCPZ4RK78CFTWHWFH": {},
	"MP-852C4QSSTZH3Z3BF4NFVKDN2KVCP278D": {},
	"MP-03fc7fb5e3b848cd88523515cd66ea9c": {},
	"MP-492e1467b897434aa528bd8e74dcde0d": {},
	"MP-XBRSXQSPSC4H4TSGMMMKHR3GQZMHL2LH": {},
	"MP-NFWJLTZ2CN55VMRNMHQ46PNSG66M287J": {},
	"MP-T8ZDKPPX5RH2QZS6J6TRVWQ7FNW8G24D": {},
	"MP-HZBQQCFV6WMVFB2JNMFFWN5658L23LRP": {},
	"MP-NT6JS5PC4S2RNLQXNDVJDCD55TBMQHDQ": {},
	"MP-VCMRKHPF7QQJGDR6PHN56VSXHDNKK52R": {},
	"MP-6TZ5WKDL8ZR5GBHX7QPQ7DTRSQDQBCN7": {},
	"MP-VN8Z4RDGHJBJM5VCFQS5TZXPC4B5MBD5": {},
}
