package product

import (
	"context"
	"testing"
	"time"

	accounts "github.com/vendasta/accounts/sdks/go/v1"

	"github.com/stretchr/testify/assert"
	marketplaceapps_v2 "github.com/vendasta/generated-protos-go/marketplace_apps/v2"
)

func TestBuildProducts(t *testing.T) {
	tests := []struct {
		name                     string
		entryUrls                []string
		activeAccountInformation []*accounts.Account
		apps                     []*marketplaceapps_v2.App
		expected                 []*Product
		expectedError            bool
	}{
		{
			name:                     "mismatch lengths errs",
			entryUrls:                []string{"https://app1.com/entry", "https://app2.com/entry"},
			activeAccountInformation: []*accounts.Account{nil, nil},
			apps:                     []*marketplaceapps_v2.App{},
			expected:                 nil,
			expectedError:            true,
		},
		{
			name:                     "builds valid apps/entry",
			entryUrls:                []string{"https://site1.com/entry"},
			activeAccountInformation: []*accounts.Account{nil},
			apps: []*marketplaceapps_v2.App{
				{
					Key: &marketplaceapps_v2.AppKey{
						AppId: "MP-123",
					},
					SharedMarketingInformation: &marketplaceapps_v2.SharedMarketingInformation{
						Name:    "app1",
						IconUrl: "https://iconstore.com/icon.png",
					},
				},
			},
			expected: []*Product{
				&Product{
					ServiceProviderID: "MP-123",
					Name:              "app1",
					EntryURL:          "https://site1.com/entry",
					LogoURL:           "https://iconstore.com/icon.png",
					Path:              "/entry",
					Host:              "https://site1.com",
					RequiresUser:      false,
				},
			},
			expectedError: false,
		},
		{
			name:                     "builds valid multiple apps/entry",
			entryUrls:                []string{"https://site1.com/entry", "https://site2.com/entry"},
			activeAccountInformation: []*accounts.Account{nil, nil},
			apps: []*marketplaceapps_v2.App{
				{
					Key: &marketplaceapps_v2.AppKey{
						AppId: "MP-123",
					},
					SharedMarketingInformation: &marketplaceapps_v2.SharedMarketingInformation{
						Name:    "app1",
						IconUrl: "https://iconstore.com/icon.png",
					},
				}, {
					Key: &marketplaceapps_v2.AppKey{
						AppId: "MP-456",
					},
					SharedMarketingInformation: &marketplaceapps_v2.SharedMarketingInformation{
						Name:    "app2",
						IconUrl: "https://iconstore.com/icon2.png",
					},
				},
			},
			expected: []*Product{
				&Product{
					ServiceProviderID: "MP-123",
					Name:              "app1",
					EntryURL:          "https://site1.com/entry",
					LogoURL:           "https://iconstore.com/icon.png",
					Path:              "/entry",
					Host:              "https://site1.com",
					RequiresUser:      false,
				},
				&Product{
					ServiceProviderID: "MP-456",
					Name:              "app2",
					EntryURL:          "https://site2.com/entry",
					LogoURL:           "https://iconstore.com/icon2.png",
					Path:              "/entry",
					Host:              "https://site2.com",
					RequiresUser:      false,
				},
			},
			expectedError: false,
		},
		{
			name:                     "handles nil apps",
			entryUrls:                []string{"https://site1.com/entry", "https://site2.com/entry"},
			activeAccountInformation: []*accounts.Account{nil, nil},
			apps: []*marketplaceapps_v2.App{
				nil,
				{
					Key: &marketplaceapps_v2.AppKey{
						AppId: "MP-456",
					},
					SharedMarketingInformation: &marketplaceapps_v2.SharedMarketingInformation{
						Name:    "app2",
						IconUrl: "https://iconstore.com/icon2.png",
					},
				},
			},
			expected: []*Product{
				nil,
				&Product{
					ServiceProviderID: "MP-456",
					Name:              "app2",
					EntryURL:          "https://site2.com/entry",
					Path:              "/entry",
					LogoURL:           "https://iconstore.com/icon2.png",
					Host:              "https://site2.com",
					RequiresUser:      false,
				},
			},
			expectedError: false,
		},
		{
			name:                     "handles empty entry urls",
			entryUrls:                []string{"https://site1.com/entry", ""},
			activeAccountInformation: []*accounts.Account{nil, nil},
			apps: []*marketplaceapps_v2.App{
				{
					Key: &marketplaceapps_v2.AppKey{
						AppId: "MP-123",
					},
					SharedMarketingInformation: &marketplaceapps_v2.SharedMarketingInformation{
						Name:    "app1",
						IconUrl: "https://iconstore.com/icon.png",
					},
				}, {
					Key: &marketplaceapps_v2.AppKey{
						AppId: "MP-456",
					},
					SharedMarketingInformation: &marketplaceapps_v2.SharedMarketingInformation{
						Name:    "app2",
						IconUrl: "https://iconstore.com/icon2.png",
					},
				},
			},
			expected: []*Product{
				&Product{
					ServiceProviderID: "MP-123",
					Name:              "app1",
					EntryURL:          "https://site1.com/entry",
					Path:              "/entry",
					LogoURL:           "https://iconstore.com/icon.png",
					Host:              "https://site1.com",
					RequiresUser:      false,
				},
				&Product{
					ServiceProviderID: "MP-456",
					Name:              "app2",
					EntryURL:          "",
					Path:              "",
					LogoURL:           "https://iconstore.com/icon2.png",
					Host:              "",
					RequiresUser:      false,
				},
			},
			expectedError: false,
		},
		{
			name:                     "allows admin access",
			entryUrls:                []string{"https://site1.com/entry"},
			activeAccountInformation: []*accounts.Account{nil},
			apps: []*marketplaceapps_v2.App{
				{
					Key: &marketplaceapps_v2.AppKey{
						AppId: "RM",
					},
					SharedMarketingInformation: &marketplaceapps_v2.SharedMarketingInformation{
						Name:    "app1",
						IconUrl: "https://iconstore.com/icon.png",
					},
				},
			},
			expected: []*Product{
				&Product{
					ServiceProviderID: "RM",
					Name:              "app1",
					EntryURL:          "https://site1.com/entry",
					LogoURL:           "https://iconstore.com/icon.png",
					Path:              "/entry",
					Host:              "https://site1.com",
					RequiresUser:      false,
				},
			},
			expectedError: false,
		},
		{
			name:                     "prevents admin access",
			entryUrls:                []string{"https://site1.com/entry"},
			activeAccountInformation: []*accounts.Account{nil},
			apps: []*marketplaceapps_v2.App{
				{
					Key: &marketplaceapps_v2.AppKey{
						AppId: "MP-VCMRKHPF7QQJGDR6PHN56VSXHDNKK52R",
					},
					SharedMarketingInformation: &marketplaceapps_v2.SharedMarketingInformation{
						Name:    "app1",
						IconUrl: "https://iconstore.com/icon.png",
					},
				},
			},
			expected: []*Product{
				&Product{
					ServiceProviderID: "MP-VCMRKHPF7QQJGDR6PHN56VSXHDNKK52R",
					Name:              "app1",
					EntryURL:          "https://site1.com/entry",
					LogoURL:           "https://iconstore.com/icon.png",
					Path:              "/entry",
					Host:              "https://site1.com",
					RequiresUser:      true,
				},
			},
			expectedError: false,
		},
		{
			name:                     "handles public entry urls",
			entryUrls:                []string{"https://site1.com/entry", ""},
			activeAccountInformation: []*accounts.Account{nil, nil},
			apps: []*marketplaceapps_v2.App{
				{
					Key: &marketplaceapps_v2.AppKey{
						AppId: "MP-123",
					},
					SharedMarketingInformation: &marketplaceapps_v2.SharedMarketingInformation{
						Name:    "app1",
						IconUrl: "https://iconstore.com/icon.png",
					},
				}, {
					Key: &marketplaceapps_v2.AppKey{
						AppId: "MP-456",
					},
					SharedMarketingInformation: &marketplaceapps_v2.SharedMarketingInformation{
						Name:    "app2",
						IconUrl: "https://iconstore.com/icon2.png",
					},
					BasicIntegration: &marketplaceapps_v2.BasicIntegration{
						ApplicationUrl: "https://site2.com/entry/<accountId>",
					},
				},
			},
			expected: []*Product{
				&Product{
					ServiceProviderID: "MP-123",
					Name:              "app1",
					EntryURL:          "https://site1.com/entry",
					Path:              "/entry",
					LogoURL:           "https://iconstore.com/icon.png",
					Host:              "https://site1.com",
					RequiresUser:      false,
				},
				&Product{
					ServiceProviderID: "MP-456",
					Name:              "app2",
					EntryURL:          "https://site2.com/entry/AG-1234567",
					Path:              "",
					LogoURL:           "https://iconstore.com/icon2.png",
					Host:              "",
					RequiresUser:      false,
				},
			},
			expectedError: false,
		},
		{
			name:      "Use activation specific Entry URL when no Entry URL",
			entryUrls: []string{""},
			activeAccountInformation: []*accounts.Account{
				&accounts.Account{
					AccountID:             "",
					BusinessID:            "",
					PartnerID:             "",
					ActivationID:          "",
					ProductID:             "",
					MarketplaceAppID:      "MP-123",
					OrderFormSubmissionID: "",
					BillingOrderID:        "",
					EditionID:             "",
					CustomEntryURL:        "www.customentry.test",
					Expiry:                time.Time{},
					Tags:                  nil,
					AnniversaryDate:       time.Time{},
					Status:                0,
					Activation:            time.Time{},
				},
			},
			apps: []*marketplaceapps_v2.App{
				{
					Key: &marketplaceapps_v2.AppKey{
						AppId: "MP-123",
					},
					SharedMarketingInformation: &marketplaceapps_v2.SharedMarketingInformation{
						Name:    "app1",
						IconUrl: "https://iconstore.com/icon.png",
					},
				},
			},
			expected: []*Product{
				&Product{
					ServiceProviderID: "MP-123",
					Name:              "app1",
					EntryURL:          "www.customentry.test",
					LogoURL:           "https://iconstore.com/icon.png",
					Path:              "",
					Host:              "",
					RequiresUser:      false,
				},
			},
			expectedError: false,
		},
		{
			name:      "Marks the Product as being an expired trial when the Account response object has it marked as a trial with an expiry in the past",
			entryUrls: []string{""},
			activeAccountInformation: []*accounts.Account{
				&accounts.Account{
					AccountID:             "",
					BusinessID:            "",
					PartnerID:             "",
					ActivationID:          "",
					ProductID:             "",
					MarketplaceAppID:      "MP-123",
					OrderFormSubmissionID: "",
					BillingOrderID:        "",
					EditionID:             "",
					CustomEntryURL:        "www.customentry.test",
					Tags:                  []string{"trial"},
					Expiry:                time.Date(2022, 1, 1, 1, 1, 1, 1, time.UTC),
					AnniversaryDate:       time.Time{},
					Status:                0,
					Activation:            time.Time{},
				},
			},
			apps: []*marketplaceapps_v2.App{
				{
					Key: &marketplaceapps_v2.AppKey{
						AppId: "MP-123",
					},
					SharedMarketingInformation: &marketplaceapps_v2.SharedMarketingInformation{
						Name:    "app1",
						IconUrl: "https://iconstore.com/icon.png",
					},
				},
			},
			expected: []*Product{
				&Product{
					ServiceProviderID: "MP-123",
					Name:              "app1",
					EntryURL:          "www.customentry.test",
					LogoURL:           "https://iconstore.com/icon.png",
					Path:              "",
					Host:              "",
					RequiresUser:      false,
					IsExpiredTrial:    true,
				},
			},
			expectedError: false,
		},
		{
			name:      "Marks the Product as not being an expired trial when the Account response object has it marked as a trial but the expiry date is in the future",
			entryUrls: []string{""},
			activeAccountInformation: []*accounts.Account{
				&accounts.Account{
					AccountID:             "",
					BusinessID:            "",
					PartnerID:             "",
					ActivationID:          "",
					ProductID:             "",
					MarketplaceAppID:      "MP-123",
					OrderFormSubmissionID: "",
					BillingOrderID:        "",
					EditionID:             "",
					CustomEntryURL:        "www.customentry.test",
					Tags:                  []string{"trial"},
					Expiry:                time.Now().Add(time.Hour * 24),
					AnniversaryDate:       time.Time{},
					Status:                0,
					Activation:            time.Time{},
				},
			},
			apps: []*marketplaceapps_v2.App{
				{
					Key: &marketplaceapps_v2.AppKey{
						AppId: "MP-123",
					},
					SharedMarketingInformation: &marketplaceapps_v2.SharedMarketingInformation{
						Name:    "app1",
						IconUrl: "https://iconstore.com/icon.png",
					},
				},
			},
			expected: []*Product{
				&Product{
					ServiceProviderID: "MP-123",
					Name:              "app1",
					EntryURL:          "www.customentry.test",
					LogoURL:           "https://iconstore.com/icon.png",
					Path:              "",
					Host:              "",
					RequiresUser:      false,
					IsExpiredTrial:    false,
				},
			},
			expectedError: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual, err := buildProducts(context.Background(), tt.entryUrls, tt.activeAccountInformation, tt.apps, "AG-1234567")
			assert.Equal(t, tt.expected, actual)
			if err != nil && !tt.expectedError {
				t.Errorf("did not expect an error, received: %v", err)
			} else if err == nil && tt.expectedError {
				t.Errorf("did not expect an error, received: %v", err)
			}
		})
	}
}
