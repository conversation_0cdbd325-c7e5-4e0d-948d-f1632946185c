package featureflags

import (
	"context"
	"testing"

	gomock "github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	aiAssistants "github.com/vendasta/generated-protos-go/ai_assistants/v1alpha1"
)

func TestGetFeaturesStatus(t *testing.T) {
	cases := []struct {
		name           string
		partnerID      string
		marketID       string
		accountGroupID string
		featureFlags   []string
		expected       map[string]bool
		expectedError  error
		setupClient    func(ctrl *gomock.Controller) *FeatureAccess
	}{
		{
			name:      "missing partner ID returns nil error and map",
			partnerID: "",
			setupClient: func(ctrl *gomock.Controller) *FeatureAccess {
				mockGoalServiceClient := NewMockGoalServiceClient(ctrl)
				return New(&stubFeatureFlagClient{
					statuses: []bool{true, false},
					err:      nil,
				}, mockGoalServiceClient)
			},
		},
		{
			name:          "list length mismatch",
			partnerID:     "ABC",
			marketID:      "",
			featureFlags:  []string{"feature1", "feature2", "feature3"},
			expected:      nil,
			expectedError: err<PERSON><PERSON>th<PERSON><PERSON>atch,
			setupClient: func(ctrl *gomock.Controller) *FeatureAccess {
				mockGoalServiceClient := NewMockGoalServiceClient(ctrl)
				return New(&stubFeatureFlagClient{
					statuses: []bool{true, false},
					err:      nil,
				}, mockGoalServiceClient)
			},
		},
		{
			name:           "builds map correctly -- with agid, goals enabled",
			partnerID:      "ABC",
			marketID:       "",
			accountGroupID: "AG-123",
			featureFlags:   []string{"feature1", "feature2", "feature3"},
			expected: map[string]bool{
				"feature1":            true,
				"feature2":            false,
				"feature3":            false,
				"group_feature_goals": true,
			},
			expectedError: nil,
			setupClient: func(ctrl *gomock.Controller) *FeatureAccess {
				mockGoalServiceClient := NewMockGoalServiceClient(ctrl)
				mockGoalServiceClient.EXPECT().GoalsDisabledForAccountGroup(gomock.Any(), gomock.Any()).Return(&aiAssistants.GoalsDisabledForAccountGroupResponse{
					GoalsDisabled: false,
				}, nil)
				return New(&stubFeatureFlagClient{
					statuses: []bool{true, false, false},
					err:      nil,
				}, mockGoalServiceClient)
			},
		},
		{
			name:           "builds map correctly -- with agid, goals disabled",
			partnerID:      "ABC",
			marketID:       "",
			accountGroupID: "AG-123",
			featureFlags:   []string{"feature1", "feature2", "feature3"},
			expected: map[string]bool{
				"feature1":            true,
				"feature2":            false,
				"feature3":            false,
				"group_feature_goals": false,
			},
			expectedError: nil,
			setupClient: func(ctrl *gomock.Controller) *FeatureAccess {
				mockGoalServiceClient := NewMockGoalServiceClient(ctrl)
				mockGoalServiceClient.EXPECT().GoalsDisabledForAccountGroup(gomock.Any(), gomock.Any()).Return(&aiAssistants.GoalsDisabledForAccountGroupResponse{
					GoalsDisabled: true,
				}, nil)
				return New(&stubFeatureFlagClient{
					statuses: []bool{true, false, false},
					err:      nil,
				}, mockGoalServiceClient)
			},
		},
		{
			name:         "builds map correctly -- no agid",
			partnerID:    "ABC",
			marketID:     "",
			featureFlags: []string{"feature1", "feature2", "feature3"},
			expected: map[string]bool{
				"feature1":            true,
				"feature2":            false,
				"feature3":            false,
				"group_feature_goals": true,
			},
			expectedError: nil,
			setupClient: func(ctrl *gomock.Controller) *FeatureAccess {
				return New(&stubFeatureFlagClient{
					statuses: []bool{true, false, false},
					err:      nil,
				}, nil)
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			client := c.setupClient(ctrl)
			actual, err := client.GetFeaturesStatus(context.Background(), c.partnerID, c.marketID, c.featureFlags, c.accountGroupID)
			assert.Equal(t, c.expected, actual)
			assert.Equal(t, c.expectedError, err)
		})
	}
}

type stubFeatureFlagClient struct {
	statuses []bool
	err      error
}

func (f *stubFeatureFlagClient) BatchGetStatus(ctx context.Context, partnerID, marketID string, featureIDs []string) ([]bool, error) {
	return f.statuses, f.err
}
