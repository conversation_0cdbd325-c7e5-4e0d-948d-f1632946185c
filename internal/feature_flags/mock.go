// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go
//
// Generated by this command:
//
//	mockgen -destination mock.go -source=interface.go -package=featureflags
//

// Package featureflags is a generated GoMock package.
package featureflags

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	v1alpha1 "github.com/vendasta/generated-protos-go/ai_assistants/v1alpha1"
	grpc "google.golang.org/grpc"
)

// MockFeatures is a mock of Features interface.
type MockFeatures struct {
	ctrl     *gomock.Controller
	recorder *MockFeaturesMockRecorder
	isgomock struct{}
}

// MockFeaturesMockRecorder is the mock recorder for MockFeatures.
type MockFeaturesMockRecorder struct {
	mock *MockFeatures
}

// NewMockFeatures creates a new mock instance.
func NewMockFeatures(ctrl *gomock.Controller) *MockFeatures {
	mock := &MockFeatures{ctrl: ctrl}
	mock.recorder = &MockFeaturesMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFeatures) EXPECT() *MockFeaturesMockRecorder {
	return m.recorder
}

// GetFeaturesStatus mocks base method.
func (m *MockFeatures) GetFeaturesStatus(ctx context.Context, partnerID, marketID string, featureFlags []string, accountGroupID string) (map[string]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFeaturesStatus", ctx, partnerID, marketID, featureFlags, accountGroupID)
	ret0, _ := ret[0].(map[string]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFeaturesStatus indicates an expected call of GetFeaturesStatus.
func (mr *MockFeaturesMockRecorder) GetFeaturesStatus(ctx, partnerID, marketID, featureFlags, accountGroupID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFeaturesStatus", reflect.TypeOf((*MockFeatures)(nil).GetFeaturesStatus), ctx, partnerID, marketID, featureFlags, accountGroupID)
}

// MockGoalServiceClient is a mock of GoalServiceClient interface.
type MockGoalServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockGoalServiceClientMockRecorder
	isgomock struct{}
}

// MockGoalServiceClientMockRecorder is the mock recorder for MockGoalServiceClient.
type MockGoalServiceClientMockRecorder struct {
	mock *MockGoalServiceClient
}

// NewMockGoalServiceClient creates a new mock instance.
func NewMockGoalServiceClient(ctrl *gomock.Controller) *MockGoalServiceClient {
	mock := &MockGoalServiceClient{ctrl: ctrl}
	mock.recorder = &MockGoalServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGoalServiceClient) EXPECT() *MockGoalServiceClientMockRecorder {
	return m.recorder
}

// GoalsDisabledForAccountGroup mocks base method.
func (m *MockGoalServiceClient) GoalsDisabledForAccountGroup(ctx context.Context, in *v1alpha1.GoalsDisabledForAccountGroupRequest, opts ...grpc.CallOption) (*v1alpha1.GoalsDisabledForAccountGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GoalsDisabledForAccountGroup", varargs...)
	ret0, _ := ret[0].(*v1alpha1.GoalsDisabledForAccountGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GoalsDisabledForAccountGroup indicates an expected call of GoalsDisabledForAccountGroup.
func (mr *MockGoalServiceClientMockRecorder) GoalsDisabledForAccountGroup(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GoalsDisabledForAccountGroup", reflect.TypeOf((*MockGoalServiceClient)(nil).GoalsDisabledForAccountGroup), varargs...)
}
