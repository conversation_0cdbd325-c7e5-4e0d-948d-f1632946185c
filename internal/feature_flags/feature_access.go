package featureflags

import (
	"context"

	aiAssistants "github.com/vendasta/generated-protos-go/ai_assistants/v1alpha1"
	"github.com/vendasta/gosdks/verrors"
	partner "github.com/vendasta/partner/sdks/go/v1"
)

const (
	// DALE-594: this is a special "feature flag" that is scoped to groups. Used for the NBLY migration to AI goals
	GoalsForGroupFeatureID = "group_feature_goals"
)

var (
	errLengthMismatch = verrors.New(verrors.InvalidArgument, "feature flag list and status list mismatch")
)

type FeatureAccess struct {
	featureClient partner.FeatureFlagGetter
	goalClient    GoalServiceClient
}

func New(partnerCenter partner.FeatureFlagGetter, goalClient GoalServiceClient) *FeatureAccess {
	return &FeatureAccess{
		featureClient: partnerCenter,
		goalClient:    goalClient,
	}
}

// GetFeaturesStatus will call Partner Center to get the status of the given features
func (f *FeatureAccess) GetFeaturesStatus(ctx context.Context, partnerID, marketID string, featureFlags []string, accountGroupID string) (map[string]bool, error) {
	if partnerID == "" {
		return nil, nil
	}
	featuresStatus, err := f.featureClient.BatchGetStatus(ctx, partnerID, marketID, featureFlags)
	if err != nil {
		return nil, err
	}
	if len(featuresStatus) != len(featureFlags) {
		return nil, errLengthMismatch
	}
	statuses := make(map[string]bool)
	for i, f := range featureFlags {
		statuses[f] = featuresStatus[i]
	}
	// DALE-594: this is a special "feature flag" that is scoped to groups. Used for the NBLY migration to AI goals
	goalsDisabled := false
	if accountGroupID != "" {
		goalsDisabledResponse, err := f.goalClient.GoalsDisabledForAccountGroup(ctx, &aiAssistants.GoalsDisabledForAccountGroupRequest{
			AccountGroupId: accountGroupID,
		})
		if err != nil {
			return nil, err
		}
		goalsDisabled = goalsDisabledResponse.GoalsDisabled
	}
	statuses[GoalsForGroupFeatureID] = !goalsDisabled
	return statuses, nil
}
