package featureflags

import (
	"context"

	aiAssistants "github.com/vendasta/generated-protos-go/ai_assistants/v1alpha1"
	grpc "google.golang.org/grpc"
)

type Features interface {
	GetFeaturesStatus(ctx context.Context, partnerID, marketID string, featureFlags []string, accountGroupID string) (map[string]bool, error)
}

//go:generate mockgen -destination mock.go -source=interface.go -package=featureflags
type GoalServiceClient interface {
	GoalsDisabledForAccountGroup(ctx context.Context, in *aiAssistants.GoalsDisabledForAccountGroupRequest, opts ...grpc.CallOption) (*aiAssistants.GoalsDisabledForAccountGroupResponse, error)
}
