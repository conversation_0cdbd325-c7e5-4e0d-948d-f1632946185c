package accountsdata

import (
	"context"

	accounts "github.com/vendasta/accounts/sdks/go/v1"

	"github.com/vendasta/gosdks/verrors"
)

// Service handles fetching apps on account groups
type Service struct {
	AccountsClient accounts.Interface
}

// NewService creates a new instance of the AccountsService
func NewService(accountsClient accounts.Interface) *Service {
	return &Service{
		AccountsClient: accountsClient,
	}
}

// List accounts on a business from the accounts microservice
func (s *Service) List(ctx context.Context, businessID string, partnerID string) ([]*Account, error) {
	if businessID == "" {
		return nil, verrors.New(verrors.InvalidArgument, "businessID is required to get account data")
	}
	if partnerID == "" {
		return nil, verrors.New(verrors.InvalidArgument, "partnerID is required to get account data")
	}
	apiAccounts, err := s.AccountsClient.List(ctx, businessID, partnerID)
	if err != nil {
		return nil, err
	}
	// We only want accessible accounts; active or cancelled
	var accessibleAccounts []*accounts.Account
	for _, acct := range apiAccounts {
		if acct.Status == accounts.ACTIVE || acct.Status == accounts.CANCELLED {
			accessibleAccounts = append(accessibleAccounts, acct)
		}
	}
	domainAccounts := make([]*Account, len(accessibleAccounts))
	for i, a := range accessibleAccounts {
		domainAccounts[i] = fromAPI(a)
	}
	return domainAccounts, nil
}
