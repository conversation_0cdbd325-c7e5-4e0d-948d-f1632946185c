package pin

import (
	"context"
	"fmt"
	"sort"

	"github.com/vendasta/atlas/internal/partnermarket"
	"github.com/vendasta/atlas/internal/pin/repo"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/statsd"
	"github.com/vendasta/gosdks/verrors"
)

// Service is the pin service implementation
type Service struct {
	repo            repo.Repository
	locationFetcher partnermarket.LocationDataFetcher
}

// NewService returns a new pin service
func NewService(r repo.Repository, locationFetcher partnermarket.LocationDataFetcher) *Service {
	return &Service{
		repo:            r,
		locationFetcher: locationFetcher,
	}
}

// Set will set the pins for a user in an business or brand
func (s *Service) SetForUser(ctx context.Context, userID, identifier string, items []string) error {
	if identifier == "" {
		return verrors.New(verrors.InvalidArgument, "an identifier is required")
	}
	if userID == "" {
		// TODO: Remaining callers are temporary sessions who should not be offered pinning in UI
		_ = statsd.Incr("setPinsNoUser", nil, 1)
		return nil
	}

	// Items are what we want to exclude from the list we save because we are saving a blacklist. First we need to
	// find all the products the account has enabled.
	ld, err := s.locationFetcher.GetLocationData(ctx, identifier, "")
	if err != nil {
		return err
	}
	// Build the product ids to match what nav ids look like
	productNavIDs := productIDsToNavIDs(ld.ServiceProviderIDs)
	// Remove all the items from the enabled products.
	blacklist := filterAndSort(productNavIDs, items, nil)

	return s.repo.Set(ctx, userID, identifier, items, blacklist)
}

// Get will return a list of pinned navigation ids for a user
func (s *Service) GetForUser(ctx context.Context, userID, identifier string) ([]string, error) {
	if identifier == "" {
		return nil, verrors.New(verrors.InvalidArgument, "an identifier is required")
	}

	// User ID is not required. If it's present, we try to fetch blacklist/priority of pins
	var blacklistPinsItems, priorityPinItems []string

	if userID == "" {
		return []string{}, nil
	}
	blacklistPins, err := s.repo.Get(ctx, userID, identifier)
	if err != nil {
		logging.Warningf(ctx, "warning failed getting pin model by user id: %v", err)
	} else if blacklistPins != nil {
		blacklistPinsItems = blacklistPins.Items
		priorityPinItems = blacklistPins.PriorityItems
	}

	// Since we have a blacklist, we must look up all the products that are enabled for the account. Then we can filter
	// out the ids that are in the blacklist.
	ld, err := s.locationFetcher.GetLocationData(ctx, identifier, "")
	if err != nil {
		return nil, err
	}
	// Convert the product ids into their nav identifiers
	productNavIDs := productIDsToNavIDs(ld.ServiceProviderIDs)
	// If there is no blacklist and no priority items set, we will just return the ids in order from internal
	if blacklistPinsItems == nil && priorityPinItems == nil {
		return productNavIDs, nil
	}
	// Filter and sort the blacklisted items
	return filterAndSort(productNavIDs, blacklistPinsItems, priorityPinItems), nil
}

// productNavID converts a service provider id into a navigation item identifier.
func productNavID(serviceProviderID string) string {
	return fmt.Sprintf("product-%s", serviceProviderID)
}

// productIDsToNavIDs converts multiple service provider ids into navigation item identifiers
func productIDsToNavIDs(serviceProviderIDs []string) []string {
	out := make([]string, len(serviceProviderIDs))
	for i, id := range serviceProviderIDs {
		out[i] = productNavID(id)
	}
	return out
}

// filterAndSort removes all elements from "items" which are in the "exclude" list
// then sorts according to the priority list
func filterAndSort(items, exclude []string, priority []string) []string {
	excludeMap := make(map[string]struct{}, len(exclude))
	for _, b := range exclude {
		excludeMap[b] = struct{}{}
	}
	var out []string
	for _, item := range items {
		_, exists := excludeMap[item]
		if !exists {
			out = append(out, item)
		}
	}
	// priority of id at start is highest, last item is 1, and no priority items are 0s for ties
	priorityMap := make(map[string]int, len(priority))
	for idx, id := range priority {
		priorityMap[id] = len(priority) - idx
	}
	sort.SliceStable(out, func(i, j int) bool {
		// move i toward start if it has higher priority
		return priorityMap[out[i]] > priorityMap[out[j]]
	})
	return out
}
