package pin

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/vendasta/atlas/internal/partnermarket"
	"github.com/vendasta/atlas/internal/pin/repo"
)

type repoStub struct {
	identifier    string
	items         []string
	priorityItems []string
}

func (r *repoStub) Insert(ctx context.Context, userID, identifier string, priorityItems []string, excludeItems []string) error {
	panic("implement me")
}

func (r *repoStub) Get(ctx context.Context, userID, identifier string) (*repo.PinModel, error) {
	return &repo.PinModel{
		UserID:        userID,
		Identifier:    r.identifier,
		Items:         r.items,
		PriorityItems: r.priorityItems,
	}, nil
}

func (r *repoStub) Set(ctx context.Context, userID, identifier string, priorityItems []string, excludeItems []string) error {
	r.identifier = identifier
	r.items = excludeItems
	r.priorityItems = priorityItems
	return nil
}

func (r *repoStub) Scan(ctx context.Context, fn repo.ScanFn) error {
	panic("implement me")
}

type locationFetcherStub struct {
	data *partnermarket.BusinessContextData
}

func (l locationFetcherStub) GetLocationData(ctx context.Context, accountGroupID, groupPath string) (*partnermarket.BusinessContextData, error) {
	return l.data, nil
}

func Test_SetForuser(t *testing.T) {
	cases := []struct {
		name             string
		items            []string
		enabledProducts  []string
		expected         []string
		expectedPriority []string
		noUser           bool
		error            error
	}{
		{
			name:             "Only saves products that are not blacklisted",
			items:            []string{"product-RM", "product-SM"},
			enabledProducts:  []string{"RM", "MS", "SM"},
			expected:         []string{"product-MS"},
			expectedPriority: []string{"product-RM", "product-SM"},
		},
		{
			name:             "Saves all product ids if there is no blacklist set",
			items:            []string{},
			enabledProducts:  []string{"RM", "MS", "SM"},
			expected:         []string{"product-RM", "product-MS", "product-SM"},
			expectedPriority: []string{},
		},
		{
			name:             "Saves no product ids if all products have been blacklisted",
			items:            []string{"product-RM", "product-MS", "product-SM"},
			enabledProducts:  []string{"RM", "MS", "SM"},
			expected:         nil,
			expectedPriority: []string{"product-RM", "product-MS", "product-SM"},
		},
		{
			name:             "No user id doesnt save",
			items:            []string{"product-RM", "product-MS", "product-SM"},
			enabledProducts:  []string{"RM", "MS", "SM"},
			expected:         nil,
			expectedPriority: nil,
			noUser:           true,
		},
	}
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			identifier := "AG-123"
			r := &repoStub{}
			lf := &locationFetcherStub{
				data: &partnermarket.BusinessContextData{
					ServiceProviderIDs: c.enabledProducts,
				},
			}
			svc := NewService(r, lf)

			userID := "userID"
			if c.noUser {
				userID = ""
			}
			err := svc.SetForUser(context.Background(), userID, identifier, c.items)
			assert.Equal(t, c.error, err)
			assert.Equal(t, c.expected, r.items)
			assert.Equal(t, c.expectedPriority, r.priorityItems)
		})
	}
}

func Test_GetForUser(t *testing.T) {
	cases := []struct {
		name            string
		blacklist       []string
		priorityList    []string
		enabledProducts []string
		expected        []string
		error           error
		noUser          bool
	}{
		{
			name:            "Returns a list of the enabled products with the ones in the blacklist excluded",
			blacklist:       []string{"product-MS"},
			enabledProducts: []string{"RM", "MS", "SM"},
			expected:        []string{"product-RM", "product-SM"},
		},
		{
			name:            "Returns all products if there are no blacklisted products",
			blacklist:       []string{},
			enabledProducts: []string{"RM", "MS", "SM"},
			expected:        []string{"product-RM", "product-MS", "product-SM"},
		},
		{
			name:            "Returns no products if they are all blacklisted",
			blacklist:       []string{"product-RM", "product-MS", "product-SM"},
			enabledProducts: []string{"RM", "MS", "SM"},
			expected:        nil,
		},
		{
			name:            "Items obey priority list, but do not affect product order otherwise",
			blacklist:       []string{"product-X", "product-XX"},
			priorityList:    []string{"product-1", "product-XXX", "product-2"},
			enabledProducts: []string{"X", "4", "3", "2", "1"},
			expected:        []string{"product-1", "product-2", "product-4", "product-3"},
		},
		{
			name:            "Items obey priority list, even when blacklist is empty",
			blacklist:       []string{},
			priorityList:    []string{"product-1", "product-XXX", "product-2"},
			enabledProducts: []string{"X", "4", "3", "2", "1"},
			expected:        []string{"product-1", "product-2", "product-X", "product-4", "product-3"},
		},
		{
			name:            "No user id return no product",
			enabledProducts: []string{"X", "4", "3", "2", "1"},
			expected:        []string{},
			noUser:          true,
		},
	}
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			identifier := "AG-123"
			r := &repoStub{
				identifier:    identifier,
				items:         c.blacklist,
				priorityItems: c.priorityList,
			}
			lf := &locationFetcherStub{
				data: &partnermarket.BusinessContextData{
					ServiceProviderIDs: c.enabledProducts,
				},
			}
			svc := NewService(r, lf)
			userID := "userID"
			if c.noUser {
				userID = ""
			}
			actual, err := svc.GetForUser(context.Background(), userID, identifier)
			assert.Equal(t, c.error, err)
			assert.Equal(t, c.expected, actual)
		})
	}
}
