package repo

import (
	"context"
	"errors"
)

// Repository of pins
type Repository interface {
	Getter
	Setter
	Scanner
}

// Getter for a pin
type Getter interface {
	Get(ctx context.Context, userID, identifier string) (*PinModel, error)
}

// Setter for a pin
type Setter interface {
	Set(ctx context.Context, userID, identifier string, priorityItems []string, excludeItems []string) error
	Insert(ctx context.Context, userID, identifier string, priorityItems []string, excludeItems []string) error
}

// nolint:stylecheck,golint
var Done = errors.New("scan complete")

type ScanFn func(*PinModel) error

type Scanner interface {
	// Scan applies a function to all pinned entities. If the scan function (fn) returns Done, the scan will stop even
	// if there are more entities available.
	Scan(ctx context.Context, fn ScanFn) error
}
