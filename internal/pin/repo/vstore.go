package repo

import (
	"context"

	"github.com/vendasta/gosdks/verrors"
	vstore "github.com/vendasta/vstore/vstore/sdks/go/v1"
)

// VStoreRepository is a vstore implementation of a pins repository
type VStoreRepository struct {
	client vstore.Interface
}

// NewVStoreRepository returns a new pin vstore repository implementation
func NewVStoreRepository(v vstore.Interface) *VStoreRepository {
	return &VStoreRepository{
		client: v,
	}
}

// Set or create a group of pins
func (v *VStoreRepository) Set(ctx context.Context, userID, identifier string, priorityItems []string, excludeItems []string) error {
	if userID == "" {
		return verrors.New(verrors.InvalidArgument, "a user id is required")
	}
	if identifier == "" {
		return verrors.New(verrors.InvalidArgument, "an identifier is required")
	}
	return v.client.ReadWriteTransaction(ctx, func(ctx context.Context, t vstore.Transaction) error {
		return t.Replace(VStoreKind, &PinModel{
			UserID:        userID,
			Identifier:    identifier,
			Items:         excludeItems,
			PriorityItems: priorityItems,
		})
	})
}

// Insert the row, succeed if it was already there
func (v *VStoreRepository) Insert(ctx context.Context, userID, identifier string, priorityItems []string, excludeItems []string) error {
	if userID == "" {
		return verrors.New(verrors.InvalidArgument, "a user id is required")
	}
	if identifier == "" {
		return verrors.New(verrors.InvalidArgument, "an identifier is required")
	}
	err := v.client.ReadWriteTransaction(ctx, func(ctx context.Context, t vstore.Transaction) error {
		return t.Insert(VStoreKind, &PinModel{
			UserID:        userID,
			Identifier:    identifier,
			Items:         excludeItems,
			PriorityItems: priorityItems,
		})
	})
	// If it's already there, succeed
	if err != nil && err == vstore.ErrEntityAlreadyExists {
		return nil
	}
	return err
}

// Get returns a pin model
func (v *VStoreRepository) Get(ctx context.Context, userID, identifier string) (*PinModel, error) {
	dst := &PinModel{}
	err := v.client.Get(ctx, vstore.NewKeySet(VStoreKind, []string{userID, identifier}), dst)
	if err != nil && err == vstore.ErrNoSuchEntity {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return dst, nil
}

func (v *VStoreRepository) Scan(ctx context.Context, fn ScanFn) error {
	iter, err := v.client.Query(ctx, vstore.NewUnboundedQuery(VStoreKind))
	if err != nil {
		return err
	}
	return iter.Do(func(result vstore.Result) error {
		p := &PinModel{}
		err := result.ToStruct(p)
		if err != nil {
			return err
		}
		err = fn(p)
		if err == Done {
			return vstore.Done
		}
		return err
	})
}
