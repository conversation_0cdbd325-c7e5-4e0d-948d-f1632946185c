package repo

import (
	"context"

	vstore "github.com/vendasta/vstore/vstore/sdks/go/v1"
)

const (
	// VStoreKind is the vstore kind for pins
	VStoreKind = "PinnedItemsV2"
)

// PinModel is a pinned item
type PinModel struct {
	// UserID is a reference to the unified user id of IAM
	UserID string `vstore:"user_id"`
	// Identifier is either an account group id or brand id
	Identifier string `vstore:"identifier"`
	// Items are a blacklist of product nav item identifiers
	Items []string `vstore:"items"`
	// PriorityItems are a list of product nav item identifiers which solely affect ordering when returning pins
	PriorityItems []string `vstore:"priority_items"`
}

// Register the pin model
func Register(ctx context.Context, vstoreClient vstore.Interface) error {
	properties := vstore.NewPropertyBuilder().
		PropertiesFromStruct((*PinModel)(nil)).
		PropertyConfig("user_id", vstore.Required(), vstore.SetPIITagPII(), vstore.PropertyDescription("A reference to the unified user ID of IAM. Defines which user this set of pinned items is for. Is unique when in conjunction with the identifier field.")).
		PropertyConfig("identifier", vstore.Required(), vstore.PropertyDescription("Is either an account group ID or group ID. Declares which business or group this set of pinned items is for. Is unique when in conjunction with the identifier field.")).
		PropertyConfig("items", vstore.PropertyDescription("A list of product navigation item identifiers (Marketplace App IDs) to explicitly exclude from the pinned list.")).
		PropertyConfig("priority_items", vstore.PropertyDescription("A list of product navigation item identifiers (Marketplace App IDs) which solely affect ordering when returning pins.")).
		Build()
	schema := vstore.NewSchema(
		VStoreKind,
		[]string{"user_id", "identifier"},
		properties,
		nil,
		vstore.NewBackupConfigBuilder().PeriodicBackup(vstore.WeeklyBackup).Build(),
	)
	_, err := vstoreClient.RegisterKind(ctx, VStoreKind, schema)
	return err
}
