package defaultlocation

import (
	"context"

	vstore "github.com/vendasta/vstore/vstore/sdks/go/v1"
)

const (
	vStoreKind = "DefaultLocation"
)

type Model struct {
	PartnerID      string `vstore:"partner_id"`
	UserID         string `vstore:"user_id"`
	AccountGroupID string `vstore:"account_group_id"`
	GroupID        string `vstore:"group_id"`
}

func KeySet(partnerID, userID string) vstore.KeySet {
	return vstore.NewKeySet(vStoreKind, []string{partnerID, userID})
}

// createOrUpdateMutator for replacing new fields
func CreateOrUpdateMutator(accountGroupID string, groupID string) Mutator {
	return func(ctx context.Context, vModel *Model) error {
		vModel.AccountGroupID = accountGroupID
		vModel.GroupID = groupID
		return nil
	}
}

func Schema() *vstore.Schema {
	fields := vstore.NewPropertyBuilder().
		StringProperty("partner_id", vstore.Required()).
		StringProperty("user_id", vstore.Required(), vstore.PropertyDescription("User ID")).
		StringProperty("account_group_id", vstore.PropertyDescription("The default account group.")).
		StringProperty("group_id", vstore.PropertyDescription("The default group.")).
		Build()
	key := []string{"partner_id", "user_id"}
	compositeIndex := vstore.NewCompositeIndexBuilder().
		Build()

	secondaryIndices := vstore.NewSecondaryIndexBuilder().Build()
	return vstore.NewSchema(
		vStoreKind,
		key,
		fields,
		secondaryIndices,
		nil,
		vstore.CompositeIndexes(compositeIndex),
		vstore.SchemaDescription("This table contains the default location for the user in the Atlas navigation system."))
}

func Register(ctx context.Context, vStoreClient vstore.Interface) error {
	schema := Schema()
	_, err := vStoreClient.RegisterKind(ctx, vStoreKind, schema)
	return err
}
