package defaultlocation

import (
	"context"

	vstore "github.com/vendasta/vstore/vstore/sdks/go/v1"
)

type VStoreRepository struct {
	vstore vstore.Interface
}

func NewVStoreRepository(vstore vstore.Interface) Repository {
	return &VStoreRepository{vstore}
}

type Mutator func(context.Context, *Model) error

func (r VStoreRepository) Set(ctx context.Context, partnerID, userID string, siteMutator Mutator) error {
	return r.vstore.ReadWriteTransaction(ctx, func(ctx context.Context, t vstore.Transaction) error {
		defaultAccount := Model{}
		err := t.Get(ctx, KeySet(partnerID, userID), &defaultAccount)
		if err == vstore.ErrNoSuchEntity {
			defaultAccount = Model{
				PartnerID: partnerID,
				UserID:    userID,
			}
		} else if err != nil {
			return err
		}
		err = siteMutator(ctx, &defaultAccount)
		if err != nil {
			return err
		}
		return t.Replace(vStoreKind, defaultAccount)
	})
}

func (r VStoreRepository) Get(ctx context.Context, partnerID, userID string) (*Model, error) {
	keySet := KeySet(partnerID, userID)
	m := &Model{}
	err := r.vstore.Get(ctx, keySet, m)
	if err == vstore.ErrNoSuchEntity {
		return nil, nil
	}
	if err != nil {
		return nil, err
	}
	return m, err
}
