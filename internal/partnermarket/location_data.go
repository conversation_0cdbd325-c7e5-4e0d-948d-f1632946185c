package partnermarket

import (
	"context"
	"strings"

	accountgroup "github.com/vendasta/account-group/sdks/go/v1"
	multilocation_v1 "github.com/vendasta/generated-protos-go/multi_location/v1"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/verrors"
	group "github.com/vendasta/group/sdks/go/v2"
	multilocation "github.com/vendasta/multi-location/sdks/go/v1"
	"golang.org/x/sync/errgroup"
)

// LocationDataFetcher handles pulling data off of the account group or brand
type LocationDataFetcher interface {
	GetLocationData(ctx context.Context, accountGroupID, groupPath string) (*BusinessContextData, error)
}

// BusinessContextData is the data of the Partner Market and brand information if a brand was specified
type BusinessContextData struct {
	PartnerID          string
	MarketID           string
	SalesPersonID      string
	BusinessName       string
	Address            string
	Country            string
	ServiceProviderIDs []string
}

// LocationDataFetch returns data for the location fetcher
type LocationDataFetch struct {
	accountGroupClient  accountgroup.Interface
	groupClient         group.Interface
	multiLocationClient multilocation.MultiLocationClientInterface
}

// NewLocationDataFetcher returns a new location data fetcher
func NewLocationDataFetcher(accountGroupClient accountgroup.Interface, groupClient group.Interface, multiLocationClient multilocation.MultiLocationClientInterface) LocationDataFetcher {
	return &LocationDataFetch{
		accountGroupClient:  accountGroupClient,
		groupClient:         groupClient,
		multiLocationClient: multiLocationClient,
	}
}

// GetDataWithAccountGroupID returns the data for an account group
func (d *LocationDataFetch) getDataWithAccountGroupID(ctx context.Context, accountGroupID string) (*BusinessContextData, error) {
	accountGroup, err := d.accountGroupClient.Get(
		ctx,
		accountGroupID,
		accountgroup.IncludeAccountGroupExternalIdentifiers(),
		accountgroup.IncludeAccounts(),
		accountgroup.IncludeNAPData(),
	)
	if err != nil {
		logging.Errorf(ctx, "error getting account group data: %v", err)
		return nil, err
	}
	if accountGroup == nil || accountGroup.ExternalIdentifiers == nil {
		logging.Errorf(ctx, "account group is nil")
		return nil, verrors.New(verrors.NotFound, "business was not found")
	}
	serviceProviderIDs := make([]string, len(accountGroup.Accounts))
	for i, a := range accountGroup.Accounts {
		if a.MarketplaceAppID == "CP" {
			serviceProviderIDs[i] = a.AccountID
		} else {
			serviceProviderIDs[i] = a.MarketplaceAppID
		}
	}

	return &BusinessContextData{
		PartnerID:          accountGroup.PartnerID,
		MarketID:           accountGroup.MarketID,
		SalesPersonID:      accountGroup.SalesPersonID,
		ServiceProviderIDs: serviceProviderIDs,
		BusinessName:       accountGroup.CompanyName,
		Address:            accountGroup.Address,
		Country:            accountGroup.NAPData.Country,
	}, nil
}

// GetPartnerAndBrandData returns partner and brand data
func (d *LocationDataFetch) getPartnerAndBrandData(ctx context.Context, groupPath string) (*BusinessContextData, error) {
	path := group.PathFromString(groupPath)
	eg, eCtx := errgroup.WithContext(ctx)
	nodes := strings.Split(groupPath, "|")
	var pid string
	var name string
	var market string
	eg.Go(func() error {
		g, err := d.groupClient.Get(eCtx, path)
		if err != nil {
			logging.Errorf(eCtx, "error getting group data: %v", err)
			return err
		}
		if g == nil {
			return verrors.New(verrors.NotFound, "group was not found")
		}
		pid = g.ForeignKeys.PartnerID
		name = g.Name
		if len(nodes) == 1 {
			brandPath, err := multilocation.BuildBrandPath(g.ForeignKeys.PartnerID, g.Name)
			if err != nil {
				return verrors.New(verrors.NotFound, "error getting brand path")
			}
			brand, err := d.multiLocationClient.GetBrand(eCtx, &multilocation_v1.GetBrandRequest{Path: &multilocation_v1.GetBrandRequest_BrandPath{BrandPath: brandPath}})
			if err != nil {
				return verrors.New(verrors.NotFound, "brand was not found")
			}
			market = brand.Brand.MarketId
		}
		return nil
	})
	if len(nodes) > 1 {
		eg.Go(func() error {
			g, err := d.groupClient.Get(eCtx, group.PathFromString(strings.Split(groupPath, "|")[0]))
			if err != nil {
				logging.Errorf(eCtx, "error getting group data: %v", err)
				return err
			}
			if g == nil {
				return verrors.New(verrors.NotFound, "group was not found")
			}
			brandPath, err := multilocation.BuildBrandPath(g.ForeignKeys.PartnerID, g.Name)
			if err != nil {
				return verrors.New(verrors.NotFound, "error getting brand path")
			}
			brand, err := d.multiLocationClient.GetBrand(eCtx, &multilocation_v1.GetBrandRequest{Path: &multilocation_v1.GetBrandRequest_BrandPath{BrandPath: brandPath}})
			if err != nil {
				return verrors.New(verrors.NotFound, "brand was not found")
			}
			market = brand.Brand.MarketId
			return nil
		})
	}
	err := eg.Wait()
	if err != nil {
		return nil, err
	}
	return &BusinessContextData{
		PartnerID:    pid,
		MarketID:     market,
		BusinessName: name,
	}, nil
}

// GetLocationData for a business or brand
func (d *LocationDataFetch) GetLocationData(ctx context.Context, accountGroupID, groupPath string) (*BusinessContextData, error) {
	if groupPath != "" && accountGroupID != "" {
		return nil, verrors.New(verrors.InvalidArgument, "a brand path and business id cannot both be specified")
	}
	if groupPath != "" && accountGroupID == "" {
		data, err := d.getPartnerAndBrandData(ctx, groupPath)
		if err != nil {
			return nil, err
		}
		return data, nil
	} else if groupPath == "" && accountGroupID != "" {
		data, err := d.getDataWithAccountGroupID(ctx, accountGroupID)
		if err != nil {
			return nil, err
		}
		return data, nil
	}
	return nil, verrors.New(verrors.InvalidArgument, "a business id or brand path must be specified")
}
