package crmcustomobjects

import (
	"context"
	"sort"
	"strings"

	"github.com/vendasta/gosdks/logging"

	featureflags "github.com/vendasta/atlas/internal/feature_flags"

	crm "github.com/vendasta/crm/sdks/go"
	crm_v1 "github.com/vendasta/generated-protos-go/crm/v1"
	"github.com/vendasta/gosdks/verrors"
)

const CustomObjectTypesFlag = "bcc_crm_custom_objects"

// Service handles getting white labeled product data
type Service struct {
	featureClient featureflags.Features
	crmClient     crm.CrmCustomObjectTypeServiceClientInterface
}

// NewService returns a new product service
func NewService(featureClient featureflags.Features, crmClient crm.CrmCustomObjectTypeServiceClientInterface) *Service {
	return &Service{
		featureClient: featureClient,
		crmClient:     crmClient,
	}
}

// GetCustomObjectTypes returns a list of custom object types order alphabetically
func (s *Service) GetCRMCustomObjectTypes(ctx context.Context, partnerID, marketID, accountGroupID string) ([]*CRMCustomObjectType, error) {
	flagMap, err := s.featureClient.GetFeaturesStatus(ctx, partnerID, marketID, []string{
		CustomObjectTypesFlag,
	}, accountGroupID)
	if err != nil {
		return nil, err
	}
	enabledCustomObjectTypes := flagMap[CustomObjectTypesFlag]
	if !enabledCustomObjectTypes {
		return []*CRMCustomObjectType{}, nil
	}

	response, err := s.crmClient.ListCustomObjectTypes(ctx, &crm_v1.ListCustomObjectTypesRequest{
		Namespace: accountGroupID,
	})
	if err != nil && verrors.IsError(verrors.PermissionDenied, err) {
		logging.Warningf(ctx, "User doesn't have permissions to see custom object types: %v", err.Error())
		return []*CRMCustomObjectType{}, nil
	}
	if err != nil {
		return nil, err
	}
	return buildCustomObjectTypes(ctx, accountGroupID, response.GetCustomObjectTypes())
}

func buildCustomObjectTypes(ctx context.Context, accountGroupID string, customObjectTypes []*crm_v1.CustomObjectType) ([]*CRMCustomObjectType, error) {
	crmCustomObjectTypes := make([]*CRMCustomObjectType, 0, len(customObjectTypes))
	for _, customObjectType := range customObjectTypes {
		if customObjectType.GetCustomObjectTypeId() == "" {
			continue
		}

		path := "/account/location/<accountId>/crm/custom-object/<customObjectTypeId>/list"
		path = strings.ReplaceAll(path, "<accountId>", accountGroupID)
		path = strings.ReplaceAll(path, "<customObjectTypeId>", customObjectType.GetCustomObjectTypeId())
		crmCustomObjectTypes = append(crmCustomObjectTypes, &CRMCustomObjectType{
			ID:   customObjectType.GetCustomObjectTypeId(),
			Name: customObjectType.GetPluralObjectName(),
			Path: path,
		})
	}

	sort.Slice(crmCustomObjectTypes, func(i, j int) bool {
		return crmCustomObjectTypes[i].Name < crmCustomObjectTypes[j].Name
	})
	return crmCustomObjectTypes, nil
}
