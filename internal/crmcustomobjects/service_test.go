package crmcustomobjects

import (
	"context"
	"github.com/stretchr/testify/assert"
	crm_v1 "github.com/vendasta/generated-protos-go/crm/v1"
	"github.com/vendasta/gosdks/verrors"
	"go.uber.org/mock/gomock"
	"testing"
)

//go:generate mockgen -package=crmcustomobjects -destination mockscrm.go --build_flags=--mod=mod github.com/vendasta/crm/sdks/go CrmCustomObjectTypeServiceClientInterface
//go:generate mockgen -package=crmcustomobjects -destination mockff.go --build_flags=--mod=mod github.com/vendasta/atlas/internal/feature_flags Features

func TestService_GetCRMCustomObjectTypes(t *testing.T) {
	type featureCall struct {
		inputPartnerID      string
		inputMarketID       string
		inputAccountGroupID string
		returnFlags         map[string]bool
		returnError         error
	}
	type listCustomObjectTypeCall struct {
		inputNamespace          string
		returnCustomObjectTypes []*crm_v1.CustomObjectType
		returnError             error
	}
	type input struct {
		partnerID      string
		marketID       string
		accountGroupID string
	}
	tests := []struct {
		name                     string
		input                    input
		featureCall              *featureCall
		listCustomObjectTypeCall *listCustomObjectTypeCall
		want                     []*CRMCustomObjectType
		wantErr                  bool
	}{
		{
			name: "happy path: list custom object types with success",
			input: input{
				accountGroupID: "AG-123",
				partnerID:      "VUNI",
				marketID:       "market1",
			},
			featureCall: &featureCall{
				inputPartnerID:      "VUNI",
				inputMarketID:       "market1",
				inputAccountGroupID: "AG-123",
				returnFlags: map[string]bool{
					CustomObjectTypesFlag: true,
				},
			},
			listCustomObjectTypeCall: &listCustomObjectTypeCall{
				inputNamespace: "AG-123",
				returnCustomObjectTypes: []*crm_v1.CustomObjectType{
					{
						Namespace:          "AG-123",
						CustomObjectTypeId: "C-2",
						PluralObjectName:   "Vehicles",
					},
					nil,
					{},
					{
						Namespace:          "AG-123",
						CustomObjectTypeId: "C-1",
						PluralObjectName:   "Bookings",
					},
					{
						Namespace:          "AG-123",
						CustomObjectTypeId: "C-3",
						PluralObjectName:   "Items",
					},
				},
			},
			want: []*CRMCustomObjectType{
				{
					ID:   "C-1",
					Name: "Bookings",
					Path: "/account/location/AG-123/crm/custom-object/C-1/list",
				},
				{
					ID:   "C-3",
					Name: "Items",
					Path: "/account/location/AG-123/crm/custom-object/C-3/list",
				},
				{
					ID:   "C-2",
					Name: "Vehicles",
					Path: "/account/location/AG-123/crm/custom-object/C-2/list",
				},
			},
		},
		{
			name: "permission denied error -> empty list",
			input: input{
				accountGroupID: "AG-123",
				partnerID:      "VUNI",
				marketID:       "market1",
			},
			featureCall: &featureCall{
				inputPartnerID:      "VUNI",
				inputMarketID:       "market1",
				inputAccountGroupID: "AG-123",
				returnFlags: map[string]bool{
					CustomObjectTypesFlag: true,
				},
			},
			listCustomObjectTypeCall: &listCustomObjectTypeCall{
				inputNamespace: "AG-123",
				returnError:    verrors.New(verrors.PermissionDenied, "permission denied"),
			},
			want: []*CRMCustomObjectType{},
		},
		{
			name: "other errors -> return err to layer above",
			input: input{
				accountGroupID: "AG-123",
				partnerID:      "VUNI",
				marketID:       "market1",
			},
			featureCall: &featureCall{
				inputPartnerID:      "VUNI",
				inputMarketID:       "market1",
				inputAccountGroupID: "AG-123",
				returnFlags: map[string]bool{
					CustomObjectTypesFlag: true,
				},
			},
			listCustomObjectTypeCall: &listCustomObjectTypeCall{
				inputNamespace: "AG-123",
				returnError:    verrors.New(verrors.Internal, "internal error"),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)

			ffMock := NewMockFeatures(ctrl)
			crmClientMock := NewMockCrmCustomObjectTypeServiceClientInterface(ctrl)

			if tt.featureCall != nil {
				ffMock.EXPECT().GetFeaturesStatus(ctx, tt.featureCall.inputPartnerID, tt.featureCall.inputMarketID, []string{
					CustomObjectTypesFlag,
				}, tt.featureCall.inputAccountGroupID).Return(tt.featureCall.returnFlags, tt.featureCall.returnError)
			}
			if tt.listCustomObjectTypeCall != nil {
				crmClientMock.EXPECT().ListCustomObjectTypes(ctx, &crm_v1.ListCustomObjectTypesRequest{
					Namespace: tt.listCustomObjectTypeCall.inputNamespace,
				}).Return(&crm_v1.ListCustomObjectTypesResponse{
					CustomObjectTypes: tt.listCustomObjectTypeCall.returnCustomObjectTypes,
				}, tt.listCustomObjectTypeCall.returnError)
			}

			s := NewService(ffMock, crmClientMock)
			got, err := s.GetCRMCustomObjectTypes(ctx, tt.input.partnerID, tt.input.marketID, tt.input.accountGroupID)
			assert.Equal(t, tt.want, got)
			assert.Equal(t, tt.wantErr, err != nil)
		})
	}
}
