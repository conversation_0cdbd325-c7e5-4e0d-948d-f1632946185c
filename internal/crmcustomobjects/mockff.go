// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/vendasta/atlas/internal/feature_flags (interfaces: Features)
//
// Generated by this command:
//
//	mockgen -package=crmcustomobjects -destination mockff.go --build_flags=--mod=mod github.com/vendasta/atlas/internal/feature_flags Features
//

// Package crmcustomobjects is a generated GoMock package.
package crmcustomobjects

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockFeatures is a mock of Features interface.
type MockFeatures struct {
	ctrl     *gomock.Controller
	recorder *MockFeaturesMockRecorder
	isgomock struct{}
}

// MockFeaturesMockRecorder is the mock recorder for MockFeatures.
type MockFeaturesMockRecorder struct {
	mock *MockFeatures
}

// NewMockFeatures creates a new mock instance.
func NewMockFeatures(ctrl *gomock.Controller) *MockFeatures {
	mock := &MockFeatures{ctrl: ctrl}
	mock.recorder = &MockFeaturesMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFeatures) EXPECT() *MockFeaturesMockRecorder {
	return m.recorder
}

// GetFeaturesStatus mocks base method.
func (m *MockFeatures) GetFeaturesStatus(ctx context.Context, partnerID, marketID string, featureFlags []string, accountGroupID string) (map[string]bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetFeaturesStatus", ctx, partnerID, marketID, featureFlags, accountGroupID)
	ret0, _ := ret[0].(map[string]bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFeaturesStatus indicates an expected call of GetFeaturesStatus.
func (mr *MockFeaturesMockRecorder) GetFeaturesStatus(ctx, partnerID, marketID, featureFlags, accountGroupID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFeaturesStatus", reflect.TypeOf((*MockFeatures)(nil).GetFeaturesStatus), ctx, partnerID, marketID, featureFlags, accountGroupID)
}
