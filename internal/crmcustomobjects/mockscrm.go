// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/vendasta/crm/sdks/go (interfaces: CrmCustomObjectTypeServiceClientInterface)
//
// Generated by this command:
//
//	mockgen -package=crmcustomobjects -destination mockscrm.go --build_flags=--mod=mod github.com/vendasta/crm/sdks/go CrmCustomObjectTypeServiceClientInterface
//

// Package crmcustomobjects is a generated GoMock package.
package crmcustomobjects

import (
	context "context"
	reflect "reflect"

	crm_v1 "github.com/vendasta/generated-protos-go/crm/v1"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// MockCrmCustomObjectTypeServiceClientInterface is a mock of CrmCustomObjectTypeServiceClientInterface interface.
type MockCrmCustomObjectTypeServiceClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockCrmCustomObjectTypeServiceClientInterfaceMockRecorder
	isgomock struct{}
}

// MockCrmCustomObjectTypeServiceClientInterfaceMockRecorder is the mock recorder for MockCrmCustomObjectTypeServiceClientInterface.
type MockCrmCustomObjectTypeServiceClientInterfaceMockRecorder struct {
	mock *MockCrmCustomObjectTypeServiceClientInterface
}

// NewMockCrmCustomObjectTypeServiceClientInterface creates a new mock instance.
func NewMockCrmCustomObjectTypeServiceClientInterface(ctrl *gomock.Controller) *MockCrmCustomObjectTypeServiceClientInterface {
	mock := &MockCrmCustomObjectTypeServiceClientInterface{ctrl: ctrl}
	mock.recorder = &MockCrmCustomObjectTypeServiceClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCrmCustomObjectTypeServiceClientInterface) EXPECT() *MockCrmCustomObjectTypeServiceClientInterfaceMockRecorder {
	return m.recorder
}

// CreateCustomObjectType mocks base method.
func (m *MockCrmCustomObjectTypeServiceClientInterface) CreateCustomObjectType(ctx context.Context, in *crm_v1.CreateCustomObjectTypeRequest, opts ...grpc.CallOption) (*crm_v1.CreateCustomObjectTypeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateCustomObjectType", varargs...)
	ret0, _ := ret[0].(*crm_v1.CreateCustomObjectTypeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateCustomObjectType indicates an expected call of CreateCustomObjectType.
func (mr *MockCrmCustomObjectTypeServiceClientInterfaceMockRecorder) CreateCustomObjectType(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateCustomObjectType", reflect.TypeOf((*MockCrmCustomObjectTypeServiceClientInterface)(nil).CreateCustomObjectType), varargs...)
}

// DeleteCustomObjectType mocks base method.
func (m *MockCrmCustomObjectTypeServiceClientInterface) DeleteCustomObjectType(ctx context.Context, in *crm_v1.DeleteCustomObjectTypeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteCustomObjectType", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteCustomObjectType indicates an expected call of DeleteCustomObjectType.
func (mr *MockCrmCustomObjectTypeServiceClientInterfaceMockRecorder) DeleteCustomObjectType(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteCustomObjectType", reflect.TypeOf((*MockCrmCustomObjectTypeServiceClientInterface)(nil).DeleteCustomObjectType), varargs...)
}

// GetMultiCustomObjectType mocks base method.
func (m *MockCrmCustomObjectTypeServiceClientInterface) GetMultiCustomObjectType(ctx context.Context, in *crm_v1.GetMultiCustomObjectTypeRequest, opts ...grpc.CallOption) (*crm_v1.GetMultiCustomObjectTypeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMultiCustomObjectType", varargs...)
	ret0, _ := ret[0].(*crm_v1.GetMultiCustomObjectTypeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiCustomObjectType indicates an expected call of GetMultiCustomObjectType.
func (mr *MockCrmCustomObjectTypeServiceClientInterfaceMockRecorder) GetMultiCustomObjectType(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiCustomObjectType", reflect.TypeOf((*MockCrmCustomObjectTypeServiceClientInterface)(nil).GetMultiCustomObjectType), varargs...)
}

// ListCustomObjectTypes mocks base method.
func (m *MockCrmCustomObjectTypeServiceClientInterface) ListCustomObjectTypes(ctx context.Context, in *crm_v1.ListCustomObjectTypesRequest, opts ...grpc.CallOption) (*crm_v1.ListCustomObjectTypesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListCustomObjectTypes", varargs...)
	ret0, _ := ret[0].(*crm_v1.ListCustomObjectTypesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListCustomObjectTypes indicates an expected call of ListCustomObjectTypes.
func (mr *MockCrmCustomObjectTypeServiceClientInterfaceMockRecorder) ListCustomObjectTypes(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListCustomObjectTypes", reflect.TypeOf((*MockCrmCustomObjectTypeServiceClientInterface)(nil).ListCustomObjectTypes), varargs...)
}

// UpdateCustomObjectType mocks base method.
func (m *MockCrmCustomObjectTypeServiceClientInterface) UpdateCustomObjectType(ctx context.Context, in *crm_v1.UpdateCustomObjectTypeRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateCustomObjectType", varargs...)
	ret0, _ := ret[0].(*emptypb.Empty)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateCustomObjectType indicates an expected call of UpdateCustomObjectType.
func (mr *MockCrmCustomObjectTypeServiceClientInterfaceMockRecorder) UpdateCustomObjectType(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateCustomObjectType", reflect.TypeOf((*MockCrmCustomObjectTypeServiceClientInterface)(nil).UpdateCustomObjectType), varargs...)
}
