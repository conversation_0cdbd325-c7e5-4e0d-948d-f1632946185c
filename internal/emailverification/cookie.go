package emailverification

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gorilla/sessions"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/verrors"
)

const (
	emailVerificationCookieName    = "email-verification"
	extendDisplayTimeDuration      = time.Hour * 24 * 7
	extendDisplayTimeShortDuration = time.Hour * 24 * 1
)

var (
	emailVerificationDeadline = time.Date(2023, time.May, 16, 0, 0, 0, 0, time.UTC)
)

func nextDisplayTimeKey(userID string) string {
	return fmt.Sprintf("%s:%s", "next-interstial-display-time", userID)
}

func initSessionValues(ctx context.Context, w http.ResponseWriter, r *http.Request, session *sessions.Session, userID string) error {
	encodedTime, err := time.Now().UTC().Add(extendDisplayTimeDuration).MarshalText()
	if err != nil {
		logging.Errorf(ctx, "error encoding time.Now()  %s", err)
		return verrors.New(verrors.Internal, "Error: encoding time")
	}

	session.Values[nextDisplayTimeKey(userID)] = encodedTime
	err = saveCookie(r, w, session)
	if err != nil {
		logging.Errorf(ctx, "Error saving cookie %s : %s", emailVerificationCookieName, err)
		return verrors.New(verrors.Internal, "Error: saving cookie")
	}
	return nil
}

func saveCookie(r *http.Request, w http.ResponseWriter, session *sessions.Session) error {
	session.Options.SameSite = http.SameSiteNoneMode
	session.Options.Secure = true
	return session.Save(r, w)
}

func extendNextDisplayTimeInSession(ctx context.Context, w http.ResponseWriter, r *http.Request, session *sessions.Session, userID string) error {
	nextDisplayTime, err := getNextDisplayTimeFromSession(session, userID)
	if err != nil {
		logging.Errorf(ctx, "error decoding time from session %s: %s", emailVerificationCookieName, err)
		return verrors.New(verrors.Internal, "Error: decoding time")
	}

	//return if the nextDisplayTime is a future date
	if nextDisplayTime.After(time.Now().UTC()) {
		return nil
	}

	now := time.Now()
	extended := extendNextDisplayTime(now)
	encodedTime, err := extended.MarshalText()
	if err != nil {
		logging.Errorf(ctx, "error encoding extended time: %s", err)
		return verrors.New(verrors.Internal, "Error: encoding time")
	}

	session.Values[nextDisplayTimeKey(userID)] = encodedTime
	err = saveCookie(r, w, session)
	if err != nil {
		logging.Errorf(ctx, "Error saving cookie %s: %s", emailVerificationCookieName, err)
		return verrors.New(verrors.Internal, "Error: saving cookie")
	}

	return nil
}

func getNextDisplayTimeFromSession(session *sessions.Session, userID string) (time.Time, error) {
	nextDisplayTimeEncoded := session.Values[nextDisplayTimeKey(userID)].([]byte)
	nextDisplayTime, err := time.Parse(time.RFC3339, string(nextDisplayTimeEncoded))
	return nextDisplayTime, err
}

// extendNextDisplayTime ensures that the user will see at least 1 week's worth of reminders before the deadline
func extendNextDisplayTime(now time.Time) time.Time {
	weekBeforeDeadline := emailVerificationDeadline.Add(-extendDisplayTimeDuration)
	if now.After(weekBeforeDeadline) {
		return now.Add(extendDisplayTimeShortDuration)
	}
	extended := now.Add(extendDisplayTimeDuration)
	if extended.After(weekBeforeDeadline) {
		return weekBeforeDeadline
	}
	return extended
}
