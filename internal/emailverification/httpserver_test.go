package emailverification

import (
	"context"
	"encoding/json"
	"net/http/httptest"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/gorilla/sessions"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/vendasta/IAM/sdks/go/iaminterceptor"
	iammock "github.com/vendasta/IAM/sdks/go/v1/mocks"
	"github.com/vendasta/IAM/sdks/go/v1/user"
	"github.com/vendasta/gosdks/cache"
	"github.com/vendasta/sso/sdks/go/sso"
)

var cookieKey = []byte("testing")

func TestEmailVerificationServer_ShouldDisplayInterstitial(t *testing.T) {
	session := sessions.NewCookieStore(cookieKey, nil)
	type input struct {
		user            *user.User
		extendThreshold bool
		customIDP       bool
	}
	tests := []struct {
		name     string
		input    []input
		expected map[string]bool
	}{
		{
			name: "User email not verified",
			input: []input{
				{
					user: &user.User{
						UserID:        "1234",
						EmailVerified: false,
					},
				},
			},
			expected: map[string]bool{
				"1234": true,
			},
		},
		{
			name: "User email is verified",
			input: []input{
				{
					user: &user.User{
						UserID:        "1234",
						EmailVerified: true,
					},
				},
			},
			expected: map[string]bool{
				"1234": false,
			},
		},
		{
			name: "User email is not verified but the user extended the display date",
			input: []input{
				{
					user: &user.User{
						UserID:        "1234",
						EmailVerified: false,
					},
					extendThreshold: true,
				},
			},
			expected: map[string]bool{
				"1234": false,
			},
		},
		{
			name: "User email is verified and the user extended the display date",
			input: []input{
				{
					user: &user.User{
						UserID:        "1234",
						EmailVerified: true,
					},
					extendThreshold: true,
				},
			},
			expected: map[string]bool{
				"1234": false,
			},
		},
		{
			name: "user1 is verified and user2 is not",
			input: []input{
				{
					user: &user.User{
						UserID:        "user1",
						EmailVerified: true,
					},
				},
				{
					user: &user.User{
						UserID:        "user2",
						EmailVerified: false,
					},
				},
			},
			expected: map[string]bool{
				"user1": false,
				"user2": true,
			},
		},
		{
			name: "Both users not verified but user1 extended display time",
			input: []input{
				{
					user: &user.User{
						UserID:        "user1",
						EmailVerified: false,
					},
					extendThreshold: true,
				},
				{
					user: &user.User{
						UserID:        "user2",
						EmailVerified: false,
					},
				},
			},
			expected: map[string]bool{
				"user1": false,
				"user2": true,
			},
		},
		{
			name: "User is unverified but has customIDP",
			input: []input{
				{
					user: &user.User{
						UserID:        "user1",
						EmailVerified: false,
					},
					customIDP: true,
				},
			},
			expected: map[string]bool{
				"user1": false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {

			for _, c := range tt.input {

				iamClient := &iammock.Interface{}
				iamClient.On("GetUser", mock.Anything, mock.Anything).Return(c.user, nil)
				mockSSO := sso.NewMockIdentityProviderAdminClient(gomock.NewController(t))
				mockSSO.EXPECT().HasCustomIdentityProvider(gomock.Any(), gomock.Any(), gomock.Any()).Return(c.customIDP, nil)
				cacheStub := cache.NewClientStub()

				e := &EmailVerificationServer{
					iamClient:         iamClient,
					session:           session,
					ssoIDPAdminClient: mockSSO,
					cacheRepo:         &EmailVerificationCacheRepo{cacheClient: cacheStub},
				}
				e.hasCustomIdentityProviderFunc = e.hasCustomIdentityProvider

				req := httptest.NewRequest("GET", "/to-display-email-verification", nil)
				res := httptest.NewRecorder()
				ctx := iaminterceptor.SetRequestInfoForTests(t, context.Background(), &iaminterceptor.TestRequestInfo{
					OriginalCaller: &iaminterceptor.TestTokenInfoIAMIdentity{
						UserID: c.user.UserID,
					},
				})
				req = req.WithContext(ctx)
				if c.extendThreshold {
					req.Method = "POST"
					e.ExtendNextDisplayTime().ServeHTTP(res, req)
					e.ShouldDisplayInterstitial().ServeHTTP(res, req)
				} else {
					e.ShouldDisplayInterstitial().ServeHTTP(res, req)
				}
				assert.Equal(t, 200, res.Result().StatusCode)
				result := ShouldDisplayInterstitialResult{}
				err := json.Unmarshal(res.Body.Bytes(), &result)
				assert.NoError(t, err)

				assert.Equal(t, tt.expected[c.user.UserID], result.Display)
			}
		})
	}
}

func TestEmailVerificationServer_ShouldDisplayInterstitial_parses_serviceProviderID_from_session(t *testing.T) {
	session := sessions.NewCookieStore(cookieKey, nil)
	iamClient := &iammock.Interface{}
	u := &user.User{UserID: "U-1234"}
	iamClient.On("GetUser", mock.Anything, mock.Anything).Return(u, nil)
	cacheStub := cache.NewClientStub()

	var gotServiceProviderID string
	var gotPartnerID string
	e := &EmailVerificationServer{
		iamClient: iamClient,
		session:   session,
		cacheRepo: &EmailVerificationCacheRepo{cacheClient: cacheStub},
		hasCustomIdentityProviderFunc: func(ctx context.Context, partnerID string, serviceProviderID string) (hasCustomIDP bool, err error) {
			gotServiceProviderID = serviceProviderID
			gotPartnerID = partnerID
			return false, nil
		},
	}

	req := httptest.NewRequest("GET", "/to-display-email-verification", nil)
	res := httptest.NewRecorder()
	ctx := iaminterceptor.SetRequestInfoForTests(t, context.Background(), &iaminterceptor.TestRequestInfo{
		OriginalCaller: &iaminterceptor.TestTokenInfoIAMIdentity{
			UserID:            u.UserID,
			ServiceProviderID: "serviceProviderID",
		},
	})
	req = req.WithContext(ctx)
	e.ShouldDisplayInterstitial().ServeHTTP(res, req)
	assert.Equal(t, 200, res.Result().StatusCode)
	result := ShouldDisplayInterstitialResult{}
	err := json.Unmarshal(res.Body.Bytes(), &result)
	assert.NoError(t, err)

	assert.True(t, result.Display)
	assert.Equal(t, "serviceProviderID", gotServiceProviderID)
	assert.Equal(t, u.GetPartnerID(), gotPartnerID)
}
