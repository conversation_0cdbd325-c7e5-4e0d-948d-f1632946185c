package emailverification

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"github.com/gorilla/sessions"
	"github.com/vendasta/IAM/sdks/go/iaminterceptor"
	iam "github.com/vendasta/IAM/sdks/go/v1"
	"github.com/vendasta/gosdks/cache"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/verrors"
	"github.com/vendasta/sso/sdks/go/sso"
)

type hasCustomIdentityProviderFunc func(ctx context.Context, partnerID string, serviceProviderID string) (hasCustomIDP bool, err error)

// nolint:golint
type EmailVerificationServer struct {
	iamClient                     iam.Interface
	session                       sessions.Store
	ssoIDPAdminClient             sso.IdentityProviderAdminClient
	cacheRepo                     *EmailVerificationCacheRepo
	hasCustomIdentityProviderFunc hasCustomIdentityProviderFunc
}

func NewEmailVerificationServer(iamClient iam.Interface, session sessions.Store, ssoIDPAdminClient sso.IdentityProviderAdminClient, cacheRepo *EmailVerificationCacheRepo) *EmailVerificationServer {
	e := &EmailVerificationServer{
		iamClient:         iamClient,
		session:           session,
		ssoIDPAdminClient: ssoIDPAdminClient,
		cacheRepo:         cacheRepo,
	}
	e.hasCustomIdentityProviderFunc = e.hasCustomIdentityProvider
	return e
}

type ShouldDisplayInterstitialResult struct {
	Display bool `json:"display"`
}

func (e *EmailVerificationServer) hasCustomIdentityProvider(ctx context.Context, partnerID string, serviceProviderID string) (hasCustomIDP bool, err error) {
	defer func() {
		logging.Tag(ctx, "has_custom_idp", fmt.Sprintf("%v", hasCustomIDP))
	}()
	hasCustomIDP, err = e.cacheRepo.Get(ctx, partnerID, serviceProviderID)
	if err == nil {
		logging.Tag(ctx, "custom_idp_cache_hit", "true")
		return hasCustomIDP, nil
	}
	logging.Tag(ctx, "custom_idp_cache_hit", "false")
	if !errors.Is(err, cache.ErrNotFound) {
		logging.Warningf(ctx, "unexpected error from cacheRepo.Get for pid %s: %s", partnerID, err.Error())
	}

	hasCustomIDP, err = e.ssoIDPAdminClient.HasCustomIdentityProvider(iaminterceptor.RemoveCallerIdentifierOnOutgoingContext(ctx), partnerID, sso.ServiceProviderID(serviceProviderID))
	if err != nil {
		return false, err
	}

	err = e.cacheRepo.Set(ctx, partnerID, serviceProviderID, hasCustomIDP)
	if err != nil {
		logging.Warningf(ctx, "failed to set custom IDP cache for pid '%s' serviceProviderID '%s': %s", partnerID, serviceProviderID, err.Error())
	}

	return hasCustomIDP, nil
}

// ShouldDisplayInterstitial looks at the user's email verification status or the timestamp in the emailVerificationCookie
//
// Handler for /to-display-email-verification
func (e *EmailVerificationServer) ShouldDisplayInterstitial() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		ctx := r.Context()
		userID := iaminterceptor.GetRequestInfo(ctx).EffectiveCallerTokenInfo().UserID()
		if userID == "" {
			logging.Infof(ctx, "UserID not present in auth token. Interstitial will not be shown")
			writeResult(ctx, w, false)
			return
		}

		u, err := e.iamClient.GetUser(iaminterceptor.RemoveCallerIdentifierOnOutgoingContext(ctx), iam.UserID(userID))
		if err != nil {
			logging.Errorf(ctx, "Error getting user %s : %s", userID, err.Error())
			err := verrors.New(verrors.InvalidArgument, "Error: Invalid userID: %s", userID)
			writeError(w, err)
			return
		}

		serviceProviderID := iaminterceptor.GetRequestInfo(ctx).EffectiveCallerTokenInfo().ServiceProviderID()
		logging.Tag(ctx, "service_provider_id", serviceProviderID)
		logging.Tag(ctx, "partner_id", u.GetPartnerID())
		logging.Tag(ctx, "user_id", userID)
		logging.Tag(ctx, "email_verified", fmt.Sprintf("%v", u.EmailVerified))

		partnerHasCustomIDPConfigured, err := e.hasCustomIdentityProviderFunc(ctx, u.GetPartnerID(), serviceProviderID)
		if err != nil {
			logging.Errorf(ctx, "Error from HasCustomIdentityProvider, assuming no custom IDP: %s", err.Error())
		}
		if partnerHasCustomIDPConfigured {
			writeResult(ctx, w, false)
			return
		}
		if u.EmailVerified {
			writeResult(ctx, w, false)
			return
		}
		session, err := e.session.Get(r, emailVerificationCookieName)
		if err != nil {
			logging.Errorf(ctx, "Error loading cookie %s : %s", emailVerificationCookieName, err)
			err := verrors.New(verrors.Internal, "Error: loading cookie")
			writeError(w, err)
			return
		}

		if session.Values[nextDisplayTimeKey(userID)] == nil {
			writeResult(ctx, w, true)
			return
		}

		nextDisplayTime, err := getNextDisplayTimeFromSession(session, userID)
		if err != nil {
			logging.Errorf(ctx, "error decoding time from session %s : %s", emailVerificationCookieName, err)
			err := verrors.New(verrors.Internal, "Error: decoding time")
			writeError(w, err)
			return
		}

		writeResult(ctx, w, time.Now().UTC().After(nextDisplayTime))
	}
}

// ExtendNextDisplayTime creates or gets the email-verification cookie
// and sets the next interstitial display time in it to the current time + extendDisplayTimeDuration.
//
// Handler for /extend-email-verification-display-date
func (e *EmailVerificationServer) ExtendNextDisplayTime() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Invalid method type. Use POST.", http.StatusMethodNotAllowed)
			return
		}
		ctx := r.Context()
		userID := iaminterceptor.GetRequestInfo(ctx).EffectiveCallerTokenInfo().UserID()

		session, err := e.session.Get(r, emailVerificationCookieName)
		if err != nil {
			logging.Errorf(ctx, "Error loading cookie %s : %s", emailVerificationCookieName, err)
			err := verrors.New(verrors.Internal, "Error: loading cookie")
			writeError(w, err)
			return
		}

		if session.Values[nextDisplayTimeKey(userID)] != nil {
			err := extendNextDisplayTimeInSession(ctx, w, r, session, userID)
			if err != nil {
				writeError(w, verrors.FromError(err))
				return
			}
		} else {
			err := initSessionValues(ctx, w, r, session, userID)
			if err != nil {
				writeError(w, verrors.FromError(err))
				return
			}
		}
	}
}

func writeResult(ctx context.Context, w http.ResponseWriter, toDisplay bool) {
	logging.Tag(ctx, "show_interstitial", fmt.Sprintf("%v", toDisplay))
	result := ShouldDisplayInterstitialResult{
		Display: toDisplay,
	}
	bodyBytes, err := json.Marshal(result)
	if err != nil {
		logging.Errorf(ctx, "Error marshalling ShouldDisplayInterstitialResult : %s", err)
		writeError(w, verrors.New(verrors.Internal, "Internal Service Error"))
		return
	}
	w.Header().Set("Content-Type", "application/json")
	_, _ = w.Write(bodyBytes)
}

func writeError(w http.ResponseWriter, err verrors.ServiceError) {
	http.Error(w, err.Error(), err.HTTPCode())
}
