package emailverification

import (
	"bytes"
	"context"
	"encoding/gob"
	"fmt"
	"time"

	"github.com/vendasta/gosdks/cache"
	"github.com/vendasta/gosdks/verrors"
)

var (
	cacheTTL = time.Minute * 10
)

// nolint:golint
type EmailVerificationCacheRepo struct {
	cacheClient cache.Client
}

func (r *EmailVerificationCacheRepo) buildKey(partnerID string, serviceProviderID string) string {
	return fmt.Sprintf("HasCustomIDP:%s:%s", partnerID, serviceProviderID)
}

func (r *EmailVerificationCacheRepo) Get(ctx context.Context, partnerID string, serviceProviderID string) (bool, error) {
	key := r.buildKey(partnerID, serviceProviderID)
	val, err := r.cacheClient.Get(ctx, key)
	if err != nil {
		return false, err
	}
	var hasCustomIDP bool
	err = gob.NewDecoder(bytes.NewBuffer(val)).Decode(&hasCustomIDP)
	if err != nil {
		return false, err
	}
	return hasCustomIDP, nil
}

func (r *EmailVerificationCacheRepo) Set(ctx context.Context, partnerID string, serviceProviderID string, hasCustomIDP bool) error {
	key := r.buildKey(partnerID, serviceProviderID)
	buf := bytes.NewBuffer(nil)
	err := gob.NewEncoder(buf).Encode(hasCustomIDP)
	if err != nil {
		return verrors.New(verrors.Internal, "failed to encode value '%t': %s", hasCustomIDP, err.Error())
	}
	return r.cacheClient.Set(ctx, key, buf.Bytes(), cacheTTL)
}

// NewRepository returns a cache repo
func NewRepository(cacheClient cache.Client) *EmailVerificationCacheRepo {
	return &EmailVerificationCacheRepo{cacheClient: cacheClient}
}
