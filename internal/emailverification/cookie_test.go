package emailverification

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func Test_extendNextDisplayTime(t *testing.T) {
	tests := []struct {
		name             string
		nowDaysBefore    int
		expectDaysBefore int
	}{
		{
			name:             "extends for 1 week by default",
			nowDaysBefore:    30,
			expectDaysBefore: 23,
		},
		{
			name:             "extends until a week before deadline when 1-2 weeks before deadline",
			nowDaysBefore:    13,
			expectDaysBefore: 7,
		},
		{
			name:             "extends for 1 day when within a week before deadline",
			nowDaysBefore:    6,
			expectDaysBefore: 5,
		},
		{
			name:             "extends for 1 day when after deadline",
			nowDaysBefore:    -1,
			expectDaysBefore: -2,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			day := 24 * time.Hour
			daysAgo := func(days int) time.Duration {
				return -day * time.Duration(days)
			}
			now := emailVerificationDeadline.Add(daysAgo(tt.nowDaysBefore))
			got := extendNextDisplayTime(now)
			expect := emailVerificationDeadline.Add(daysAgo(tt.expectDaysBefore))
			assert.Equal(t, expect, got)
		})
	}
}
