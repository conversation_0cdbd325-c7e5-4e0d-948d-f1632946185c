package api

import (
	atlas_v1 "github.com/vendasta/generated-protos-go/atlas/v1"

	"github.com/vendasta/atlas/internal/location"
	"github.com/vendasta/atlas/internal/navbardata"
	constants "github.com/vendasta/atlas/internal/navigation"
	"github.com/vendasta/atlas/internal/sidenavigation"
)

// CentersToCenterNavigationItems converts navbardata Center to api format
func CentersToCenterNavigationItems(centers []*navbardata.Center) []*atlas_v1.CenterNavigationItem {
	cNavItems := make([]*atlas_v1.CenterNavigationItem, len(centers))
	for i, c := range centers {
		cNavItems[i] = &atlas_v1.CenterNavigationItem{
			Name:     c.Name,
			EntryUrl: c.URL,
			CenterId: c.ID,
		}
	}
	return cNavItems
}

// UserItemToUserNavigationItems converts navbardata Center to api format
func UserItemToUserNavigationItems(items []*navbardata.UserItem) []*atlas_v1.UserNavigationItem {
	itm := make([]*atlas_v1.UserNavigationItem, len(items))
	for i, c := range items {
		itm[i] = &atlas_v1.UserNavigationItem{
			Text: c.Text,
			Url:  c.URL,
		}
	}
	return itm
}

// BrandingToProto converts white label data to its proto form
func BrandingToProto(b *sidenavigation.Branding) *atlas_v1.Branding {
	if b == nil {
		return nil
	}
	branding := &atlas_v1.Branding{
		Theme:              b.Theme.ToUITheme(),
		LogoUrl:            b.LogoURL,
		PartnerName:        b.PartnerName,
		CenterName:         b.BusinessCenterName,
		CobrandingLogoUrl:  b.CobrandingLogoURL,
		MarketName:         b.MarketName,
		DarkModeLogoUrl:    b.DarkModeLogoURL,
		BusinessAppUiTheme: b.BusinessAppUITheme.ToUITheme(),
	}

	// only return exit link configuration if both fields are set
	// this means that the exit link configuration will only be used when a partner has a custom sso dashboard configured within partner center
	if b.ExitLinkConfiguration.ExitLinkText != "" && b.ExitLinkConfiguration.ExitLinkURL != "" {
		branding.ExitLinkConfiguration = &atlas_v1.ExitLinkConfiguration{
			ExitLinkText: b.ExitLinkConfiguration.ExitLinkText,
			ExitLinkUrl:  b.ExitLinkConfiguration.ExitLinkURL,
		}
	}

	return branding
}

// SalesInfoToProto returns the proto form of sales info
func SalesInfoToProto(s *sidenavigation.SalesInfo) *atlas_v1.SalesInfo {
	if s == nil {
		return nil
	}
	return &atlas_v1.SalesInfo{
		MarketName: s.MarketName,
		SalesContact: &atlas_v1.SalesContact{
			SalesPersonId:     s.SalesPersonID,
			Email:             s.Email,
			FirstName:         s.FirstName,
			LastName:          s.LastName,
			PhoneNumber:       s.PhoneNumber,
			PhotoUrlSecure:    s.PhotoURL,
			JobTitle:          s.JobTitle,
			Country:           s.Country,
			MeetingBookingUrl: s.MeetingBookingURL,
		},
	}
}

// RetentionToProto returns the proto form of retention config
func RetentionToProto(r *sidenavigation.Retention) *atlas_v1.RetentionConfig {
	if r == nil {
		return nil
	}
	return &atlas_v1.RetentionConfig{
		CancellationNotificationEmail: r.CancellationNotificationEmail,
	}
}

// ItemListToPinnedItem converts a list of items to a pinned item list
func ItemListToPinnedItem(items []string) []*atlas_v1.PinnedItem {
	p := make([]*atlas_v1.PinnedItem, len(items))
	for i, item := range items {
		p[i] = &atlas_v1.PinnedItem{
			NavigationId: item,
		}
	}
	return p
}

// AccountGroupsToLocation converts a account group to a location proto
func AccountGroupsToLocation(accountGroups []*location.AccountGroup, includeSessionData bool) []*atlas_v1.Location {
	locs := make([]*atlas_v1.Location, len(accountGroups))
	for i, l := range accountGroups {
		ag := &atlas_v1.AccountGroup{
			AccountGroupId: l.AccountGroupID,
			Name:           l.Name,
			Address:        l.Address,
		}
		if includeSessionData {
			ag.ActivatedProductIds = l.ActivatedProductIDs
			ag.Url = l.URL
		}
		locs[i] = &atlas_v1.Location{
			Location: &atlas_v1.Location_AccountGroup{
				AccountGroup: ag,
			},
		}
	}
	return locs
}

// BrandsToLocation converts a slice of brands to a slice of locations protos
func BrandsToLocation(brands []*location.Brand, includeSessionData bool) []*atlas_v1.Location {
	locs := make([]*atlas_v1.Location, len(brands))
	for i, l := range brands {
		b := &atlas_v1.Brand{
			Name:      l.Name,
			PathNodes: l.PathNodes,
			HasAccess: false,
		}
		if includeSessionData {
			b.Url = l.URL
			b.HasAccess = l.HasAccess
		}
		locs[i] = &atlas_v1.Location{
			Location: &atlas_v1.Location_Brand{
				Brand: b,
			},
		}
	}
	return locs
}

func UserSwitcherDataToProto(data []*navbardata.UserSwitcherData) []*atlas_v1.UserSwitcherData {
	if len(data) == 0 {
		return nil
	}
	userSwitcherData := make([]*atlas_v1.UserSwitcherData, len(data))
	for i, d := range data {
		userSwitcherData[i] = &atlas_v1.UserSwitcherData{
			UserId:      d.UserID,
			PartnerId:   d.PartnerID,
			PartnerName: d.PartnerName,
			EntryUrl:    d.EntryURL,
		}
	}
	return userSwitcherData
}

func PlatformModeFromProto(data atlas_v1.PlatformMode) string {
	if data == atlas_v1.PlatformMode_MOBILE {
		return constants.PlatformModeMobile
	}
	return constants.PlatformModeWeb
}
