package api

import (
	"context"

	google_protobuf "github.com/golang/protobuf/ptypes/empty"
	iam "github.com/vendasta/IAM/sdks/go/v1"
	"github.com/vendasta/atlas/internal/lang"
	atlas_v1 "github.com/vendasta/generated-protos-go/atlas/v1"
	"github.com/vendasta/gosdks/statsd"
)

// PinsServer manages pinned navigation items
type LanguagesServer struct {
	svc lang.Languages
}

// NewPinsServer returns a new pins server instance
func NewLanguagesServer(svc lang.Languages) *LanguagesServer {
	return &LanguagesServer{
		svc: svc,
	}
}

// SetLanguage will remember your currently selected language
func (p *LanguagesServer) SetLanguage(ctx context.Context, req *atlas_v1.SetLanguageRequest) (*google_protobuf.Empty, error) {
	//nolint: staticcheck
	userID := iam.GetSessionUserIDFromContext(ctx)
	if userID == "" {
		// We can't do anything with these requests, and they likely reflect some odd behaviour in calls (ie. allowing AG- sessions to save their user's language
		_ = statsd.Incr("setLangWithNoUser", nil, 1)
		return &google_protobuf.Empty{}, nil
	}

	err := p.svc.SetLanguageForUserIAM(ctx, userID, req.GetLanguage())
	if err != nil {
		return nil, err
	}
	return &google_protobuf.Empty{}, nil
}

// GetLanguage will return your currently selected language
func (p *LanguagesServer) GetLanguage(ctx context.Context, req *atlas_v1.GetLanguageRequest) (*atlas_v1.GetLanguageResponse, error) {
	// This userID might be empty, but we still need to return a language for those users
	//nolint: staticcheck
	userID := iam.GetSessionUserIDFromContext(ctx)
	language, err := p.svc.GetLanguageForUser(ctx, userID)
	if err != nil {
		return nil, err
	}
	return &atlas_v1.GetLanguageResponse{
		Language: language,
	}, nil
}
