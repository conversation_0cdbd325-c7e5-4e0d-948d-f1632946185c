# API Services

## AtlasServer

This service is responsible for retrieving the data that populates the top navbar (Atlas),
and the business side navbar (Business Nav).

### Atlas Navbar
For the Atlas navbar it uses the `GetData` function to populate information.

It loads the center selector's items, which are a collection of links to SSO a user into other
areas of our platform. These items loaded into the center selector are only what the user has access to.

E.g. A partner user could access Partner Center, Vendor Center, and Sales and Success Center. An SMB
could have access to just business center. (In which case the center selector would not show up at all).

A user that has multiple subject types (e.g. a partner, an SMB, and a digital agent), will have access to
all the centers that that specific subject type would.

[![](https://mermaid.ink/img/eyJjb2RlIjoiZ3JhcGggVERcbiAgICBDbGllbnRbQnJvd3Nlcl0gLS0-fEdldERhdGF8IEFbQXRsYXMgwrVTZXJ2aWNlXVxuICAgIEEgLS0-IEJ7R2V0IGZhY2FkZSdzIGRhdGF9XG4gICAgQiAtLT58UlBDfCBDW1doaXRlbGFiZWwgRGF0YV1cbiAgICBCIC0tPnxSUEN8IERbVXNlciBJbmZvcm1hdGlvbl1cbiAgICBCIC0tPnxSUEN8IEVbU1NPIExpbmtzXVxuICAgIEIgLS0-fFJQQ3wgRlsuLi5dXG4gICIsIm1lcm1haWQiOnsidGhlbWUiOiJkZWZhdWx0In0sInVwZGF0ZUVkaXRvciI6ZmFsc2UsImF1dG9TeW5jIjp0cnVlLCJ1cGRhdGVEaWFncmFtIjpmYWxzZX0)](https://mermaid-js.github.io/mermaid-live-editor/edit##eyJjb2RlIjoiZ3JhcGggVERcbiAgICBDbGllbnRbQnJvd3Nlcl0gLS0-fEdldG5EYXRhfCBBW0F0bGFzIMK1U2VydmljZV1cbiAgICBBIC0tPiBCe0dldCBmYWNhZGUncyBkYXRhfVxuICAgIEIgLS0-fFJQQ3wgQ1tXaGl0ZWxhYmVsIERhdGFdXG4gICAgQiAtLT58UlBDfCBEW1VzZXIgSW5mb3JtYXRpb25dXG4gICAgQiAtLT58UlBDfCBFW1NTTyBMaW5rc11cbiAgICBCIC0tPnxSUEN8IEZbLi4uXVxuICAiLCJtZXJtYWlkIjoie1xuICBcInRoZW1lXCI6IFwiZGVmYXVsdFwiXG59IiwidXBkYXRlRWRpdG9yIjpmYWxzZSwiYXV0b1N5bmMiOnRydWUsInVwZGF0ZURpYWdyYW0iOmZhbHNlfQ)
```mermaid
graph TD
    Client[Browser] -->|GetData| A[Atlas µService]
    A --> B{Get facade's data}
    B -->|RPC| C[Whitelabel Data]
    B -->|RPC| D[User Information]
    B -->|RPC| E[SSO Links]
    B -->|RPC| F[...]
  
```


### Business Navbar
For the Business navbar it uses the `GetNavigationData` function to populate information.

It loads information such as that which is necessary for an SMB user to be able to navigate around the
pages in Business App, the information necessary to SSO them into the products
that are activated for their business, whitelabel information, and etc.

It is important to note that when this endpoint is called, it is always called alongside another call to
the `GetData` RPC. `GetData` is solely for populating the top navigation Atlas bar, `GetNavigationData`
is solely for populating the side business navigation bar.

[![](https://mermaid.ink/img/eyJjb2RlIjoiZ3JhcGggVERcbiAgICBDbGllbnRbQnJvd3Nlcl0gLS0-fEdldE5hdmlnYXRpb25EYXRhfCBBW0F0bGFzIMK1U2VydmljZV1cbiAgICBBIC0tPiBCe0dldCBmYWNhZGUncyBkYXRhfVxuICAgIEIgLS0-fFJQQ3wgQ1tXaGl0ZWxhYmVsIERhdGFdXG4gICAgQiAtLT58UlBDfCBEW0J1c2luZXNzIEluZm9ybWF0aW9uXVxuICAgIEIgLS0-fFJQQ3wgRVtBY3RpdmF0ZWQgUHJvZHVjdCBJbmZvcm1hdGlvbiArIFNTT11cbiAgICBCIC0tPnxSUEN8IEZbLi4uXVxuICAiLCJtZXJtYWlkIjp7InRoZW1lIjoiZGVmYXVsdCJ9LCJ1cGRhdGVFZGl0b3IiOmZhbHNlLCJhdXRvU3luYyI6dHJ1ZSwidXBkYXRlRGlhZ3JhbSI6ZmFsc2V9)](https://mermaid-js.github.io/mermaid-live-editor/edit##eyJjb2RlIjoiZ3JhcGggVERcbiAgICBDbGllbnRbQnJvd3Nlcl0gLS0-fEdldE5hdmlnYXRpb25EYXRhfCBBW0F0bGFzIMK1U2VydmljZV1cbiAgICBBIC0tPiBCe0dldCBmYWNhZGUncyBkYXRhfVxuICAgIEIgLS0-fFJQQ3wgQ1tXaGl0ZWxhYmVsIERhdGFdXG4gICAgQiAtLT58UlBDfCBEW0J1c2luZXNzIEluZm9ybWF0aW9uXVxuICAgIEIgLS0-fFJQQ3wgRVtBY3RpdmF0ZWQgUHJvZHVjdCBJbmZvcm1hdGlvbiArIFNTT11cbiAgICBCIC0tPnxSUEN8IEZbLi5dXG4gICIsIm1lcm1haWQiOiJ7XG4gIFwidGhlbWVcIjogXCJkZWZhdWx0XCJcbn0iLCJ1cGRhdGVFZGl0b3IiOmZhbHNlLCJhdXRvU3luYyI6dHJ1ZSwidXBkYXRlRGlhZ3JhbSI6ZmFsc2V9)
```mermaid
graph TD
    Client[Browser] -->|GetNavigationData| A[Atlas µService]
    A --> B{Get facade's data}
    B -->|RPC| C[Whitelabel Data]
    B -->|RPC| D[Business Information]
    B -->|RPC| E[Activated Product Information + SSO]
    B -->|RPC| F[...]
  
```

## LanguagesServer

The purpose of the languages server is to manage the user's stored choice of language to display the
platform in. This just proxies a call through to IAM right now.

## PinsServer

This server manages the user's pinned navigation items, a user can pin products to their navigation in Business
App's side navigation, as well as in the product view page in Business App.
