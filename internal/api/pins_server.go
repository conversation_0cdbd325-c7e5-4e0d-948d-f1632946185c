package api

import (
	"context"

	google_protobuf "github.com/golang/protobuf/ptypes/empty"
	iam "github.com/vendasta/IAM/sdks/go/v1"
	"github.com/vendasta/atlas/internal/pin"
	atlas_v1 "github.com/vendasta/generated-protos-go/atlas/v1"
)

// PinsServer manages pinned navigation items
type PinsServer struct {
	pins pin.Pins
}

// NewPinsServer returns a new pins server instance
func NewPinsServer(p pin.Pins) *PinsServer {
	return &PinsServer{
		pins: p,
	}
}

// SetPins will set the pins for a user in an business or brand
func (p *PinsServer) SetPins(ctx context.Context, req *atlas_v1.SetPinsRequest) (*google_protobuf.Empty, error) {
	reqItems := req.GetItems()
	items := make([]string, len(reqItems))
	for i, item := range reqItems {
		items[i] = item.GetNavigationId()
	}

	// If an impersonating user is calling set pins we will set them for the person they are impersonating
	//nolint: staticcheck
	tokenInfo := iam.GetRequestInfo(ctx).EffectiveCallerTokenInfo()
	userID := tokenInfo.UserID()
	impersonateeUserID, _ := tokenInfo.ImpersonateeUserID()
	if impersonateeUserID != "" {
		userID = impersonateeUserID
	}

	err := p.pins.SetForUser(ctx, userID, req.GetIdentifier(), items)
	if err != nil {
		return nil, err
	}

	return &google_protobuf.Empty{}, nil
}

// GetPins will return the pins for a business or brand
// nolint: staticcheck
func (p *PinsServer) GetPins(ctx context.Context, req *atlas_v1.GetPinsRequest) (*atlas_v1.GetPinsResponse, error) {
	// If an impersonating user is calling get pins we will get them for the person they are impersonating
	tokenInfo := iam.GetRequestInfo(ctx).EffectiveCallerTokenInfo()
	userID := tokenInfo.UserID()
	impersonateeUserID, _ := tokenInfo.ImpersonateeUserID()
	if impersonateeUserID != "" {
		userID = impersonateeUserID
	}
	pins, err := p.pins.GetForUser(ctx, userID, req.GetIdentifier())
	if err != nil {
		return nil, err
	}
	return &atlas_v1.GetPinsResponse{
		Items: ItemListToPinnedItem(pins),
	}, nil
}
