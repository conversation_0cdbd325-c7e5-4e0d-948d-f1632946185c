package api

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"cloud.google.com/go/pubsub"
	google_protobuf "github.com/golang/protobuf/ptypes/empty"
	"github.com/pborman/uuid"
	iam "github.com/vendasta/IAM/sdks/go/v1"
	atlas_v1 "github.com/vendasta/generated-protos-go/atlas/v1"
	partnerpb "github.com/vendasta/generated-protos-go/partner/v1"
	"github.com/vendasta/gosdks/config"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/verrors"
	partnerv2 "github.com/vendasta/partner/sdks/go/v2"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc/metadata"

	featureflags "github.com/vendasta/atlas/internal/feature_flags"
	"github.com/vendasta/atlas/internal/location"
	"github.com/vendasta/atlas/internal/navbardata"
	navigationitems "github.com/vendasta/atlas/internal/navigation"
	navigationconfig "github.com/vendasta/atlas/internal/navigation/config"
	"github.com/vendasta/atlas/internal/product"
	"github.com/vendasta/atlas/internal/sidenavigation"
)

// AtlasServer is http server for Atlas
type AtlasServer struct {
	topNav           *navbardata.Service
	sideNav          sidenavigation.Navigation
	navConfig        *navigationconfig.NavigationConfigService
	locations        location.Locations
	iam              iam.AuthService
	psClient         *pubsub.Client
	topic            *pubsub.Topic
	envName          string
	iamClient        iam.Interface
	whitelabelClient *partnerv2.WhitelabelClient
	featureClient    featureflags.Features
}

// GetData is the endpoint for retrieving data for the atlas navbar
func (s *AtlasServer) GetData(ctx context.Context, req *atlas_v1.GetDataRequest) (*atlas_v1.GetDataResponse, error) {
	ctx = setCaller(ctx)
	//nolint: staticcheck
	tokenInfo := iam.GetRequestInfo(ctx).EffectiveCallerTokenInfo()
	userID := tokenInfo.UserID()
	serviceProviderID := tokenInfo.ServiceProviderID()
	impersonatee, _ := tokenInfo.ImpersonateeUserID()
	trackCallerInfoOnLogs(ctx, req.GetPartnerId(), req.GetAccountGroupId(), userID, impersonatee)
	data, err := s.topNav.GetNavbarData(
		ctx,
		req.GetPartnerId(),
		req.GetMarketId(),
		req.GetAccountGroupId(),
		req.GetGroupPath(),
		req.GetSignOutNextUrl(),
		userID,
		impersonatee,
		serviceProviderID,
	)
	if err != nil {
		return nil, verrors.ToGrpcError(err)
	}

	res := &atlas_v1.GetDataResponse{
		Centers:              CentersToCenterNavigationItems(data.Centers),
		Username:             data.Username,
		ImpersonateeUsername: data.ImpersonateeUsername,
		Email:                data.Email,
		EmailVerified:        data.EmailVerified,
		User:                 UserItemToUserNavigationItems(data.UserItems),
		SignOutUrl:           data.SignOutURL,
		Theme:                data.Theme.ToUITheme(),
		BusinessAppUiTheme:   data.BusinessAppTheme.ToUITheme(),
		Language:             data.Language,
		NotificationsEnabled: data.NotificationsEnabled,
		UserId:               userID,
		LocationData: &atlas_v1.LocationData{
			BusinessName: data.BusinessName,
			Address:      data.Address,
		},
		UserSwitcherData: UserSwitcherDataToProto(data.UserSwitcherData),
		PartnerName:      data.PartnerName,
	}
	return res, nil
}

// GetNavigationData is the endpoint for retrieving data to populate the side navbar which wraps atlas
func (s *AtlasServer) GetNavigationData(ctx context.Context, req *atlas_v1.GetNavigationDataRequest) (*atlas_v1.GetNavigationDataResponse, error) {
	businessID := req.GetAccountGroupId()
	partnerID := req.GetPartnerId()
	groupPath := req.GetGroupPath()
	logging.Tag(ctx, "GetNavigationData.businessID", businessID)
	logging.Tag(ctx, "GetNavigationData.partnerID", partnerID)
	logging.Tag(ctx, "GetNavigationData.groupPath", groupPath)
	if (businessID == "" && groupPath == "") || (businessID != "" && groupPath != "") {
		return nil, verrors.New(verrors.InvalidArgument, "either account_group_id or brand_path must be specified")
	}
	ctx = setCaller(ctx)
	//nolint: staticcheck
	tokenInfo := iam.GetRequestInfo(ctx).EffectiveCallerTokenInfo()
	userID := tokenInfo.UserID()
	impersonateeUserID, isImpersonating := tokenInfo.ImpersonateeUserID()
	logging.Tag(ctx, "GetNavigationData.userID", userID)
	logging.Tag(ctx, "GetNavigationData.impersonateeUserID", impersonateeUserID)
	logging.Tag(ctx, "GetNavigationData.isImpersonating", fmt.Sprintf("%t", isImpersonating))

	trackCallerInfoOnLogs(ctx, partnerID, req.GetAccountGroupId(), userID, impersonateeUserID)

	platformMode := PlatformModeFromProto(req.GetPlatformMode())
	logging.Tag(ctx, "GetNavigationData.platformMode", string(platformMode))

	var (
		navigationData *sidenavigation.NavigationData
		navConfig      *navigationconfig.NavigationConfig
		partnerConfig  *partnerpb.GetConfigurationResponse
	)

	g, errCtx := errgroup.WithContext(ctx)

	// Fetch navigationData concurrently
	g.Go(func() error {
		var err error
		navigationData, err = s.sideNav.GetNavigationData(errCtx, &sidenavigation.NavigationDataArgs{
			UserID:             userID,
			ImpersonateeUserID: impersonateeUserID,
			BusinessID:         businessID,
			GroupPath:          groupPath,
			PartnerID:          partnerID,
			MarketID:           req.GetMarketId(),
			FeatureFlags: []string{
				navigationitems.PartnerFeatureIDBusinessEmails,
				navigationitems.PartnerFeatureIDMeetingScheduler,
				navigationitems.PartnerFeatureIDSingleNav,
				navigationitems.PartnerFeatureIDCRMOpportunity,
				navigationitems.PartnerFeatureIDDynamicLists,
				navigationitems.PartnerFeatureIDSalesFeatures,
				navigationitems.PartnerFeatureIDEmbeddedListings,
				navigationitems.PartnerFeatureIDEmbeddedSocialMarketing,
				navigationitems.PartnerFeatureIDEmbeddedAdIntel,
				navigationitems.PartnerFeatureIDCampaignsSMS,
				navigationitems.PartnerFeatureIDAIAssistants,
				navigationitems.PartnerFeatureIDRouteToNewIntegrations,
				navigationitems.PartnerFeatureSMBPayments,
				navigationitems.PartnerFeatureIDRepmanNPS,
				navigationitems.PartnerFeatureIDRepmanNewMLDesign,
				navigationitems.PartnerFeatureIDCRMMultilocation,
				navigationitems.PartnerKeywordTrackingOverviewFeature,
				navigationitems.CRMCustomObjectsFeatureID,
				navigationitems.PartnerFeatureIDLisAnalytics,
				navigationitems.PartnerFeatureIDRepmanBusinessAppNPS,
			},
		})
		if err != nil {
			return verrors.WrapError(err, "Error fetching navigation data").WithSourceLocation()
		}
		return nil
	})

	// Fetch navConfig concurrently
	g.Go(func() error {
		var err error
		var locationType string
		if businessID != "" {
			locationType = "singleLocation"
			if platformMode == navigationitems.PlatformModeMobile {
				locationType += "Mobile"
			}
		} else if groupPath != "" {
			locationType = "multiLocation"
		}
		navConfig, err = getNavConfig(errCtx, locationType, s, partnerID)
		if err != nil {
			return verrors.WrapError(err, "Error fetching nav config").WithSourceLocation()
		}
		return nil
	})

	// Fetch partnerConfig concurrently
	g.Go(func() error {
		var err error
		partnerConfig, err = s.whitelabelClient.GetConfiguration(errCtx, &partnerpb.GetConfigurationRequest{
			PartnerId: req.GetPartnerId(),
			MarketId:  req.GetMarketId(),
		})
		if err != nil {
			return verrors.WrapError(err, "Error fetching partner config").WithSourceLocation()
		}
		return nil
	})

	// Wait for all the data to be fetched
	if err := g.Wait(); err != nil {
		if navigationitems.VerboseLoggingEnabled(partnerID) {
			logging.Errorf(ctx, "Error fetching data: %v", err)
		}
		return nil, err
	}

	productNavs, err := s.getProductNavs(navConfig, navigationData.Products)
	if err != nil {
		return nil, err
	}

	navItems, dropdownItems, err := navigationitems.GetNavigationItems(
		ctx,
		&navigationitems.GetNavigationItemsArgs{
			SubjectID:            navigationData.SubjectID,
			WhitelabelURL:        navigationData.Branding.BusinessCenterURL,
			TabPermissions:       navigationData.TabPermissions,
			AccountGroupID:       businessID,
			GroupPath:            groupPath,
			Products:             navigationData.Products,
			IncludeBrandItems:    len(navigationData.AssociatedGroups) > 0 || groupPath != "",
			AccountsData:         navigationData.AccountData,
			Features:             navigationData.Features,
			FeatureFlags:         navigationData.FeatureFlags,
			Environment:          s.envName,
			BrandTabStatuses:     navigationData.BrandData.GetTabStatuses(),
			PinnedItems:          navigationData.PinnedItems,
			PartnerID:            req.GetPartnerId(),
			MarketID:             req.GetMarketId(),
			ReferralLink:         navigationData.ReferralLink,
			Country:              navigationData.Country,
			IsImpersonating:      isImpersonating,
			NavConfig:            navConfig.Items,
			ProductNavConfigs:    productNavs,
			EditProfileEnabled:   partnerConfig.GetConfiguration().GetBusinessCenterConfiguration().GetIsBusinessProfileEditable(),
			PlatformMode:         platformMode,
			CRMCustomObjectTypes: navigationData.CRMCustomObjectTypes,
		},
	)
	if err != nil {
		return nil, verrors.WrapError(err, "Error getting navigation items").WithSourceLocation()
	}

	userView := atlas_v1.UserViewType_USER_VIEW_TYPE_SMB
	if navigationData.ElevatedUserView {
		userView = atlas_v1.UserViewType_USER_VIEW_TYPE_ADMIN
	}

	return &atlas_v1.GetNavigationDataResponse{
		Branding:        BrandingToProto(navigationData.Branding),
		PinnedItems:     ItemListToPinnedItem(navigationData.PinnedItems),
		DefaultLocation: navigationData.DefaultLocation,
		AssociatedLocationIds: &atlas_v1.AssociatedLocationIDs{
			AccountGroupIds: navigationData.AssociatedAccountGroups,
			GroupPaths:      navigationData.AssociatedGroups,
		},
		Language:         navigationData.Language,
		DropdownItems:    dropdownItems,
		CurrentBrandName: navigationData.BrandData.GetName(),
		RetentionConfig:  RetentionToProto(navigationData.Retention),
		UserView:         userView,
		TotalLocations: &atlas_v1.TotalLocations{
			Accounts: navigationData.TotalBusinesses,
			Brands:   navigationData.TotalBrands,
		},
		UserId:                 userID,
		BusinessAppBranding:    navigationData.BusinessAppBranding,
		DisableBusinessNav:     navigationData.DisableBusinessNav,
		DisableProductSwitcher: navigationData.DisableProductSwitcher,
		NavigationItems:        navItems,
	}, nil
}

func (s *AtlasServer) GetSalesInfo(ctx context.Context, req *atlas_v1.GetSalesInfoRequest) (*atlas_v1.GetSalesInfoResponse, error) {
	businessID := req.GetAccountGroupId()
	groupPath := req.GetGroupPath()
	if (businessID == "" && groupPath == "") || (businessID != "" && groupPath != "") {
		return nil, verrors.New(verrors.InvalidArgument, "either account_group_id or brand_path must be specified")
	}
	salesInfo, err := s.sideNav.GetSalesInfo(ctx, businessID, groupPath)
	if err != nil {
		return nil, err
	}
	return &atlas_v1.GetSalesInfoResponse{
		SalesInfo: SalesInfoToProto(salesInfo),
	}, nil
}

// GetLocations returns location or brands
func (s *AtlasServer) GetLocations(ctx context.Context, req *atlas_v1.GetLocationsRequest) (*atlas_v1.GetLocationsResponse, error) {
	//nolint: staticcheck
	session, _ := iam.GetSessionFromContext(ctx)
	var locations []*atlas_v1.Location
	ctx = setCaller(ctx)
	switch req.GetIdentifier().(type) {
	case *atlas_v1.GetLocationsRequest_AccountGroups_:
		// delegate access checks to the account groups microservice
		ids := req.GetAccountGroups().GetAccountGroupIds()
		if session == "" && len(ids) > 1 {
			return nil, verrors.New(verrors.InvalidArgument, "can only specify one account group")
		}
		accountGroups, err := s.locations.GetAccountGroups(ctx, ids)
		if err != nil {
			return nil, err
		}
		locations = AccountGroupsToLocation(accountGroups, session != "")
	case *atlas_v1.GetLocationsRequest_Groups_:
		paths := req.GetGroups().GetGroupPaths()
		if session == "" && len(paths) > 1 {
			return nil, verrors.New(verrors.InvalidArgument, "can only specify one account group")
		}
		successfulPaths, err := s.iam.AccessGroups(ctx, paths)
		if err != nil {
			if len(successfulPaths) == 0 {
				logging.Errorf(ctx, "user does not have access to brands: %s", err)
				return nil, verrors.New(verrors.PermissionDenied, "permission denied")
			}
			paths = successfulPaths
		}
		brands, err := s.locations.GetBrands(ctx, paths)
		if err != nil {
			return nil, err
		}
		locations = BrandsToLocation(brands, session != "")
	default:
		logging.Errorf(ctx, "invalid location request: %v", req.GetIdentifier())
		return nil, verrors.New(verrors.InvalidArgument, "invalid location request")
	}
	return &atlas_v1.GetLocationsResponse{
		Locations: locations,
	}, nil
}

func (s *AtlasServer) ListElevatedLocations(ctx context.Context, req *atlas_v1.ListElevatedLocationsRequest) (*atlas_v1.ListElevatedLocationsResponse, error) {
	return nil, verrors.New(verrors.Gone, "Depricated: use ListLocations instead")
}

// ContactUs will send out a pubsub
func (s *AtlasServer) ContactUs(ctx context.Context, req *atlas_v1.ContactUsRequest) (*google_protobuf.Empty, error) {
	accountGroupID := req.GetAccountGroupId()
	if accountGroupID == "" {
		return nil, verrors.New(verrors.InvalidArgument, "account group id is required")
	}
	message := req.GetMessage()
	if message == "" {
		message = "Please help me find the right products for our business."
	}
	if len(message) > 500 {
		return nil, verrors.New(verrors.InvalidArgument, "message must be less than 500 characters long")
	}

	data, err := s.sideNav.GetContactUsData(ctx, accountGroupID)
	if err != nil {
		return nil, err
	}
	//nolint: staticcheck
	email, _ := iam.GetSessionEmailFromContext(ctx)
	messageData := map[string]string{
		"vbcUserId":          data.UserID,
		"_messageId":         fmt.Sprintf("vbc-%s/%s", s.envName, strings.Replace(uuid.NewUUID().String(), "-", "", -1)),
		"vbcUserLastName":    data.LastName,
		"vbcUserFirstName":   data.FirstName,
		"_publishedDateTime": time.Now().UTC().Format(time.RFC3339),
		"accountGroupId":     accountGroupID,
		"vbcUserEmail":       email,
		"marketId":           data.MarketID,
		"partnerId":          data.PartnerID,
		"message":            message,
	}
	messageByteData, err := json.Marshal(messageData)
	if err != nil {
		return nil, verrors.New(verrors.Internal, "Error in Marshalling the message : %v", err.Error())
	}
	pMessage := &pubsub.Message{Data: messageByteData}
	_, err = s.topic.Publish(ctx, pMessage).Get(ctx)
	if err != nil {
		return nil, verrors.New(verrors.Internal, "Error in publishing the message : %v", err.Error())
	}
	return &google_protobuf.Empty{}, nil
}

// GetDefaultLocation gets a user's default location that they have set for the given partner.
func (s *AtlasServer) GetDefaultLocation(ctx context.Context, req *atlas_v1.GetDefaultLocationRequest) (*atlas_v1.GetDefaultLocationResponse, error) {
	res, err := s.locations.GetDefaultLocation(ctx, req.PartnerId)
	if err != nil {
		return nil, err
	}
	if res.GroupID != "" {
		return &atlas_v1.GetDefaultLocationResponse{
			Location: &atlas_v1.GetDefaultLocationResponse_GroupId{
				GroupId: res.GroupID,
			},
		}, nil
	}
	return &atlas_v1.GetDefaultLocationResponse{
		Location: &atlas_v1.GetDefaultLocationResponse_AccountGroupId{
			AccountGroupId: res.AccountGroupID,
		},
	}, nil
}

// SetDefaultLocation sets a user's default location for the given partner.
func (s *AtlasServer) SetDefaultLocation(ctx context.Context, req *atlas_v1.SetDefaultLocationRequest) (*google_protobuf.Empty, error) {
	err := s.locations.SetDefaultLocation(ctx, req.PartnerId, req.GetAccountGroupId(), req.GetGroupId())
	if err != nil {
		return nil, err
	}
	return &google_protobuf.Empty{}, nil
}

// NewAtlasServer creates a new AtlasServer entity
func NewAtlasServer(topNav *navbardata.Service, sideNav sidenavigation.Navigation, locations location.Locations, authService iam.AuthService, psClient *pubsub.Client, iamClient iam.Interface, whitelabelClient *partnerv2.WhitelabelClient, featureClient featureflags.Features) *AtlasServer {
	t := psClient.Topic("user-general-interested")
	return &AtlasServer{
		topNav:           topNav,
		sideNav:          sideNav,
		locations:        locations,
		iam:              authService,
		psClient:         psClient,
		topic:            t,
		envName:          config.CurEnv().Name(),
		iamClient:        iamClient,
		navConfig:        navigationconfig.New(nil),
		whitelabelClient: whitelabelClient,
		featureClient:    featureClient,
	}
}

// nolint: staticcheck
func setCaller(ctx context.Context) context.Context {
	session, _ := iam.GetSessionFromContext(ctx)
	if session == "" {
		return ctx
	}
	return iam.SetCallerIdentifierOnOutgoingContext(ctx, session)
}

func trackCallerInfoOnLogs(ctx context.Context, partnerID string, agid string, userID string, impersonateeUserID string) {
	logging.Tag(ctx, "has-partner-id", fmt.Sprintf("%t", partnerID != ""))
	logging.Tag(ctx, "has-user-id", fmt.Sprintf("%t", userID != ""))
	logging.Tag(ctx, "has-impersonatee-user-id", fmt.Sprintf("%t", impersonateeUserID != ""))
	logging.Tag(ctx, "has-account-id", fmt.Sprintf("%t", agid != ""))

	md, ok := metadata.FromIncomingContext(ctx)
	if ok {
		// This is proxied in via bifrost
		origins := md.Get("Origin")
		// Should only be one of these, but it's an array so we can't be sure
		origin := strings.Join(origins, ",")
		logging.Tag(ctx, "origin", origin)
	} else {
		logging.Tag(ctx, "origin", "unknown")
	}
}

func (s *AtlasServer) ListLocations(ctx context.Context, req *atlas_v1.ListLocationsRequest) (*atlas_v1.ListLocationsResponse, error) {
	//Validate arguments
	if req.PartnerId == "" {
		return nil, verrors.New(verrors.InvalidArgument, "PartnerID is required")
	}
	if !req.IncludeAccountGroups && !req.IncludeBrands {
		return nil, verrors.New(verrors.InvalidArgument, "Must include at least one of AccountGroups, Brands")
	}
	if req.PageSize > 500 {
		//Max Page size set here arbitrarily to prevent maliciously large calls
		return nil, verrors.New(verrors.InvalidArgument, "Maximum page size exceeded")
	}

	//We convert to and from a string to allow us to switch paging methodologies without requiring a change in proto structure
	cursorInt64, err := strconv.ParseInt(req.Cursor, 10, 64)
	if err != nil {
		cursorInt64 = 0
	}
	locations, cursor, hasMore, err := s.locations.ListLocations(ctx, req.PartnerId, req.Search, cursorInt64, req.PageSize, req.IncludeAccountGroups, req.IncludeBrands)
	if err != nil {
		return nil, err
	}
	cursorString := strconv.FormatInt(cursor, 10)
	return &atlas_v1.ListLocationsResponse{
		Locations: locationsToProto(locations),
		Cursor:    cursorString,
		HasMore:   hasMore,
	}, nil
}

// Converts the internal location type to the corresponding protobuf type
func locationsToProto(locations []location.Location) []*atlas_v1.Location {
	locs := make([]*atlas_v1.Location, len(locations))
	for i, l := range locations {
		if l.IsBrand {
			ag := &atlas_v1.Brand{
				Name:      l.Name,
				PathNodes: strings.Split(l.LocationId, "|"),
				HasAccess: true,
				Url:       l.Url,
			}
			locs[i] = &atlas_v1.Location{
				Location: &atlas_v1.Location_Brand{
					Brand: ag,
				},
			}
		} else {
			ag := &atlas_v1.AccountGroup{
				AccountGroupId:      l.LocationId,
				Name:                l.Name,
				Address:             fmt.Sprintf("%v, %v, %v", l.Address, l.City, l.State),
				Url:                 l.Url,
				ActivatedProductIds: l.ActivatedProducts,
			}
			locs[i] = &atlas_v1.Location{
				Location: &atlas_v1.Location_AccountGroup{
					AccountGroup: ag,
				},
			}
		}
	}
	return locs
}

func getNavConfig(ctx context.Context, locationType string, s *AtlasServer, partnerID string) (*navigationconfig.NavigationConfig, error) {
	logging.Tag(ctx, "GetNavigationData.getNavConfig.locationType", locationType)
	var navConfig *navigationconfig.NavigationConfig
	var err error
	switch locationType {
	case "singleLocation":
		navConfig, err = s.navConfig.GetLinkConfig(partnerID)
		if err != nil || len(navConfig.Items) == 0 {
			logging.Infof(ctx, "No single location config found for partner using default")
			navConfig, err = s.navConfig.GetLinkConfig(navigationconfig.SingleLocationID)
		}
	case "multiLocation":
		navConfig, err = s.navConfig.GetLinkConfig(navigationconfig.GetMultilocationConfigID(partnerID))
		if err != nil || len(navConfig.Items) == 0 {
			logging.Infof(ctx, "No multi location config found for partner using default")
			navConfig, err = s.navConfig.GetLinkConfig(navigationconfig.MultiLocationID)
		}
	case "singleLocationMobile":
		navConfig, err = s.navConfig.GetLinkConfig(navigationconfig.SingleLocationMobileID)
	}
	if logVerbosely := navigationitems.VerboseLoggingEnabled(partnerID); logVerbosely && navConfig != nil {
		logging.Infof(ctx, "GetNavigationData.getNavConfig.navConfig.Items: %+v", navConfig.Items)
	}
	return navConfig, err
}

func (s *AtlasServer) getProductNavs(navConfig *navigationconfig.NavigationConfig, products []*product.Product) ([]*navigationconfig.NavigationConfig, error) {
	productNavIDs := make([]string, 0, len(products))
	for _, item := range products {
		navItemID := navigationitems.GetExternalProductConfigID(item.ServiceProviderID)
		if !navConfig.HasFeature(navItemID) {
			productNavIDs = append(productNavIDs, navItemID)
		}
	}

	opts := []navigationconfig.ConfigOption{
		navigationconfig.GenerateConfigIfNilOption(navigationitems.GetDefaultProductNavConfigBuilder(products)),
	}
	return s.navConfig.GetMultiLinkConfig(productNavIDs, opts...)
}
