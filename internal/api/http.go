package api

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"path/filepath"

	"github.com/vendasta/gosdks/logging"
)

// AssetsHandler provides the translation assets that are declared as consts in atlas
func AssetsHandler() http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		file := filepath.Base(r.URL.EscapedPath())
		lang, err := os.ReadFile(fmt.Sprintf("/usr/bin/locales/%s", file))
		if err == nil {
			w.Header().Add("content-type", "application/json")
			_, _ = w.Write(lang)
			return
		}
		logging.Warningf(context.Background(), "Error reading file %s: %s", file, err.Error())
		w.Write<PERSON>eader(http.StatusNotFound)
	}
}
