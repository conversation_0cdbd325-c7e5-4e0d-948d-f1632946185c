package api

import (
	"testing"

	"github.com/stretchr/testify/assert"
	atlas_v1 "github.com/vendasta/generated-protos-go/atlas/v1"

	"github.com/vendasta/atlas/internal/navbardata"
	"github.com/vendasta/atlas/internal/sidenavigation"
)

func Test_BrandingToProto(t *testing.T) {
	cases := []struct {
		name          string
		inputBranding *sidenavigation.Branding
		outputProto   *atlas_v1.Branding
	}{
		{
			name: "Happy path - converts branding to proto",
			inputBranding: &sidenavigation.Branding{
				Theme:              0,
				LogoURL:            "https://invalid.url.com/",
				PartnerName:        "<PERSON>'s Partner",
				BusinessCenterName: "VBC",
				BusinessCenterURL:  "https://vbc.com/",
			},
			outputProto: &atlas_v1.Branding{
				Theme:       0,
				LogoUrl:     "https://invalid.url.com/",
				PartnerName: "Shane's Partner",
				CenterName:  "VBC",
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			actual := BrandingToProto(c.inputBranding)
			assert.Equal(t, c.outputProto, actual)
		})
	}
}

func Test_SalesInfoToProto(t *testing.T) {
	cases := []struct {
		name           string
		inputSalesinfo *sidenavigation.SalesInfo
		outputProto    *atlas_v1.SalesInfo
	}{
		{
			name: "Happy path - converts sales info to proto",
			inputSalesinfo: &sidenavigation.SalesInfo{
				MarketName:        "#Vendasta University",
				SalesPersonID:     "U-123",
				Email:             "<EMAIL>",
				FirstName:         "Yannel",
				LastName:          "S. Justin",
				PhoneNumber:       "+1(636)-5997113",
				PhotoURL:          "mypicture.com/gsy",
				JobTitle:          "Administrator",
				Country:           "United Estates",
				MeetingBookingURL: "mycalendar.com/gsy",
			},
			outputProto: &atlas_v1.SalesInfo{
				MarketName: "#Vendasta University",
				SalesContact: &atlas_v1.SalesContact{
					SalesPersonId:     "U-123",
					Email:             "<EMAIL>",
					FirstName:         "Yannel",
					LastName:          "S. Justin",
					PhoneNumber:       "+1(636)-5997113",
					PhotoUrlSecure:    "mypicture.com/gsy",
					JobTitle:          "Administrator",
					Country:           "United Estates",
					MeetingBookingUrl: "mycalendar.com/gsy",
				},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			actual := SalesInfoToProto(c.inputSalesinfo)
			assert.Equal(t, c.outputProto, actual)
		})
	}
}

func TestUserSwitcherDataToProto(t *testing.T) {
	cases := []struct {
		name   string
		input  []*navbardata.UserSwitcherData
		output []*atlas_v1.UserSwitcherData
	}{
		{
			name: "Happy path - converts user switcher data to proto",
			input: []*navbardata.UserSwitcherData{
				{
					UserID:      "U-123",
					PartnerID:   "ABC",
					PartnerName: "ABC Partner",
				},
				{
					UserID:      "U-456",
					PartnerID:   "VUNI",
					PartnerName: "VUNI Partner",
				},
			},
			output: []*atlas_v1.UserSwitcherData{
				{
					UserId:      "U-123",
					PartnerId:   "ABC",
					PartnerName: "ABC Partner",
				},
				{
					UserId:      "U-456",
					PartnerId:   "VUNI",
					PartnerName: "VUNI Partner",
				},
			},
		},
		{
			name:   "Nil input - returns Empty output",
			input:  nil,
			output: nil,
		},
	}
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			actual := UserSwitcherDataToProto(c.input)
			assert.Equal(t, c.output, actual)
		})
	}
}
