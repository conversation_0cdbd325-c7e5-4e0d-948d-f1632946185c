package branding

import (
	"context"

	partnerProto "github.com/vendasta/generated-protos-go/partner/v1"
	partnerV2 "github.com/vendasta/partner/sdks/go/v2"
)

type Service struct {
	brandingClient *partnerV2.BrandingV2ServiceClient
}

func NewService(brandingClient *partnerV2.BrandingV2ServiceClient) *Service {
	return &Service{
		brandingClient: brandingClient,
	}
}

func (s *Service) GetBranding(ctx context.Context, partnerID string, marketID string) (*partnerProto.BrandingV2, error) {
	response, err := s.brandingClient.GetBranding(ctx, partnerID, marketID)

	if err != nil {
		return nil, err
	}
	return response, nil
}
func (s *Service) GetMultiBranding(ctx context.Context, getRequests []*partnerProto.GetBrandingV2Request) ([]*partnerProto.BrandingV2, error) {
	response, err := s.brandingClient.GetMultiBranding(ctx, getRequests)
	if err != nil {
		return nil, err
	}
	return response, nil
}
