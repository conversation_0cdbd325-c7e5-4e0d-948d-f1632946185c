// Code generated by MockGen. DO NOT EDIT.
// Source: interface.go

// Package branding is a generated GoMock package.
package branding

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	partner_v1 "github.com/vendasta/generated-protos-go/partner/v1"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// GetBranding mocks base method.
func (m *MockServiceInterface) GetBranding(ctx context.Context, partnerID, marketID string) (*partner_v1.BrandingV2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBranding", ctx, partnerID, marketID)
	ret0, _ := ret[0].(*partner_v1.BrandingV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBranding indicates an expected call of GetBranding.
func (mr *MockServiceInterfaceMockRecorder) GetBranding(ctx, partnerID, marketID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBranding", reflect.TypeOf((*MockServiceInterface)(nil).GetBranding), ctx, partnerID, marketID)
}

// GetMultiBranding mocks base method.
func (m *MockServiceInterface) GetMultiBranding(ctx context.Context, getRequests []*partner_v1.GetBrandingV2Request) ([]*partner_v1.BrandingV2, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetMultiBranding", ctx, getRequests)
	ret0, _ := ret[0].([]*partner_v1.BrandingV2)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMultiBranding indicates an expected call of GetMultiBranding.
func (mr *MockServiceInterfaceMockRecorder) GetMultiBranding(ctx, getRequests interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMultiBranding", reflect.TypeOf((*MockServiceInterface)(nil).GetMultiBranding), ctx, getRequests)
}
