package branding

import (
	atlas_v1 "github.com/vendasta/generated-protos-go/atlas/v1"
	partner_v1 "github.com/vendasta/generated-protos-go/partner/v1"
)

// Theme is a theme colour
type Theme int

const (
	// DarkTheme is a dark theme
	DarkTheme = iota
	// LightTheme is a light theme
	LightTheme
	// UserPreference uses the user's local preference
	UserPreference
)

// ToUITheme converts a theme to a ui theme
func (t Theme) ToUITheme() atlas_v1.UITheme {
	switch t {
	case LightTheme:
		return atlas_v1.UITheme_UI_THEME_LIGHT
	case UserPreference:
		return atlas_v1.UITheme_UI_THEME_USER_PREFERENCE
	default:
		return atlas_v1.UITheme_UI_THEME_DARK
	}
}

// PartnerProtoToTheme converts a partner proto to a Theme type
func PartnerProtoToTheme(brandingTheme partner_v1.UITheme) Theme {
	switch brandingTheme {
	case partner_v1.UITheme_UI_THEME_LIGHT:
		return LightTheme
	case partner_v1.UITheme_UI_THEME_USER_PREFERENCE:
		return UserPreference
	default:
		return DarkTheme
	}
}
