package branding

import (
	"context"

	partnerProto "github.com/vendasta/generated-protos-go/partner/v1"
)

//go:generate mockgen -destination=mocks.go -source=interface.go -package branding
type ServiceInterface interface {
	GetBranding(ctx context.Context, partnerID string, marketID string) (*partnerProto.BrandingV2, error)
	GetMultiBranding(ctx context.Context, getRequests []*partnerProto.GetBrandingV2Request) ([]*partnerProto.BrandingV2, error)
}
