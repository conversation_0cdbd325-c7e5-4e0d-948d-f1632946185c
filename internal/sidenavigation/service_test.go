package sidenavigation

import (
	"context"
	"encoding/json"
	"errors"
	"net/url"
	"regexp"
	"testing"

	"github.com/golang/mock/gomock"

	structpb "github.com/golang/protobuf/ptypes/struct"
	vendastatypes2 "github.com/vendasta/generated-protos-go/vendasta_types"
	tesseract "github.com/vendasta/tesseract/sdks/go/v1"

	partner_v1 "github.com/vendasta/generated-protos-go/partner/v1"

	iam "github.com/vendasta/IAM/sdks/go/v1/mocks"
	"github.com/vendasta/gosdks/verrors"

	featureflags "github.com/vendasta/atlas/internal/feature_flags"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/vendasta/atlas/internal/accountsdata"
	"github.com/vendasta/atlas/internal/branding"
	"github.com/vendasta/atlas/internal/lang"
	"github.com/vendasta/atlas/internal/location"
	"github.com/vendasta/atlas/internal/partnermarket"
	"github.com/vendasta/atlas/internal/product"
	domain "github.com/vendasta/domain/sdks/go/v2"
	partnerprotos "github.com/vendasta/generated-protos-go/partner/v1"
)

type stubResponses struct {
	brandingV1Stub     *partnerprotos.Branding
	brandingV1Error    error
	brandingV2Stub     *partnerprotos.BrandingV2
	brandingV2Error    error
	configurationStub  *partnerprotos.Configuration
	configurationError error
	dataFetcher        partnermarket.LocationDataFetcher
	salesInfoFetcher   SalesInfoFetcher
	smbFetcher         location.SMBFetcher
	domain             domain.Interface
	products           product.ProductsFetcher
	accounts           accountsdata.Accounts
	locationFetcher    location.Locations
	featureClient      featureflags.Features
	tesseractClient    tesseractFakeResponses
	disableBusinessNav bool
}

type tesseractFakeResponses struct {
	results            []string
	err                error
	expectedStatement  string
	expectedParams     map[string]interface{}
	iterErr            error
	deserializationErr error
}

func TestGetNavigationData(t *testing.T) {
	tests := []struct {
		name          string
		args          *NavigationDataArgs
		stubResponses *stubResponses
		expected      *NavigationData
		expectedError bool
	}{
		{
			name: "partner market passed in",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "ABC",
				MarketID:   "123",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:    &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error:   nil,
				configurationStub: &partnerprotos.Configuration{EnabledFeatures: []string{"feature", "co-branding-business-app-disabled", "white-labeling"}, BusinessCenterConfiguration: &partnerprotos.BusinessCenterConfiguration{CancellationNotificationEmail: "email"}, SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "",
					BusinessCenterName: "Business App",
					MarketName:         "My Market",
				},
				SubjectID:    "UID-123",
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				Features:     &Features{EnabledFeatures: []string{"feature", "co-branding-business-app-disabled", "white-labeling"}},
				FeatureFlags: nil,
				Retention:    &Retention{CancellationNotificationEmail: "email"},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "partner market not passed in",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "",
				MarketID:   "",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:    &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error:   nil,
				configurationStub: &partnerprotos.Configuration{EnabledFeatures: []string{"feature1", "feature2", "co-branding-business-app-disabled", "white-labeling"}, SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "",
					BusinessCenterName: "Business App",
					MarketName:         "My Market",
				},
				SubjectID:    "UID-123",
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				Features:     &Features{EnabledFeatures: []string{"feature1", "feature2", "co-branding-business-app-disabled", "white-labeling"}},
				FeatureFlags: nil,
				Retention:    &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "brand passed in",
			args: &NavigationDataArgs{
				BusinessID: "",
				GroupPath:  "G-123",
				PartnerID:  "",
				MarketID:   "",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:    &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error:   nil,
				configurationStub: &partnerprotos.Configuration{EnabledFeatures: []string{"feature", "co-branding-business-app-disabled", "white-labeling"}, SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID: "ABC",
						MarketID:  "123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:   &domainFake{},
				products: &productsFake{},
				accounts: &accountsFake{},
				locationFetcher: &locationsFake{
					brandsResponse: []*location.Brand{
						{
							Name:      "brand",
							PathNodes: []string{"G-123"},
							HasAccess: true,
							URL:       "",
						},
					},
				},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "",
					BusinessCenterName: "Business App",
					MarketName:         "My Market",
				},
				SubjectID: "UID-123",
				BrandData: &location.Brand{
					Name:      "brand",
					PathNodes: []string{"G-123"},
					HasAccess: true,
				},
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				Features:     &Features{EnabledFeatures: []string{"feature", "co-branding-business-app-disabled", "white-labeling"}},
				FeatureFlags: nil,
				Retention:    &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "returns whitelabeled domain",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "ABC",
				MarketID:   "123",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:    &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error:   nil,
				configurationStub: &partnerprotos.Configuration{SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "https://vstatic-prod.apigateway.co/atlas-client/assets/powered-by-us.png",
					BusinessCenterName: "Business App",
					BusinessCenterURL:  "https://wldomain.com",
					CobrandingLogoURL:  "",
					MarketName:         "My Market",
				},
				SubjectID:    "UID-123",
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				FeatureFlags: nil,
				Features:     &Features{EnabledFeatures: nil},
				Retention:    &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "returns disableBusinessNav value from partner config",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "ABC",
				MarketID:   "123",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:  &partnerprotos.Branding{},
				brandingV1Error: nil,
				configurationStub: &partnerprotos.Configuration{
					SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
					DisableBusinessNav: true,
				},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher:       &smbFetcherFake{},
				salesInfoFetcher: &salesInfoFake{},
				domain:           &domainFake{},
				products:         &productsFake{},
				accounts:         &accountsFake{},
				locationFetcher:  &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "https://vstatic-prod.apigateway.co/atlas-client/assets/powered-by-us.png",
					BusinessCenterName: "Business App",
					CobrandingLogoURL:  "",
				},
				PinnedItems:        []string(nil),
				Language:           lang.DefaultLanguage,
				FeatureFlags:       nil,
				Features:           &Features{},
				Retention:          &Retention{},
				ReferralLink:       &ReferralLink{},
				DisableBusinessNav: true,
			},
			expectedError: false,
		},
		{
			name: "returns associated account group and group ids",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "ABC",
				MarketID:   "123",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:    &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error:   nil,
				configurationStub: &partnerprotos.Configuration{SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{
					SubjectID:               "UID-123",
					AssociatedAccountGroups: []string{"AG-123", "AG-456", "AG-789"},
					GroupAssociations:       []string{"G-UNIT", "G-WILLIKERS", "G-ANOTHERONE"},
				}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "https://vstatic-prod.apigateway.co/atlas-client/assets/powered-by-us.png",
					BusinessCenterName: "Business App",
					BusinessCenterURL:  "https://wldomain.com",
					MarketName:         "My Market",
				},
				SubjectID:               "UID-123",
				PinnedItems:             []string(nil),
				Language:                lang.DefaultLanguage,
				AssociatedAccountGroups: []string{"AG-123", "AG-456", "AG-789"},
				AssociatedGroups:        []string{"G-UNIT", "G-WILLIKERS", "G-ANOTHERONE"},
				FeatureFlags:            nil,
				TotalBrands:             3,
				TotalBusinesses:         3,
				Features:                &Features{EnabledFeatures: nil},
				Retention:               &Retention{CancellationNotificationEmail: ""},
				ReferralLink:            &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "returns associated accounts data for the current business",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "ABC",
				MarketID:   "123",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:    &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error:   nil,
				configurationStub: &partnerprotos.Configuration{SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{
					SubjectID:               "UID-123",
					AssociatedAccountGroups: []string{"AG-123", "AG-456", "AG-789"},
					GroupAssociations:       []string{"G-UNIT", "G-WILLIKERS", "G-ANOTHERONE"},
				}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				products: &productsFake{},
				accounts: &accountsFake{
					response: []*accountsdata.Account{
						{
							AppID:     "RM",
							EditionID: "Foo",
							IsTrial:   true,
						},
						{
							AppID:     "SM",
							EditionID: "EDITION-5HNRPJW8",
							IsTrial:   false,
						},
					},
				},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "https://vstatic-prod.apigateway.co/atlas-client/assets/powered-by-us.png",
					BusinessCenterName: "Business App",
					BusinessCenterURL:  "https://wldomain.com",
					CobrandingLogoURL:  "",
					MarketName:         "My Market",
				},
				SubjectID:               "UID-123",
				PinnedItems:             []string(nil),
				Language:                lang.DefaultLanguage,
				AssociatedAccountGroups: []string{"AG-123", "AG-456", "AG-789"},
				AssociatedGroups:        []string{"G-UNIT", "G-WILLIKERS", "G-ANOTHERONE"},
				AccountData: []*accountsdata.Account{
					{
						AppID:     "RM",
						EditionID: "Foo",
						IsTrial:   true,
					},
					{
						AppID:     "SM",
						EditionID: "EDITION-5HNRPJW8",
						IsTrial:   false,
					},
				},
				FeatureFlags:    nil,
				TotalBusinesses: 3,
				TotalBrands:     3,
				Features:        &Features{EnabledFeatures: nil},
				Retention:       &Retention{CancellationNotificationEmail: ""},
				ReferralLink:    &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "returns associated accounts data for the current business, with nil",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "ABC",
				MarketID:   "123",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:    &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error:   nil,
				configurationStub: &partnerprotos.Configuration{SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
						BusinessName:  "brand",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{
					SubjectID:               "UID-123",
					AssociatedAccountGroups: []string{"AG-123", "AG-456", "AG-789"},
					GroupAssociations:       []string{"G-UNIT", "G-WILLIKERS", "G-ANOTHERONE"},
				}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				products: &productsFake{},
				accounts: &accountsFake{
					response: []*accountsdata.Account{
						nil,
						{
							AppID:     "SM",
							EditionID: "EDITION-5HNRPJW8",
							IsTrial:   false,
						},
					},
				},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "https://vstatic-prod.apigateway.co/atlas-client/assets/powered-by-us.png",
					BusinessCenterName: "Business App",
					BusinessCenterURL:  "https://wldomain.com",
					CobrandingLogoURL:  "",
					MarketName:         "My Market",
				},
				SubjectID:               "UID-123",
				PinnedItems:             []string(nil),
				Language:                lang.DefaultLanguage,
				AssociatedAccountGroups: []string{"AG-123", "AG-456", "AG-789"},
				AssociatedGroups:        []string{"G-UNIT", "G-WILLIKERS", "G-ANOTHERONE"},
				AccountData: []*accountsdata.Account{
					nil,
					{
						AppID:     "SM",
						EditionID: "EDITION-5HNRPJW8",
						IsTrial:   false,
					},
				},
				FeatureFlags:    nil,
				TotalBrands:     3,
				TotalBusinesses: 3,
				Features:        &Features{EnabledFeatures: nil},
				Retention:       &Retention{CancellationNotificationEmail: ""},
				ReferralLink:    &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "no location data",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "ABC",
				MarketID:   "123",
			},
			stubResponses: &stubResponses{
				configurationStub: &partnerprotos.Configuration{SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher:       &partnermarket.DataFetcherFake{},
			},
			expected:      nil,
			expectedError: true,
		},
		{
			name: "returns associated accounts data for the current business, with nil",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "ABC",
				MarketID:   "123",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:    &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error:   nil,
				configurationStub: &partnerprotos.Configuration{SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{
					SubjectID:               "UID-123",
					AssociatedAccountGroups: []string{"AG-123", "AG-456", "AG-789"},
					GroupAssociations:       []string{"G-UNIT", "G-WILLIKERS", "G-ANOTHERONE"},
				}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				products: &productsFake{},
				accounts: &accountsFake{
					response: []*accountsdata.Account{
						nil,
						{
							AppID:     "SM",
							EditionID: "EDITION-5HNRPJW8",
							IsTrial:   false,
						},
					},
				},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "https://vstatic-prod.apigateway.co/atlas-client/assets/powered-by-us.png",
					BusinessCenterName: "Business App",
					BusinessCenterURL:  "https://wldomain.com",
					CobrandingLogoURL:  "",
					MarketName:         "My Market",
				},
				SubjectID:               "UID-123",
				PinnedItems:             []string(nil),
				Language:                lang.DefaultLanguage,
				AssociatedAccountGroups: []string{"AG-123", "AG-456", "AG-789"},
				AssociatedGroups:        []string{"G-UNIT", "G-WILLIKERS", "G-ANOTHERONE"},
				AccountData: []*accountsdata.Account{
					nil,
					{
						AppID:     "SM",
						EditionID: "EDITION-5HNRPJW8",
						IsTrial:   false,
					},
				},
				FeatureFlags:    nil,
				TotalBusinesses: 3,
				TotalBrands:     3,
				Features:        &Features{EnabledFeatures: nil},
				Retention:       &Retention{CancellationNotificationEmail: ""},
				ReferralLink:    &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "feature flag is enabled",
			args: &NavigationDataArgs{
				BusinessID:   "AG-123",
				GroupPath:    "",
				PartnerID:    "ABC",
				MarketID:     "123",
				FeatureFlags: []string{"my-feature"},
			},
			stubResponses: &stubResponses{
				brandingV1Stub:    &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error:   nil,
				configurationStub: &partnerprotos.Configuration{EnabledFeatures: []string{"feature", "co-branding-business-app-disabled", "white-labeling"}, SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
				featureClient: &featureFake{
					response: map[string]bool{"my-feature": true},
				},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "",
					BusinessCenterName: "Business App",
					MarketName:         "My Market",
				},
				SubjectID:    "UID-123",
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				Features:     &Features{EnabledFeatures: []string{"feature", "co-branding-business-app-disabled", "white-labeling"}},
				FeatureFlags: map[string]bool{"my-feature": true},
				Retention:    &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "feature flag is disabled",
			args: &NavigationDataArgs{
				BusinessID:   "AG-123",
				GroupPath:    "",
				PartnerID:    "ABC",
				MarketID:     "123",
				FeatureFlags: []string{"my-feature"},
			},
			stubResponses: &stubResponses{
				brandingV1Stub:    &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error:   nil,
				configurationStub: &partnerprotos.Configuration{EnabledFeatures: []string{"feature", "co-branding-business-app-disabled", "white-labeling"}, SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
				featureClient: &featureFake{
					response: map[string]bool{"my-feature": false},
				},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "",
					BusinessCenterName: "Business App",
					MarketName:         "My Market",
				},
				SubjectID:    "UID-123",
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				Features:     &Features{EnabledFeatures: []string{"feature", "co-branding-business-app-disabled", "white-labeling"}},
				FeatureFlags: map[string]bool{"my-feature": false},
				Retention:    &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "returns white labeled logo",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "ABC",
				MarketID:   "123",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:    &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error:   nil,
				configurationStub: &partnerprotos.Configuration{EnabledFeatures: []string{}, SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "https://vstatic-prod.apigateway.co/atlas-client/assets/powered-by-us.png",
					BusinessCenterName: "Business App",
					MarketName:         "My Market",
				},
				SubjectID:    "UID-123",
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				Features:     &Features{EnabledFeatures: []string{}},
				Retention:    &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "returns co-branded logo",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "ABC",
				MarketID:   "123",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:    &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error:   nil,
				configurationStub: &partnerprotos.Configuration{EnabledFeatures: []string{"white-labeling"}, SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "",
					BusinessCenterName: "Business App",
					CobrandingLogoURL:  "https://vstatic-prod.apigateway.co/atlas-client/assets/powered-by-us.png",
					MarketName:         "My Market",
				},
				SubjectID:    "UID-123",
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				Features:     &Features{EnabledFeatures: []string{"white-labeling"}},
				Retention:    &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "returns branded logo",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "ABC",
				MarketID:   "123",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:    &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error:   nil,
				configurationStub: &partnerprotos.Configuration{EnabledFeatures: []string{"co-branding-business-app-disabled", "white-labeling"}, SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "",
					BusinessCenterName: "Business App",
					CobrandingLogoURL:  "",
					MarketName:         "My Market",
				},
				SubjectID:    "UID-123",
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				Features:     &Features{EnabledFeatures: []string{"co-branding-business-app-disabled", "white-labeling"}},
				Retention:    &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "returns referral link when available",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "ABC",
				MarketID:   "123",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:  &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error: nil,
				configurationStub: &partnerprotos.Configuration{
					EnabledFeatures:    []string{"co-branding-business-app-disabled", "white-labeling"},
					SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
					BusinessCenterConfiguration: &partnerprotos.BusinessCenterConfiguration{
						ReferralLink: &partner_v1.ReferralLink{LongUrl: "https://www.coollink.com", ShortenedUrl: "link.com/123"},
					},
				},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "",
					BusinessCenterName: "Business App",
					CobrandingLogoURL:  "",
					MarketName:         "My Market",
				},
				SubjectID:   "UID-123",
				PinnedItems: []string(nil),
				Language:    lang.DefaultLanguage,
				Features:    &Features{EnabledFeatures: []string{"co-branding-business-app-disabled", "white-labeling"}},
				Retention:   &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{
					LongURL:      "https://www.coollink.com",
					ShortenedURL: "link.com/123",
				},
			},
			expectedError: false,
		},
		{
			name: "Dark mode logo is present",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "",
				MarketID:   "",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:  &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error: nil,
				brandingV2Stub: &partnerprotos.BrandingV2{
					LogoUrl:         "Lovely light mode logo",
					DarkModeLogoUrl: "Really nice dark mode logo",
					UiTheme:         partnerprotos.UITheme_UI_THEME_DARK,
				},
				configurationStub: &partnerprotos.Configuration{EnabledFeatures: []string{"feature1", "feature2", "co-branding-business-app-disabled", "white-labeling"}, SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					LogoURL:            "Lovely light mode logo",
					DarkModeLogoURL:    "Really nice dark mode logo",
					BusinessCenterName: "Business App",
					MarketName:         "My Market",
				},
				SubjectID:    "UID-123",
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				Features:     &Features{EnabledFeatures: []string{"feature1", "feature2", "co-branding-business-app-disabled", "white-labeling"}},
				FeatureFlags: nil,
				Retention:    &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "Dark theme set in partner µservice",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "",
				MarketID:   "",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:  &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error: nil,
				brandingV2Stub: &partnerprotos.BrandingV2{
					UiTheme: partnerprotos.UITheme_UI_THEME_DARK,
				},
				configurationStub: &partnerprotos.Configuration{EnabledFeatures: []string{"feature1", "feature2", "co-branding-business-app-disabled", "white-labeling"}, SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					BusinessCenterName: "Business App",
					MarketName:         "My Market",
				},
				SubjectID:    "UID-123",
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				Features:     &Features{EnabledFeatures: []string{"feature1", "feature2", "co-branding-business-app-disabled", "white-labeling"}},
				FeatureFlags: nil,
				Retention:    &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "Light theme set in partner µservice",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "",
				MarketID:   "",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:  &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error: nil,
				brandingV2Stub: &partnerprotos.BrandingV2{
					UiTheme: partnerprotos.UITheme_UI_THEME_LIGHT,
				},
				configurationStub: &partnerprotos.Configuration{EnabledFeatures: []string{"feature1", "feature2", "co-branding-business-app-disabled", "white-labeling"}, SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.LightTheme,
					BusinessCenterName: "Business App",
					MarketName:         "My Market",
				},
				SubjectID:    "UID-123",
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				Features:     &Features{EnabledFeatures: []string{"feature1", "feature2", "co-branding-business-app-disabled", "white-labeling"}},
				FeatureFlags: nil,
				Retention:    &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "User Preference theme set in partner µservice",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "",
				MarketID:   "",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:  &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error: nil,
				brandingV2Stub: &partnerprotos.BrandingV2{
					UiTheme: partnerprotos.UITheme_UI_THEME_USER_PREFERENCE,
				},
				configurationStub: &partnerprotos.Configuration{EnabledFeatures: []string{"feature1", "feature2", "co-branding-business-app-disabled", "white-labeling"}, SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.UserPreference,
					BusinessCenterName: "Business App",
					MarketName:         "My Market",
				},
				SubjectID:    "UID-123",
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				Features:     &Features{EnabledFeatures: []string{"feature1", "feature2", "co-branding-business-app-disabled", "white-labeling"}},
				FeatureFlags: nil,
				Retention:    &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "No theme set in partner µservice results in user preference from Atlas",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "",
				MarketID:   "",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:    &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error:   nil,
				brandingV2Stub:    &partnerprotos.BrandingV2{},
				configurationStub: &partnerprotos.Configuration{EnabledFeatures: []string{"feature1", "feature2", "co-branding-business-app-disabled", "white-labeling"}, SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					BusinessCenterName: "Business App",
					MarketName:         "My Market",
				},
				SubjectID:    "UID-123",
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				Features:     &Features{EnabledFeatures: []string{"feature1", "feature2", "co-branding-business-app-disabled", "white-labeling"}},
				FeatureFlags: nil,
				Retention:    &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
		{
			name: "Invalid theme set in partner µservice results in user preference from Atlas",
			args: &NavigationDataArgs{
				BusinessID: "AG-123",
				GroupPath:  "",
				PartnerID:  "",
				MarketID:   "",
			},
			stubResponses: &stubResponses{
				brandingV1Stub:  &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error: nil,
				brandingV2Stub: &partnerprotos.BrandingV2{
					UiTheme: partnerprotos.UITheme_UI_THEME_INVALID,
				},
				configurationStub: &partnerprotos.Configuration{EnabledFeatures: []string{"feature1", "feature2", "co-branding-business-app-disabled", "white-labeling"}, SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true}},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{SalesPersonID: "SAL-123"},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &NavigationData{
				Branding: &Branding{
					Theme:              branding.DarkTheme,
					BusinessCenterName: "Business App",
					MarketName:         "My Market",
				},
				SubjectID:    "UID-123",
				PinnedItems:  []string(nil),
				Language:     lang.DefaultLanguage,
				Features:     &Features{EnabledFeatures: []string{"feature1", "feature2", "co-branding-business-app-disabled", "white-labeling"}},
				FeatureFlags: nil,
				Retention:    &Retention{CancellationNotificationEmail: ""},
				ReferralLink: &ReferralLink{},
			},
			expectedError: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)

			partnerStub := NewMockWhitelabelClient(ctrl)
			partnerStub.EXPECT().GetBranding(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(&partnerprotos.GetBrandingResponse{
					Branding: tt.stubResponses.brandingV1Stub,
				}, tt.stubResponses.brandingV1Error).AnyTimes()
			partnerStub.EXPECT().GetConfiguration(gomock.Any(), gomock.Any()).
				Return(&partner_v1.GetConfigurationResponse{
					Configuration: tt.stubResponses.configurationStub,
				}, tt.stubResponses.configurationError).AnyTimes()

			brandingStub := branding.NewMockServiceInterface(ctrl)
			brandingStub.EXPECT().GetBranding(gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.stubResponses.brandingV2Stub, tt.stubResponses.brandingV2Error).AnyTimes()

			iamStub := &iam.Interface{}
			iamStub.
				On("ListPersonas", mock.Anything, mock.Anything,
					mock.Anything, mock.Anything, mock.Anything, mock.Anything).
				Return(nil, "", false, nil)
			iamStub.On("AccessResource", mock.Anything, mock.Anything).Return(verrors.New(verrors.Canceled, "canceled"))

			pinsStub := &pinsFake{}
			languageStub := &languageFake{response: lang.DefaultLanguage}

			svc := &Service{
				partnerClient:    partnerStub,
				brandingService:  brandingStub,
				pins:             pinsStub,
				languages:        languageStub,
				dataFetcher:      tt.stubResponses.dataFetcher,
				salesInfoFetcher: tt.stubResponses.salesInfoFetcher,
				smbFetcher:       tt.stubResponses.smbFetcher,
				domainClient:     tt.stubResponses.domain,
				products:         tt.stubResponses.products,
				accounts:         tt.stubResponses.accounts,
				locationFetcher:  tt.stubResponses.locationFetcher,
				featureClient:    tt.stubResponses.featureClient,
				iamClient:        iamStub,
			}
			actual, err := svc.GetNavigationData(ctx, tt.args)
			if tt.expectedError && err == nil {
				t.Error("expected an error but got nil")
			} else if !tt.expectedError && err != nil {
				t.Errorf("did not expected an error but got: %v", err)
			}
			if tt.expected != nil {
				assert.Equal(t, tt.expected.Branding, actual.Branding)
				assert.Equal(t, tt.expected.AccountData, actual.AccountData)
			}
			assert.Equal(t, tt.expected, actual)
		})
	}
}

func Test_GetSalesInfo(t *testing.T) {
	tests := []struct {
		name          string
		businessId    string
		groupPath     string
		expected      *SalesInfo
		expectedError bool
		stubResponses *stubResponses
	}{
		{
			name:       "GetSalesInfo for account group",
			businessId: "AG-123",
			groupPath:  "",
			stubResponses: &stubResponses{
				brandingV1Stub:  &partnerprotos.Branding{Name: "My Market"},
				brandingV1Error: nil,
				configurationStub: &partnerprotos.Configuration{
					EnabledFeatures:    []string{"co-branding-business-app-disabled", "white-labeling"},
					SalesConfiguration: &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID:     "ABC",
						MarketID:      "123",
						SalesPersonID: "SAL-123",
					},
				},
				smbFetcher: &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				salesInfoFetcher: &salesInfoFake{
					response: &SalesInfo{
						SalesPersonID: "SAL-123",
						FirstName:     "Sales",
						LastName:      "Person",
						PhoneNumber:   "**********",
						Email:         "<EMAIL>",
					},
				},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &SalesInfo{
				MarketName:        "My Market",
				SalesPersonID:     "SAL-123",
				Email:             "<EMAIL>",
				FirstName:         "Sales",
				LastName:          "Person",
				PhoneNumber:       "**********",
				PhotoURL:          "",
				JobTitle:          "",
				Country:           "",
				MeetingBookingURL: "",
			},
			expectedError: false,
		},

		{
			name:       "GetSalesInfo when no salesperson is assigned",
			businessId: "AG-123",
			groupPath:  "",
			stubResponses: &stubResponses{
				brandingV1Stub: &partnerprotos.Branding{Name: "My Market"},
				configurationStub: &partnerprotos.Configuration{
					EnabledFeatures:    []string{"co-branding-business-app-disabled", "white-labeling"},
					EmailConfiguration: &partner_v1.EmailConfiguration{SenderEmail: "<EMAIL>"},
				},
				configurationError: nil,
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID: "ABC",
						MarketID:  "123",
					},
				},
				smbFetcher:      &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expected: &SalesInfo{
				Email: "<EMAIL>",
			},
			expectedError: false,
		},
		{
			name:       "Error in GetSalesInfo",
			businessId: "AG-123",
			groupPath:  "",
			stubResponses: &stubResponses{
				brandingV1Stub: &partnerprotos.Branding{Name: "My Market"},
				configurationStub: &partnerprotos.Configuration{
					EnabledFeatures:    []string{"co-branding-business-app-disabled", "white-labeling"},
					EmailConfiguration: &partner_v1.EmailConfiguration{SenderEmail: "<EMAIL>"},
				},
				configurationError: verrors.New(verrors.NotFound, "The partner id provided is not valid"),
				dataFetcher: &partnermarket.DataFetcherFake{
					Response: &partnermarket.BusinessContextData{
						PartnerID: "ABC",
						MarketID:  "123",
					},
				},
				smbFetcher:      &smbFetcherFake{response: &location.SMBData{SubjectID: "UID-123"}},
				domain:          &domainFake{},
				products:        &productsFake{},
				accounts:        &accountsFake{},
				locationFetcher: &locationsFake{},
			},
			expectedError: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)

			partnerStub := NewMockWhitelabelClient(ctrl)
			partnerStub.EXPECT().GetBranding(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(&partnerprotos.GetBrandingResponse{
					Branding: tt.stubResponses.brandingV1Stub,
				}, tt.stubResponses.brandingV1Error).AnyTimes()
			partnerStub.EXPECT().GetConfiguration(gomock.Any(), gomock.Any()).
				Return(&partner_v1.GetConfigurationResponse{
					Configuration: tt.stubResponses.configurationStub,
				}, tt.stubResponses.configurationError).AnyTimes()

			brandingStub := branding.NewMockServiceInterface(ctrl)
			brandingStub.EXPECT().GetBranding(gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.stubResponses.brandingV2Stub, tt.stubResponses.brandingV2Error).AnyTimes()

			pinsStub := &pinsFake{}
			languageStub := &languageFake{response: lang.DefaultLanguage}

			svc := &Service{
				partnerClient:    partnerStub,
				brandingService:  brandingStub,
				pins:             pinsStub,
				languages:        languageStub,
				dataFetcher:      tt.stubResponses.dataFetcher,
				salesInfoFetcher: tt.stubResponses.salesInfoFetcher,
				smbFetcher:       tt.stubResponses.smbFetcher,
				domainClient:     tt.stubResponses.domain,
				products:         tt.stubResponses.products,
				accounts:         tt.stubResponses.accounts,
				locationFetcher:  tt.stubResponses.locationFetcher,
				featureClient:    tt.stubResponses.featureClient,
				iamClient:        &iam.Interface{},
			}
			actual, err := svc.GetSalesInfo(ctx, tt.businessId, tt.groupPath)
			if tt.expectedError && err == nil {
				t.Error("expected an error but got nil")
			} else if !tt.expectedError && err != nil {
				t.Errorf("did not expected an error but got: %v", err)
			}
			if tt.expected != nil {
				assert.Equal(t, tt.expected, actual)
				//	assert.Equal(t, tt.expected., actual.AccountData)
			}
			assert.Equal(t, tt.expected, actual)
		})
	}
}
func TestWhitelabelProductNames(t *testing.T) {
	tests := []struct {
		name     string
		appNames map[string]*partnerprotos.Branding_App
		products []*product.Product
		expected []*product.Product
	}{
		{
			name: "Whitelabels app",
			appNames: map[string]*partnerprotos.Branding_App{
				"RM": &partnerprotos.Branding_App{Name: "Rep Man"},
			},
			products: []*product.Product{
				&product.Product{
					ServiceProviderID: "RM",
					Name:              "Reputation Man",
					EntryURL:          "https://repman.com/entry",
					LogoURL:           "https://repman.com/entry/icon.png",
				},
				&product.Product{
					ServiceProviderID: "MP-123",
					Name:              "App Man",
					EntryURL:          "https://appman.com/entry",
					LogoURL:           "https://appman.com/entry/icon.png",
				},
			},
			expected: []*product.Product{
				&product.Product{
					ServiceProviderID: "RM",
					Name:              "Rep Man",
					EntryURL:          "https://repman.com/entry",
					LogoURL:           "https://repman.com/entry/icon.png",
				},
				&product.Product{
					ServiceProviderID: "MP-123",
					Name:              "App Man",
					EntryURL:          "https://appman.com/entry",
					LogoURL:           "https://appman.com/entry/icon.png",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := whitelabelProductNames(tt.appNames, tt.products)
			assert.Equal(t, tt.expected, actual)
		})
	}
}

func TestBuildProductsList(t *testing.T) {
	tests := []struct {
		name            string
		accountGroupID  string
		userPermissions map[string][]string
		products        []*product.Product
		expected        []*product.Product
	}{
		{
			name:            "no permissions returns full products slice",
			accountGroupID:  "AG-123",
			userPermissions: nil,
			products: []*product.Product{
				&product.Product{ServiceProviderID: "RM"},
			},
			expected: []*product.Product{
				&product.Product{ServiceProviderID: "RM"},
			},
		},
		{
			name:            "account group in permissions list with app id returns all products",
			accountGroupID:  "AG-123",
			userPermissions: map[string][]string{"AG-123": []string{"RM", "SM"}},
			products: []*product.Product{
				&product.Product{ServiceProviderID: "RM"},
				&product.Product{ServiceProviderID: "SM"},
			},
			expected: []*product.Product{
				&product.Product{ServiceProviderID: "RM"},
				&product.Product{ServiceProviderID: "SM"},
			},
		},
		{
			name:            "app id not in permissions list should only return permission products",
			accountGroupID:  "AG-123",
			userPermissions: map[string][]string{"AG-123": []string{"RM"}},
			products: []*product.Product{
				&product.Product{ServiceProviderID: "RM"},
				&product.Product{ServiceProviderID: "SM"},
			},
			expected: []*product.Product{
				&product.Product{ServiceProviderID: "RM"},
			},
		},
		{
			name:            "account group not in permissions list should return all products",
			accountGroupID:  "AG-123",
			userPermissions: map[string][]string{"AG-456": []string{"RM"}},
			products: []*product.Product{
				&product.Product{ServiceProviderID: "RM"},
				&product.Product{ServiceProviderID: "SM"},
			},
			expected: []*product.Product{
				&product.Product{ServiceProviderID: "RM"},
				&product.Product{ServiceProviderID: "SM"},
			},
		},
		{
			name:            "no permissions returns full product list excluding expired trials",
			accountGroupID:  "AG-123",
			userPermissions: nil,
			products: []*product.Product{
				&product.Product{ServiceProviderID: "RM", IsExpiredTrial: true},
				&product.Product{ServiceProviderID: "SM"},
			},
			expected: []*product.Product{
				&product.Product{ServiceProviderID: "SM"},
			},
		},
		{
			name:            "no permissions returns full product list excluding expired trials",
			accountGroupID:  "AG-123",
			userPermissions: nil,
			products: []*product.Product{
				&product.Product{ServiceProviderID: "RM", IsExpiredTrial: true},
				&product.Product{ServiceProviderID: "SM"},
			},
			expected: []*product.Product{
				&product.Product{ServiceProviderID: "SM"},
			},
		},
		{
			name:            "app id not in permissions list should only return products that are not expired trials",
			accountGroupID:  "AG-123",
			userPermissions: map[string][]string{"AG-123": []string{"RM"}},
			products: []*product.Product{
				&product.Product{ServiceProviderID: "RM", IsExpiredTrial: true},
				&product.Product{ServiceProviderID: "SM"},
			},
			expected: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := buildProductsList(tt.accountGroupID, tt.userPermissions, tt.products)
			assert.Equal(t, tt.expected, actual)
		})
	}
}

func Test_appendProductPermissions(t *testing.T) {
	tests := []struct {
		name               string
		accountGroupID     string
		productPermissions map[string][]string
		serviceProviderID  string
		expected           map[string][]string
	}{
		{
			name:               "permissions for AGID exist and serviceProviderID is appended",
			accountGroupID:     "AG-123",
			productPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
			serviceProviderID:  "x",
			expected:           map[string][]string{"AG-123": {"RM", "SM", "x"}},
		},
		{
			name:               "permissions for AGID do not exist and serviceProviderID is not appended",
			accountGroupID:     "AG-123",
			productPermissions: map[string][]string{"AG-999": {"RM", "SM"}},
			serviceProviderID:  "x",
			expected:           map[string][]string{"AG-999": {"RM", "SM"}},
		},
		{
			name:               "permissions are nil and serviceProviderID is not appended",
			accountGroupID:     "AG-123",
			productPermissions: nil,
			serviceProviderID:  "x",
			expected:           nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := appendProductPermission(tt.productPermissions, tt.accountGroupID, tt.serviceProviderID)
			assert.Equal(t, tt.expected, actual)
		})
	}
}
func Test_buildTabPermissions(t *testing.T) {
	tests := []struct {
		name           string
		accountGroupID string
		tabPermissions map[string][]string
		expected       []string
	}{
		{
			name:           "PID has all tab permissions in one AGID",
			accountGroupID: "AG-999",
			tabPermissions: map[string][]string{"AG-123": {"customer-voice"}, "AG-456": {"new-inbox"}},
			expected:       nil,
		},
		{
			name:           "PID has permission to all tabs - bulk updater",
			accountGroupID: "AG-123",
			tabPermissions: map[string][]string{},
			expected:       nil,
		},
		{
			name:           "PID has no permissions in one AGID",
			accountGroupID: "AG-123",
			tabPermissions: map[string][]string{"AG-123": {""}, "AG-456": {"new-inbox"}},
			expected:       []string{""},
		},
		{
			name:           "PID has no permissions - bulk updater",
			accountGroupID: "AG-123",
			tabPermissions: map[string][]string{"AG-123": {""}, "AG-456": {""}},
			expected:       []string{""},
		},
		{
			name:           "PID has no permissions - start with empty list",
			accountGroupID: "AG-123",
			tabPermissions: map[string][]string{"AG-123": {}},
			expected:       []string{""},
		},
		{
			name:           "tab permissions attribute not exists for PID - access to all tabs",
			accountGroupID: "AG-123",
			expected:       nil,
		},
		{
			name:           "nil tab permissions attribute not exists for PID - access to all tabs",
			accountGroupID: "AG-123",
			tabPermissions: nil,
			expected:       nil,
		},
		{
			name:           "PID has permissions in multiple AGID",
			accountGroupID: "AG-123",
			tabPermissions: map[string][]string{"AG-123": []string{"customer-voice", "new-inbox"}, "AG-456": []string{"new-inbox"}},
			expected:       []string{"customer-voice", "new-inbox"},
		},
		{
			name:           "PID has no permissions - start with nil list",
			accountGroupID: "AG-123",
			tabPermissions: map[string][]string{"AG-123": nil},
			expected:       []string{""},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := buildTabPermissions(tt.accountGroupID, tt.tabPermissions)
			assert.Equal(t, tt.expected, actual)
		})
	}
}

func Test_getEnabledFeatures(t *testing.T) {
	tests := []struct {
		name     string
		config   *partnerprotos.Configuration
		expected *Features
	}{
		{
			name:     "no config should return nil",
			config:   nil,
			expected: nil,
		},
		{
			name:     "no features should return no features",
			config:   &partnerprotos.Configuration{},
			expected: &Features{},
		},
		{
			name: "return all features",
			config: &partnerprotos.Configuration{
				EnabledFeatures:       []string{"crm-lead-scoring"},
				HasBrandsEnabled:      true,
				HasProductMarketplace: true,
				BusinessCenterConfiguration: &partnerprotos.BusinessCenterConfiguration{
					ShowContentLibrary:          true,
					ShowLmiDashboard:            true,
					ShowExecutiveReport:         true,
					ShowStore:                   true,
					ShowFulfillment:             true,
					ShowInviteTeam:              true,
					ShowOrderPage:               true,
					ShowInvoices:                true,
					MeetingSchedulerBusinessApp: true,
					ShowFiles:                   true,
					ShowMyProducts:              true,
					ShowCustomers:               true,
					ShowCrmCompanies:            true,
					ShowCrmTasks:                true,
					ShowCrmOpportunities:        true,
					ShowDynamicLists:            true,
					ShowLeaderboard:             true,
					ShowCustomForms:             true,
					ShowAutomations:             true,
					ShowDashboard:               true,
					ShowInboxMessage:            true,
					ShowAiAssistant:             true,
					ShowLeadScoring:             true,
				},
			},
			expected: &Features{
				EnabledFeatures:       []string{"crm-lead-scoring"},
				ShowContentLibrary:    true,
				ShowLmiDashboard:      true,
				ShowExecutiveReport:   true,
				ShowStore:             true,
				ShowFulfillment:       true,
				ShowInviteTeam:        true,
				ShowInvoices:          true,
				ShowOrderPage:         true,
				ShowMeetingScheduler:  true,
				ShowFiles:             true,
				ShowMyProducts:        true,
				ShowCustomers:         true,
				ShowCRMCompanies:      true,
				ShowCRMTasks:          true,
				ShowCRMOpportunities:  true,
				ShowDynamicLists:      true,
				ShowLeaderboard:       true,
				ShowCustomForms:       true,
				ShowAutomations:       true,
				ShowHome:              true,
				ShowInboxMessage:      true,
				HasBrandsEnabled:      true,
				HasProductMarketplace: true,
				ShowAIAssistant:       true,
				ShowLeadScoring:       true,
			},
		},
		{
			name: "feature configured but not enabled should not return",
			config: &partnerprotos.Configuration{
				BusinessCenterConfiguration: &partnerprotos.BusinessCenterConfiguration{
					ShowLeadScoring: true,
				},
			},
			expected: &Features{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			svc := &Service{}
			actual := svc.getEnabledFeatures(ctx, tt.config)
			if tt.expected != nil {
				assert.Equal(t, tt.expected, actual)
			} else {
				assert.Nil(t, actual)
			}
		})
	}
}

type pinsFake struct {
	response []string
	err      error
}

func (p *pinsFake) GetForUser(ctx context.Context, userID, identifier string) ([]string, error) {
	return p.response, p.err
}

type languageFake struct {
	response string
	err      error
}

func (l *languageFake) GetLanguageForUser(ctx context.Context, userID string) (string, error) {
	return l.response, l.err
}

type salesInfoFake struct {
	response *SalesInfo
	err      error
}

func (s *salesInfoFake) GetSalesInfo(ctx context.Context, partnerID, salesPersonID string) (*SalesInfo, error) {
	return s.response, s.err
}

type smbFetcherFake struct {
	response *location.SMBData
	err      error
}

func (s *smbFetcherFake) GetSMBData(ctx context.Context, partnerID string) (*location.SMBData, error) {
	return s.response, s.err
}

type domainFake struct {
	response *domain.DomainMapping
	err      error
}

func (d *domainFake) GetDomainByIdentifier(ctx context.Context, identifier domain.Identifier) (*domain.DomainMapping, error) {
	return d.response, d.err
}

func (d *domainFake) GetIdentifierByDomain(ctx context.Context, domain string) (domain.Identifier, error) {
	return "", errors.New("not implemented")
}

type productsFake struct {
	response []*product.Product
	err      error
}

func (p *productsFake) GetProducts(ctx context.Context, partnerID string, marketID string, accountGroupID string, serviceProviderIDs []string, impersonateeUserID string) ([]*product.Product, error) {
	return p.response, p.err
}

type accountsFake struct {
	response []*accountsdata.Account
	err      error
}

type locationsFake struct {
	brandsResponse        []*location.Brand
	err                   error
	accountGroupsResponse []*location.AccountGroup
	accountGroupsErr      error
}

func (l *locationsFake) GetAccountGroups(ctx context.Context, accountGroupIDs []string) ([]*location.AccountGroup, error) {
	return l.accountGroupsResponse, l.accountGroupsErr
}

func (l *locationsFake) ListElevatedAccountGroups(ctx context.Context, partnerId string, search string, cursor string, pageSize int64) ([]*location.AccountGroup, string, bool, error) {
	return nil, "", false, l.err
}

func (l *locationsFake) GetAccountGroupCount(ctx context.Context, partnerId string) int64 {
	return 0
}

func (l *locationsFake) GetGroupCount(ctx context.Context, partnerId string) int64 {
	return 0
}

func (l *locationsFake) GetBrands(ctx context.Context, brandIDs []string) ([]*location.Brand, error) {
	return l.brandsResponse, l.err
}

func (l *locationsFake) ListElevatedBrands(ctx context.Context, partnerId string, search string, cursor string, pageSize int64) ([]*location.Brand, string, bool, error) {
	return l.brandsResponse, "", false, l.err
}

func (l *locationsFake) GetBrandWithTabEnablement(ctx context.Context, partnerID string, groupPath string) (*location.Brand, error) {
	if len(l.brandsResponse) == 0 {
		return nil, l.err
	}
	return l.brandsResponse[0], l.err
}

func (l *locationsFake) GetVBCWhitelabelURLs(ctx context.Context, partnerIDs []string) (map[string]url.URL, error) {
	return nil, l.err
}

func (a *locationsFake) GetDefaultLocation(ctx context.Context, partnerID string) (*location.DefaultLocation, error) {
	return nil, nil
}
func (a *locationsFake) SetDefaultLocation(ctx context.Context, partnerID, accountGroupID, groupID string) error {
	return nil
}

func (a *accountsFake) List(ctx context.Context, businessID, accountGroupID string) ([]*accountsdata.Account, error) {
	return a.response, a.err
}

func (a *locationsFake) ListLocations(ctx context.Context, partnerId string, search string, cursor int64, pageSize int64, includeAccountGroups bool, includeBrands bool) ([]location.Location, int64, bool, error) {
	return []location.Location{}, 0, false, nil
}

type featureFake struct {
	response map[string]bool
	err      error
}

func (f *featureFake) GetFeaturesStatus(ctx context.Context, partnerID, marketID string, featureFlags []string, accountGroupID string) (map[string]bool, error) {
	return f.response, f.err
}

type tesseractFake struct {
	results            []string
	err                error
	expectedStatement  string
	expectedParams     map[string]interface{}
	iterErr            error
	deserializationErr error
	t                  *testing.T
}

func (t tesseractFake) Query(ctx context.Context, query tesseract.Query) (tesseract.Iterator, error) {
	if t.expectedStatement != "" && query.ToProto().String() != t.expectedStatement {
		return nil, errors.New("unexpected query")
	}
	return nil, t.err
}
func (t tesseractFake) ExecuteSQL(ctx context.Context, stmt tesseract.Statement) (tesseract.ResultIterator, error) {
	whitespace := regexp.MustCompile(`\s+`)
	if t.expectedStatement != "" {
		expectedSql := whitespace.ReplaceAllString(t.expectedStatement, " ")
		actualSql := whitespace.ReplaceAllString(stmt.SQL, " ")
		assert.Equal(t.t, expectedSql, actualSql)
	}
	if t.expectedParams != nil {
		assert.Equal(t.t, t.expectedParams, stmt.Params)
	}
	return &resultIteratorFake{results: t.results, err: t.iterErr, resultErr: t.deserializationErr}, t.err
}

type resultIteratorFake struct {
	results   []string
	err       error
	resultErr error
}

func (r *resultIteratorFake) Do(f func(tesseract.Result) error) error {
	if r.err != nil {
		return r.err
	}
	for _, result := range r.results {
		err := f(tesseractResultFake{value: []byte(result), err: r.resultErr})
		if err != nil {
			return err
		}
	}
	return nil
}
func (r *resultIteratorFake) Next() (tesseract.Result, error) {
	panic("implement me")
}
func (r *resultIteratorFake) Done() error {
	panic("implement me")
}

type tesseractResultFake struct {
	value []byte
	err   error
}

func (t tesseractResultFake) ToStruct(in tesseract.T) error {
	if t.err != nil {
		return t.err
	}
	return json.Unmarshal(t.value, &in)
}
func (t tesseractResultFake) ColumnByName(name string, in tesseract.T) error {
	panic("implement me")
}
func (t tesseractResultFake) Vals() *structpb.Struct {
	panic("implement me")
}
func (t tesseractResultFake) Schema() *vendastatypes2.Schema {
	panic("implement me")
}
