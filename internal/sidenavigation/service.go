package sidenavigation

import (
	"context"
	"net/url"
	"time"

	"github.com/vendasta/atlas/internal/crmcustomobjects"

	"google.golang.org/grpc"

	iamv2 "github.com/vendasta/IAM/sdks/go/v2"

	partnerProto "github.com/vendasta/generated-protos-go/partner/v1"

	tesseract "github.com/vendasta/tesseract/sdks/go/v1"

	"github.com/vendasta/IAM/sdks/go/v1/subject"
	"github.com/vendasta/IAM/sdks/go/v1/subjectcontext"

	internaliam "github.com/vendasta/atlas/internal/iam"

	"golang.org/x/sync/errgroup"

	iam "github.com/vendasta/IAM/sdks/go/v1"
	accountgroup "github.com/vendasta/account-group/sdks/go/v1"
	"github.com/vendasta/atlas/internal/accountsdata"
	"github.com/vendasta/atlas/internal/branding"
	featureflags "github.com/vendasta/atlas/internal/feature_flags"
	"github.com/vendasta/atlas/internal/lang"
	"github.com/vendasta/atlas/internal/location"
	"github.com/vendasta/atlas/internal/partnermarket"
	"github.com/vendasta/atlas/internal/pin"
	"github.com/vendasta/atlas/internal/product"
	domain "github.com/vendasta/domain/sdks/go/v2"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/verrors"
)

//go:generate mockgen -source=service.go -destination=mocks.go -package=sidenavigation
type WhitelabelClient interface {
	GetBranding(ctx context.Context, in *partnerProto.GetBrandingRequest, opts ...grpc.CallOption) (*partnerProto.GetBrandingResponse, error)
	GetConfiguration(ctx context.Context, in *partnerProto.GetConfigurationRequest, opts ...grpc.CallOption) (*partnerProto.GetConfigurationResponse, error)
}

// Service allows for getting business navigation data
type Service struct {
	partnerClient        WhitelabelClient
	brandingService      branding.ServiceInterface
	domainClient         domain.Interface
	dataFetcher          partnermarket.LocationDataFetcher
	salesInfoFetcher     SalesInfoFetcher
	smbFetcher           location.SMBFetcher
	iamClient            iam.Interface
	pins                 pin.Getter
	languages            lang.Getter
	products             product.ProductsFetcher
	accounts             accountsdata.Accounts
	locationFetcher      location.Locations
	featureClient        featureflags.Features
	agClient             accountgroup.Interface
	tesseractClient      tesseract.Interface
	crmCustomObjectTypes crmcustomobjects.CRMCustomObjectTypesFetcher
}

// NewService makes a new instance of the Service
func NewService(
	partnerClient WhitelabelClient, brandingService branding.ServiceInterface, pins pin.Getter, iamClient iam.Interface,
	iamV2Client iamv2.ClientInterface, dataFetcher partnermarket.LocationDataFetcher, domainClient domain.Interface,
	langFetcher lang.Getter, productsFetcher product.ProductsFetcher, accounts accountsdata.Accounts,
	locations location.Locations, featureClient featureflags.Features, agClient accountgroup.Interface,
	tesseractClient tesseract.Interface, crmCustomObjectTypes crmcustomobjects.CRMCustomObjectTypesFetcher,
) *Service {
	salesInfoFetcher := NewSalesInfoFetch(iamClient, iamV2Client)
	smbFetcher := location.NewSMBFetcher(iamClient)
	return &Service{
		partnerClient:        partnerClient,
		brandingService:      brandingService,
		dataFetcher:          dataFetcher,
		pins:                 pins,
		salesInfoFetcher:     salesInfoFetcher,
		smbFetcher:           smbFetcher,
		domainClient:         domainClient,
		languages:            langFetcher,
		products:             productsFetcher,
		iamClient:            iamClient,
		accounts:             accounts,
		locationFetcher:      locations,
		featureClient:        featureClient,
		agClient:             agClient,
		tesseractClient:      tesseractClient,
		crmCustomObjectTypes: crmCustomObjectTypes,
	}
}

// GetNavigationData for the sidebar
// nolint:gocyclo,staticcheck
func (s *Service) GetNavigationData(ctx context.Context, args *NavigationDataArgs) (*NavigationData, error) {
	locationData, err := s.dataFetcher.GetLocationData(ctx, args.BusinessID, args.GroupPath)
	if err != nil {
		return nil, err
	}
	if locationData == nil {
		return nil, verrors.New(verrors.NotFound, "location locationData was not found")
	}
	eCtx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()
	eg, eCtx := errgroup.WithContext(eCtx)
	var brandingData *brandingData
	eg.Go(func() error {
		brandingData, err = s.getBranding(eCtx, locationData.PartnerID, locationData.MarketID)
		return err
	})
	var pins []string
	language := lang.DefaultLanguage
	var smbData *location.SMBData
	var elevatedUserView bool
	var totalBusinesses int64
	var totalBrands int64

	eg.Go(func() error {
		var err error
		if args.GroupPath == "" && args.BusinessID != "" {
			// If the user is impersonating then use the impersonatee's pinned navigation items instead
			userID := args.UserID
			if args.ImpersonateeUserID != "" {
				userID = args.ImpersonateeUserID
			}
			pins, err = s.pins.GetForUser(eCtx, userID, args.BusinessID)
			if err != nil {
				// An error getting the pins will not prevent the caller from getting the rest of the side nav data.
				logging.Errorf(eCtx, "failed to get pins: %s", err.Error())
			}
		}
		return nil
	})
	eg.Go(func() error {
		var err error
		language, err = s.languages.GetLanguageForUser(eCtx, args.UserID)
		return err
	})
	eg.Go(func() error {
		var err error
		smbData, err = s.smbFetcher.GetSMBData(eCtx, locationData.PartnerID)
		if err != nil {
			logging.Warningf(eCtx, "GetSMBData failed, continuing: %s", err.Error())
		}
		return nil
	})
	var whitelabelURL string
	eg.Go(func() error {
		dm, err := s.domainClient.GetDomainByIdentifier(eCtx, domain.VBCPartner(locationData.PartnerID))
		if err != nil {
			return err
		}
		whitelabelURL = buildWhiteLabelURL(dm)
		return nil
	})
	var featuresConfig *Features
	var retentionConfig *Retention
	var isSalesPerson bool
	var coBrandingLogoURL string
	var businessAppBranding = false
	var referralLink *ReferralLink
	var disableBusinessNav bool
	var disableProductSwitcher bool
	var exitLinkText string
	var exitLinkURL string

	eg.Go(func() error {
		partnerConfig, err := s.partnerClient.GetConfiguration(eCtx, &partnerProto.GetConfigurationRequest{
			PartnerId: locationData.PartnerID,
			MarketId:  locationData.MarketID,
		})
		if err != nil {
			return err
		}
		if !stringInSlice("co-branding-business-app-disabled", partnerConfig.GetConfiguration().GetEnabledFeatures()) && stringInSlice("white-labeling", partnerConfig.GetConfiguration().GetEnabledFeatures()) {
			coBrandingLogoURL = "https://vstatic-prod.apigateway.co/atlas-client/assets/powered-by-us.png"
		}

		disableBusinessNav = partnerConfig.GetConfiguration().GetDisableBusinessNav()
		disableProductSwitcher = partnerConfig.GetConfiguration().GetDisableProductSwitcher()
		featuresConfig = s.getEnabledFeatures(eCtx, partnerConfig.GetConfiguration())
		retentionConfig = s.getRetentionConfig(eCtx, partnerConfig.GetConfiguration())

		if partnerConfig.GetConfiguration().GetBusinessCenterConfiguration() != nil {
			businessAppBranding = partnerConfig.GetConfiguration().GetBusinessCenterConfiguration().GetBusinessAppBranding()
		}

		if partnerConfig.GetConfiguration().GetExitLinkConfiguration() != nil {
			exitLinkText = partnerConfig.GetConfiguration().GetExitLinkConfiguration().GetExitLinkText()
			exitLinkURL = partnerConfig.GetConfiguration().GetExitLinkConfiguration().GetExitLinkUrl()
		}

		referralLink = s.getReferralLink(partnerConfig.GetConfiguration())
		elevatedUserView = internaliam.HasElevatedAccess(
			eCtx, s.iamClient, locationData.PartnerID,
			partnerConfig.GetConfiguration().GetSalesConfiguration().GetStSalespersonSharedAccountAccess() &&
				partnerConfig.GetConfiguration().GetSalesConfiguration().GetStBusinessCenterAccess())

		if elevatedUserView {
			elevatedEg, elCtx := errgroup.WithContext(eCtx)
			elevatedEg.Go(func() error {
				totalBusinesses = s.locationFetcher.GetAccountGroupCount(elCtx, locationData.PartnerID)
				return nil
			})
			elevatedEg.Go(func() error {
				totalBrands = s.locationFetcher.GetGroupCount(elCtx, locationData.PartnerID)
				return nil
			})
			if partnerConfig.GetConfiguration().GetSalesConfiguration().GetStSalespersonSharedAccountAccess() &&
				partnerConfig.GetConfiguration().GetSalesConfiguration().GetStBusinessCenterAccess() {
				elevatedEg.Go(func() error {
					session, err := iam.GetSessionFromContext(elCtx)
					if err != nil {
						return err
					}

					salesPerson, err := s.iamClient.GetBySessionID(elCtx, subjectcontext.New(subject.SalesPersonSubjectType, locationData.PartnerID), session)
					if err != nil {
						logging.Warningf(ctx, "failed to get salesperson GetBySessionID, continuing", err.Error())
					}

					isSalesPerson = salesPerson != nil
					return nil
				})
			}
			return elevatedEg.Wait()
		}
		return nil
	})

	var featureFlags map[string]bool
	if len(args.FeatureFlags) > 0 {
		eg.Go(func() error {
			var err error
			featureFlags, err = s.featureClient.GetFeaturesStatus(eCtx, locationData.PartnerID, locationData.MarketID, args.FeatureFlags, args.BusinessID)
			return err
		})
	}
	var products []*product.Product
	var accounts []*accountsdata.Account
	var customObjectTypes []*crmcustomobjects.CRMCustomObjectType

	if args.BusinessID != "" {
		eg.Go(func() error {
			var err error
			products, err = s.products.GetProducts(eCtx, locationData.PartnerID, locationData.MarketID, args.BusinessID, locationData.ServiceProviderIDs, args.ImpersonateeUserID)
			return err
		})

		eg.Go(func() error {
			var err error
			accounts, err = s.accounts.List(eCtx, args.BusinessID, locationData.PartnerID)
			return err
		})

		eg.Go(func() error {
			var err error
			partnerConfig, err := s.partnerClient.GetConfiguration(eCtx, &partnerProto.GetConfigurationRequest{
				PartnerId: locationData.PartnerID,
				MarketId:  locationData.MarketID,
			})
			if err != nil {
				return nil
			}
			featuresConfig = s.getEnabledFeatures(eCtx, partnerConfig.GetConfiguration())
			if !featuresConfig.ShowCRMCustomObjects {
				return nil
			}
			customObjectTypes, err = s.crmCustomObjectTypes.GetCRMCustomObjectTypes(eCtx, args.PartnerID, args.MarketID, args.BusinessID)
			return err
		})
	}

	var brand *location.Brand
	if args.GroupPath != "" {
		eg.Go(func() error {
			var err error
			brand, err = s.locationFetcher.GetBrandWithTabEnablement(eCtx, locationData.PartnerID, args.GroupPath)
			return err
		})
	}
	if err := eg.Wait(); err != nil {
		logging.Errorf(ctx, "error in errgroup: %v", err)
		return nil, err
	}
	if totalBusinesses < 1 && !isSalesPerson {
		totalBusinesses = int64(len(smbData.GetAssociatedAccountGroups()))
	}
	if isSalesPerson {
		totalBusinesses = -1
	}
	if totalBrands < 1 || isSalesPerson {
		totalBrands = int64(len(smbData.GetGroupAssociations()))
	}
	var logoURL = brandingData.LogoURL
	if !stringInSlice("white-labeling", featuresConfig.EnabledFeatures) {
		logoURL = "https://vstatic-prod.apigateway.co/atlas-client/assets/powered-by-us.png"
	}

	return &NavigationData{
		Branding: &Branding{
			Theme:              brandingData.Theme,
			LogoURL:            logoURL,
			BusinessCenterName: brandingData.BusinessCenterName,
			PartnerName:        brandingData.PartnerName,
			BusinessCenterURL:  whitelabelURL,
			CobrandingLogoURL:  coBrandingLogoURL,
			MarketName:         brandingData.MarketName,
			DarkModeLogoURL:    brandingData.DarkModeLogoURL,
			BusinessAppUITheme: brandingData.BusinessAppUITheme,
			ExitLinkConfiguration: ExitLinkConfiguration{
				ExitLinkText: exitLinkText,
				ExitLinkURL:  exitLinkURL,
			},
		},
		BrandData:               brand,
		PinnedItems:             pins,
		Language:                language,
		Products:                buildProductsList(args.BusinessID, smbData.GetProductPermissions(), products),
		SubjectID:               smbData.GetSubjectID(),
		DefaultLocation:         smbData.GetDefaultAccountGroup(),
		TabPermissions:          buildTabPermissions(args.BusinessID, smbData.GetAccountTabPermissions()),
		AssociatedAccountGroups: smbData.GetAssociatedAccountGroups(),
		AssociatedGroups:        smbData.GetGroupAssociations(),
		AccountData:             accounts,
		Features:                featuresConfig,
		FeatureFlags:            featureFlags,
		Retention:               retentionConfig,
		ElevatedUserView:        elevatedUserView,
		TotalBusinesses:         totalBusinesses,
		TotalBrands:             totalBrands,
		BusinessAppBranding:     businessAppBranding,
		ReferralLink:            referralLink,
		Country:                 locationData.Country,
		DisableBusinessNav:      disableBusinessNav,
		DisableProductSwitcher:  disableProductSwitcher,
		CRMCustomObjectTypes:    customObjectTypes,
	}, nil
}

func (s *Service) GetSalesInfo(ctx context.Context, businessID, groupPath string) (*SalesInfo, error) {
	eCtx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()

	locationData, err := s.dataFetcher.GetLocationData(ctx, businessID, groupPath)
	if err != nil {
		return nil, err
	}
	var brandingData *brandingData
	var salesInfo *SalesInfo
	brandingData, err = s.getBranding(eCtx, locationData.PartnerID, locationData.MarketID)
	if err != nil {
		return nil, err
	}

	if locationData.SalesPersonID == "" {
		partner, err := s.partnerClient.GetConfiguration(eCtx, &partnerProto.GetConfigurationRequest{
			PartnerId: locationData.PartnerID,
			MarketId:  locationData.MarketID,
		})
		if err != nil {
			return nil, err
		}
		salesInfo = &SalesInfo{
			Email: partner.GetConfiguration().GetEmailConfiguration().GetSenderEmail(),
		}
	} else {
		salesInfo, err = s.salesInfoFetcher.GetSalesInfo(eCtx, locationData.PartnerID, locationData.SalesPersonID)
		if err != nil {
			return nil, err
		}
		if salesInfo != nil && brandingData != nil {
			salesInfo.MarketName = brandingData.MarketName
		}
	}
	return salesInfo, nil
}

func appendProductPermission(productPermissions map[string][]string, businessID string, serviceProviderID string) map[string][]string {
	if p, ok := productPermissions[businessID]; ok {
		productPermissions[businessID] = append(p, serviceProviderID)
	}
	return productPermissions
}

type brandingData struct {
	MarketName         string
	Theme              branding.Theme
	LogoURL            string
	PartnerName        string
	BusinessCenterName string
	WhiteLabelAppNames map[string]*partnerProto.Branding_App
	DarkModeLogoURL    string
	BusinessAppUITheme branding.Theme
}

// GetBranding returns the branding for a Partner Market
// nolint: staticcheck
func (s *Service) getBranding(ctx context.Context, partnerID, marketID string) (*brandingData, error) {
	eGroup, egCtx := errgroup.WithContext(ctx)
	var brandingV1 *partnerProto.GetBrandingResponse
	var brandingV2 *partnerProto.BrandingV2

	eGroup.Go(func() error {
		var err error
		brandingV1, err = s.partnerClient.GetBranding(egCtx, &partnerProto.GetBrandingRequest{
			PartnerId: partnerID,
			MarketId:  marketID,
		})
		return err
	})
	eGroup.Go(func() error {
		var err error
		brandingV2, err = s.brandingService.GetBranding(egCtx, partnerID, marketID)
		return err
	})
	err := eGroup.Wait()
	if err != nil {
		logging.Errorf(ctx, "error getting branding: %v", err)
		return nil, err
	}

	businessCenterName := "Business App"
	if brandingV1 != nil && brandingV1.GetBranding().GetApps() != nil && brandingV1.GetBranding().GetApps()["VBC"] != nil {
		if brandingV1.GetBranding().GetApps()["VBC"].Name != "" {
			businessCenterName = brandingV1.GetBranding().GetApps()["VBC"].Name
		}
	}

	wlData := &brandingData{
		Theme:              branding.PartnerProtoToTheme(brandingV2.GetUiTheme()),
		MarketName:         brandingV1.GetBranding().GetName(),
		BusinessCenterName: businessCenterName,
		PartnerName:        brandingV2.GetName(),
		WhiteLabelAppNames: brandingV1.GetBranding().GetApps(),
		LogoURL:            brandingV2.GetLogoUrl(),
		DarkModeLogoURL:    brandingV2.GetDarkModeLogoUrl(),
		BusinessAppUITheme: branding.PartnerProtoToTheme(brandingV2.GetBusinessAppUiTheme()),
	}
	return wlData, nil
}

func (s *Service) getEnabledFeatures(ctx context.Context, config *partnerProto.Configuration) *Features {
	if config == nil {
		return nil
	}
	f := &Features{
		EnabledFeatures:       config.EnabledFeatures,
		HasBrandsEnabled:      config.HasBrandsEnabled,
		HasProductMarketplace: config.HasProductMarketplace,
	}
	if config.BusinessCenterConfiguration != nil {
		f.ShowContentLibrary = config.BusinessCenterConfiguration.ShowContentLibrary
		f.ShowLmiDashboard = config.BusinessCenterConfiguration.ShowLmiDashboard
		f.ShowExecutiveReport = config.BusinessCenterConfiguration.ShowExecutiveReport
		f.ShowStore = config.BusinessCenterConfiguration.ShowStore
		f.ShowFulfillment = config.BusinessCenterConfiguration.ShowFulfillment
		f.ShowInviteTeam = config.BusinessCenterConfiguration.ShowInviteTeam
		f.ShowOrderPage = config.BusinessCenterConfiguration.ShowOrderPage
		f.ShowInvoices = config.BusinessCenterConfiguration.ShowInvoices
		f.ShowMeetingScheduler = config.BusinessCenterConfiguration.MeetingSchedulerBusinessApp
		f.ShowFiles = config.BusinessCenterConfiguration.ShowFiles
		f.ShowMyProducts = config.BusinessCenterConfiguration.ShowMyProducts
		f.ShowCustomers = config.BusinessCenterConfiguration.ShowCustomers
		f.ShowCRMCompanies = config.BusinessCenterConfiguration.ShowCrmCompanies
		f.ShowCRMTasks = config.BusinessCenterConfiguration.ShowCrmTasks
		f.ShowCRMOpportunities = config.BusinessCenterConfiguration.ShowCrmOpportunities
		f.ShowCRMCustomObjects = config.BusinessCenterConfiguration.ShowCrmCustomObjects
		f.ShowDynamicLists = config.BusinessCenterConfiguration.ShowDynamicLists
		f.ShowLeaderboard = config.BusinessCenterConfiguration.ShowLeaderboard
		f.ShowCustomForms = config.BusinessCenterConfiguration.ShowCustomForms
		f.ShowAutomations = config.BusinessCenterConfiguration.ShowAutomations
		f.ShowHome = config.BusinessCenterConfiguration.ShowDashboard
		f.ShowInboxMessage = config.BusinessCenterConfiguration.ShowInboxMessage
		f.ShowAIAssistant = config.BusinessCenterConfiguration.ShowAiAssistant
		f.ShowLeadScoring = config.BusinessCenterConfiguration.ShowLeadScoring && stringInSlice("crm-lead-scoring", config.EnabledFeatures)
	}
	return f
}

func stringInSlice(item string, list []string) bool {
	for _, i := range list {
		if i == item {
			return true
		}
	}
	return false
}

func (s *Service) getRetentionConfig(ctx context.Context, config *partnerProto.Configuration) *Retention {
	if config == nil {
		return nil
	}
	r := &Retention{}
	if config.BusinessCenterConfiguration != nil {
		r.CancellationNotificationEmail = config.BusinessCenterConfiguration.CancellationNotificationEmail
	}
	return r
}

func (s *Service) getReferralLink(config *partnerProto.Configuration) *ReferralLink {
	if config == nil {
		return nil
	}
	link := &ReferralLink{}
	if config.BusinessCenterConfiguration != nil && config.BusinessCenterConfiguration.ReferralLink != nil {
		link.LongURL = config.BusinessCenterConfiguration.ReferralLink.LongUrl
		link.ShortenedURL = config.BusinessCenterConfiguration.ReferralLink.ShortenedUrl
	}
	return link
}

// ContactUsData is the data returned for contact us
type ContactUsData struct {
	UserID    string
	FirstName string
	LastName  string
	MarketID  string
	PartnerID string
}

// GetContactUsData data returns the contact us data
// nolint:staticcheck
func (s *Service) GetContactUsData(ctx context.Context, accountGroupID string) (*ContactUsData, error) {
	agData, err := s.dataFetcher.GetLocationData(ctx, accountGroupID, "")
	if err != nil {
		return nil, err
	}

	session, _ := iam.GetSessionFromContext(ctx)
	smbUser, err := s.iamClient.SMBBySessionID(ctx, agData.PartnerID, session)
	if err != nil {
		return nil, err
	}

	return &ContactUsData{
		UserID:    smbUser.SubjectId,
		FirstName: smbUser.FirstName,
		LastName:  smbUser.LastName,
		MarketID:  agData.MarketID,
		PartnerID: agData.PartnerID,
	}, nil
}

func buildWhiteLabelURL(dm *domain.DomainMapping) string {
	if dm == nil || dm.Primary == nil {
		return ""
	}
	scheme := "https"
	if !dm.Primary.Secure {
		scheme = "http"
	}
	n := url.URL{
		Scheme: scheme,
		Host:   dm.Primary.Name,
	}
	return n.String()
}

func buildTabPermissions(accountGroupID string, permissions map[string][]string) []string {
	//atlas server validates accountGroupID to be valid and no empty
	//however permissions could be nil or empty when have access to all and none
	if accountGroupID == "" {
		return nil
	}
	if perm, ok := permissions[accountGroupID]; ok {
		if len(perm) == 0 {
			return []string{""}
		}
		return perm
	}
	return nil
}

func buildProductsList(accountGroupID string, userPermissions map[string][]string, products []*product.Product) []*product.Product {
	var nonExpiredProducts []*product.Product
	for _, p := range products {
		if p != nil && !p.IsExpiredTrial {
			nonExpiredProducts = append(nonExpiredProducts, p)
		}
	}
	if userPermissions == nil {
		return nonExpiredProducts
	}
	perm, ok := userPermissions[accountGroupID]
	if !ok {
		return nonExpiredProducts
	}
	productPermissions := make(map[string]struct{})
	for _, p := range perm {
		productPermissions[p] = struct{}{}
	}
	var visibleProductsList []*product.Product
	for _, p := range nonExpiredProducts {
		if p == nil {
			continue
		}
		if _, ok := productPermissions[p.ServiceProviderID]; ok {
			visibleProductsList = append(visibleProductsList, p)
		}
	}
	return visibleProductsList
}

func whitelabelProductNames(appNames map[string]*partnerProto.Branding_App, products []*product.Product) []*product.Product {
	for _, p := range products {
		if n, ok := appNames[p.ServiceProviderID]; ok {
			p.Name = n.Name
		}
	}
	return products
}
