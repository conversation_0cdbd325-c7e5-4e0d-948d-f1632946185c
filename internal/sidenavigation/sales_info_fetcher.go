package sidenavigation

import (
	"context"

	"github.com/vendasta/IAM/sdks/go/iaminterceptor"
	iam "github.com/vendasta/IAM/sdks/go/v1"
	"github.com/vendasta/IAM/sdks/go/v1/subject"
	iamv2 "github.com/vendasta/IAM/sdks/go/v2"
	iamuser "github.com/vendasta/IAM/sdks/go/v2/user"
	"github.com/vendasta/IAM/sdks/go/v2/useridentifier"
	"github.com/vendasta/gosdks/logging"
	"github.com/vendasta/gosdks/verrors"
	"golang.org/x/sync/errgroup"
)

const defaultPhoto = "https://lh3.googleusercontent.com/70DGcv8fyAzy1n_cWh8GyVbsCIWzPGZMdBFiwJa8Ot-Ss5Rf_oZY8hKOG2wAttY4_iuU5TlO9NLlDrBGuEJBj6DHwRnGd2CJ32J2Im3c1g"

// SalesInfoFetcher is an interface for fetching sales info
type SalesInfoFetcher interface {
	GetSalesInfo(ctx context.Context, partnerID, salesPersonID string) (*SalesInfo, error)
}

// SalesInfoFetch handles fetching data to inflate sales info
type SalesInfoFetch struct {
	iamClient   iam.SalesPersonAPI
	iamV2Client iamv2.ClientInterface
}

// NewSalesInfoFetch returns a new sales info fetcher
func NewSalesInfoFetch(iamClient iam.SalesPersonAPI, iamV2Client iamv2.ClientInterface) *SalesInfoFetch {
	return &SalesInfoFetch{
		iamClient:   iamClient,
		iamV2Client: iamV2Client,
	}
}

// GetSalesInfo returns sales info
func (s *SalesInfoFetch) GetSalesInfo(ctx context.Context, partnerID, salesPersonID string) (*SalesInfo, error) {
	if salesPersonID == "" || partnerID == "" {
		return nil, nil
	}
	eg, egCtx := errgroup.WithContext(ctx)

	var sp *subject.SalesPerson
	eg.Go(func() error {
		var err error
		sp, err = s.iamClient.SalesPersonBySubjectID(egCtx, partnerID, salesPersonID)
		if err != nil {
			if verrors.IsError(verrors.NotFound, err) {
				return nil
			}
			logging.Warningf(egCtx, "failed in SalesPersonBySubjectID: %s", err.Error())
		}
		return err
	})

	var iamUser *iamuser.User
	eg.Go(func() error {
		var err error
		//nolint: staticcheck
		iamUser, err = s.iamV2Client.GetUser(iaminterceptor.RemoveCallerIdentifierOnOutgoingContext(egCtx), useridentifier.SubjectID(salesPersonID))
		if err != nil {
			if verrors.IsError(verrors.NotFound, err) {
				return nil
			}
			logging.Warningf(egCtx, "failed in GetUser: %s", err.Error())
		}
		return err
	})
	if err := eg.Wait(); err != nil {
		return nil, err
	}
	if sp == nil {
		return nil, nil
	}

	salesInfo := &SalesInfo{
		SalesPersonID: sp.SubjectID(),
		Email:         sp.Email(),
		FirstName:     sp.FirstName,
		LastName:      sp.LastName,
		JobTitle:      sp.JobTitle,
		Country:       sp.Country,
	}
	if len(sp.PhoneNumbers) > 0 {
		salesInfo.PhoneNumber = sp.PhoneNumbers[0]
	}
	salesInfo.PhotoURL = profileImageURL(sp, iamUser)
	salesInfo.MeetingBookingURL = iamUser.MeetingBookingURL

	return salesInfo, nil
}

func profileImageURL(role *subject.SalesPerson, user *iamuser.User) string {
	if user.ProfileImage != "" {
		return user.ProfileImage
	}
	if role.PictureServingURLSecure != "" {
		return role.PictureServingURLSecure
	}
	if role.PictureServingURL != "" {
		return role.PictureServingURL
	}
	return defaultPhoto
}
