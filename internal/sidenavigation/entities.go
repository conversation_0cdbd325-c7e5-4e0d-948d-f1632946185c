package sidenavigation

import (
	"github.com/vendasta/atlas/internal/accountsdata"
	"github.com/vendasta/atlas/internal/branding"
	"github.com/vendasta/atlas/internal/crmcustomobjects"
	"github.com/vendasta/atlas/internal/location"
	"github.com/vendasta/atlas/internal/product"
)

// Branding contains whitelabel data for a partner market
type Branding struct {
	Theme                 branding.Theme
	LogoURL               string
	PartnerName           string
	BusinessCenterName    string
	BusinessCenterURL     string
	CobrandingLogoURL     string
	MarketName            string
	DarkModeLogoURL       string
	BusinessAppUITheme    branding.Theme
	ExitLinkConfiguration ExitLinkConfiguration
}

// SalesInfo is the info for a sales person
type SalesInfo struct {
	MarketName        string
	SalesPersonID     string
	Email             string
	FirstName         string
	LastName          string
	PhoneNumber       string
	PhotoURL          string
	JobTitle          string
	Country           string
	MeetingBookingURL string
}

type Features struct {
	EnabledFeatures       []string
	ShowContentLibrary    bool
	ShowLmiDashboard      bool
	ShowExecutiveReport   bool
	ShowStore             bool
	ShowFulfillment       bool
	ShowMarketplace       bool
	ShowInviteTeam        bool
	ShowInvoices          bool
	ShowOrderPage         bool
	ShowMeetingScheduler  bool
	ShowFiles             bool
	ShowMyProducts        bool
	ShowCustomers         bool
	ShowCRMCompanies      bool
	ShowCRMTasks          bool
	ShowCRMOpportunities  bool
	ShowCRMCustomObjects  bool
	ShowDynamicLists      bool
	ShowLeaderboard       bool
	ShowCustomForms       bool
	ShowAutomations       bool
	ShowHome              bool
	ShowInboxMessage      bool
	HasBrandsEnabled      bool
	HasProductMarketplace bool
	HasSMPostPerformance  bool
	ShowAIAssistant       bool
	ShowLeadScoring       bool
}

type ExitLinkConfiguration struct {
	ExitLinkText string
	ExitLinkURL  string
}

type Retention struct {
	CancellationNotificationEmail string
}

type ReferralLink struct {
	LongURL      string
	ShortenedURL string
}

// NavigationData contains the data to fill out the Atlas side navigation
type NavigationData struct {
	Branding                *Branding
	SubjectID               string
	BrandData               *location.Brand
	PinnedItems             []string
	AssociatedAccountGroups []string
	AssociatedGroups        []string
	AccountData             []*accountsdata.Account
	DefaultLocation         string
	TabPermissions          []string
	Language                string
	Products                []*product.Product
	Features                *Features
	FeatureFlags            map[string]bool
	Retention               *Retention
	ElevatedUserView        bool
	TotalBusinesses         int64
	TotalBrands             int64
	BusinessAppBranding     bool
	ReferralLink            *ReferralLink
	DisableBusinessNav      bool
	DisableProductSwitcher  bool
	Country                 string
	CRMCustomObjectTypes    []*crmcustomobjects.CRMCustomObjectType
}

// NavigationDataArgs are arguments for the GetNavigationData method
type NavigationDataArgs struct {
	UserID             string
	ImpersonateeUserID string
	BusinessID         string
	GroupPath          string
	PartnerID          string
	MarketID           string
	FeatureFlags       []string
	PlatformMode       string
}
