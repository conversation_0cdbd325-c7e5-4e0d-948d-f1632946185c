package iam

import (
	"context"

	iam "github.com/vendasta/IAM/sdks/go/v1"
	"github.com/vendasta/IAM/sdks/go/v1/attribute"
	iam_attributes "github.com/vendasta/generated-protos-go/iam/attributes"
	atlas_resources "github.com/vendasta/iam-resources/applications/atlas-microservice"
)

var (
	app                     = atlas_resources.AtlasMicroservice{}
	elevatedResource        = atlas_resources.ElevatedAccessResource{}
	elevatedResourceNoSales = atlas_resources.ElevatedAccessResourceNoSalespeople{}
)

func HasElevatedAccess(ctx context.Context, iamClient iam.Interface, partnerID string, salesPeople bool) bool {
	resource := elevatedResource.ResourceKind()
	if !salesPeople {
		resource = elevatedResourceNoSales.ResourceKind()
	}
	//nolint: staticcheck
	session, _ := iam.GetSessionFromContext(ctx)
	err := iamClient.AccessResource(ctx, &iam.AccessResource{
		SessionID:   session,
		OwnerID:     app.AppID(),
		ResourceID:  resource,
		AccessScope: []iam.AccessScope{iam.READ, iam.WRITE, iam.DELETE},
		ResourceAttributes: &iam_attributes.StructAttribute{Attributes: map[string]*iam_attributes.Attribute{
			"partner_id": attribute.String(partnerID),
		}},
	})
	return err == nil
}
