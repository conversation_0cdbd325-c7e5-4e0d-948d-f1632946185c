package lang

import (
	"context"

	iam "github.com/vendasta/IAM/sdks/go/v1"
	"github.com/vendasta/IAM/sdks/go/v1/user"
	"github.com/vendasta/gosdks/statsd"
	"github.com/vendasta/gosdks/verrors"
	"golang.org/x/text/language"
	"google.golang.org/grpc/metadata"
)

const DefaultLanguage = "en"

var LanguageTagMap = map[language.Tag]string{
	language.AmericanEnglish:      "en",
	language.English:              "en",
	language.CanadianFrench:       "fr-ca",
	language.French:               "fr-ca",
	language.Czech:                "cs",
	language.BrazilianPortuguese:  "pt",
	language.Portuguese:           "pt",
	language.Dutch:                "nl",
	language.German:               "de",
	language.Russian:              "ru",
	language.Afrikaans:            "af",
	language.LatinAmericanSpanish: "es-419",
	language.Italian:              "it",
	language.Make("fr-FR"):        "fr-fr",
	language.Make("cs-CZ"):        "cs",
}

type UserAPI interface {
	GetUser(ctx context.Context, identifier iam.UserIdentifier) (*user.User, error)
	UpdateUser(ctx context.Context, userID string, operations ...iam.UserUpdateOperation) error
}

// Service is the pin service implementation
type Service struct {
	iamUserAPI UserAPI
	matcher    language.Matcher
}

// NewService returns a new language service
func NewService(iamUserAPI UserAPI) *Service {
	return &Service{
		iamUserAPI: iamUserAPI,
		matcher: language.NewMatcher([]language.Tag{
			// Do not reorder this list!
			language.AmericanEnglish,
			language.English,
			language.CanadianFrench,
			language.French,
			language.Czech,
			language.BrazilianPortuguese,
			language.Portuguese,
			language.Dutch,
			language.German,
			language.Russian,
			language.Afrikaans,
			language.LatinAmericanSpanish,
			language.Italian,
			language.Make("fr-FR"),
			language.Make("cs-CZ"),
		}),
	}
}

// SetLanguageForUserIAM will set the language for the user in IAM, used to store inference from user's requests
func (s *Service) SetLanguageForUserIAM(ctx context.Context, userID, languageCode string) error {
	return s.iamUserAPI.UpdateUser(ctx, userID, iam.SetLanguageCode(languageCode))
}

// GetLanguage returns the language for the optional user. If a user is provided and they do not have a language,
// inference will be made based on browser headers and saved on the user in the background.
func (s *Service) GetLanguageForUser(ctx context.Context, userID string) (string, error) {
	// Prefer to get from userID, only warn on cant or error. continue if none
	if userID != "" {
		lang, err := s.GetLanguageForUserIAM(ctx, userID)
		if err != nil {
			return "", err
		}
		if lang != "" {
			return s.getBestLanguage(ctx, lang)
		}
	} else {
		_ = statsd.Incr("getLangNoUser", nil, 1)
	}
	// infer from context
	languageFromCtx, err := s.getBestLanguageFromContext(ctx)
	if err != nil || languageFromCtx == "" {
		return DefaultLanguage, nil
	}
	return languageFromCtx, nil
}

// GetLanguageForUserIAM gets the language for the user from IAM
func (s *Service) GetLanguageForUserIAM(ctx context.Context, userID string) (string, error) {
	usr, err := s.iamUserAPI.GetUser(ctx, iam.UserID(userID))
	if err != nil {
		return "", err
	}
	return usr.GetLanguageCode(), nil
}

func (s *Service) getBestLanguageFromContext(ctx context.Context) (string, error) {
	headers, ok := metadata.FromIncomingContext(ctx)
	if !ok {
		return "", verrors.New(verrors.Unavailable, "unable to get headers from context")
	}
	acceptLanguageResults := headers.Get("accept-language")
	if len(acceptLanguageResults) > 0 {
		for _, lang := range acceptLanguageResults {
			tag, _ := language.MatchStrings(s.matcher, lang)
			if bestLang, ok := LanguageTagMap[tag]; ok {
				return bestLang, nil
			}
		}
	}
	return "", nil
}

func (s *Service) getBestLanguage(ctx context.Context, languageCode string) (string, error) {
	tag, _ := language.MatchStrings(s.matcher, languageCode)
	if bestLang, ok := LanguageTagMap[tag]; ok {
		return bestLang, nil
	}
	return DefaultLanguage, nil
}
