package lang

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	iam "github.com/vendasta/IAM/sdks/go/v1"
	"github.com/vendasta/IAM/sdks/go/v1/user"
	"google.golang.org/grpc/metadata"
)

func TestService_getBestLanguageFromContext(t *testing.T) {
	type testcase struct {
		name                 string
		acceptLanguageHeader string
		expectedLanguage     string
		expectedError        error
	}
	cases := []testcase{
		{
			name:                 "matches en",
			acceptLanguageHeader: "en",
			expectedLanguage:     "en",
			expectedError:        nil,
		},
		{
			name:                 "matches en-US",
			acceptLanguageHeader: "en-US",
			expectedLanguage:     "en",
			expectedError:        nil,
		},
		{
			name:                 "matches en-US with fallback",
			acceptLanguageHeader: "en-US;en",
			expectedLanguage:     "en",
			expectedError:        nil,
		},
		{
			name:                 "matches fallback",
			acceptLanguageHeader: "mx-MX;en-US",
			expectedLanguage:     "en",
			expectedError:        nil,
		},
		{
			name:                 "matches fr",
			acceptLanguageHeader: "fr",
			expectedLanguage:     "fr-ca",
			expectedError:        nil,
		},
		{
			name:                 "matches cs-CZ",
			acceptLanguageHeader: "cs-CZ",
			expectedLanguage:     "cs",
			expectedError:        nil,
		},
		{
			name:                 "matches pt-BR",
			acceptLanguageHeader: "pt-BR",
			expectedLanguage:     "pt",
			expectedError:        nil,
		},
		{
			name:                 "matches fr-FR",
			acceptLanguageHeader: "fr-FR",
			expectedLanguage:     "fr-fr",
			expectedError:        nil,
		},
		{
			name:                 "matches fr-fr",
			acceptLanguageHeader: "fr-fr",
			expectedLanguage:     "fr-fr",
			expectedError:        nil,
		},
		{
			name:                 "matches nl",
			acceptLanguageHeader: "nl",
			expectedLanguage:     "nl",
			expectedError:        nil,
		},
		{
			name:                 "matches de",
			acceptLanguageHeader: "de",
			expectedLanguage:     "de",
			expectedError:        nil,
		},
		{
			name:                 "returns default language from non-matching locale",
			acceptLanguageHeader: "mx-MX",
			expectedLanguage:     DefaultLanguage,
			expectedError:        nil,
		},
		{
			name:                 "matches es-419",
			acceptLanguageHeader: "es-419",
			expectedLanguage:     "es-419",
			expectedError:        nil,
		},
	}
	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := metadata.NewIncomingContext(context.Background(), metadata.New(map[string]string{
				"Accept-Language": c.acceptLanguageHeader,
			}))

			s := NewService(nil)
			lang, err := s.getBestLanguageFromContext(ctx)
			assert.Equal(t, c.expectedLanguage, lang)
			assert.Equal(t, c.expectedError, err)
		})
	}
}

type userApiStub struct {
	GetUserFunc func(ctx context.Context, identifier iam.UserIdentifier) (*user.User, error)
}

func (m *userApiStub) GetUser(ctx context.Context, identifier iam.UserIdentifier) (*user.User, error) {
	if m.GetUserFunc != nil {
		return m.GetUserFunc(ctx, identifier)
	}
	return nil, nil
}
func (m *userApiStub) UpdateUser(ctx context.Context, userID string, operations ...iam.UserUpdateOperation) error {
	panic("not implemented")
}

func TestService_GetLanguageForUser(t *testing.T) {
	type fields struct {
		iamUserAPI UserAPI
	}
	type args struct {
		ctx    context.Context
		userID string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "handles es-419 on user",
			fields: fields{
				iamUserAPI: &userApiStub{
					GetUserFunc: func(ctx context.Context, identifier iam.UserIdentifier) (*user.User, error) {
						return &user.User{
							LanguageCode: "es-419",
						}, nil
					},
				},
			},
			args: args{
				ctx:    context.Background(),
				userID: "UID-123",
			},
			want:    "es-419",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := NewService(tt.fields.iamUserAPI)
			got, err := s.GetLanguageForUser(tt.args.ctx, tt.args.userID)
			if (err != nil) != tt.wantErr {
				t.Errorf("Service.GetLanguageForUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("Service.GetLanguageForUser() = %v, want %v", got, tt.want)
			}
		})
	}
}
