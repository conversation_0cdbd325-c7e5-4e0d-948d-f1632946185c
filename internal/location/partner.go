package location

import (
	accountgroup "github.com/vendasta/account-group/sdks/go/v1"
	group "github.com/vendasta/group/sdks/go/v2"
)

func getPartnerIDsFromAccountGroups(accountGroups []*accountgroup.AccountGroup) []string {
	if accountGroups == nil {
		return nil
	}
	known := make(map[string]struct{})
	var partnerIDs []string
	for _, a := range accountGroups {
		if a == nil || a.ExternalIdentifiers == nil {
			continue
		}
		if _, ok := known[a.PartnerID]; ok {
			continue
		}
		known[a.PartnerID] = struct{}{}
		partnerIDs = append(partnerIDs, a.PartnerID)
	}
	return partnerIDs
}

func getPartnerIDsFromGroup(group []*group.Group) []string {
	if group == nil {
		return nil
	}
	known := make(map[string]struct{})
	var partnerIDs []string
	for _, g := range group {
		if g == nil {
			continue
		}
		if _, ok := known[g.ForeignKeys.PartnerID]; ok {
			continue
		}
		known[g.ForeignKeys.PartnerID] = struct{}{}
		partnerIDs = append(partnerIDs, g.ForeignKeys.PartnerID)
	}
	return partnerIDs
}
