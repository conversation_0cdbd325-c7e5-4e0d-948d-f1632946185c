package location

import (
	"context"
	"net/url"
)

// DefaultLocation can only be oneof account group or group - the other will be set to an empty string.
type DefaultLocation struct {
	AccountGroupID string
	GroupID        string
}

// Locations is the interface of a location service
type Locations interface {
	GetAccountGroups(ctx context.Context, accountGroupIDs []string) ([]*AccountGroup, error)
	GetBrands(ctx context.Context, brandIDs []string) ([]*Brand, error)
	GetBrandWithTabEnablement(ctx context.Context, partnerID string, groupPath string) (*Brand, error)
	GetAccountGroupCount(ctx context.Context, partnerID string) int64
	GetGroupCount(ctx context.Context, partnerID string) int64
	GetVBCWhitelabelURLs(ctx context.Context, partnerIDs []string) (map[string]url.URL, error)
	GetDefaultLocation(ctx context.Context, partnerID string) (*DefaultLocation, error)
	SetDefaultLocation(ctx context.Context, partnerID, accountGroupID, groupID string) error
	ListLocations(ctx context.Context, partnerID string, search string, cursor int64, pageSize int64, includeAccountGroups, includeBrands bool) ([]Location, int64, bool, error)
}
