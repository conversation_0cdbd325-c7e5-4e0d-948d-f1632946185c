package location

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"reflect"
	"regexp"
	"testing"

	"github.com/golang/mock/gomock"
	structpb "github.com/golang/protobuf/ptypes/struct"
	"github.com/vendasta/IAM/sdks/go/v1/subjectcontext"
	iam_attributes "github.com/vendasta/generated-protos-go/iam/attributes"
	partner_v1 "github.com/vendasta/generated-protos-go/partner/v1"
	vendastatypes2 "github.com/vendasta/generated-protos-go/vendasta_types"
	"github.com/vendasta/gosdks/verrors"
	partner "github.com/vendasta/partner/sdks/go/v1"
	tesseract "github.com/vendasta/tesseract/sdks/go/v1"

	"github.com/vendasta/atlas/internal/accountsdata"
	featureflags "github.com/vendasta/atlas/internal/feature_flags"
	"github.com/vendasta/atlas/internal/partnermarket"
	"github.com/vendasta/atlas/internal/product"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/vendasta/IAM/sdks/go/iaminterceptor"
	iam "github.com/vendasta/IAM/sdks/go/v1"
	iamMock "github.com/vendasta/IAM/sdks/go/v1/mocks"
	iammock "github.com/vendasta/IAM/sdks/go/v1/mocks"
	"github.com/vendasta/IAM/sdks/go/v1/subject"
	accountgroup "github.com/vendasta/account-group/sdks/go/v1"
	domain "github.com/vendasta/domain/sdks/go/v2"
	group "github.com/vendasta/group/sdks/go/v2"
	multilocation "github.com/vendasta/multi-location/sdks/go/v1"

	defaultlocation "github.com/vendasta/atlas/internal/default-location"
)

type stubResponses struct {
	brandingStub       *partner.Branding
	brandingError      error
	configurationStub  *partner.Configuration
	configurationError error
	dataFetcher        partnermarket.LocationDataFetcher
	smbFetcher         SMBFetcher
	domain             domain.Interface
	products           product.ProductsFetcher
	accounts           accountsdata.Accounts
	locationFetcher    Locations
	featureClient      featureflags.Features
	tesseractClient    tesseractFakeResponses
	accountGroupExpect func(agMock *accountgroup.MockInterface)
}

type domainFake struct {
	response *domain.DomainMapping
	err      error
}

func (d *domainFake) GetDomainByIdentifier(ctx context.Context, identifier domain.Identifier) (*domain.DomainMapping, error) {
	return d.response, d.err
}

func (d *domainFake) GetIdentifierByDomain(ctx context.Context, domain string) (domain.Identifier, error) {
	return "", errors.New("not implemented")
}

type tesseractFake struct {
	results            []string
	err                error
	expectedStatement  string
	expectedParams     map[string]interface{}
	iterErr            error
	deserializationErr error
	t                  *testing.T
}

func (t tesseractFake) Query(ctx context.Context, query tesseract.Query) (tesseract.Iterator, error) {
	if t.expectedStatement != "" && query.ToProto().String() != t.expectedStatement {
		return nil, errors.New("unexpected query")
	}
	return nil, t.err
}
func (t tesseractFake) ExecuteSQL(ctx context.Context, stmt tesseract.Statement) (tesseract.ResultIterator, error) {
	whitespace := regexp.MustCompile(`\s+`)
	if t.expectedStatement != "" {
		expectedSql := whitespace.ReplaceAllString(t.expectedStatement, " ")
		actualSql := whitespace.ReplaceAllString(stmt.SQL, " ")
		assert.Equal(t.t, expectedSql, actualSql)
	}
	if t.expectedParams != nil {
		assert.Equal(t.t, t.expectedParams, stmt.Params)
	}
	return &resultIteratorFake{results: t.results, err: t.iterErr, resultErr: t.deserializationErr}, t.err
}

type resultIteratorFake struct {
	results   []string
	err       error
	resultErr error
}

func (r *resultIteratorFake) Do(f func(tesseract.Result) error) error {
	if r.err != nil {
		return r.err
	}
	for _, result := range r.results {
		err := f(tesseractResultFake{value: []byte(result), err: r.resultErr})
		if err != nil {
			return err
		}
	}
	return nil
}
func (r *resultIteratorFake) Next() (tesseract.Result, error) {
	panic("implement me")
}
func (r *resultIteratorFake) Done() error {
	panic("implement me")
}

type tesseractResultFake struct {
	value []byte
	err   error
}

func (t tesseractResultFake) ToStruct(in tesseract.T) error {
	if t.err != nil {
		return t.err
	}
	return json.Unmarshal(t.value, &in)
}
func (t tesseractResultFake) ColumnByName(name string, in tesseract.T) error {
	panic("implement me")
}
func (t tesseractResultFake) Vals() *structpb.Struct {
	panic("implement me")
}
func (t tesseractResultFake) Schema() *vendastatypes2.Schema {
	panic("implement me")
}

type smbFetcherFake struct {
	response *SMBData
	err      error
}

func (s *smbFetcherFake) GetSMBData(ctx context.Context, partnerID string) (*SMBData, error) {
	return s.response, s.err
}

type tesseractFakeResponses struct {
	results            []string
	err                error
	expectedStatement  string
	expectedParams     map[string]interface{}
	iterErr            error
	deserializationErr error
}

func TestBuildAddress(t *testing.T) {
	cases := []struct {
		name   string
		input  *accountgroup.AccountGroup
		output string
	}{
		{
			name: "full address",
			input: &accountgroup.AccountGroup{NAPData: &accountgroup.NAPData{
				Address: "1801 W International Speedway Blvd",
				City:    "Daytona Beach",
				State:   "Florida",
			}},
			output: "1801 W International Speedway Blvd, Daytona Beach, Florida",
		},
		{
			name: "missing city",
			input: &accountgroup.AccountGroup{NAPData: &accountgroup.NAPData{
				Address: "1801 W International Speedway Blvd",
				State:   "Florida",
			}},
			output: "1801 W International Speedway Blvd, Florida",
		},
		{
			name: "missing state",
			input: &accountgroup.AccountGroup{NAPData: &accountgroup.NAPData{
				Address: "1801 W International Speedway Blvd",
				City:    "Daytona Beach",
			}},
			output: "1801 W International Speedway Blvd, Daytona Beach",
		},
		{
			name: "missing address",
			input: &accountgroup.AccountGroup{NAPData: &accountgroup.NAPData{
				City:  "Daytona Beach",
				State: "Florida",
			}},
			output: "Daytona Beach, Florida",
		},
		{
			name:   "no address data",
			input:  &accountgroup.AccountGroup{NAPData: &accountgroup.NAPData{}},
			output: "",
		},
		{
			name:   "nil nap data",
			input:  &accountgroup.AccountGroup{NAPData: nil},
			output: "",
		},
		{
			name:   "nil account group",
			input:  nil,
			output: "",
		},
		{
			name: "has address 2",
			input: &accountgroup.AccountGroup{NAPData: &accountgroup.NAPData{
				Address:  "220 3rd Avenue South",
				Address2: "Suite 405",
				City:     "Saskatoon",
				State:    "Saskatchewan",
			}},
			output: "220 3rd Avenue South, Suite 405, Saskatoon, Saskatchewan",
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			actual := buildAddress(c.input)
			assert.Equal(t, c.output, actual)
		})
	}
}

func TestBuildPaths(t *testing.T) {
	tests := []struct {
		name           string
		brandPathInput []string
		expected       []group.Path
	}{
		{
			name:           "nil list returns nil",
			brandPathInput: nil,
			expected:       nil,
		},
		{
			name:           "empty list returns nil",
			brandPathInput: []string{},
			expected:       nil,
		},
		{
			name:           "single group paths",
			brandPathInput: []string{"G-XDW4HFWX", "G-SFBRC7MX"},
			expected: []group.Path{
				{Nodes: []string{"G-XDW4HFWX"}},
				{Nodes: []string{"G-SFBRC7MX"}},
			},
		},
		{
			name:           "sub group paths",
			brandPathInput: []string{"G-57N67B3P|G-2HDXV5PZ|G-Q2CMPDF7"},
			expected: []group.Path{
				{Nodes: []string{"G-57N67B3P"}},
				{Nodes: []string{"G-57N67B3P", "G-2HDXV5PZ"}},
				{Nodes: []string{"G-57N67B3P", "G-2HDXV5PZ", "G-Q2CMPDF7"}},
			},
		},
		{
			name:           "dedupes group paths",
			brandPathInput: []string{"G-57N67B3P|G-2HDXV5PZ|G-Q2CMPDF7", "G-57N67B3P|G-2HDXV5PZ|G-QMLXCFNZ"},
			expected: []group.Path{
				{Nodes: []string{"G-57N67B3P"}},
				{Nodes: []string{"G-57N67B3P", "G-2HDXV5PZ"}},
				{Nodes: []string{"G-57N67B3P", "G-2HDXV5PZ", "G-Q2CMPDF7"}},
				{Nodes: []string{"G-57N67B3P", "G-2HDXV5PZ", "G-QMLXCFNZ"}},
			},
		},
		{
			name:           "dedupes partial group paths",
			brandPathInput: []string{"G-2F2GC3G4|G-FB37WCJL", "G-2F2GC3G4|G-HM5BF8TJ|G-23R872W2"},
			expected: []group.Path{
				{Nodes: []string{"G-2F2GC3G4"}},
				{Nodes: []string{"G-2F2GC3G4", "G-FB37WCJL"}},
				{Nodes: []string{"G-2F2GC3G4", "G-HM5BF8TJ"}},
				{Nodes: []string{"G-2F2GC3G4", "G-HM5BF8TJ", "G-23R872W2"}},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual := buildPaths(tt.brandPathInput)
			assert.EqualValues(t, tt.expected, actual)
		})
	}
}

func TestBuildBrand(t *testing.T) {
	tests := []struct {
		name              string
		groupInput        []*group.Group
		requestedBrandIDs []string
		whitelabelURL     map[string]url.URL
		expected          []*Brand
		expectedError     error
	}{
		{
			name:              "no groups",
			groupInput:        []*group.Group{},
			requestedBrandIDs: []string{"G-123"},
			expected:          nil,
		},
		{
			name: "no requested brands",
			groupInput: []*group.Group{
				{
					Name:    "Brand1",
					GroupID: "G-123",
					Path:    group.Path{Nodes: []string{"G-123"}},
				},
			},
			whitelabelURL: map[string]url.URL{
				"ABC": {
					Scheme: "https",
					Host:   "mysite.com",
				},
			},
			requestedBrandIDs: []string{},
			expected:          nil,
		},
		{
			name: "missing whitelabel domain",
			groupInput: []*group.Group{
				{
					Name:    "Brand1",
					GroupID: "G-123",
					Path:    group.Path{Nodes: []string{"G-123"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
			},
			whitelabelURL:     map[string]url.URL{},
			requestedBrandIDs: []string{"G-123"},
			expected:          nil,
			expectedError:     errDomainNotFound,
		},
		{
			name: "1 group returned",
			groupInput: []*group.Group{
				{
					Name:    "Brand1",
					GroupID: "G-123",
					Path:    group.Path{Nodes: []string{"G-123"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
			},
			whitelabelURL: map[string]url.URL{
				"ABC": {
					Scheme: "https",
					Host:   "mysite.com",
				},
			},
			requestedBrandIDs: []string{"G-123"},
			expected: []*Brand{
				{Name: "Brand1", PathNodes: []string{"G-123"}, URL: "https://mysite.com/account/brands/G-123/"},
			},
		},
		{
			name: "brand name has a slash in it",
			groupInput: []*group.Group{
				{
					Name:    "Brand/name",
					GroupID: "G-123",
					Path:    group.Path{Nodes: []string{"G-123"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
			},
			whitelabelURL: map[string]url.URL{
				"ABC": {
					Scheme: "https",
					Host:   "mysite.com",
				},
			},
			requestedBrandIDs: []string{"G-123"},
			expected: []*Brand{
				{Name: "Brand/name", PathNodes: []string{"G-123"}, URL: "https://mysite.com/account/brands/G-123/"},
			},
		},
		{
			name: "multiple requested brands",
			groupInput: []*group.Group{
				{
					Name:    "Brand1",
					GroupID: "G-123",
					Path:    group.Path{Nodes: []string{"G-123"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
				{
					Name:    "Brand2",
					GroupID: "G-456",
					Path:    group.Path{Nodes: []string{"G-456"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
			},
			whitelabelURL: map[string]url.URL{
				"ABC": {
					Scheme: "https",
					Host:   "mysite.com",
				},
			},
			requestedBrandIDs: []string{"G-123", "G-456"},
			expected: []*Brand{
				{Name: "Brand1", PathNodes: []string{"G-123"}, URL: "https://mysite.com/account/brands/G-123/"},
				{Name: "Brand2", PathNodes: []string{"G-456"}, URL: "https://mysite.com/account/brands/G-456/"},
			},
		},
		{
			name: "child brand requested",
			groupInput: []*group.Group{
				{
					Name:    "Brand1",
					GroupID: "G-456",
					Path:    group.Path{Nodes: []string{"G-123", "G-456"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
				{
					Name:    "Parent",
					GroupID: "G-123",
					Path:    group.Path{Nodes: []string{"G-123"}},
				},
			},
			whitelabelURL: map[string]url.URL{
				"ABC": {
					Scheme: "https",
					Host:   "mysite.com",
				},
			},
			requestedBrandIDs: []string{"G-123|G-456"},
			expected: []*Brand{
				{Name: "Parent | Brand1", PathNodes: []string{"G-123", "G-456"}, URL: "https://mysite.com/account/brands/G-123%7CG-456/"},
			},
		},
		{
			name: "parent and child brand",
			groupInput: []*group.Group{
				{
					Name:    "Brand1",
					GroupID: "G-456",
					Path:    group.Path{Nodes: []string{"G-123", "G-456"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
				{
					Name:    "Parent",
					GroupID: "G-123",
					Path:    group.Path{Nodes: []string{"G-123"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
			},
			whitelabelURL: map[string]url.URL{
				"ABC": {
					Scheme: "https",
					Host:   "mysite.com",
				},
			},
			requestedBrandIDs: []string{"G-123|G-456", "G-123"},
			expected: []*Brand{
				{Name: "Parent | Brand1", PathNodes: []string{"G-123", "G-456"}, URL: "https://mysite.com/account/brands/G-123%7CG-456/"},
				{Name: "Parent", PathNodes: []string{"G-123"}, URL: "https://mysite.com/account/brands/G-123/"},
			},
		},
		{
			name: "parent and deep child",
			groupInput: []*group.Group{
				{
					Name:    "Parent 2",
					GroupID: "G-456",
					Path:    group.Path{Nodes: []string{"G-123", "G-456"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
				{
					Name:    "Child",
					GroupID: "G-789",
					Path:    group.Path{Nodes: []string{"G-123", "G-456", "G-789"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
				{
					Name:    "Parent 1",
					GroupID: "G-123",
					Path:    group.Path{Nodes: []string{"G-123"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
			},
			whitelabelURL: map[string]url.URL{
				"ABC": {
					Scheme: "https",
					Host:   "mysite.com",
				},
			},
			requestedBrandIDs: []string{"G-123|G-456|G-789", "G-123"},
			expected: []*Brand{
				{Name: "Parent 1 | Parent 2 - Child", PathNodes: []string{"G-123", "G-456", "G-789"}, URL: "https://mysite.com/account/brands/G-123%7CG-456%7CG-789/"},
				{Name: "Parent 1", PathNodes: []string{"G-123"}, URL: "https://mysite.com/account/brands/G-123/"},
			},
		},
		{
			name: "check very deep child",
			groupInput: []*group.Group{
				{
					Name:    "Child",
					GroupID: "G-101112",
					Path:    group.Path{Nodes: []string{"G-123", "G-456", "G-789", "G-101112"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
				{
					Name:    "Parent 3",
					GroupID: "G-789",
					Path:    group.Path{Nodes: []string{"G-123", "G-456", "G-789"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
				{
					Name:    "Parent 2",
					GroupID: "G-456",
					Path:    group.Path{Nodes: []string{"G-123", "G-456"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
				{
					Name:    "Parent 1",
					GroupID: "G-123",
					Path:    group.Path{Nodes: []string{"G-123"}},
					ForeignKeys: group.ForeignKeys{
						PartnerID: "ABC",
					},
				},
			},
			whitelabelURL: map[string]url.URL{
				"ABC": {
					Scheme: "https",
					Host:   "mysite.com",
				},
			},
			requestedBrandIDs: []string{"G-123|G-456|G-789|G-101112"},
			expected: []*Brand{
				{Name: "Parent 1 | Parent 2 - Parent 3 - Child", PathNodes: []string{"G-123", "G-456", "G-789", "G-101112"}, URL: "https://mysite.com/account/brands/G-123%7CG-456%7CG-789%7CG-101112/"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			actual, _ := buildBrands(tt.groupInput, tt.requestedBrandIDs, tt.whitelabelURL)
			assert.EqualValues(t, tt.expected, actual)
		})
	}
}

type defaultLocationRepositoryStub struct {
	DefaultLocation *defaultlocation.Model
	Error           error
}

func (r *defaultLocationRepositoryStub) Set(ctx context.Context, partnerID, userID string, siteMutator defaultlocation.Mutator) error {
	return nil
}

func (r *defaultLocationRepositoryStub) Get(ctx context.Context, partnerID, userID string) (*defaultlocation.Model, error) {
	return r.DefaultLocation, r.Error
}

func TestService_GetDefaultLocation(t *testing.T) {
	type fields struct {
		iamClient                 *iammock.Interface
		defaultLocationRepository defaultlocation.Repository
		domain                    domain.Interface
		tesseractClient           tesseractFakeResponses
		accountGroupExpect        func(agMock *accountgroup.MockInterface)
		smbFetcher                SMBFetcher
		configurationStub         *partner.Configuration
		configurationError        error
	}
	type args struct {
		ctx       context.Context
		partnerID string
	}
	type smbBySessionIDResponse struct {
		subject *subject.SMB
		err     error
	}
	tests := []struct {
		name                   string
		fields                 fields
		args                   args
		smbBySessionIDResponse *smbBySessionIDResponse
		want                   *DefaultLocation
		wantErr                bool
	}{
		{
			name:    "partner ID required",
			wantErr: true,
			fields: fields{
				iamClient: &iammock.Interface{},
			},
		},
		{
			name:    "authentication required",
			args:    args{ctx: context.Background(), partnerID: "ABC"},
			wantErr: true,
			fields: fields{
				iamClient: &iammock.Interface{},
			},
		},
		{
			name: "returns repo error",
			fields: fields{
				defaultLocationRepository: &defaultLocationRepositoryStub{
					Error: errors.New(""),
				},
				iamClient: &iammock.Interface{},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				tesseractClient: tesseractFakeResponses{
					results: []string{
						`{
							"LocationId": "AG-001",
							"Name": "Account Group 1",
							"Address": "123 Fake St.",
							"City": "Townsville",
							"State": "??",
							"IsBrand": false
						}`, `{
							"LocationId": "G-001",
							"Name": "Brand 1",
							"IsBrand": true
						}`,
					},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
			},
			args: args{
				ctx: iaminterceptor.SetRequestInfoForTests(t, context.Background(), &iaminterceptor.TestRequestInfo{
					DirectCaller: &iaminterceptor.TestTokenInfoIAMIdentity{
						UserID: "U-1234",
					},
				}),
				partnerID: "ABC",
			},
			wantErr: true,
		},
		{
			name: "returns location from repo",
			fields: fields{
				defaultLocationRepository: &defaultLocationRepositoryStub{
					DefaultLocation: &defaultlocation.Model{
						AccountGroupID: "AG-123",
					},
				},
				iamClient: &iammock.Interface{},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				tesseractClient: tesseractFakeResponses{
					results: []string{
						`{
							"LocationId": "AG-001",
							"Name": "Account Group 1",
							"Address": "123 Fake St.",
							"City": "Townsville",
							"State": "??",
							"IsBrand": false
						}`, `{
							"LocationId": "G-001",
							"Name": "Brand 1",
							"IsBrand": true
						}`,
					},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
			},
			args: args{
				ctx: iaminterceptor.SetRequestInfoForTests(t, context.Background(), &iaminterceptor.TestRequestInfo{
					DirectCaller: &iaminterceptor.TestTokenInfoIAMIdentity{
						UserID: "U-1234",
					},
				}),
				partnerID: "ABC",
			},
			want: &DefaultLocation{
				AccountGroupID: "AG-123",
			},
		},
		{
			name: "does not return error getting IAM SMB subject",
			fields: fields{
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{"AG-001"}, gomock.Any()).Times(1)
				},
				defaultLocationRepository: &defaultLocationRepositoryStub{},
				iamClient:                 &iammock.Interface{},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
				tesseractClient: tesseractFakeResponses{
					results: []string{
						`{
							"LocationId": "AG-001",
							"Name": "Account Group 1",
							"Address": "123 Fake St.",
							"City": "Townsville",
							"State": "??",
							"IsBrand": false
						}`, `{
							"LocationId": "G-001",
							"Name": "Brand 1",
							"IsBrand": true
						}`,
					},
				},
			},
			args: args{
				ctx: iaminterceptor.SetRequestInfoForTests(t, context.Background(), &iaminterceptor.TestRequestInfo{
					DirectCaller: &iaminterceptor.TestTokenInfoIAMIdentity{
						UserID: "U-1234",
					},
				}),
				partnerID: "ABC",
			},
			smbBySessionIDResponse: &smbBySessionIDResponse{err: errors.New("")},
			want:                   &DefaultLocation{},
		},
		{
			name: "returns the default location from the SMB subject",
			fields: fields{
				defaultLocationRepository: &defaultLocationRepositoryStub{},
				iamClient:                 &iammock.Interface{},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
				tesseractClient: tesseractFakeResponses{
					results: []string{
						`{
							"LocationId": "AG-001",
							"Name": "Account Group 1",
							"Address": "123 Fake St.",
							"City": "Townsville",
							"State": "??",
							"IsBrand": false
						}`, `{
							"LocationId": "G-001",
							"Name": "Brand 1",
							"IsBrand": true
						}`,
					},
				},
			},
			args: args{
				ctx: iaminterceptor.SetRequestInfoForTests(t, context.Background(), &iaminterceptor.TestRequestInfo{
					DirectCaller: &iaminterceptor.TestTokenInfoIAMIdentity{
						UserID: "U-1234",
					},
				}),
				partnerID: "ABC",
			},
			smbBySessionIDResponse: &smbBySessionIDResponse{subject: &subject.SMB{DefaultAccountGroup: "AG-456"}},
			want: &DefaultLocation{
				AccountGroupID: "AG-456",
			},
		},
		{
			name: "returns empty value and no error if no user id",
			fields: fields{
				defaultLocationRepository: &defaultLocationRepositoryStub{
					DefaultLocation: &defaultlocation.Model{
						AccountGroupID: "AG-123",
					},
				},
				iamClient: &iammock.Interface{},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				tesseractClient: tesseractFakeResponses{
					results: []string{
						`{
							"LocationId": "AG-001",
							"Name": "Account Group 1",
							"Address": "123 Fake St.",
							"City": "Townsville",
							"State": "??",
							"IsBrand": false
						}`, `{
							"LocationId": "G-001",
							"Name": "Brand 1",
							"IsBrand": true
						}`,
					},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
			},
			args: args{
				ctx: iaminterceptor.SetRequestInfoForTests(t, context.Background(), &iaminterceptor.TestRequestInfo{
					DirectCaller: &iaminterceptor.TestTokenInfoIAMIdentity{},
				}),
				partnerID: "ABC",
			},
			want: &DefaultLocation{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			accountGroupMock := accountgroup.NewMockInterface(ctrl)
			if tt.fields.accountGroupExpect != nil {
				tt.fields.accountGroupExpect(accountGroupMock)
			}
			partnerStub := &partner.MockInterface{}
			partnerStub.On("GetConfiguration", mock.Anything, mock.Anything, mock.Anything).
				Return(tt.fields.configurationStub, tt.fields.configurationError)
			s := &Service{
				iamClient:                 tt.fields.iamClient,
				defaultLocationRepository: tt.fields.defaultLocationRepository,
				domainClient:              tt.fields.domain,
				accountGroupClient:        accountGroupMock,
				partnerClient:             partnerStub,
				smbFetcher:                tt.fields.smbFetcher,
				tesseractClient: &tesseractFake{
					results:            tt.fields.tesseractClient.results,
					err:                tt.fields.tesseractClient.err,
					expectedStatement:  tt.fields.tesseractClient.expectedStatement,
					expectedParams:     tt.fields.tesseractClient.expectedParams,
					iterErr:            tt.fields.tesseractClient.iterErr,
					deserializationErr: tt.fields.tesseractClient.deserializationErr,
					t:                  t,
				},
			}
			tt.fields.iamClient.On("AccessResource", mock.Anything, mock.Anything).Return(errors.New(""))
			if tt.smbBySessionIDResponse != nil {
				tt.fields.iamClient.On("SMBBySessionID", mock.Anything, mock.Anything, mock.Anything).Return(tt.smbBySessionIDResponse.subject, tt.smbBySessionIDResponse.err)
			}
			got, err := s.GetDefaultLocation(tt.args.ctx, tt.args.partnerID)
			if (err != nil) != tt.wantErr {
				t.Errorf("Service.GetDefaultLocation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("Service.GetDefaultLocation() = %v, want %v", got, tt.want)
			}
			ctrl.Finish()
		})
	}
}

func TestService_SetDefaultLocation(t *testing.T) {
	type fields struct {
		accountGroupClient        accountgroup.Interface
		groupClient               group.Interface
		domainClient              domain.Interface
		multiLocationClient       multilocation.MultiLocationClientInterface
		iamAuth                   iam.AuthService
		iamClient                 iam.Interface
		defaultLocationRepository defaultlocation.Repository
	}
	type args struct {
		ctx            context.Context
		partnerID      string
		accountGroupID string
		groupID        string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "partner ID required",
			wantErr: true,
		},
		{
			name:    "authentication required",
			fields:  fields{},
			args:    args{ctx: context.Background(), partnerID: "ABC"},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &Service{
				accountGroupClient:        tt.fields.accountGroupClient,
				groupClient:               tt.fields.groupClient,
				domainClient:              tt.fields.domainClient,
				multiLocationClient:       tt.fields.multiLocationClient,
				iamAuth:                   tt.fields.iamAuth,
				iamClient:                 tt.fields.iamClient,
				defaultLocationRepository: tt.fields.defaultLocationRepository,
			}
			if err := s.SetDefaultLocation(tt.args.ctx, tt.args.partnerID, tt.args.accountGroupID, tt.args.groupID); (err != nil) != tt.wantErr {
				t.Errorf("Service.SetDefaultLocation() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestListLocations(t *testing.T) {
	tests := []struct {
		name          string
		ctx           context.Context
		stubResponses *stubResponses

		partnerId            string
		search               string
		cursor               int64
		pageSize             int64
		includeAccountGroups bool
		includeBrands        bool
		isElevated           bool
		isSalesPerson        bool
		isMarketAdmin        bool
		isPartner            bool
		subjectPid           string

		expectedLocations []Location
		expectedCursor    int64
		expectedHasMore   bool
		expectedError     error
	}{
		{
			name: "Returns an empty list if tesseract returns an empty list",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedLocations:    []Location{},
			expectedCursor:       25,
		},
		{
			name: "Returns results from tesseract",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{"AG-001"}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
				tesseractClient: tesseractFakeResponses{
					results: []string{
						`{
							"LocationId": "AG-001",
							"Name": "Account Group 1",
							"Address": "123 Fake St.",
							"City": "Townsville",
							"State": "??",
							"IsBrand": false
						}`, `{
							"LocationId": "G-001",
							"Name": "Brand 1",
							"IsBrand": true
						}`,
					},
				},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedLocations: []Location{
				{
					LocationId:        "AG-001",
					Name:              "Account Group 1",
					Address:           "123 Fake St.",
					City:              "Townsville",
					State:             "??",
					Url:               "https://wldomain.com/account/location/AG-001",
					ActivatedProducts: []string(nil),
					IsBrand:           false,
				},
				{
					LocationId:        "G-001",
					Name:              "Brand 1",
					Url:               "https://wldomain.com/account/brands/G-001",
					ActivatedProducts: []string(nil),
					IsBrand:           true,
				},
			},
			expectedCursor:  25,
			expectedHasMore: false,
		},
		{
			name: "Elevated locations are not included for smb user",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:               "UID-123",
					ProductPermissions:      map[string][]string{"AG-123": {"RM", "SM"}},
					AssociatedAccountGroups: []string{"AG-123", "AG-321"},
					GroupAssociations:       []string{"G-123", "G-321"},
				}},
				tesseractClient: tesseractFakeResponses{
					expectedStatement: `
						SELECT *
						FROM ( SELECT account_group_id AS location_id, company_name AS name, address, city, state, FALSE AS is_brand
							  FROM account_group$accountgroupv2 AS agid
							  WHERE agid.account_group_id IN ('AG-123', 'AG-321')
								AND agid.partner_id = @partnerId
								AND agid.deleted IS NULL
							  UNION
							  SELECT path AS location_id, name AS name, NULL AS address, NULL AS city, NULL AS state, TRUE AS is_brand
							  FROM group$groupwithmembersv2 AS brand
							  WHERE brand.path IN ('G-123', 'G-321')
								AND brand.partner_id = @partnerId
								AND brand.deleted IS NULL
								AND brand.namespace = 'brands' ) AS loc
						WHERE loc.name ILIKE '%' || @search || '%'
							OR address ILIKE '%' || @search || '%'
							OR city ILIKE '%' || @search || '%'
							OR state ILIKE '%' || @search || '%'
						ORDER BY loc.name
						LIMIT @pageSize OFFSET @cursor
					`,
				},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedLocations:    []Location{},
			expectedCursor:       25,
		},
		{
			name: "Elevated locations are included when appropriate",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:               "UID-123",
					ProductPermissions:      map[string][]string{"AG-123": {"RM", "SM"}},
					AssociatedAccountGroups: []string{"AG-123", "AG-321"},
					GroupAssociations:       []string{"G-123", "G-321"},
				}},
				tesseractClient: tesseractFakeResponses{
					expectedStatement: `
						SELECT *
						FROM ( SELECT account_group_id AS location_id, company_name AS name, address, city, state, FALSE AS is_brand
							  FROM account_group$accountgroupv2 AS agid
							  WHERE agid.partner_id = @partnerId
								AND agid.deleted IS NULL
							  UNION
							  SELECT path AS location_id, name AS name, NULL AS address, NULL AS city, NULL AS state, TRUE AS is_brand
							  FROM group$groupwithmembersv2 AS brand
							  WHERE brand.partner_id = @partnerId
								AND brand.deleted IS NULL
								AND brand.namespace = 'brands' ) AS loc
						WHERE loc.name ILIKE '%' || @search || '%'
							OR address ILIKE '%' || @search || '%'
							OR city ILIKE '%' || @search || '%'
							OR state ILIKE '%' || @search || '%'
						ORDER BY loc.name
						LIMIT @pageSize OFFSET @cursor
					`,
				},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedLocations:    []Location{},
			expectedCursor:       25,
			isElevated:           true,
		},
		{
			name: "Elevated salesperson locations are included when appropriate",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:               "UID-123",
					ProductPermissions:      map[string][]string{"AG-123": {"RM", "SM"}},
					AssociatedAccountGroups: []string{"AG-123", "AG-321"},
					GroupAssociations:       []string{"G-123", "G-321"},
				}},
				tesseractClient: tesseractFakeResponses{
					expectedStatement: `
						SELECT *
						FROM ( SELECT account_group_id AS location_id, company_name AS name, address, city, state, FALSE AS is_brand
							  FROM account_group$accountgroupv2 AS agid
							  WHERE (agid.account_group_id IN ('AG-123', 'AG-321') OR agid.market_id in (@marketIds))
								AND agid.partner_id = @partnerId
								AND agid.deleted IS NULL
							  UNION
							  SELECT path AS location_id, name AS name, NULL AS address, NULL AS city, NULL AS state, TRUE AS is_brand
							  FROM group$groupwithmembersv2 AS brand
							  WHERE brand.path IN ('G-123', 'G-321')
								AND brand.partner_id = @partnerId
								AND brand.deleted IS NULL
								AND brand.namespace = 'brands' ) AS loc
						WHERE loc.name ILIKE '%' || @search || '%'
							OR address ILIKE '%' || @search || '%'
							OR city ILIKE '%' || @search || '%'
							OR state ILIKE '%' || @search || '%'
						ORDER BY loc.name
						LIMIT @pageSize OFFSET @cursor
					`,
				},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedLocations:    []Location{},
			expectedCursor:       25,
			isElevated:           true,
			isSalesPerson:        true,
		},
		{
			name: "market admin markets are included",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:               "UID-123",
					ProductPermissions:      map[string][]string{"AG-123": {"RM", "SM"}},
					AssociatedAccountGroups: []string{"AG-123", "AG-321"},
					GroupAssociations:       []string{"G-123", "G-321"},
				}},
				tesseractClient: tesseractFakeResponses{
					expectedStatement: `
						SELECT *
						FROM ( SELECT account_group_id AS location_id, company_name AS name, address, city, state, FALSE AS is_brand
							  FROM account_group$accountgroupv2 AS agid
							  WHERE (agid.account_group_id IN ('AG-123', 'AG-321') OR agid.market_id in (@marketIds))
								AND agid.partner_id = @partnerId
								AND agid.deleted IS NULL
							  UNION
							  SELECT path AS location_id, name AS name, NULL AS address, NULL AS city, NULL AS state, TRUE AS is_brand
							  FROM group$groupwithmembersv2 AS brand
							  WHERE brand.path IN ('G-123', 'G-321')
								AND brand.partner_id = @partnerId
								AND brand.deleted IS NULL
								AND brand.namespace = 'brands' ) AS loc
						WHERE loc.name ILIKE '%' || @search || '%'
							OR address ILIKE '%' || @search || '%'
							OR city ILIKE '%' || @search || '%'
							OR state ILIKE '%' || @search || '%'
						ORDER BY loc.name
						LIMIT @pageSize OFFSET @cursor
					`,
				},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedLocations:    []Location{},
			expectedCursor:       25,
			isElevated:           true,
			isMarketAdmin:        true,
		},
		{
			name: "Elevated locations are included when a user is both a partner and a salesperson",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
				tesseractClient: tesseractFakeResponses{
					expectedStatement: `
						SELECT *
						FROM ( SELECT account_group_id AS location_id, company_name AS name, address, city, state, FALSE AS is_brand
							  FROM account_group$accountgroupv2 AS agid
							  WHERE agid.partner_id = @partnerId
								AND agid.deleted IS NULL
							  UNION
							  SELECT path AS location_id, name AS name, NULL AS address, NULL AS city, NULL AS state, TRUE AS is_brand
							  FROM group$groupwithmembersv2 AS brand
							  WHERE brand.partner_id = @partnerId
								AND brand.deleted IS NULL
								AND brand.namespace = 'brands' ) AS loc
						WHERE loc.name ILIKE '%' || @search || '%'
							OR address ILIKE '%' || @search || '%'
							OR city ILIKE '%' || @search || '%'
							OR state ILIKE '%' || @search || '%'
						ORDER BY loc.name
						LIMIT @pageSize OFFSET @cursor
					`,
				},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedLocations:    []Location{},
			expectedCursor:       25,
			isElevated:           true,
			isSalesPerson:        true,
			isPartner:            true,
			subjectPid:           "ABC",
		},
		{
			name: "salesperson locations are included when a user has a partner subject on a different pid",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
				tesseractClient: tesseractFakeResponses{
					expectedStatement: `
						SELECT *
						FROM ( SELECT account_group_id AS location_id, company_name AS name, address, city, state, FALSE AS is_brand
							  FROM account_group$accountgroupv2 AS agid
							  WHERE (agid.account_group_id IN ('') OR agid.market_id in (@marketIds))
								AND agid.partner_id = @partnerId
								AND agid.deleted IS NULL
							  UNION
							  SELECT path AS location_id, name AS name, NULL AS address, NULL AS city, NULL AS state, TRUE AS is_brand
							  FROM group$groupwithmembersv2 AS brand
							  WHERE brand.path IN ('')
								AND brand.partner_id = @partnerId
								AND brand.deleted IS NULL
								AND brand.namespace = 'brands' ) AS loc
						WHERE loc.name ILIKE '%' || @search || '%'
							OR address ILIKE '%' || @search || '%'
							OR city ILIKE '%' || @search || '%'
							OR state ILIKE '%' || @search || '%'
						ORDER BY loc.name
						LIMIT @pageSize OFFSET @cursor
					`,
				},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedLocations:    []Location{},
			expectedCursor:       25,
			isElevated:           true,
			isSalesPerson:        true,
			isPartner:            true,
			subjectPid:           "VUNI",
		},
		{
			name: "Expected parameters are included",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
				tesseractClient: tesseractFakeResponses{
					expectedParams: map[string]interface{}{"cursor": int64(25), "marketIds": []string{"<nil>"}, "pageSize": int64(25), "partnerId": "ABC", "search": "Search Term"},
				},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			cursor:               25,
			search:               "Search Term",
			expectedLocations:    []Location{},
			expectedCursor:       50,
			isElevated:           true,
			isSalesPerson:        true,
		},
		{
			name: "Brands are not included if they are not requested",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
				tesseractClient: tesseractFakeResponses{
					expectedStatement: `
						SELECT *
						FROM ( SELECT account_group_id AS location_id, company_name AS name, address, city, state, FALSE AS is_brand
							  FROM account_group$accountgroupv2 AS agid
							  WHERE agid.account_group_id IN ('')
								AND agid.partner_id = @partnerId
								AND agid.deleted IS NULL ) AS loc
						WHERE loc.name ILIKE '%' || @search || '%'
							OR address ILIKE '%' || @search || '%'
							OR city ILIKE '%' || @search || '%'
							OR state ILIKE '%' || @search || '%'
						ORDER BY loc.name
						LIMIT @pageSize OFFSET @cursor
					`,
				},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			expectedLocations:    []Location{},
			expectedCursor:       25,
		},
		{
			name: "AccountGroups are not included if they are not requested",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
				tesseractClient: tesseractFakeResponses{
					expectedStatement: `
						SELECT *
						FROM ( SELECT path AS location_id, name AS name, NULL AS address, NULL AS city, NULL AS state, TRUE AS is_brand
							  FROM group$groupwithmembersv2 AS brand
							  WHERE brand.path IN ('')
								AND brand.partner_id = @partnerId
								AND brand.deleted IS NULL
								AND brand.namespace = 'brands' ) AS loc
						WHERE loc.name ILIKE '%' || @search || '%'
							OR address ILIKE '%' || @search || '%'
							OR city ILIKE '%' || @search || '%'
							OR state ILIKE '%' || @search || '%'
						ORDER BY loc.name
						LIMIT @pageSize OFFSET @cursor
					`,
				},
			},
			partnerId:         "ABC",
			pageSize:          25,
			includeBrands:     true,
			expectedLocations: []Location{},
			expectedCursor:    25,
		},
		{
			name: "Returns an error if both includeAccountGroups and includeBrands are false",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
			},
			partnerId:     "ABC",
			pageSize:      25,
			expectedError: fmt.Errorf("neither account groups nor brands were requested"),
		},
		{
			name: "If no smb data is available, do not return an error",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{err: fmt.Errorf("an error")},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedLocations:    []Location{},
			expectedCursor:       25,
		},
		{
			name: "Returns active product ids",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{"AG-123"}, gomock.Any()).Return([]*accountgroup.AccountGroup{
						{
							Accounts: []*accountgroup.Account{
								{
									AccountID:        "AG-123",
									MarketplaceAppID: "RM",
								},
								{
									AccountID:        "AG-123",
									MarketplaceAppID: "SM",
								},
							},
							ExternalIdentifiers: &accountgroup.ExternalIdentifiers{PartnerID: "ABC"},
						},
					}, nil)
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
				tesseractClient: tesseractFakeResponses{
					results: []string{
						`{
							"LocationId": "AG-123",
							"Name": "Account Group 1",
							"Address": "123 Fake St.",
							"City": "Townsville",
							"State": "??",
							"IsBrand": false
						}`, `{
							"LocationId": "G-001",
							"Name": "Brand 1",
							"IsBrand": true
						}`,
					},
				},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedLocations: []Location{
				{
					LocationId:        "AG-123",
					Name:              "Account Group 1",
					Address:           "123 Fake St.",
					City:              "Townsville",
					State:             "??",
					Url:               "https://wldomain.com/account/location/AG-123",
					ActivatedProducts: []string{"RM", "SM"},
					IsBrand:           false,
				},
				{
					LocationId:        "G-001",
					Name:              "Brand 1",
					Url:               "https://wldomain.com/account/brands/G-001",
					ActivatedProducts: []string(nil),
					IsBrand:           true,
				},
			},
			expectedCursor: 25,
		},
		{
			name: "Does not error if GetAccountGroups fails",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{"AG-123"}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
				tesseractClient: tesseractFakeResponses{
					results: []string{
						`{
							"LocationId": "AG-123",
							"Name": "Account Group 1",
							"Address": "123 Fake St.",
							"City": "Townsville",
							"State": "??",
							"IsBrand": false
						}`, `{
							"LocationId": "G-001",
							"Name": "Brand 1",
							"IsBrand": true
						}`,
					},
				},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedLocations: []Location{
				{
					LocationId:        "AG-123",
					Name:              "Account Group 1",
					Address:           "123 Fake St.",
					City:              "Townsville",
					State:             "??",
					Url:               "https://wldomain.com/account/location/AG-123",
					ActivatedProducts: []string(nil),
					IsBrand:           false,
				},
				{
					LocationId:        "G-001",
					Name:              "Brand 1",
					Url:               "https://wldomain.com/account/brands/G-001",
					ActivatedProducts: []string(nil),
					IsBrand:           true,
				},
			},
			expectedCursor: 25,
		},
		{
			name: "Errors when tesseract errors",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
				tesseractClient: tesseractFakeResponses{
					err: fmt.Errorf("an error"),
				},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedError:        fmt.Errorf("executeSQL err: an error"),
		},
		{
			name: "Errors when iterating results fails",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
				tesseractClient: tesseractFakeResponses{
					iterErr: fmt.Errorf("an error"),
					results: []string{
						`{
							"LocationId": "AG-001",
							"Name": "Account Group 1",
							"Address": "123 Fake St.",
							"City": "Townsville",
							"State": "??",
							"IsBrand": false
						}`, `{
							"LocationId": "G-001",
							"Name": "Brand 1",
							"IsBrand": true
						}`,
					},
				},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedError:        fmt.Errorf("error iterating location results: an error"),
		},
		{
			name: "Errors when iterating results fails",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
				tesseractClient: tesseractFakeResponses{
					deserializationErr: fmt.Errorf("an error"),
					results: []string{
						`{
							"LocationId": "AG-001",
							"Name": "Account Group 1",
							"Address": "123 Fake St.",
							"City": "Townsville",
							"State": "??",
							"IsBrand": false
						}`, `{
							"LocationId": "G-001",
							"Name": "Brand 1",
							"IsBrand": true
						}`,
					},
				},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedError:        fmt.Errorf("error iterating location results: error deserializing into loc: an error"),
		},
		{
			name: "Returns an error if GetVBCWhitelabelURLs fails",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				domain: &domainFake{
					response: nil,
					err:      fmt.Errorf("an error"),
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedError:        fmt.Errorf("an error"),
		},
		{
			name: "Returns an error if GetConfiguration fails",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationError: fmt.Errorf("an error"),
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
			},
			partnerId:            "ABC",
			pageSize:             25,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedError:        fmt.Errorf("could not get partner configuration: an error"),
		},
		{
			name: "iterates the cursor by the page size",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
			},
			partnerId:            "ABC",
			pageSize:             12,
			cursor:               24,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedLocations:    []Location{},
			expectedCursor:       36,
		},
		{
			name: "returns has more if there were enough results to fill page",
			ctx:  context.Background(),
			stubResponses: &stubResponses{
				accountGroupExpect: func(agMock *accountgroup.MockInterface) {
					agMock.EXPECT().GetMulti(gomock.Any(), []string{"AG-001"}, gomock.Any())
				},
				domain: &domainFake{
					response: &domain.DomainMapping{
						Primary: &domain.Domain{
							Secure: true,
							Name:   "wldomain.com",
						},
					},
				},
				configurationStub: &partner.Configuration{
					EnabledFeatures:             []string{},
					BusinessCenterConfiguration: &partner.BusinessCenterConfiguration{},
					SalesConfiguration:          &partner_v1.SalesConfiguration{StSalespersonSharedAccountAccess: true, StBusinessCenterAccess: true},
				},
				smbFetcher: &smbFetcherFake{response: &SMBData{
					SubjectID:          "UID-123",
					ProductPermissions: map[string][]string{"AG-123": {"RM", "SM"}},
				}},
				tesseractClient: tesseractFakeResponses{
					results: []string{
						`{
							"LocationId": "AG-001",
							"Name": "Account Group 1",
							"Address": "123 Fake St.",
							"City": "Townsville",
							"State": "??",
							"IsBrand": false
						}`, `{
							"LocationId": "G-001",
							"Name": "Brand 1",
							"IsBrand": true
						}`,
					},
				},
			},
			partnerId:            "ABC",
			pageSize:             2,
			cursor:               2,
			includeAccountGroups: true,
			includeBrands:        true,
			expectedLocations: []Location{
				{
					LocationId:        "AG-001",
					Name:              "Account Group 1",
					Address:           "123 Fake St.",
					City:              "Townsville",
					State:             "??",
					Url:               "https://wldomain.com/account/location/AG-001",
					ActivatedProducts: []string(nil),
					IsBrand:           false,
				},
				{
					LocationId:        "G-001",
					Name:              "Brand 1",
					Url:               "https://wldomain.com/account/brands/G-001",
					ActivatedProducts: []string(nil),
					IsBrand:           true,
				},
			},
			expectedCursor:  4,
			expectedHasMore: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			partnerStub := &partner.MockInterface{}
			iamStub := &iamMock.Interface{}
			ctrl := gomock.NewController(t)
			accountGroupMock := accountgroup.NewMockInterface(ctrl)
			if tt.stubResponses.accountGroupExpect != nil {
				tt.stubResponses.accountGroupExpect(accountGroupMock)
			}

			partnerStub.On("GetConfiguration", mock.Anything, mock.Anything, mock.Anything).
				Return(tt.stubResponses.configurationStub, tt.stubResponses.configurationError)
			if tt.isElevated {
				iamStub.On("AccessResource", mock.Anything, mock.Anything).Return(nil)
				if tt.isSalesPerson {
					sub := iamMock.Subject{}
					sub.On("Attributes").Return(nil)
					sub.On("PartnerID").Return(tt.subjectPid)
					iamStub.On("GetBySessionID", mock.Anything, subjectcontext.New(subject.SalesPersonSubjectType, "ABC"), mock.Anything).Return(&sub, nil)
					if tt.isPartner {
						iamStub.On("GetBySessionID", mock.Anything, subjectcontext.New(subject.PartnerSubjectType, ""), mock.Anything).Return(&sub, nil)
					} else {
						iamStub.On("GetBySessionID", mock.Anything, subjectcontext.New(subject.PartnerSubjectType, ""), mock.Anything).Return(nil, nil)
					}
				} else if tt.isMarketAdmin {
					sub := iamMock.Subject{}
					sub.On("Attributes").Return(
						map[string]*iam_attributes.Attribute{
							"accessible_markets": {
								Kind: &iam_attributes.Attribute_ListAttribute{
									ListAttribute: &iam_attributes.ListAttribute{
										Attributes: []*iam_attributes.Attribute{
											{Kind: &iam_attributes.Attribute_StringAttribute{StringAttribute: "market1"}},
											{Kind: &iam_attributes.Attribute_StringAttribute{StringAttribute: "market2"}},
										},
									},
								},
							},
						},
					)
					iamStub.On("GetBySessionID", mock.Anything, subjectcontext.New(subject.SalesPersonSubjectType, "ABC"), mock.Anything).Return(nil, nil)
					iamStub.On("GetBySessionID", mock.Anything, subjectcontext.New(subject.PartnerSubjectType, ""), mock.Anything).Return(&sub, nil)
				} else {
					iamStub.On("GetBySessionID", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil)
				}
			} else {
				iamStub.On("AccessResource", mock.Anything, mock.Anything).Return(verrors.New(verrors.PermissionDenied, "permission denied"))
			}

			svc := &Service{
				accountGroupClient: accountGroupMock,
				partnerClient:      partnerStub,
				smbFetcher:         tt.stubResponses.smbFetcher,
				domainClient:       tt.stubResponses.domain,
				iamClient:          iamStub,
				tesseractClient: &tesseractFake{
					results:            tt.stubResponses.tesseractClient.results,
					err:                tt.stubResponses.tesseractClient.err,
					expectedStatement:  tt.stubResponses.tesseractClient.expectedStatement,
					expectedParams:     tt.stubResponses.tesseractClient.expectedParams,
					iterErr:            tt.stubResponses.tesseractClient.iterErr,
					deserializationErr: tt.stubResponses.tesseractClient.deserializationErr,
					t:                  t,
				},
			}

			locations, cursor, hasMore, err := svc.ListLocations(tt.ctx, tt.partnerId, tt.search, tt.cursor, tt.pageSize, tt.includeAccountGroups, tt.includeBrands)
			assert.EqualValues(t, tt.expectedLocations, locations)
			assert.EqualValues(t, tt.expectedCursor, cursor)
			assert.EqualValues(t, tt.expectedHasMore, hasMore)
			assert.EqualValues(t, tt.expectedError, err)

			ctrl.Finish()
		})
	}
}
