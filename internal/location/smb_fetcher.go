package location

import (
	"context"

	"github.com/vendasta/gosdks/verrors"

	iam "github.com/vendasta/IAM/sdks/go/v1"
	"github.com/vendasta/IAM/sdks/go/v1/subject"
)

// SMBData data about an smb user
type SMBData struct {
	SubjectID               string
	DefaultAccountGroup     string
	AssociatedAccountGroups []string
	GroupAssociations       []string
	AccountTabPermissions   map[string][]string
	ProductPermissions      map[string][]string
}

func (s *SMBData) GetSubjectID() string {
	if s == nil {
		return ""
	}
	return s.SubjectID
}

func (s *SMBData) GetDefaultAccountGroup() string {
	if s == nil {
		return ""
	}
	return s.DefaultAccountGroup
}

func (s *SMBData) GetAssociatedAccountGroups() []string {
	if s == nil {
		return nil
	}
	return s.AssociatedAccountGroups
}

func (s *SMBData) GetGroupAssociations() []string {
	if s == nil {
		return nil
	}
	return s.GroupAssociations
}

func (s *SMBData) GetAccountTabPermissions() map[string][]string {
	if s == nil {
		return nil
	}
	return s.AccountTabPermissions
}

func (s *SMBData) GetProductPermissions() map[string][]string {
	if s == nil {
		return nil
	}
	return s.ProductPermissions
}

// SMBFetcher will return smb data
type SMBFetcher interface {
	GetSMBData(ctx context.Context, partnerID string) (*SMBData, error)
}

// SMBFetch handles getting smb data
type SMBFetch struct {
	iamClient iam.SMBAPI
}

// NewSMBFetcher returns a new smb fetcher
func NewSMBFetcher(iamClient iam.SMBAPI) *SMBFetch {
	return &SMBFetch{
		iamClient: iamClient,
	}
}

// GetSMBData returns an smb subject id and default account group for an smb
// nolint: staticcheck
func (s *SMBFetch) GetSMBData(ctx context.Context, partnerID string) (*SMBData, error) {
	session, err := iam.GetSessionFromContext(ctx)
	if err != nil {
		return nil, err
	}
	smbUser, err := s.iamClient.SMBBySessionID(ctx, partnerID, session)
	if err != nil {
		return nil, err
	}
	if smbUser == nil {
		return nil, verrors.New(verrors.NotFound, "smb not found")
	}
	return &SMBData{
		SubjectID:               smbUser.SubjectID(),
		DefaultAccountGroup:     smbUser.DefaultAccountGroup,
		AssociatedAccountGroups: smbUser.AccountGroupAssociations,
		GroupAssociations:       smbUser.GroupAssociations,
		AccountTabPermissions:   smbUser.BusinessCenterTabs,
		ProductPermissions:      buildProductPermissionsMap(smbUser.AccountAccessPermissions),
	}, nil
}

func buildProductPermissionsMap(accountPermissions []*subject.AccountAccessPermission) map[string][]string {
	if accountPermissions == nil {
		return nil
	}
	permissions := make(map[string][]string)
	for _, a := range accountPermissions {
		permissions[a.AccountGroupID] = a.AccountIDs
	}
	return permissions
}
