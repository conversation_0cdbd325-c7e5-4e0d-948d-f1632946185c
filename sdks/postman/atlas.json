{"info": {"name": "atlas", "description": "APIs for atlas", "schema": "https://schema.getpostman.com/json/collection/v2.1.0"}, "item": [{"name": "atlas.v1.Atlas", "description": "The Atlas service ", "variable": [], "item": [{"name": "GetData", "description": "GetData gets the data that populates the Atlas navbar ", "variable": [], "request": {"url": "https://atlas-api-{{env}}.vendasta-internal.com/atlas.v1.Atlas/GetData", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"partnerId\": \"\",\n  \"marketId\": \"\",\n  \"accountGroupId\": \"\",\n  \"signOutNextUrl\": \"\",\n  \"groupPath\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetNavigationData", "description": "GetNavigationData gets the navigation data that populates the sidenav ", "variable": [], "request": {"url": "https://atlas-api-{{env}}.vendasta-internal.com/atlas.v1.Atlas/GetNavigationData", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"accountGroupId\": \"\",\n  \"groupPath\": \"\",\n  \"partnerId\": \"\",\n  \"marketId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetSalesInfo", "description": "GetSalesInfo gets the sales info that populates the sidenav ", "variable": [], "request": {"url": "https://atlas-api-{{env}}.vendasta-internal.com/atlas.v1.Atlas/GetSalesInfo", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"accountGroupId\": \"\",\n  \"groupPath\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetLocations", "description": "Deprecated (use ListLocations): GetLocations returns associated account groups or groups ", "variable": [], "request": {"url": "https://atlas-api-{{env}}.vendasta-internal.com/atlas.v1.Atlas/GetLocations", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"accountGroups\": {\n    \"accountGroupIds\": [\n      \"\"\n    ]\n  }\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "ListElevatedLocations", "description": "Deprecated (use ListLocations): ListElevatedLocations returns a paged list of locations available to an elevated user. ", "variable": [], "request": {"url": "https://atlas-api-{{env}}.vendasta-internal.com/atlas.v1.Atlas/ListElevatedLocations", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"partnerId\": \"\",\n  \"cursor\": \"\",\n  \"pageSize\": 0,\n  \"search\": \"\",\n  \"accountGroups\": false\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "ListLocations", "description": "ListLocations returns a paged list of locations available to a user. ", "variable": [], "request": {"url": "https://atlas-api-{{env}}.vendasta-internal.com/atlas.v1.Atlas/ListLocations", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"partnerId\": \"\",\n  \"cursor\": \"\",\n  \"pageSize\": 0,\n  \"search\": \"\",\n  \"includeAccountGroups\": false,\n  \"includeBrands\": false\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "ContactUs", "description": "ContactUs notifies the platform that the user is interested in contacting the partner ", "variable": [], "request": {"url": "https://atlas-api-{{env}}.vendasta-internal.com/atlas.v1.Atlas/ContactUs", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"accountGroupId\": \"\",\n  \"message\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "SetDefaultLocation", "description": "SetDefaultLocationRequest sets the default location on a subject ", "variable": [], "request": {"url": "https://atlas-api-{{env}}.vendasta-internal.com/atlas.v1.Atlas/SetDefaultLocation", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"accountGroupId\": \"\",\n  \"partnerId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetDefaultAccount", "description": "", "variable": [], "request": {"url": "https://atlas-api-{{env}}.vendasta-internal.com/atlas.v1.Atlas/GetDefaultAccount", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"partnerId\": \"\",\n  \"userId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "Set<PERSON><PERSON>aultAccount", "description": "", "variable": [], "request": {"url": "https://atlas-api-{{env}}.vendasta-internal.com/atlas.v1.Atlas/SetDefaultAccount", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"partnerId\": \"\",\n  \"userId\": \"\",\n  \"accountGroupId\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}], "auth": {"type": "", "bearer": []}}, {"name": "atlas.v1.Pins", "description": "The service for managing pinned products and navbar items ", "variable": [], "item": [{"name": "SetPins", "description": "SetPins will set the pins for a user in an business or brand ", "variable": [], "request": {"url": "https://atlas-api-{{env}}.vendasta-internal.com/atlas.v1.Pins/SetPins", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"identifier\": \"\",\n  \"items\": [\n    {\n      \"navigationId\": \"\"\n    }\n  ]\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetPins", "description": "GetPins will return the pins for a business or brand ", "variable": [], "request": {"url": "https://atlas-api-{{env}}.vendasta-internal.com/atlas.v1.Pins/GetPins", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"identifier\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}], "auth": {"type": "", "bearer": []}}, {"name": "atlas.v1.Languages", "description": "The service for managing your preferred language ", "variable": [], "item": [{"name": "SetLanguage", "description": "SetLanguage will remember your currently selected language ", "variable": [], "request": {"url": "https://atlas-api-{{env}}.vendasta-internal.com/atlas.v1.Languages/SetLanguage", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{\n  \"language\": \"\"\n}", "options": {"raw": {"language": "json"}}}}}, {"name": "GetLanguage", "description": "GetLanguage will return your currently selected language ", "variable": [], "request": {"url": "https://atlas-api-{{env}}.vendasta-internal.com/atlas.v1.Languages/GetLanguage", "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{session}}", "type": "string"}]}, "method": "POST", "description": "", "header": null, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}}], "auth": {"type": "", "bearer": []}}], "variable": [], "auth": []}