# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: atlas/v1/api.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from vendasta_types import annotations_pb2 as vendasta__types_dot_annotations__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='atlas/v1/api.proto',
  package='atlas.v1',
  syntax='proto3',
  serialized_pb=_b('\n\x12\x61tlas/v1/api.proto\x12\x08\x61tlas.v1\x1a\x1bgoogle/protobuf/empty.proto\x1a vendasta_types/annotations.proto\"A\n\x12UserNavigationItem\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\x12\x10\n\x08route_id\x18\x03 \x01(\t\"J\n\x14\x43\x65nterNavigationItem\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tentry_url\x18\x02 \x01(\t\x12\x11\n\tcenter_id\x18\x03 \x01(\t\"\x80\x01\n\x0eGetDataRequest\x12\x12\n\npartner_id\x18\x01 \x01(\t\x12\x11\n\tmarket_id\x18\x02 \x01(\t\x12\x18\n\x10\x61\x63\x63ount_group_id\x18\x03 \x01(\t\x12\x19\n\x11sign_out_next_url\x18\x04 \x01(\t\x12\x12\n\ngroup_path\x18\x05 \x01(\t\"6\n\x0cLocationData\x12\x15\n\rbusiness_name\x18\x01 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x02 \x01(\t\"`\n\x10UserSwitcherData\x12\x0f\n\x07user_id\x18\x01 \x01(\t\x12\x12\n\npartner_id\x18\x02 \x01(\t\x12\x14\n\x0cpartner_name\x18\x03 \x01(\t\x12\x11\n\tentry_url\x18\x04 \x01(\t\"\x97\x04\n\x0fGetDataResponse\x12*\n\x04user\x18\x01 \x03(\x0b\x32\x1c.atlas.v1.UserNavigationItem\x12/\n\x07\x63\x65nters\x18\x02 \x03(\x0b\x32\x1e.atlas.v1.CenterNavigationItem\x12\x10\n\x08username\x18\x03 \x01(\t\x12\r\n\x05\x65mail\x18\x04 \x01(\t\x12\x14\n\x0csign_out_url\x18\x05 \x01(\t\x12 \n\x05theme\x18\x06 \x01(\x0e\x32\x11.atlas.v1.UITheme\x12\x10\n\x08language\x18\x07 \x01(\t\x12&\n\x07theming\x18\x08 \x01(\x0b\x32\x11.atlas.v1.ThemingB\x02\x18\x01\x12\x1d\n\x15notifications_enabled\x18\t \x01(\x08\x12\x0f\n\x07user_id\x18\n \x01(\t\x12-\n\rlocation_data\x18\x0b \x01(\x0b\x32\x16.atlas.v1.LocationData\x12\x1d\n\x15impersonatee_username\x18\x0c \x01(\t\x12\x16\n\x0e\x65mail_verified\x18\r \x01(\x08\x12\x36\n\x12user_switcher_data\x18\x0e \x03(\x0b\x32\x1a.atlas.v1.UserSwitcherData\x12\x14\n\x0cpartner_name\x18\x0f \x01(\t\x12\x30\n\x15\x62usiness_app_ui_theme\x18\x10 \x01(\x0e\x32\x11.atlas.v1.UITheme\"\x9e\x01\n\x18GetNavigationDataRequest\x12\x18\n\x10\x61\x63\x63ount_group_id\x18\x01 \x01(\t\x12\x12\n\ngroup_path\x18\x02 \x01(\t\x12\x12\n\npartner_id\x18\x03 \x01(\t\x12\x11\n\tmarket_id\x18\x04 \x01(\t\x12-\n\rplatform_mode\x18\x05 \x01(\x0e\x32\x16.atlas.v1.PlatformMode\"C\n\x13GetSalesInfoRequest\x12\x18\n\x10\x61\x63\x63ount_group_id\x18\x01 \x01(\t\x12\x12\n\ngroup_path\x18\x02 \x01(\t\"?\n\x14GetSalesInfoResponse\x12\'\n\nsales_info\x18\x03 \x01(\x0b\x32\x13.atlas.v1.SalesInfo\"\x91\x01\n\x15SideNavigationSection\x12\x16\n\x0etranslation_id\x18\x01 \x01(\t\x12;\n\x15side_navigation_items\x18\x02 \x03(\x0b\x32\x1c.atlas.v1.SideNavigationItem\x12\r\n\x05label\x18\x03 \x01(\t\x12\x14\n\x0c\x63hip_content\x18\x04 \x01(\t\"\xfc\x01\n\x17SideNavigationContainer\x12\x16\n\x0etranslation_id\x18\x01 \x01(\t\x12;\n\x15side_navigation_items\x18\x02 \x03(\x0b\x32\x1c.atlas.v1.SideNavigationItem\x12\x0c\n\x04icon\x18\x03 \x01(\t\x12\x10\n\x08logo_url\x18\x04 \x01(\t\x12\r\n\x05label\x18\x05 \x01(\t\x12\x11\n\tshow_icon\x18\x06 \x01(\x08\x12\x14\n\x0c\x63hip_content\x18\x07 \x01(\t\x12\x0b\n\x03url\x18\x08 \x01(\t\x12\x10\n\x08pinnable\x18\t \x01(\x08\x12\x15\n\rnavigation_id\x18\n \x01(\t\"\xa2\x03\n\x12SideNavigationLink\x12\x15\n\rnavigation_id\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\x12\x0c\n\x04path\x18\x03 \x01(\t\x12\x1b\n\x13service_provider_id\x18\x04 \x01(\t\x12\x10\n\x08logo_url\x18\x05 \x01(\t\x12\x0c\n\x04icon\x18\x06 \x01(\t\x12\x16\n\x0etranslation_id\x18\x07 \x01(\t\x12\x10\n\x08\x65xternal\x18\x08 \x01(\x08\x12\r\n\x05label\x18\t \x01(\t\x12\x11\n\tshow_icon\x18\n \x01(\x08\x12\x10\n\x08pinnable\x18\x0b \x01(\x08\x12\x14\n\x0c\x63hip_content\x18\x0c \x01(\t\x12\x10\n\x08is_trial\x18\r \x01(\x08\x12\x15\n\ruser_required\x18\x0e \x01(\x08\x12\x17\n\x0fopen_in_new_tab\x18\x0f \x01(\x08\x12/\n\tsub_links\x18\x10 \x03(\x0b\x32\x1c.atlas.v1.SideNavigationLink\x12\x12\n\nlaunch_url\x18\x11 \x01(\t\x12\"\n\x1a\x64\x65scription_translation_id\x18\x12 \x01(\t\"P\n\x0c\x44ropdownItem\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x16\n\x0etranslation_id\x18\x03 \x01(\t\x12\r\n\x05label\x18\x04 \x01(\t\"\xe6\x01\n\x12SideNavigationItem\x12\x42\n\x17side_navigation_section\x18\x01 \x01(\x0b\x32\x1f.atlas.v1.SideNavigationSectionH\x00\x12\x46\n\x19side_navigation_container\x18\x02 \x01(\x0b\x32!.atlas.v1.SideNavigationContainerH\x00\x12<\n\x14side_navigation_link\x18\x03 \x01(\x0b\x32\x1c.atlas.v1.SideNavigationLinkH\x00\x42\x06\n\x04item\"O\n\tSalesInfo\x12\x13\n\x0bmarket_name\x18\x01 \x01(\t\x12-\n\rsales_contact\x18\x02 \x01(\x0b\x32\x16.atlas.v1.SalesContact\"\xce\x01\n\x0cSalesContact\x12\x17\n\x0fsales_person_id\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x12\n\nfirst_name\x18\x03 \x01(\t\x12\x11\n\tlast_name\x18\x04 \x01(\t\x12\x14\n\x0cphone_number\x18\x05 \x01(\t\x12\x18\n\x10photo_url_secure\x18\x06 \x01(\t\x12\x11\n\tjob_title\x18\x07 \x01(\t\x12\x0f\n\x07\x63ountry\x18\x08 \x01(\t\x12\x1b\n\x13meeting_booking_url\x18\t \x01(\t\"J\n\x05\x42rand\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\npath_nodes\x18\x02 \x03(\t\x12\x12\n\nhas_access\x18\x03 \x01(\x08\x12\x0b\n\x03url\x18\x04 \x01(\t\"#\n\nPinnedItem\x12\x15\n\rnavigation_id\x18\x01 \x01(\t\"\x91\x02\n\x08\x42randing\x12 \n\x05theme\x18\x01 \x01(\x0e\x32\x11.atlas.v1.UITheme\x12\x10\n\x08logo_url\x18\x02 \x01(\t\x12\x14\n\x0cpartner_name\x18\x03 \x01(\t\x12\x13\n\x0b\x63\x65nter_name\x18\x04 \x01(\t\x12&\n\x07theming\x18\x05 \x01(\x0b\x32\x11.atlas.v1.ThemingB\x02\x18\x01\x12\x1b\n\x13\x63obranding_logo_url\x18\x06 \x01(\t\x12\x13\n\x0bmarket_name\x18\x07 \x01(\t\x12\x1a\n\x12\x64\x61rk_mode_logo_url\x18\x08 \x01(\t\x12\x30\n\x15\x62usiness_app_ui_theme\x18\t \x01(\x0e\x32\x11.atlas.v1.UITheme\"s\n\x0c\x41\x63\x63ountGroup\x12\x18\n\x10\x61\x63\x63ount_group_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x03 \x01(\t\x12\x1d\n\x15\x61\x63tivated_product_ids\x18\x04 \x03(\t\x12\x0b\n\x03url\x18\x05 \x01(\t\"i\n\x08Location\x12/\n\raccount_group\x18\x01 \x01(\x0b\x32\x16.atlas.v1.AccountGroupH\x00\x12 \n\x05\x62rand\x18\x02 \x01(\x0b\x32\x0f.atlas.v1.BrandH\x00\x42\n\n\x08location\"G\n\x15\x41ssociatedLocationIDs\x12\x19\n\x11\x61\x63\x63ount_group_ids\x18\x01 \x03(\t\x12\x13\n\x0bgroup_paths\x18\x02 \x03(\t\"\xc4\x02\n\x07Theming\x12\x15\n\rprimary_color\x18\x01 \x01(\t\x12\x1b\n\x13primary_hover_color\x18\x02 \x01(\t\x12\x1c\n\x14primary_active_color\x18\x03 \x01(\t\x12\x17\n\x0fsecondary_color\x18\x04 \x01(\t\x12\x1d\n\x15secondary_hover_color\x18\x05 \x01(\t\x12\x1e\n\x16secondary_active_color\x18\x06 \x01(\t\x12\x12\n\nfont_color\x18\x07 \x01(\t\x12\x1b\n\x13\x66ont_disabled_color\x18\x08 \x01(\t\x12\x15\n\raccents_color\x18\t \x01(\t\x12\x1c\n\x14\x61\x63\x63\x65nts_active_color\x18\n \x01(\t\x12\x13\n\x0b\x66ocus_color\x18\x0b \x01(\t\x12\x14\n\x0c\x62order_color\x18\x0c \x01(\t\":\n\x0fRetentionConfig\x12\'\n\x1f\x63\x61ncellation_notification_email\x18\x01 \x01(\t\"2\n\x0eTotalLocations\x12\x10\n\x08\x61\x63\x63ounts\x18\x01 \x01(\x03\x12\x0e\n\x06\x62rands\x18\x02 \x01(\x03\"\xf5\x04\n\x19GetNavigationDataResponse\x12$\n\x08\x62randing\x18\x02 \x01(\x0b\x32\x12.atlas.v1.Branding\x12\'\n\nsales_info\x18\x03 \x01(\x0b\x32\x13.atlas.v1.SalesInfo\x12*\n\x0cpinned_items\x18\x04 \x03(\x0b\x32\x14.atlas.v1.PinnedItem\x12@\n\x17\x61ssociated_location_ids\x18\x05 \x01(\x0b\x32\x1f.atlas.v1.AssociatedLocationIDs\x12\x18\n\x10\x64\x65\x66\x61ult_location\x18\x06 \x01(\t\x12\x10\n\x08language\x18\x08 \x01(\t\x12.\n\x0e\x64ropdown_items\x18\t \x03(\x0b\x32\x16.atlas.v1.DropdownItem\x12\x1a\n\x12\x63urrent_brand_name\x18\n \x01(\t\x12)\n\tuser_view\x18\x0b \x01(\x0e\x32\x16.atlas.v1.UserViewType\x12\x33\n\x10retention_config\x18\x0c \x01(\x0b\x32\x19.atlas.v1.RetentionConfig\x12\x31\n\x0ftotal_locations\x18\r \x01(\x0b\x32\x18.atlas.v1.TotalLocations\x12\x0f\n\x07user_id\x18\x0e \x01(\t\x12\x1d\n\x15\x62usiness_app_branding\x18\x0f \x01(\x08\x12\x1c\n\x14\x64isable_business_nav\x18\x10 \x01(\x08\x12\x36\n\x10navigation_items\x18\x11 \x03(\x0b\x32\x1c.atlas.v1.SideNavigationItemJ\x04\x08\x01\x10\x02J\x04\x08\x07\x10\x08\"I\n\x0eSetPinsRequest\x12\x12\n\nidentifier\x18\x01 \x01(\t\x12#\n\x05items\x18\x03 \x03(\x0b\x32\x14.atlas.v1.PinnedItem\"$\n\x0eGetPinsRequest\x12\x12\n\nidentifier\x18\x01 \x01(\t\"6\n\x0fGetPinsResponse\x12#\n\x05items\x18\x01 \x03(\x0b\x32\x14.atlas.v1.PinnedItem\"\xed\x01\n\x13GetLocationsRequest\x12\x45\n\x0e\x61\x63\x63ount_groups\x18\x01 \x01(\x0b\x32+.atlas.v1.GetLocationsRequest.AccountGroupsH\x00\x12\x36\n\x06groups\x18\x02 \x01(\x0b\x32$.atlas.v1.GetLocationsRequest.GroupsH\x00\x1a*\n\rAccountGroups\x12\x19\n\x11\x61\x63\x63ount_group_ids\x18\x01 \x03(\t\x1a\x1d\n\x06Groups\x12\x13\n\x0bgroup_paths\x18\x01 \x03(\tB\x0c\n\nidentifier\"=\n\x14GetLocationsResponse\x12%\n\tlocations\x18\x01 \x03(\x0b\x32\x12.atlas.v1.Location\"\x99\x01\n\x1cListElevatedLocationsRequest\x12\x12\n\npartner_id\x18\x01 \x01(\t\x12\x0e\n\x06\x63ursor\x18\x02 \x01(\t\x12\x11\n\tpage_size\x18\x03 \x01(\x03\x12\x0e\n\x06search\x18\x04 \x01(\t\x12\x18\n\x0e\x61\x63\x63ount_groups\x18\x05 \x01(\x08H\x00\x12\x10\n\x06\x62rands\x18\x06 \x01(\x08H\x00\x42\x06\n\x04type\"h\n\x1dListElevatedLocationsResponse\x12%\n\tlocations\x18\x01 \x03(\x0b\x32\x12.atlas.v1.Location\x12\x0e\n\x06\x63ursor\x18\x02 \x01(\t\x12\x10\n\x08has_more\x18\x03 \x01(\x08\"\x95\x01\n\x14ListLocationsRequest\x12\x12\n\npartner_id\x18\x02 \x01(\t\x12\x0e\n\x06\x63ursor\x18\x03 \x01(\t\x12\x11\n\tpage_size\x18\x04 \x01(\x03\x12\x0e\n\x06search\x18\x05 \x01(\t\x12\x1e\n\x16include_account_groups\x18\x06 \x01(\x08\x12\x16\n\x0einclude_brands\x18\x07 \x01(\x08\"`\n\x15ListLocationsResponse\x12%\n\tlocations\x18\x01 \x03(\x0b\x32\x12.atlas.v1.Location\x12\x0e\n\x06\x63ursor\x18\x02 \x01(\t\x12\x10\n\x08has_more\x18\x03 \x01(\x08\"&\n\x12SetLanguageRequest\x12\x10\n\x08language\x18\x01 \x01(\t\"\x14\n\x12GetLanguageRequest\"\'\n\x13GetLanguageResponse\x12\x10\n\x08language\x18\x01 \x01(\t\"=\n\x10\x43ontactUsRequest\x12\x18\n\x10\x61\x63\x63ount_group_id\x18\x01 \x01(\t\x12\x0f\n\x07message\x18\x02 \x01(\t\"k\n\x19SetDefaultLocationRequest\x12\x12\n\npartner_id\x18\x02 \x01(\t\x12\x1a\n\x10\x61\x63\x63ount_group_id\x18\x01 \x01(\tH\x00\x12\x12\n\x08group_id\x18\x03 \x01(\tH\x00\x42\n\n\x08location\"/\n\x19GetDefaultLocationRequest\x12\x12\n\npartner_id\x18\x01 \x01(\t\"X\n\x1aGetDefaultLocationResponse\x12\x1a\n\x10\x61\x63\x63ount_group_id\x18\x01 \x01(\tH\x00\x12\x12\n\x08group_id\x18\x02 \x01(\tH\x00\x42\n\n\x08location*N\n\x07UITheme\x12\x11\n\rUI_THEME_DARK\x10\x00\x12\x12\n\x0eUI_THEME_LIGHT\x10\x01\x12\x1c\n\x18UI_THEME_USER_PREFERENCE\x10\x02*@\n\x0cUserViewType\x12\x16\n\x12USER_VIEW_TYPE_SMB\x10\x00\x12\x18\n\x14USER_VIEW_TYPE_ADMIN\x10\x01*#\n\x0cPlatformMode\x12\x07\n\x03WEB\x10\x00\x12\n\n\x06MOBILE\x10\x01\x32\xe8\x07\n\x05\x41tlas\x12Y\n\x07GetData\x12\x18.atlas.v1.GetDataRequest\x1a\x19.atlas.v1.GetDataResponse\"\x19\x82\xb5\x18\x15\n\x05\x61\x64min\n\x0c\x62usiness-app\x12w\n\x11GetNavigationData\x12\".atlas.v1.GetNavigationDataRequest\x1a#.atlas.v1.GetNavigationDataResponse\"\x19\x82\xb5\x18\x15\n\x05\x61\x64min\n\x0c\x62usiness-app\x12h\n\x0cGetSalesInfo\x12\x1d.atlas.v1.GetSalesInfoRequest\x1a\x1e.atlas.v1.GetSalesInfoResponse\"\x19\x82\xb5\x18\x15\n\x05\x61\x64min\n\x0c\x62usiness-app\x12h\n\x0cGetLocations\x12\x1d.atlas.v1.GetLocationsRequest\x1a\x1e.atlas.v1.GetLocationsResponse\"\x19\x82\xb5\x18\x15\n\x05\x61\x64min\n\x0c\x62usiness-app\x12\x83\x01\n\x15ListElevatedLocations\x12&.atlas.v1.ListElevatedLocationsRequest\x1a\'.atlas.v1.ListElevatedLocationsResponse\"\x19\x82\xb5\x18\x15\n\x05\x61\x64min\n\x0c\x62usiness-app\x12k\n\rListLocations\x12\x1e.atlas.v1.ListLocationsRequest\x1a\x1f.atlas.v1.ListLocationsResponse\"\x19\x82\xb5\x18\x15\n\x05\x61\x64min\n\x0c\x62usiness-app\x12Z\n\tContactUs\x12\x1a.atlas.v1.ContactUsRequest\x1a\x16.google.protobuf.Empty\"\x19\x82\xb5\x18\x15\n\x05\x61\x64min\n\x0c\x62usiness-app\x12l\n\x12SetDefaultLocation\x12#.atlas.v1.SetDefaultLocationRequest\x1a\x16.google.protobuf.Empty\"\x19\x82\xb5\x18\x15\n\x05\x61\x64min\n\x0c\x62usiness-app\x12z\n\x12GetDefaultLocation\x12#.atlas.v1.GetDefaultLocationRequest\x1a$.atlas.v1.GetDefaultLocationResponse\"\x19\x82\xb5\x18\x15\n\x05\x61\x64min\n\x0c\x62usiness-app2\xb9\x01\n\x04Pins\x12V\n\x07SetPins\x12\x18.atlas.v1.SetPinsRequest\x1a\x16.google.protobuf.Empty\"\x19\x82\xb5\x18\x15\n\x05\x61\x64min\n\x0c\x62usiness-app\x12Y\n\x07GetPins\x12\x18.atlas.v1.GetPinsRequest\x1a\x19.atlas.v1.GetPinsResponse\"\x19\x82\xb5\x18\x15\n\x05\x61\x64min\n\x0c\x62usiness-app2\xd2\x01\n\tLanguages\x12^\n\x0bSetLanguage\x12\x1c.atlas.v1.SetLanguageRequest\x1a\x16.google.protobuf.Empty\"\x19\x82\xb5\x18\x15\n\x05\x61\x64min\n\x0c\x62usiness-app\x12\x65\n\x0bGetLanguage\x12\x1c.atlas.v1.GetLanguageRequest\x1a\x1d.atlas.v1.GetLanguageResponse\"\x19\x82\xb5\x18\x15\n\x05\x61\x64min\n\x0c\x62usiness-appBf\n\x1f\x63om.vendasta.atlas.v1.generatedB\x08\x41piProtoZ9github.com/vendasta/generated-protos-go/atlas/v1;atlas_v1b\x06proto3')
  ,
  dependencies=[google_dot_protobuf_dot_empty__pb2.DESCRIPTOR,vendasta__types_dot_annotations__pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

_UITHEME = _descriptor.EnumDescriptor(
  name='UITheme',
  full_name='atlas.v1.UITheme',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UI_THEME_DARK', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UI_THEME_LIGHT', index=1, number=1,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UI_THEME_USER_PREFERENCE', index=2, number=2,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=5940,
  serialized_end=6018,
)
_sym_db.RegisterEnumDescriptor(_UITHEME)

UITheme = enum_type_wrapper.EnumTypeWrapper(_UITHEME)
_USERVIEWTYPE = _descriptor.EnumDescriptor(
  name='UserViewType',
  full_name='atlas.v1.UserViewType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='USER_VIEW_TYPE_SMB', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='USER_VIEW_TYPE_ADMIN', index=1, number=1,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=6020,
  serialized_end=6084,
)
_sym_db.RegisterEnumDescriptor(_USERVIEWTYPE)

UserViewType = enum_type_wrapper.EnumTypeWrapper(_USERVIEWTYPE)
_PLATFORMMODE = _descriptor.EnumDescriptor(
  name='PlatformMode',
  full_name='atlas.v1.PlatformMode',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='WEB', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='MOBILE', index=1, number=1,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=6086,
  serialized_end=6121,
)
_sym_db.RegisterEnumDescriptor(_PLATFORMMODE)

PlatformMode = enum_type_wrapper.EnumTypeWrapper(_PLATFORMMODE)
UI_THEME_DARK = 0
UI_THEME_LIGHT = 1
UI_THEME_USER_PREFERENCE = 2
USER_VIEW_TYPE_SMB = 0
USER_VIEW_TYPE_ADMIN = 1
WEB = 0
MOBILE = 1



_USERNAVIGATIONITEM = _descriptor.Descriptor(
  name='UserNavigationItem',
  full_name='atlas.v1.UserNavigationItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='text', full_name='atlas.v1.UserNavigationItem.text', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='url', full_name='atlas.v1.UserNavigationItem.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='route_id', full_name='atlas.v1.UserNavigationItem.route_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=95,
  serialized_end=160,
)


_CENTERNAVIGATIONITEM = _descriptor.Descriptor(
  name='CenterNavigationItem',
  full_name='atlas.v1.CenterNavigationItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='atlas.v1.CenterNavigationItem.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='entry_url', full_name='atlas.v1.CenterNavigationItem.entry_url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='center_id', full_name='atlas.v1.CenterNavigationItem.center_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=162,
  serialized_end=236,
)


_GETDATAREQUEST = _descriptor.Descriptor(
  name='GetDataRequest',
  full_name='atlas.v1.GetDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='atlas.v1.GetDataRequest.partner_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='market_id', full_name='atlas.v1.GetDataRequest.market_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='account_group_id', full_name='atlas.v1.GetDataRequest.account_group_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sign_out_next_url', full_name='atlas.v1.GetDataRequest.sign_out_next_url', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='group_path', full_name='atlas.v1.GetDataRequest.group_path', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=239,
  serialized_end=367,
)


_LOCATIONDATA = _descriptor.Descriptor(
  name='LocationData',
  full_name='atlas.v1.LocationData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='business_name', full_name='atlas.v1.LocationData.business_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='address', full_name='atlas.v1.LocationData.address', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=369,
  serialized_end=423,
)


_USERSWITCHERDATA = _descriptor.Descriptor(
  name='UserSwitcherData',
  full_name='atlas.v1.UserSwitcherData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='user_id', full_name='atlas.v1.UserSwitcherData.user_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='atlas.v1.UserSwitcherData.partner_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partner_name', full_name='atlas.v1.UserSwitcherData.partner_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='entry_url', full_name='atlas.v1.UserSwitcherData.entry_url', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=425,
  serialized_end=521,
)


_GETDATARESPONSE = _descriptor.Descriptor(
  name='GetDataResponse',
  full_name='atlas.v1.GetDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='user', full_name='atlas.v1.GetDataResponse.user', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='centers', full_name='atlas.v1.GetDataResponse.centers', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='username', full_name='atlas.v1.GetDataResponse.username', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='email', full_name='atlas.v1.GetDataResponse.email', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sign_out_url', full_name='atlas.v1.GetDataResponse.sign_out_url', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='theme', full_name='atlas.v1.GetDataResponse.theme', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='language', full_name='atlas.v1.GetDataResponse.language', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='theming', full_name='atlas.v1.GetDataResponse.theming', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=_descriptor._ParseOptions(descriptor_pb2.FieldOptions(), _b('\030\001'))),
    _descriptor.FieldDescriptor(
      name='notifications_enabled', full_name='atlas.v1.GetDataResponse.notifications_enabled', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='atlas.v1.GetDataResponse.user_id', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='location_data', full_name='atlas.v1.GetDataResponse.location_data', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='impersonatee_username', full_name='atlas.v1.GetDataResponse.impersonatee_username', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='email_verified', full_name='atlas.v1.GetDataResponse.email_verified', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='user_switcher_data', full_name='atlas.v1.GetDataResponse.user_switcher_data', index=13,
      number=14, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partner_name', full_name='atlas.v1.GetDataResponse.partner_name', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='business_app_ui_theme', full_name='atlas.v1.GetDataResponse.business_app_ui_theme', index=15,
      number=16, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=524,
  serialized_end=1059,
)


_GETNAVIGATIONDATAREQUEST = _descriptor.Descriptor(
  name='GetNavigationDataRequest',
  full_name='atlas.v1.GetNavigationDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group_id', full_name='atlas.v1.GetNavigationDataRequest.account_group_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='group_path', full_name='atlas.v1.GetNavigationDataRequest.group_path', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='atlas.v1.GetNavigationDataRequest.partner_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='market_id', full_name='atlas.v1.GetNavigationDataRequest.market_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='platform_mode', full_name='atlas.v1.GetNavigationDataRequest.platform_mode', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1062,
  serialized_end=1220,
)


_GETSALESINFOREQUEST = _descriptor.Descriptor(
  name='GetSalesInfoRequest',
  full_name='atlas.v1.GetSalesInfoRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group_id', full_name='atlas.v1.GetSalesInfoRequest.account_group_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='group_path', full_name='atlas.v1.GetSalesInfoRequest.group_path', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1222,
  serialized_end=1289,
)


_GETSALESINFORESPONSE = _descriptor.Descriptor(
  name='GetSalesInfoResponse',
  full_name='atlas.v1.GetSalesInfoResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sales_info', full_name='atlas.v1.GetSalesInfoResponse.sales_info', index=0,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1291,
  serialized_end=1354,
)


_SIDENAVIGATIONSECTION = _descriptor.Descriptor(
  name='SideNavigationSection',
  full_name='atlas.v1.SideNavigationSection',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='translation_id', full_name='atlas.v1.SideNavigationSection.translation_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='side_navigation_items', full_name='atlas.v1.SideNavigationSection.side_navigation_items', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='label', full_name='atlas.v1.SideNavigationSection.label', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='chip_content', full_name='atlas.v1.SideNavigationSection.chip_content', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1357,
  serialized_end=1502,
)


_SIDENAVIGATIONCONTAINER = _descriptor.Descriptor(
  name='SideNavigationContainer',
  full_name='atlas.v1.SideNavigationContainer',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='translation_id', full_name='atlas.v1.SideNavigationContainer.translation_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='side_navigation_items', full_name='atlas.v1.SideNavigationContainer.side_navigation_items', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='icon', full_name='atlas.v1.SideNavigationContainer.icon', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='logo_url', full_name='atlas.v1.SideNavigationContainer.logo_url', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='label', full_name='atlas.v1.SideNavigationContainer.label', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='show_icon', full_name='atlas.v1.SideNavigationContainer.show_icon', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='chip_content', full_name='atlas.v1.SideNavigationContainer.chip_content', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='url', full_name='atlas.v1.SideNavigationContainer.url', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='pinnable', full_name='atlas.v1.SideNavigationContainer.pinnable', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='navigation_id', full_name='atlas.v1.SideNavigationContainer.navigation_id', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1505,
  serialized_end=1757,
)


_SIDENAVIGATIONLINK = _descriptor.Descriptor(
  name='SideNavigationLink',
  full_name='atlas.v1.SideNavigationLink',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='navigation_id', full_name='atlas.v1.SideNavigationLink.navigation_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='url', full_name='atlas.v1.SideNavigationLink.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='path', full_name='atlas.v1.SideNavigationLink.path', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='service_provider_id', full_name='atlas.v1.SideNavigationLink.service_provider_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='logo_url', full_name='atlas.v1.SideNavigationLink.logo_url', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='icon', full_name='atlas.v1.SideNavigationLink.icon', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='translation_id', full_name='atlas.v1.SideNavigationLink.translation_id', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='external', full_name='atlas.v1.SideNavigationLink.external', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='label', full_name='atlas.v1.SideNavigationLink.label', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='show_icon', full_name='atlas.v1.SideNavigationLink.show_icon', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='pinnable', full_name='atlas.v1.SideNavigationLink.pinnable', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='chip_content', full_name='atlas.v1.SideNavigationLink.chip_content', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='is_trial', full_name='atlas.v1.SideNavigationLink.is_trial', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='user_required', full_name='atlas.v1.SideNavigationLink.user_required', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='open_in_new_tab', full_name='atlas.v1.SideNavigationLink.open_in_new_tab', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sub_links', full_name='atlas.v1.SideNavigationLink.sub_links', index=15,
      number=16, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='launch_url', full_name='atlas.v1.SideNavigationLink.launch_url', index=16,
      number=17, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='description_translation_id', full_name='atlas.v1.SideNavigationLink.description_translation_id', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1760,
  serialized_end=2178,
)


_DROPDOWNITEM = _descriptor.Descriptor(
  name='DropdownItem',
  full_name='atlas.v1.DropdownItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='url', full_name='atlas.v1.DropdownItem.url', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='path', full_name='atlas.v1.DropdownItem.path', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='translation_id', full_name='atlas.v1.DropdownItem.translation_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='label', full_name='atlas.v1.DropdownItem.label', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2180,
  serialized_end=2260,
)


_SIDENAVIGATIONITEM = _descriptor.Descriptor(
  name='SideNavigationItem',
  full_name='atlas.v1.SideNavigationItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='side_navigation_section', full_name='atlas.v1.SideNavigationItem.side_navigation_section', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='side_navigation_container', full_name='atlas.v1.SideNavigationItem.side_navigation_container', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='side_navigation_link', full_name='atlas.v1.SideNavigationItem.side_navigation_link', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='item', full_name='atlas.v1.SideNavigationItem.item',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=2263,
  serialized_end=2493,
)


_SALESINFO = _descriptor.Descriptor(
  name='SalesInfo',
  full_name='atlas.v1.SalesInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='market_name', full_name='atlas.v1.SalesInfo.market_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sales_contact', full_name='atlas.v1.SalesInfo.sales_contact', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2495,
  serialized_end=2574,
)


_SALESCONTACT = _descriptor.Descriptor(
  name='SalesContact',
  full_name='atlas.v1.SalesContact',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sales_person_id', full_name='atlas.v1.SalesContact.sales_person_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='email', full_name='atlas.v1.SalesContact.email', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='first_name', full_name='atlas.v1.SalesContact.first_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='last_name', full_name='atlas.v1.SalesContact.last_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='phone_number', full_name='atlas.v1.SalesContact.phone_number', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='photo_url_secure', full_name='atlas.v1.SalesContact.photo_url_secure', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='job_title', full_name='atlas.v1.SalesContact.job_title', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='country', full_name='atlas.v1.SalesContact.country', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='meeting_booking_url', full_name='atlas.v1.SalesContact.meeting_booking_url', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2577,
  serialized_end=2783,
)


_BRAND = _descriptor.Descriptor(
  name='Brand',
  full_name='atlas.v1.Brand',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='atlas.v1.Brand.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='path_nodes', full_name='atlas.v1.Brand.path_nodes', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='has_access', full_name='atlas.v1.Brand.has_access', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='url', full_name='atlas.v1.Brand.url', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2785,
  serialized_end=2859,
)


_PINNEDITEM = _descriptor.Descriptor(
  name='PinnedItem',
  full_name='atlas.v1.PinnedItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='navigation_id', full_name='atlas.v1.PinnedItem.navigation_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2861,
  serialized_end=2896,
)


_BRANDING = _descriptor.Descriptor(
  name='Branding',
  full_name='atlas.v1.Branding',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='theme', full_name='atlas.v1.Branding.theme', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='logo_url', full_name='atlas.v1.Branding.logo_url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partner_name', full_name='atlas.v1.Branding.partner_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='center_name', full_name='atlas.v1.Branding.center_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='theming', full_name='atlas.v1.Branding.theming', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=_descriptor._ParseOptions(descriptor_pb2.FieldOptions(), _b('\030\001'))),
    _descriptor.FieldDescriptor(
      name='cobranding_logo_url', full_name='atlas.v1.Branding.cobranding_logo_url', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='market_name', full_name='atlas.v1.Branding.market_name', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='dark_mode_logo_url', full_name='atlas.v1.Branding.dark_mode_logo_url', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='business_app_ui_theme', full_name='atlas.v1.Branding.business_app_ui_theme', index=8,
      number=9, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2899,
  serialized_end=3172,
)


_ACCOUNTGROUP = _descriptor.Descriptor(
  name='AccountGroup',
  full_name='atlas.v1.AccountGroup',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group_id', full_name='atlas.v1.AccountGroup.account_group_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='name', full_name='atlas.v1.AccountGroup.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='address', full_name='atlas.v1.AccountGroup.address', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='activated_product_ids', full_name='atlas.v1.AccountGroup.activated_product_ids', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='url', full_name='atlas.v1.AccountGroup.url', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3174,
  serialized_end=3289,
)


_LOCATION = _descriptor.Descriptor(
  name='Location',
  full_name='atlas.v1.Location',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group', full_name='atlas.v1.Location.account_group', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='brand', full_name='atlas.v1.Location.brand', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='location', full_name='atlas.v1.Location.location',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=3291,
  serialized_end=3396,
)


_ASSOCIATEDLOCATIONIDS = _descriptor.Descriptor(
  name='AssociatedLocationIDs',
  full_name='atlas.v1.AssociatedLocationIDs',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group_ids', full_name='atlas.v1.AssociatedLocationIDs.account_group_ids', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='group_paths', full_name='atlas.v1.AssociatedLocationIDs.group_paths', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3398,
  serialized_end=3469,
)


_THEMING = _descriptor.Descriptor(
  name='Theming',
  full_name='atlas.v1.Theming',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='primary_color', full_name='atlas.v1.Theming.primary_color', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='primary_hover_color', full_name='atlas.v1.Theming.primary_hover_color', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='primary_active_color', full_name='atlas.v1.Theming.primary_active_color', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='secondary_color', full_name='atlas.v1.Theming.secondary_color', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='secondary_hover_color', full_name='atlas.v1.Theming.secondary_hover_color', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='secondary_active_color', full_name='atlas.v1.Theming.secondary_active_color', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='font_color', full_name='atlas.v1.Theming.font_color', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='font_disabled_color', full_name='atlas.v1.Theming.font_disabled_color', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='accents_color', full_name='atlas.v1.Theming.accents_color', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='accents_active_color', full_name='atlas.v1.Theming.accents_active_color', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='focus_color', full_name='atlas.v1.Theming.focus_color', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='border_color', full_name='atlas.v1.Theming.border_color', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3472,
  serialized_end=3796,
)


_RETENTIONCONFIG = _descriptor.Descriptor(
  name='RetentionConfig',
  full_name='atlas.v1.RetentionConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='cancellation_notification_email', full_name='atlas.v1.RetentionConfig.cancellation_notification_email', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3798,
  serialized_end=3856,
)


_TOTALLOCATIONS = _descriptor.Descriptor(
  name='TotalLocations',
  full_name='atlas.v1.TotalLocations',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounts', full_name='atlas.v1.TotalLocations.accounts', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='brands', full_name='atlas.v1.TotalLocations.brands', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3858,
  serialized_end=3908,
)


_GETNAVIGATIONDATARESPONSE = _descriptor.Descriptor(
  name='GetNavigationDataResponse',
  full_name='atlas.v1.GetNavigationDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='branding', full_name='atlas.v1.GetNavigationDataResponse.branding', index=0,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sales_info', full_name='atlas.v1.GetNavigationDataResponse.sales_info', index=1,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='pinned_items', full_name='atlas.v1.GetNavigationDataResponse.pinned_items', index=2,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='associated_location_ids', full_name='atlas.v1.GetNavigationDataResponse.associated_location_ids', index=3,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='default_location', full_name='atlas.v1.GetNavigationDataResponse.default_location', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='language', full_name='atlas.v1.GetNavigationDataResponse.language', index=5,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='dropdown_items', full_name='atlas.v1.GetNavigationDataResponse.dropdown_items', index=6,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='current_brand_name', full_name='atlas.v1.GetNavigationDataResponse.current_brand_name', index=7,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='user_view', full_name='atlas.v1.GetNavigationDataResponse.user_view', index=8,
      number=11, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='retention_config', full_name='atlas.v1.GetNavigationDataResponse.retention_config', index=9,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='total_locations', full_name='atlas.v1.GetNavigationDataResponse.total_locations', index=10,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='atlas.v1.GetNavigationDataResponse.user_id', index=11,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='business_app_branding', full_name='atlas.v1.GetNavigationDataResponse.business_app_branding', index=12,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='disable_business_nav', full_name='atlas.v1.GetNavigationDataResponse.disable_business_nav', index=13,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='navigation_items', full_name='atlas.v1.GetNavigationDataResponse.navigation_items', index=14,
      number=17, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3911,
  serialized_end=4540,
)


_SETPINSREQUEST = _descriptor.Descriptor(
  name='SetPinsRequest',
  full_name='atlas.v1.SetPinsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='identifier', full_name='atlas.v1.SetPinsRequest.identifier', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='items', full_name='atlas.v1.SetPinsRequest.items', index=1,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4542,
  serialized_end=4615,
)


_GETPINSREQUEST = _descriptor.Descriptor(
  name='GetPinsRequest',
  full_name='atlas.v1.GetPinsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='identifier', full_name='atlas.v1.GetPinsRequest.identifier', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4617,
  serialized_end=4653,
)


_GETPINSRESPONSE = _descriptor.Descriptor(
  name='GetPinsResponse',
  full_name='atlas.v1.GetPinsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='items', full_name='atlas.v1.GetPinsResponse.items', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4655,
  serialized_end=4709,
)


_GETLOCATIONSREQUEST_ACCOUNTGROUPS = _descriptor.Descriptor(
  name='AccountGroups',
  full_name='atlas.v1.GetLocationsRequest.AccountGroups',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group_ids', full_name='atlas.v1.GetLocationsRequest.AccountGroups.account_group_ids', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4862,
  serialized_end=4904,
)

_GETLOCATIONSREQUEST_GROUPS = _descriptor.Descriptor(
  name='Groups',
  full_name='atlas.v1.GetLocationsRequest.Groups',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='group_paths', full_name='atlas.v1.GetLocationsRequest.Groups.group_paths', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4906,
  serialized_end=4935,
)

_GETLOCATIONSREQUEST = _descriptor.Descriptor(
  name='GetLocationsRequest',
  full_name='atlas.v1.GetLocationsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_groups', full_name='atlas.v1.GetLocationsRequest.account_groups', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='groups', full_name='atlas.v1.GetLocationsRequest.groups', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[_GETLOCATIONSREQUEST_ACCOUNTGROUPS, _GETLOCATIONSREQUEST_GROUPS, ],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='identifier', full_name='atlas.v1.GetLocationsRequest.identifier',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=4712,
  serialized_end=4949,
)


_GETLOCATIONSRESPONSE = _descriptor.Descriptor(
  name='GetLocationsResponse',
  full_name='atlas.v1.GetLocationsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='locations', full_name='atlas.v1.GetLocationsResponse.locations', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4951,
  serialized_end=5012,
)


_LISTELEVATEDLOCATIONSREQUEST = _descriptor.Descriptor(
  name='ListElevatedLocationsRequest',
  full_name='atlas.v1.ListElevatedLocationsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='atlas.v1.ListElevatedLocationsRequest.partner_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cursor', full_name='atlas.v1.ListElevatedLocationsRequest.cursor', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='page_size', full_name='atlas.v1.ListElevatedLocationsRequest.page_size', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='search', full_name='atlas.v1.ListElevatedLocationsRequest.search', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='account_groups', full_name='atlas.v1.ListElevatedLocationsRequest.account_groups', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='brands', full_name='atlas.v1.ListElevatedLocationsRequest.brands', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='type', full_name='atlas.v1.ListElevatedLocationsRequest.type',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=5015,
  serialized_end=5168,
)


_LISTELEVATEDLOCATIONSRESPONSE = _descriptor.Descriptor(
  name='ListElevatedLocationsResponse',
  full_name='atlas.v1.ListElevatedLocationsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='locations', full_name='atlas.v1.ListElevatedLocationsResponse.locations', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cursor', full_name='atlas.v1.ListElevatedLocationsResponse.cursor', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='has_more', full_name='atlas.v1.ListElevatedLocationsResponse.has_more', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5170,
  serialized_end=5274,
)


_LISTLOCATIONSREQUEST = _descriptor.Descriptor(
  name='ListLocationsRequest',
  full_name='atlas.v1.ListLocationsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='atlas.v1.ListLocationsRequest.partner_id', index=0,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cursor', full_name='atlas.v1.ListLocationsRequest.cursor', index=1,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='page_size', full_name='atlas.v1.ListLocationsRequest.page_size', index=2,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='search', full_name='atlas.v1.ListLocationsRequest.search', index=3,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='include_account_groups', full_name='atlas.v1.ListLocationsRequest.include_account_groups', index=4,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='include_brands', full_name='atlas.v1.ListLocationsRequest.include_brands', index=5,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5277,
  serialized_end=5426,
)


_LISTLOCATIONSRESPONSE = _descriptor.Descriptor(
  name='ListLocationsResponse',
  full_name='atlas.v1.ListLocationsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='locations', full_name='atlas.v1.ListLocationsResponse.locations', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cursor', full_name='atlas.v1.ListLocationsResponse.cursor', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='has_more', full_name='atlas.v1.ListLocationsResponse.has_more', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5428,
  serialized_end=5524,
)


_SETLANGUAGEREQUEST = _descriptor.Descriptor(
  name='SetLanguageRequest',
  full_name='atlas.v1.SetLanguageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='language', full_name='atlas.v1.SetLanguageRequest.language', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5526,
  serialized_end=5564,
)


_GETLANGUAGEREQUEST = _descriptor.Descriptor(
  name='GetLanguageRequest',
  full_name='atlas.v1.GetLanguageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5566,
  serialized_end=5586,
)


_GETLANGUAGERESPONSE = _descriptor.Descriptor(
  name='GetLanguageResponse',
  full_name='atlas.v1.GetLanguageResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='language', full_name='atlas.v1.GetLanguageResponse.language', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5588,
  serialized_end=5627,
)


_CONTACTUSREQUEST = _descriptor.Descriptor(
  name='ContactUsRequest',
  full_name='atlas.v1.ContactUsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group_id', full_name='atlas.v1.ContactUsRequest.account_group_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='message', full_name='atlas.v1.ContactUsRequest.message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5629,
  serialized_end=5690,
)


_SETDEFAULTLOCATIONREQUEST = _descriptor.Descriptor(
  name='SetDefaultLocationRequest',
  full_name='atlas.v1.SetDefaultLocationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='atlas.v1.SetDefaultLocationRequest.partner_id', index=0,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='account_group_id', full_name='atlas.v1.SetDefaultLocationRequest.account_group_id', index=1,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='group_id', full_name='atlas.v1.SetDefaultLocationRequest.group_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='location', full_name='atlas.v1.SetDefaultLocationRequest.location',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=5692,
  serialized_end=5799,
)


_GETDEFAULTLOCATIONREQUEST = _descriptor.Descriptor(
  name='GetDefaultLocationRequest',
  full_name='atlas.v1.GetDefaultLocationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='atlas.v1.GetDefaultLocationRequest.partner_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5801,
  serialized_end=5848,
)


_GETDEFAULTLOCATIONRESPONSE = _descriptor.Descriptor(
  name='GetDefaultLocationResponse',
  full_name='atlas.v1.GetDefaultLocationResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group_id', full_name='atlas.v1.GetDefaultLocationResponse.account_group_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='group_id', full_name='atlas.v1.GetDefaultLocationResponse.group_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='location', full_name='atlas.v1.GetDefaultLocationResponse.location',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=5850,
  serialized_end=5938,
)

_GETDATARESPONSE.fields_by_name['user'].message_type = _USERNAVIGATIONITEM
_GETDATARESPONSE.fields_by_name['centers'].message_type = _CENTERNAVIGATIONITEM
_GETDATARESPONSE.fields_by_name['theme'].enum_type = _UITHEME
_GETDATARESPONSE.fields_by_name['theming'].message_type = _THEMING
_GETDATARESPONSE.fields_by_name['location_data'].message_type = _LOCATIONDATA
_GETDATARESPONSE.fields_by_name['user_switcher_data'].message_type = _USERSWITCHERDATA
_GETDATARESPONSE.fields_by_name['business_app_ui_theme'].enum_type = _UITHEME
_GETNAVIGATIONDATAREQUEST.fields_by_name['platform_mode'].enum_type = _PLATFORMMODE
_GETSALESINFORESPONSE.fields_by_name['sales_info'].message_type = _SALESINFO
_SIDENAVIGATIONSECTION.fields_by_name['side_navigation_items'].message_type = _SIDENAVIGATIONITEM
_SIDENAVIGATIONCONTAINER.fields_by_name['side_navigation_items'].message_type = _SIDENAVIGATIONITEM
_SIDENAVIGATIONLINK.fields_by_name['sub_links'].message_type = _SIDENAVIGATIONLINK
_SIDENAVIGATIONITEM.fields_by_name['side_navigation_section'].message_type = _SIDENAVIGATIONSECTION
_SIDENAVIGATIONITEM.fields_by_name['side_navigation_container'].message_type = _SIDENAVIGATIONCONTAINER
_SIDENAVIGATIONITEM.fields_by_name['side_navigation_link'].message_type = _SIDENAVIGATIONLINK
_SIDENAVIGATIONITEM.oneofs_by_name['item'].fields.append(
  _SIDENAVIGATIONITEM.fields_by_name['side_navigation_section'])
_SIDENAVIGATIONITEM.fields_by_name['side_navigation_section'].containing_oneof = _SIDENAVIGATIONITEM.oneofs_by_name['item']
_SIDENAVIGATIONITEM.oneofs_by_name['item'].fields.append(
  _SIDENAVIGATIONITEM.fields_by_name['side_navigation_container'])
_SIDENAVIGATIONITEM.fields_by_name['side_navigation_container'].containing_oneof = _SIDENAVIGATIONITEM.oneofs_by_name['item']
_SIDENAVIGATIONITEM.oneofs_by_name['item'].fields.append(
  _SIDENAVIGATIONITEM.fields_by_name['side_navigation_link'])
_SIDENAVIGATIONITEM.fields_by_name['side_navigation_link'].containing_oneof = _SIDENAVIGATIONITEM.oneofs_by_name['item']
_SALESINFO.fields_by_name['sales_contact'].message_type = _SALESCONTACT
_BRANDING.fields_by_name['theme'].enum_type = _UITHEME
_BRANDING.fields_by_name['theming'].message_type = _THEMING
_BRANDING.fields_by_name['business_app_ui_theme'].enum_type = _UITHEME
_LOCATION.fields_by_name['account_group'].message_type = _ACCOUNTGROUP
_LOCATION.fields_by_name['brand'].message_type = _BRAND
_LOCATION.oneofs_by_name['location'].fields.append(
  _LOCATION.fields_by_name['account_group'])
_LOCATION.fields_by_name['account_group'].containing_oneof = _LOCATION.oneofs_by_name['location']
_LOCATION.oneofs_by_name['location'].fields.append(
  _LOCATION.fields_by_name['brand'])
_LOCATION.fields_by_name['brand'].containing_oneof = _LOCATION.oneofs_by_name['location']
_GETNAVIGATIONDATARESPONSE.fields_by_name['branding'].message_type = _BRANDING
_GETNAVIGATIONDATARESPONSE.fields_by_name['sales_info'].message_type = _SALESINFO
_GETNAVIGATIONDATARESPONSE.fields_by_name['pinned_items'].message_type = _PINNEDITEM
_GETNAVIGATIONDATARESPONSE.fields_by_name['associated_location_ids'].message_type = _ASSOCIATEDLOCATIONIDS
_GETNAVIGATIONDATARESPONSE.fields_by_name['dropdown_items'].message_type = _DROPDOWNITEM
_GETNAVIGATIONDATARESPONSE.fields_by_name['user_view'].enum_type = _USERVIEWTYPE
_GETNAVIGATIONDATARESPONSE.fields_by_name['retention_config'].message_type = _RETENTIONCONFIG
_GETNAVIGATIONDATARESPONSE.fields_by_name['total_locations'].message_type = _TOTALLOCATIONS
_GETNAVIGATIONDATARESPONSE.fields_by_name['navigation_items'].message_type = _SIDENAVIGATIONITEM
_SETPINSREQUEST.fields_by_name['items'].message_type = _PINNEDITEM
_GETPINSRESPONSE.fields_by_name['items'].message_type = _PINNEDITEM
_GETLOCATIONSREQUEST_ACCOUNTGROUPS.containing_type = _GETLOCATIONSREQUEST
_GETLOCATIONSREQUEST_GROUPS.containing_type = _GETLOCATIONSREQUEST
_GETLOCATIONSREQUEST.fields_by_name['account_groups'].message_type = _GETLOCATIONSREQUEST_ACCOUNTGROUPS
_GETLOCATIONSREQUEST.fields_by_name['groups'].message_type = _GETLOCATIONSREQUEST_GROUPS
_GETLOCATIONSREQUEST.oneofs_by_name['identifier'].fields.append(
  _GETLOCATIONSREQUEST.fields_by_name['account_groups'])
_GETLOCATIONSREQUEST.fields_by_name['account_groups'].containing_oneof = _GETLOCATIONSREQUEST.oneofs_by_name['identifier']
_GETLOCATIONSREQUEST.oneofs_by_name['identifier'].fields.append(
  _GETLOCATIONSREQUEST.fields_by_name['groups'])
_GETLOCATIONSREQUEST.fields_by_name['groups'].containing_oneof = _GETLOCATIONSREQUEST.oneofs_by_name['identifier']
_GETLOCATIONSRESPONSE.fields_by_name['locations'].message_type = _LOCATION
_LISTELEVATEDLOCATIONSREQUEST.oneofs_by_name['type'].fields.append(
  _LISTELEVATEDLOCATIONSREQUEST.fields_by_name['account_groups'])
_LISTELEVATEDLOCATIONSREQUEST.fields_by_name['account_groups'].containing_oneof = _LISTELEVATEDLOCATIONSREQUEST.oneofs_by_name['type']
_LISTELEVATEDLOCATIONSREQUEST.oneofs_by_name['type'].fields.append(
  _LISTELEVATEDLOCATIONSREQUEST.fields_by_name['brands'])
_LISTELEVATEDLOCATIONSREQUEST.fields_by_name['brands'].containing_oneof = _LISTELEVATEDLOCATIONSREQUEST.oneofs_by_name['type']
_LISTELEVATEDLOCATIONSRESPONSE.fields_by_name['locations'].message_type = _LOCATION
_LISTLOCATIONSRESPONSE.fields_by_name['locations'].message_type = _LOCATION
_SETDEFAULTLOCATIONREQUEST.oneofs_by_name['location'].fields.append(
  _SETDEFAULTLOCATIONREQUEST.fields_by_name['account_group_id'])
_SETDEFAULTLOCATIONREQUEST.fields_by_name['account_group_id'].containing_oneof = _SETDEFAULTLOCATIONREQUEST.oneofs_by_name['location']
_SETDEFAULTLOCATIONREQUEST.oneofs_by_name['location'].fields.append(
  _SETDEFAULTLOCATIONREQUEST.fields_by_name['group_id'])
_SETDEFAULTLOCATIONREQUEST.fields_by_name['group_id'].containing_oneof = _SETDEFAULTLOCATIONREQUEST.oneofs_by_name['location']
_GETDEFAULTLOCATIONRESPONSE.oneofs_by_name['location'].fields.append(
  _GETDEFAULTLOCATIONRESPONSE.fields_by_name['account_group_id'])
_GETDEFAULTLOCATIONRESPONSE.fields_by_name['account_group_id'].containing_oneof = _GETDEFAULTLOCATIONRESPONSE.oneofs_by_name['location']
_GETDEFAULTLOCATIONRESPONSE.oneofs_by_name['location'].fields.append(
  _GETDEFAULTLOCATIONRESPONSE.fields_by_name['group_id'])
_GETDEFAULTLOCATIONRESPONSE.fields_by_name['group_id'].containing_oneof = _GETDEFAULTLOCATIONRESPONSE.oneofs_by_name['location']
DESCRIPTOR.message_types_by_name['UserNavigationItem'] = _USERNAVIGATIONITEM
DESCRIPTOR.message_types_by_name['CenterNavigationItem'] = _CENTERNAVIGATIONITEM
DESCRIPTOR.message_types_by_name['GetDataRequest'] = _GETDATAREQUEST
DESCRIPTOR.message_types_by_name['LocationData'] = _LOCATIONDATA
DESCRIPTOR.message_types_by_name['UserSwitcherData'] = _USERSWITCHERDATA
DESCRIPTOR.message_types_by_name['GetDataResponse'] = _GETDATARESPONSE
DESCRIPTOR.message_types_by_name['GetNavigationDataRequest'] = _GETNAVIGATIONDATAREQUEST
DESCRIPTOR.message_types_by_name['GetSalesInfoRequest'] = _GETSALESINFOREQUEST
DESCRIPTOR.message_types_by_name['GetSalesInfoResponse'] = _GETSALESINFORESPONSE
DESCRIPTOR.message_types_by_name['SideNavigationSection'] = _SIDENAVIGATIONSECTION
DESCRIPTOR.message_types_by_name['SideNavigationContainer'] = _SIDENAVIGATIONCONTAINER
DESCRIPTOR.message_types_by_name['SideNavigationLink'] = _SIDENAVIGATIONLINK
DESCRIPTOR.message_types_by_name['DropdownItem'] = _DROPDOWNITEM
DESCRIPTOR.message_types_by_name['SideNavigationItem'] = _SIDENAVIGATIONITEM
DESCRIPTOR.message_types_by_name['SalesInfo'] = _SALESINFO
DESCRIPTOR.message_types_by_name['SalesContact'] = _SALESCONTACT
DESCRIPTOR.message_types_by_name['Brand'] = _BRAND
DESCRIPTOR.message_types_by_name['PinnedItem'] = _PINNEDITEM
DESCRIPTOR.message_types_by_name['Branding'] = _BRANDING
DESCRIPTOR.message_types_by_name['AccountGroup'] = _ACCOUNTGROUP
DESCRIPTOR.message_types_by_name['Location'] = _LOCATION
DESCRIPTOR.message_types_by_name['AssociatedLocationIDs'] = _ASSOCIATEDLOCATIONIDS
DESCRIPTOR.message_types_by_name['Theming'] = _THEMING
DESCRIPTOR.message_types_by_name['RetentionConfig'] = _RETENTIONCONFIG
DESCRIPTOR.message_types_by_name['TotalLocations'] = _TOTALLOCATIONS
DESCRIPTOR.message_types_by_name['GetNavigationDataResponse'] = _GETNAVIGATIONDATARESPONSE
DESCRIPTOR.message_types_by_name['SetPinsRequest'] = _SETPINSREQUEST
DESCRIPTOR.message_types_by_name['GetPinsRequest'] = _GETPINSREQUEST
DESCRIPTOR.message_types_by_name['GetPinsResponse'] = _GETPINSRESPONSE
DESCRIPTOR.message_types_by_name['GetLocationsRequest'] = _GETLOCATIONSREQUEST
DESCRIPTOR.message_types_by_name['GetLocationsResponse'] = _GETLOCATIONSRESPONSE
DESCRIPTOR.message_types_by_name['ListElevatedLocationsRequest'] = _LISTELEVATEDLOCATIONSREQUEST
DESCRIPTOR.message_types_by_name['ListElevatedLocationsResponse'] = _LISTELEVATEDLOCATIONSRESPONSE
DESCRIPTOR.message_types_by_name['ListLocationsRequest'] = _LISTLOCATIONSREQUEST
DESCRIPTOR.message_types_by_name['ListLocationsResponse'] = _LISTLOCATIONSRESPONSE
DESCRIPTOR.message_types_by_name['SetLanguageRequest'] = _SETLANGUAGEREQUEST
DESCRIPTOR.message_types_by_name['GetLanguageRequest'] = _GETLANGUAGEREQUEST
DESCRIPTOR.message_types_by_name['GetLanguageResponse'] = _GETLANGUAGERESPONSE
DESCRIPTOR.message_types_by_name['ContactUsRequest'] = _CONTACTUSREQUEST
DESCRIPTOR.message_types_by_name['SetDefaultLocationRequest'] = _SETDEFAULTLOCATIONREQUEST
DESCRIPTOR.message_types_by_name['GetDefaultLocationRequest'] = _GETDEFAULTLOCATIONREQUEST
DESCRIPTOR.message_types_by_name['GetDefaultLocationResponse'] = _GETDEFAULTLOCATIONRESPONSE
DESCRIPTOR.enum_types_by_name['UITheme'] = _UITHEME
DESCRIPTOR.enum_types_by_name['UserViewType'] = _USERVIEWTYPE
DESCRIPTOR.enum_types_by_name['PlatformMode'] = _PLATFORMMODE

UserNavigationItem = _reflection.GeneratedProtocolMessageType('UserNavigationItem', (_message.Message,), dict(
  DESCRIPTOR = _USERNAVIGATIONITEM,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.UserNavigationItem)
  ))
_sym_db.RegisterMessage(UserNavigationItem)

CenterNavigationItem = _reflection.GeneratedProtocolMessageType('CenterNavigationItem', (_message.Message,), dict(
  DESCRIPTOR = _CENTERNAVIGATIONITEM,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.CenterNavigationItem)
  ))
_sym_db.RegisterMessage(CenterNavigationItem)

GetDataRequest = _reflection.GeneratedProtocolMessageType('GetDataRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDATAREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetDataRequest)
  ))
_sym_db.RegisterMessage(GetDataRequest)

LocationData = _reflection.GeneratedProtocolMessageType('LocationData', (_message.Message,), dict(
  DESCRIPTOR = _LOCATIONDATA,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.LocationData)
  ))
_sym_db.RegisterMessage(LocationData)

UserSwitcherData = _reflection.GeneratedProtocolMessageType('UserSwitcherData', (_message.Message,), dict(
  DESCRIPTOR = _USERSWITCHERDATA,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.UserSwitcherData)
  ))
_sym_db.RegisterMessage(UserSwitcherData)

GetDataResponse = _reflection.GeneratedProtocolMessageType('GetDataResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDATARESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetDataResponse)
  ))
_sym_db.RegisterMessage(GetDataResponse)

GetNavigationDataRequest = _reflection.GeneratedProtocolMessageType('GetNavigationDataRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETNAVIGATIONDATAREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetNavigationDataRequest)
  ))
_sym_db.RegisterMessage(GetNavigationDataRequest)

GetSalesInfoRequest = _reflection.GeneratedProtocolMessageType('GetSalesInfoRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETSALESINFOREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetSalesInfoRequest)
  ))
_sym_db.RegisterMessage(GetSalesInfoRequest)

GetSalesInfoResponse = _reflection.GeneratedProtocolMessageType('GetSalesInfoResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETSALESINFORESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetSalesInfoResponse)
  ))
_sym_db.RegisterMessage(GetSalesInfoResponse)

SideNavigationSection = _reflection.GeneratedProtocolMessageType('SideNavigationSection', (_message.Message,), dict(
  DESCRIPTOR = _SIDENAVIGATIONSECTION,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SideNavigationSection)
  ))
_sym_db.RegisterMessage(SideNavigationSection)

SideNavigationContainer = _reflection.GeneratedProtocolMessageType('SideNavigationContainer', (_message.Message,), dict(
  DESCRIPTOR = _SIDENAVIGATIONCONTAINER,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SideNavigationContainer)
  ))
_sym_db.RegisterMessage(SideNavigationContainer)

SideNavigationLink = _reflection.GeneratedProtocolMessageType('SideNavigationLink', (_message.Message,), dict(
  DESCRIPTOR = _SIDENAVIGATIONLINK,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SideNavigationLink)
  ))
_sym_db.RegisterMessage(SideNavigationLink)

DropdownItem = _reflection.GeneratedProtocolMessageType('DropdownItem', (_message.Message,), dict(
  DESCRIPTOR = _DROPDOWNITEM,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.DropdownItem)
  ))
_sym_db.RegisterMessage(DropdownItem)

SideNavigationItem = _reflection.GeneratedProtocolMessageType('SideNavigationItem', (_message.Message,), dict(
  DESCRIPTOR = _SIDENAVIGATIONITEM,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SideNavigationItem)
  ))
_sym_db.RegisterMessage(SideNavigationItem)

SalesInfo = _reflection.GeneratedProtocolMessageType('SalesInfo', (_message.Message,), dict(
  DESCRIPTOR = _SALESINFO,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SalesInfo)
  ))
_sym_db.RegisterMessage(SalesInfo)

SalesContact = _reflection.GeneratedProtocolMessageType('SalesContact', (_message.Message,), dict(
  DESCRIPTOR = _SALESCONTACT,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SalesContact)
  ))
_sym_db.RegisterMessage(SalesContact)

Brand = _reflection.GeneratedProtocolMessageType('Brand', (_message.Message,), dict(
  DESCRIPTOR = _BRAND,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.Brand)
  ))
_sym_db.RegisterMessage(Brand)

PinnedItem = _reflection.GeneratedProtocolMessageType('PinnedItem', (_message.Message,), dict(
  DESCRIPTOR = _PINNEDITEM,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.PinnedItem)
  ))
_sym_db.RegisterMessage(PinnedItem)

Branding = _reflection.GeneratedProtocolMessageType('Branding', (_message.Message,), dict(
  DESCRIPTOR = _BRANDING,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.Branding)
  ))
_sym_db.RegisterMessage(Branding)

AccountGroup = _reflection.GeneratedProtocolMessageType('AccountGroup', (_message.Message,), dict(
  DESCRIPTOR = _ACCOUNTGROUP,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.AccountGroup)
  ))
_sym_db.RegisterMessage(AccountGroup)

Location = _reflection.GeneratedProtocolMessageType('Location', (_message.Message,), dict(
  DESCRIPTOR = _LOCATION,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.Location)
  ))
_sym_db.RegisterMessage(Location)

AssociatedLocationIDs = _reflection.GeneratedProtocolMessageType('AssociatedLocationIDs', (_message.Message,), dict(
  DESCRIPTOR = _ASSOCIATEDLOCATIONIDS,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.AssociatedLocationIDs)
  ))
_sym_db.RegisterMessage(AssociatedLocationIDs)

Theming = _reflection.GeneratedProtocolMessageType('Theming', (_message.Message,), dict(
  DESCRIPTOR = _THEMING,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.Theming)
  ))
_sym_db.RegisterMessage(Theming)

RetentionConfig = _reflection.GeneratedProtocolMessageType('RetentionConfig', (_message.Message,), dict(
  DESCRIPTOR = _RETENTIONCONFIG,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.RetentionConfig)
  ))
_sym_db.RegisterMessage(RetentionConfig)

TotalLocations = _reflection.GeneratedProtocolMessageType('TotalLocations', (_message.Message,), dict(
  DESCRIPTOR = _TOTALLOCATIONS,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.TotalLocations)
  ))
_sym_db.RegisterMessage(TotalLocations)

GetNavigationDataResponse = _reflection.GeneratedProtocolMessageType('GetNavigationDataResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETNAVIGATIONDATARESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetNavigationDataResponse)
  ))
_sym_db.RegisterMessage(GetNavigationDataResponse)

SetPinsRequest = _reflection.GeneratedProtocolMessageType('SetPinsRequest', (_message.Message,), dict(
  DESCRIPTOR = _SETPINSREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SetPinsRequest)
  ))
_sym_db.RegisterMessage(SetPinsRequest)

GetPinsRequest = _reflection.GeneratedProtocolMessageType('GetPinsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPINSREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetPinsRequest)
  ))
_sym_db.RegisterMessage(GetPinsRequest)

GetPinsResponse = _reflection.GeneratedProtocolMessageType('GetPinsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPINSRESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetPinsResponse)
  ))
_sym_db.RegisterMessage(GetPinsResponse)

GetLocationsRequest = _reflection.GeneratedProtocolMessageType('GetLocationsRequest', (_message.Message,), dict(

  AccountGroups = _reflection.GeneratedProtocolMessageType('AccountGroups', (_message.Message,), dict(
    DESCRIPTOR = _GETLOCATIONSREQUEST_ACCOUNTGROUPS,
    __module__ = 'atlas.v1.api_pb2'
    # @@protoc_insertion_point(class_scope:atlas.v1.GetLocationsRequest.AccountGroups)
    ))
  ,

  Groups = _reflection.GeneratedProtocolMessageType('Groups', (_message.Message,), dict(
    DESCRIPTOR = _GETLOCATIONSREQUEST_GROUPS,
    __module__ = 'atlas.v1.api_pb2'
    # @@protoc_insertion_point(class_scope:atlas.v1.GetLocationsRequest.Groups)
    ))
  ,
  DESCRIPTOR = _GETLOCATIONSREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetLocationsRequest)
  ))
_sym_db.RegisterMessage(GetLocationsRequest)
_sym_db.RegisterMessage(GetLocationsRequest.AccountGroups)
_sym_db.RegisterMessage(GetLocationsRequest.Groups)

GetLocationsResponse = _reflection.GeneratedProtocolMessageType('GetLocationsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETLOCATIONSRESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetLocationsResponse)
  ))
_sym_db.RegisterMessage(GetLocationsResponse)

ListElevatedLocationsRequest = _reflection.GeneratedProtocolMessageType('ListElevatedLocationsRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTELEVATEDLOCATIONSREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.ListElevatedLocationsRequest)
  ))
_sym_db.RegisterMessage(ListElevatedLocationsRequest)

ListElevatedLocationsResponse = _reflection.GeneratedProtocolMessageType('ListElevatedLocationsResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTELEVATEDLOCATIONSRESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.ListElevatedLocationsResponse)
  ))
_sym_db.RegisterMessage(ListElevatedLocationsResponse)

ListLocationsRequest = _reflection.GeneratedProtocolMessageType('ListLocationsRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTLOCATIONSREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.ListLocationsRequest)
  ))
_sym_db.RegisterMessage(ListLocationsRequest)

ListLocationsResponse = _reflection.GeneratedProtocolMessageType('ListLocationsResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTLOCATIONSRESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.ListLocationsResponse)
  ))
_sym_db.RegisterMessage(ListLocationsResponse)

SetLanguageRequest = _reflection.GeneratedProtocolMessageType('SetLanguageRequest', (_message.Message,), dict(
  DESCRIPTOR = _SETLANGUAGEREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SetLanguageRequest)
  ))
_sym_db.RegisterMessage(SetLanguageRequest)

GetLanguageRequest = _reflection.GeneratedProtocolMessageType('GetLanguageRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETLANGUAGEREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetLanguageRequest)
  ))
_sym_db.RegisterMessage(GetLanguageRequest)

GetLanguageResponse = _reflection.GeneratedProtocolMessageType('GetLanguageResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETLANGUAGERESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetLanguageResponse)
  ))
_sym_db.RegisterMessage(GetLanguageResponse)

ContactUsRequest = _reflection.GeneratedProtocolMessageType('ContactUsRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONTACTUSREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.ContactUsRequest)
  ))
_sym_db.RegisterMessage(ContactUsRequest)

SetDefaultLocationRequest = _reflection.GeneratedProtocolMessageType('SetDefaultLocationRequest', (_message.Message,), dict(
  DESCRIPTOR = _SETDEFAULTLOCATIONREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SetDefaultLocationRequest)
  ))
_sym_db.RegisterMessage(SetDefaultLocationRequest)

GetDefaultLocationRequest = _reflection.GeneratedProtocolMessageType('GetDefaultLocationRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDEFAULTLOCATIONREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetDefaultLocationRequest)
  ))
_sym_db.RegisterMessage(GetDefaultLocationRequest)

GetDefaultLocationResponse = _reflection.GeneratedProtocolMessageType('GetDefaultLocationResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDEFAULTLOCATIONRESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetDefaultLocationResponse)
  ))
_sym_db.RegisterMessage(GetDefaultLocationResponse)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\037com.vendasta.atlas.v1.generatedB\010ApiProtoZ9github.com/vendasta/generated-protos-go/atlas/v1;atlas_v1'))
_GETDATARESPONSE.fields_by_name['theming'].has_options = True
_GETDATARESPONSE.fields_by_name['theming']._options = _descriptor._ParseOptions(descriptor_pb2.FieldOptions(), _b('\030\001'))
_BRANDING.fields_by_name['theming'].has_options = True
_BRANDING.fields_by_name['theming']._options = _descriptor._ParseOptions(descriptor_pb2.FieldOptions(), _b('\030\001'))
# @@protoc_insertion_point(module_scope)
