# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: atlas/v1/api.proto

import sys
_b=sys.version_info[0]<3 and (lambda x:x) or (lambda x:x.encode('latin1'))
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
from google.protobuf import descriptor_pb2
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import empty_pb2 as google_dot_protobuf_dot_empty__pb2
from vendasta_types import annotations_pb2 as vendasta__types_dot_annotations__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='atlas/v1/api.proto',
  package='atlas.v1',
  syntax='proto3',
  serialized_pb=_b('\n\x12\x61tlas/v1/api.proto\x12\x08\x61tlas.v1\x1a\x1bgoogle/protobuf/empty.proto\x1a vendasta_types/annotations.proto\"A\n\x12UserNavigationItem\x12\x0c\n\x04text\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\x12\x10\n\x08route_id\x18\x03 \x01(\t\"J\n\x14\x43\x65nterNavigationItem\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tentry_url\x18\x02 \x01(\t\x12\x11\n\tcenter_id\x18\x03 \x01(\t\"\x80\x01\n\x0eGetDataRequest\x12\x12\n\npartner_id\x18\x01 \x01(\t\x12\x11\n\tmarket_id\x18\x02 \x01(\t\x12\x18\n\x10\x61\x63\x63ount_group_id\x18\x03 \x01(\t\x12\x19\n\x11sign_out_next_url\x18\x04 \x01(\t\x12\x12\n\ngroup_path\x18\x05 \x01(\t\"6\n\x0cLocationData\x12\x15\n\rbusiness_name\x18\x01 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x02 \x01(\t\"\xe0\x02\n\x0fGetDataResponse\x12*\n\x04user\x18\x01 \x03(\x0b\x32\x1c.atlas.v1.UserNavigationItem\x12/\n\x07\x63\x65nters\x18\x02 \x03(\x0b\x32\x1e.atlas.v1.CenterNavigationItem\x12\x10\n\x08username\x18\x03 \x01(\t\x12\r\n\x05\x65mail\x18\x04 \x01(\t\x12\x14\n\x0csign_out_url\x18\x05 \x01(\t\x12$\n\x05theme\x18\x06 \x01(\x0e\x32\x11.atlas.v1.UIThemeB\x02\x18\x01\x12\x10\n\x08language\x18\x07 \x01(\t\x12\"\n\x07theming\x18\x08 \x01(\x0b\x32\x11.atlas.v1.Theming\x12\x1d\n\x15notifications_enabled\x18\t \x01(\x08\x12\x0f\n\x07user_id\x18\n \x01(\t\x12-\n\rlocation_data\x18\x0b \x01(\x0b\x32\x16.atlas.v1.LocationData\"o\n\x18GetNavigationDataRequest\x12\x18\n\x10\x61\x63\x63ount_group_id\x18\x01 \x01(\t\x12\x12\n\ngroup_path\x18\x02 \x01(\t\x12\x12\n\npartner_id\x18\x03 \x01(\t\x12\x11\n\tmarket_id\x18\x04 \x01(\t\"\x91\x01\n\x15SideNavigationSection\x12\x16\n\x0etranslation_id\x18\x01 \x01(\t\x12;\n\x15side_navigation_items\x18\x02 \x03(\x0b\x32\x1c.atlas.v1.SideNavigationItem\x12\r\n\x05label\x18\x03 \x01(\t\x12\x14\n\x0c\x63hip_content\x18\x04 \x01(\t\"\xc6\x01\n\x17SideNavigationContainer\x12\x16\n\x0etranslation_id\x18\x01 \x01(\t\x12;\n\x15side_navigation_items\x18\x02 \x03(\x0b\x32\x1c.atlas.v1.SideNavigationItem\x12\x0c\n\x04icon\x18\x03 \x01(\t\x12\x10\n\x08logo_url\x18\x04 \x01(\t\x12\r\n\x05label\x18\x05 \x01(\t\x12\x11\n\tshow_icon\x18\x06 \x01(\x08\x12\x14\n\x0c\x63hip_content\x18\x07 \x01(\t\"\x89\x02\n\x12SideNavigationLink\x12\x15\n\rnavigation_id\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\x12\x0c\n\x04path\x18\x03 \x01(\t\x12\x1b\n\x13service_provider_id\x18\x04 \x01(\t\x12\x10\n\x08logo_url\x18\x05 \x01(\t\x12\x0c\n\x04icon\x18\x06 \x01(\t\x12\x16\n\x0etranslation_id\x18\x07 \x01(\t\x12\x10\n\x08\x65xternal\x18\x08 \x01(\x08\x12\r\n\x05label\x18\t \x01(\t\x12\x11\n\tshow_icon\x18\n \x01(\x08\x12\x10\n\x08pinnable\x18\x0b \x01(\x08\x12\x14\n\x0c\x63hip_content\x18\x0c \x01(\t\x12\x10\n\x08is_trial\x18\r \x01(\x08\"P\n\x0c\x44ropdownItem\x12\x0b\n\x03url\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x16\n\x0etranslation_id\x18\x03 \x01(\t\x12\r\n\x05label\x18\x04 \x01(\t\"\xe6\x01\n\x12SideNavigationItem\x12\x42\n\x17side_navigation_section\x18\x01 \x01(\x0b\x32\x1f.atlas.v1.SideNavigationSectionH\x00\x12\x46\n\x19side_navigation_container\x18\x02 \x01(\x0b\x32!.atlas.v1.SideNavigationContainerH\x00\x12<\n\x14side_navigation_link\x18\x03 \x01(\x0b\x32\x1c.atlas.v1.SideNavigationLinkH\x00\x42\x06\n\x04item\"O\n\tSalesInfo\x12\x13\n\x0bmarket_name\x18\x01 \x01(\t\x12-\n\rsales_contact\x18\x02 \x01(\x0b\x32\x16.atlas.v1.SalesContact\"\xa0\x01\n\x0cSalesContact\x12\x17\n\x0fsales_person_id\x18\x01 \x01(\t\x12\r\n\x05\x65mail\x18\x02 \x01(\t\x12\x12\n\nfirst_name\x18\x03 \x01(\t\x12\x11\n\tlast_name\x18\x04 \x01(\t\x12\x14\n\x0cphone_number\x18\x05 \x01(\t\x12\x18\n\x10photo_url_secure\x18\x06 \x01(\t\x12\x11\n\tjob_title\x18\x07 \x01(\t\"J\n\x05\x42rand\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x12\n\npath_nodes\x18\x02 \x03(\t\x12\x12\n\nhas_access\x18\x03 \x01(\x08\x12\x0b\n\x03url\x18\x04 \x01(\t\"#\n\nPinnedItem\x12\x15\n\rnavigation_id\x18\x01 \x01(\t\"\x91\x01\n\x08\x42randing\x12$\n\x05theme\x18\x01 \x01(\x0e\x32\x11.atlas.v1.UIThemeB\x02\x18\x01\x12\x10\n\x08logo_url\x18\x02 \x01(\t\x12\x14\n\x0cpartner_name\x18\x03 \x01(\t\x12\x13\n\x0b\x63\x65nter_name\x18\x04 \x01(\t\x12\"\n\x07theming\x18\x05 \x01(\x0b\x32\x11.atlas.v1.Theming\"s\n\x0c\x41\x63\x63ountGroup\x12\x18\n\x10\x61\x63\x63ount_group_id\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0f\n\x07\x61\x64\x64ress\x18\x03 \x01(\t\x12\x1d\n\x15\x61\x63tivated_product_ids\x18\x04 \x03(\t\x12\x0b\n\x03url\x18\x05 \x01(\t\"i\n\x08Location\x12/\n\raccount_group\x18\x01 \x01(\x0b\x32\x16.atlas.v1.AccountGroupH\x00\x12 \n\x05\x62rand\x18\x02 \x01(\x0b\x32\x0f.atlas.v1.BrandH\x00\x42\n\n\x08location\"G\n\x15\x41ssociatedLocationIDs\x12\x19\n\x11\x61\x63\x63ount_group_ids\x18\x01 \x03(\t\x12\x13\n\x0bgroup_paths\x18\x02 \x03(\t\"\xc4\x02\n\x07Theming\x12\x15\n\rprimary_color\x18\x01 \x01(\t\x12\x1b\n\x13primary_hover_color\x18\x02 \x01(\t\x12\x1c\n\x14primary_active_color\x18\x03 \x01(\t\x12\x17\n\x0fsecondary_color\x18\x04 \x01(\t\x12\x1d\n\x15secondary_hover_color\x18\x05 \x01(\t\x12\x1e\n\x16secondary_active_color\x18\x06 \x01(\t\x12\x12\n\nfont_color\x18\x07 \x01(\t\x12\x1b\n\x13\x66ont_disabled_color\x18\x08 \x01(\t\x12\x15\n\raccents_color\x18\t \x01(\t\x12\x1c\n\x14\x61\x63\x63\x65nts_active_color\x18\n \x01(\t\x12\x13\n\x0b\x66ocus_color\x18\x0b \x01(\t\x12\x14\n\x0c\x62order_color\x18\x0c \x01(\t\":\n\x0fRetentionConfig\x12\'\n\x1f\x63\x61ncellation_notification_email\x18\x01 \x01(\t\"2\n\x0eTotalLocations\x12\x10\n\x08\x61\x63\x63ounts\x18\x01 \x01(\x03\x12\x0e\n\x06\x62rands\x18\x02 \x01(\x03\"\xcb\x04\n\x19GetNavigationDataResponse\x12\x33\n\raccount_items\x18\x01 \x03(\x0b\x32\x1c.atlas.v1.SideNavigationItem\x12$\n\x08\x62randing\x18\x02 \x01(\x0b\x32\x12.atlas.v1.Branding\x12\'\n\nsales_info\x18\x03 \x01(\x0b\x32\x13.atlas.v1.SalesInfo\x12*\n\x0cpinned_items\x18\x04 \x03(\x0b\x32\x14.atlas.v1.PinnedItem\x12@\n\x17\x61ssociated_location_ids\x18\x05 \x01(\x0b\x32\x1f.atlas.v1.AssociatedLocationIDs\x12\x18\n\x10\x64\x65\x66\x61ult_location\x18\x06 \x01(\t\x12\x31\n\x0b\x62rand_items\x18\x07 \x03(\x0b\x32\x1c.atlas.v1.SideNavigationItem\x12\x10\n\x08language\x18\x08 \x01(\t\x12.\n\x0e\x64ropdown_items\x18\t \x03(\x0b\x32\x16.atlas.v1.DropdownItem\x12\x1a\n\x12\x63urrent_brand_name\x18\n \x01(\t\x12)\n\tuser_view\x18\x0b \x01(\x0e\x32\x16.atlas.v1.UserViewType\x12\x33\n\x10retention_config\x18\x0c \x01(\x0b\x32\x19.atlas.v1.RetentionConfig\x12\x31\n\x0ftotal_locations\x18\r \x01(\x0b\x32\x18.atlas.v1.TotalLocations\"I\n\x0eSetPinsRequest\x12\x12\n\nidentifier\x18\x01 \x01(\t\x12#\n\x05items\x18\x03 \x03(\x0b\x32\x14.atlas.v1.PinnedItem\"$\n\x0eGetPinsRequest\x12\x12\n\nidentifier\x18\x01 \x01(\t\"6\n\x0fGetPinsResponse\x12#\n\x05items\x18\x01 \x03(\x0b\x32\x14.atlas.v1.PinnedItem\"\xed\x01\n\x13GetLocationsRequest\x12\x45\n\x0e\x61\x63\x63ount_groups\x18\x01 \x01(\x0b\x32+.atlas.v1.GetLocationsRequest.AccountGroupsH\x00\x12\x36\n\x06groups\x18\x02 \x01(\x0b\x32$.atlas.v1.GetLocationsRequest.GroupsH\x00\x1a*\n\rAccountGroups\x12\x19\n\x11\x61\x63\x63ount_group_ids\x18\x01 \x03(\t\x1a\x1d\n\x06Groups\x12\x13\n\x0bgroup_paths\x18\x01 \x03(\tB\x0c\n\nidentifier\"=\n\x14GetLocationsResponse\x12%\n\tlocations\x18\x01 \x03(\x0b\x32\x12.atlas.v1.Location\"\x99\x01\n\x1cListElevatedLocationsRequest\x12\x12\n\npartner_id\x18\x01 \x01(\t\x12\x0e\n\x06\x63ursor\x18\x02 \x01(\t\x12\x11\n\tpage_size\x18\x03 \x01(\x03\x12\x0e\n\x06search\x18\x04 \x01(\t\x12\x18\n\x0e\x61\x63\x63ount_groups\x18\x05 \x01(\x08H\x00\x12\x10\n\x06\x62rands\x18\x06 \x01(\x08H\x00\x42\x06\n\x04type\"h\n\x1dListElevatedLocationsResponse\x12%\n\tlocations\x18\x01 \x03(\x0b\x32\x12.atlas.v1.Location\x12\x0e\n\x06\x63ursor\x18\x02 \x01(\t\x12\x10\n\x08has_more\x18\x03 \x01(\x08\"&\n\x12SetLanguageRequest\x12\x10\n\x08language\x18\x01 \x01(\t\"\x14\n\x12GetLanguageRequest\"\'\n\x13GetLanguageResponse\x12\x10\n\x08language\x18\x01 \x01(\t\"=\n\x10\x43ontactUsRequest\x12\x18\n\x10\x61\x63\x63ount_group_id\x18\x01 \x01(\t\x12\x0f\n\x07message\x18\x02 \x01(\t\"I\n\x19SetDefaultLocationRequest\x12\x18\n\x10\x61\x63\x63ount_group_id\x18\x01 \x01(\t\x12\x12\n\npartner_id\x18\x02 \x01(\t*0\n\x07UITheme\x12\x11\n\rUI_THEME_DARK\x10\x00\x12\x12\n\x0eUI_THEME_LIGHT\x10\x01*@\n\x0cUserViewType\x12\x16\n\x12USER_VIEW_TYPE_SMB\x10\x00\x12\x18\n\x14USER_VIEW_TYPE_ADMIN\x10\x01\x32\xa9\x06\n\x05\x41tlas\x12j\n\x07GetData\x12\x18.atlas.v1.GetDataRequest\x1a\x19.atlas.v1.GetDataResponse\"*\x82\xb5\x18&\n\x12marketplace.common\n\x10\x61tlas.navigation\x12\x88\x01\n\x11GetNavigationData\x12\".atlas.v1.GetNavigationDataRequest\x1a#.atlas.v1.GetNavigationDataResponse\"*\x82\xb5\x18&\n\x12marketplace.common\n\x10\x61tlas.navigation\x12\x87\x01\n\x0cGetLocations\x12\x1d.atlas.v1.GetLocationsRequest\x1a\x1e.atlas.v1.GetLocationsResponse\"8\x82\xb5\x18\x34\n\x12marketplace.common\n\x0f\x62usiness:manage\n\rbusiness:read\x12\xa2\x01\n\x15ListElevatedLocations\x12&.atlas.v1.ListElevatedLocationsRequest\x1a\'.atlas.v1.ListElevatedLocationsResponse\"8\x82\xb5\x18\x34\n\x12marketplace.common\n\x0f\x62usiness:manage\n\rbusiness:read\x12k\n\tContactUs\x12\x1a.atlas.v1.ContactUsRequest\x1a\x16.google.protobuf.Empty\"*\x82\xb5\x18&\n\x12marketplace.common\n\x10\x61tlas.navigation\x12\x8c\x01\n\x12SetDefaultLocation\x12#.atlas.v1.SetDefaultLocationRequest\x1a\x16.google.protobuf.Empty\"9\x82\xb5\x18\x35\n\x12marketplace.common\n\x0f\x62usiness:manage\n\x0e\x62usiness:write2\xdb\x01\n\x04Pins\x12g\n\x07SetPins\x12\x18.atlas.v1.SetPinsRequest\x1a\x16.google.protobuf.Empty\"*\x82\xb5\x18&\n\x12marketplace.common\n\x10\x61tlas.navigation\x12j\n\x07GetPins\x12\x18.atlas.v1.GetPinsRequest\x1a\x19.atlas.v1.GetPinsResponse\"*\x82\xb5\x18&\n\x12marketplace.common\n\x10\x61tlas.navigation2\x92\x02\n\tLanguages\x12~\n\x0bSetLanguage\x12\x1c.atlas.v1.SetLanguageRequest\x1a\x16.google.protobuf.Empty\"9\x82\xb5\x18\x35\n\x12marketplace.common\n\x0flanguage:manage\n\x0elanguage:write\x12\x84\x01\n\x0bGetLanguage\x12\x1c.atlas.v1.GetLanguageRequest\x1a\x1d.atlas.v1.GetLanguageResponse\"8\x82\xb5\x18\x34\n\x12marketplace.common\n\x0flanguage:manage\n\rlanguage:readB+\n\x1f\x63om.vendasta.atlas.v1.generatedB\x08\x41piProtob\x06proto3')
  ,
  dependencies=[google_dot_protobuf_dot_empty__pb2.DESCRIPTOR,vendasta__types_dot_annotations__pb2.DESCRIPTOR,])
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

_UITHEME = _descriptor.EnumDescriptor(
  name='UITheme',
  full_name='atlas.v1.UITheme',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UI_THEME_DARK', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='UI_THEME_LIGHT', index=1, number=1,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=4631,
  serialized_end=4679,
)
_sym_db.RegisterEnumDescriptor(_UITHEME)

UITheme = enum_type_wrapper.EnumTypeWrapper(_UITHEME)
_USERVIEWTYPE = _descriptor.EnumDescriptor(
  name='UserViewType',
  full_name='atlas.v1.UserViewType',
  filename=None,
  file=DESCRIPTOR,
  values=[
    _descriptor.EnumValueDescriptor(
      name='USER_VIEW_TYPE_SMB', index=0, number=0,
      options=None,
      type=None),
    _descriptor.EnumValueDescriptor(
      name='USER_VIEW_TYPE_ADMIN', index=1, number=1,
      options=None,
      type=None),
  ],
  containing_type=None,
  options=None,
  serialized_start=4681,
  serialized_end=4745,
)
_sym_db.RegisterEnumDescriptor(_USERVIEWTYPE)

UserViewType = enum_type_wrapper.EnumTypeWrapper(_USERVIEWTYPE)
UI_THEME_DARK = 0
UI_THEME_LIGHT = 1
USER_VIEW_TYPE_SMB = 0
USER_VIEW_TYPE_ADMIN = 1



_USERNAVIGATIONITEM = _descriptor.Descriptor(
  name='UserNavigationItem',
  full_name='atlas.v1.UserNavigationItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='text', full_name='atlas.v1.UserNavigationItem.text', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='url', full_name='atlas.v1.UserNavigationItem.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='route_id', full_name='atlas.v1.UserNavigationItem.route_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=95,
  serialized_end=160,
)


_CENTERNAVIGATIONITEM = _descriptor.Descriptor(
  name='CenterNavigationItem',
  full_name='atlas.v1.CenterNavigationItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='atlas.v1.CenterNavigationItem.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='entry_url', full_name='atlas.v1.CenterNavigationItem.entry_url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='center_id', full_name='atlas.v1.CenterNavigationItem.center_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=162,
  serialized_end=236,
)


_GETDATAREQUEST = _descriptor.Descriptor(
  name='GetDataRequest',
  full_name='atlas.v1.GetDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='atlas.v1.GetDataRequest.partner_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='market_id', full_name='atlas.v1.GetDataRequest.market_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='account_group_id', full_name='atlas.v1.GetDataRequest.account_group_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sign_out_next_url', full_name='atlas.v1.GetDataRequest.sign_out_next_url', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='group_path', full_name='atlas.v1.GetDataRequest.group_path', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=239,
  serialized_end=367,
)


_LOCATIONDATA = _descriptor.Descriptor(
  name='LocationData',
  full_name='atlas.v1.LocationData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='business_name', full_name='atlas.v1.LocationData.business_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='address', full_name='atlas.v1.LocationData.address', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=369,
  serialized_end=423,
)


_GETDATARESPONSE = _descriptor.Descriptor(
  name='GetDataResponse',
  full_name='atlas.v1.GetDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='user', full_name='atlas.v1.GetDataResponse.user', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='centers', full_name='atlas.v1.GetDataResponse.centers', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='username', full_name='atlas.v1.GetDataResponse.username', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='email', full_name='atlas.v1.GetDataResponse.email', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sign_out_url', full_name='atlas.v1.GetDataResponse.sign_out_url', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='theme', full_name='atlas.v1.GetDataResponse.theme', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=_descriptor._ParseOptions(descriptor_pb2.FieldOptions(), _b('\030\001'))),
    _descriptor.FieldDescriptor(
      name='language', full_name='atlas.v1.GetDataResponse.language', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='theming', full_name='atlas.v1.GetDataResponse.theming', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='notifications_enabled', full_name='atlas.v1.GetDataResponse.notifications_enabled', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='user_id', full_name='atlas.v1.GetDataResponse.user_id', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='location_data', full_name='atlas.v1.GetDataResponse.location_data', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=426,
  serialized_end=778,
)


_GETNAVIGATIONDATAREQUEST = _descriptor.Descriptor(
  name='GetNavigationDataRequest',
  full_name='atlas.v1.GetNavigationDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group_id', full_name='atlas.v1.GetNavigationDataRequest.account_group_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='group_path', full_name='atlas.v1.GetNavigationDataRequest.group_path', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='atlas.v1.GetNavigationDataRequest.partner_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='market_id', full_name='atlas.v1.GetNavigationDataRequest.market_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=780,
  serialized_end=891,
)


_SIDENAVIGATIONSECTION = _descriptor.Descriptor(
  name='SideNavigationSection',
  full_name='atlas.v1.SideNavigationSection',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='translation_id', full_name='atlas.v1.SideNavigationSection.translation_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='side_navigation_items', full_name='atlas.v1.SideNavigationSection.side_navigation_items', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='label', full_name='atlas.v1.SideNavigationSection.label', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='chip_content', full_name='atlas.v1.SideNavigationSection.chip_content', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=894,
  serialized_end=1039,
)


_SIDENAVIGATIONCONTAINER = _descriptor.Descriptor(
  name='SideNavigationContainer',
  full_name='atlas.v1.SideNavigationContainer',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='translation_id', full_name='atlas.v1.SideNavigationContainer.translation_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='side_navigation_items', full_name='atlas.v1.SideNavigationContainer.side_navigation_items', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='icon', full_name='atlas.v1.SideNavigationContainer.icon', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='logo_url', full_name='atlas.v1.SideNavigationContainer.logo_url', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='label', full_name='atlas.v1.SideNavigationContainer.label', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='show_icon', full_name='atlas.v1.SideNavigationContainer.show_icon', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='chip_content', full_name='atlas.v1.SideNavigationContainer.chip_content', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1042,
  serialized_end=1240,
)


_SIDENAVIGATIONLINK = _descriptor.Descriptor(
  name='SideNavigationLink',
  full_name='atlas.v1.SideNavigationLink',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='navigation_id', full_name='atlas.v1.SideNavigationLink.navigation_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='url', full_name='atlas.v1.SideNavigationLink.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='path', full_name='atlas.v1.SideNavigationLink.path', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='service_provider_id', full_name='atlas.v1.SideNavigationLink.service_provider_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='logo_url', full_name='atlas.v1.SideNavigationLink.logo_url', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='icon', full_name='atlas.v1.SideNavigationLink.icon', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='translation_id', full_name='atlas.v1.SideNavigationLink.translation_id', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='external', full_name='atlas.v1.SideNavigationLink.external', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='label', full_name='atlas.v1.SideNavigationLink.label', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='show_icon', full_name='atlas.v1.SideNavigationLink.show_icon', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='pinnable', full_name='atlas.v1.SideNavigationLink.pinnable', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='chip_content', full_name='atlas.v1.SideNavigationLink.chip_content', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='is_trial', full_name='atlas.v1.SideNavigationLink.is_trial', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1243,
  serialized_end=1508,
)


_DROPDOWNITEM = _descriptor.Descriptor(
  name='DropdownItem',
  full_name='atlas.v1.DropdownItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='url', full_name='atlas.v1.DropdownItem.url', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='path', full_name='atlas.v1.DropdownItem.path', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='translation_id', full_name='atlas.v1.DropdownItem.translation_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='label', full_name='atlas.v1.DropdownItem.label', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1510,
  serialized_end=1590,
)


_SIDENAVIGATIONITEM = _descriptor.Descriptor(
  name='SideNavigationItem',
  full_name='atlas.v1.SideNavigationItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='side_navigation_section', full_name='atlas.v1.SideNavigationItem.side_navigation_section', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='side_navigation_container', full_name='atlas.v1.SideNavigationItem.side_navigation_container', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='side_navigation_link', full_name='atlas.v1.SideNavigationItem.side_navigation_link', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='item', full_name='atlas.v1.SideNavigationItem.item',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=1593,
  serialized_end=1823,
)


_SALESINFO = _descriptor.Descriptor(
  name='SalesInfo',
  full_name='atlas.v1.SalesInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='market_name', full_name='atlas.v1.SalesInfo.market_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sales_contact', full_name='atlas.v1.SalesInfo.sales_contact', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1825,
  serialized_end=1904,
)


_SALESCONTACT = _descriptor.Descriptor(
  name='SalesContact',
  full_name='atlas.v1.SalesContact',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='sales_person_id', full_name='atlas.v1.SalesContact.sales_person_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='email', full_name='atlas.v1.SalesContact.email', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='first_name', full_name='atlas.v1.SalesContact.first_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='last_name', full_name='atlas.v1.SalesContact.last_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='phone_number', full_name='atlas.v1.SalesContact.phone_number', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='photo_url_secure', full_name='atlas.v1.SalesContact.photo_url_secure', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='job_title', full_name='atlas.v1.SalesContact.job_title', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1907,
  serialized_end=2067,
)


_BRAND = _descriptor.Descriptor(
  name='Brand',
  full_name='atlas.v1.Brand',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='atlas.v1.Brand.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='path_nodes', full_name='atlas.v1.Brand.path_nodes', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='has_access', full_name='atlas.v1.Brand.has_access', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='url', full_name='atlas.v1.Brand.url', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2069,
  serialized_end=2143,
)


_PINNEDITEM = _descriptor.Descriptor(
  name='PinnedItem',
  full_name='atlas.v1.PinnedItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='navigation_id', full_name='atlas.v1.PinnedItem.navigation_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2145,
  serialized_end=2180,
)


_BRANDING = _descriptor.Descriptor(
  name='Branding',
  full_name='atlas.v1.Branding',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='theme', full_name='atlas.v1.Branding.theme', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=_descriptor._ParseOptions(descriptor_pb2.FieldOptions(), _b('\030\001'))),
    _descriptor.FieldDescriptor(
      name='logo_url', full_name='atlas.v1.Branding.logo_url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partner_name', full_name='atlas.v1.Branding.partner_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='center_name', full_name='atlas.v1.Branding.center_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='theming', full_name='atlas.v1.Branding.theming', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2183,
  serialized_end=2328,
)


_ACCOUNTGROUP = _descriptor.Descriptor(
  name='AccountGroup',
  full_name='atlas.v1.AccountGroup',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group_id', full_name='atlas.v1.AccountGroup.account_group_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='name', full_name='atlas.v1.AccountGroup.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='address', full_name='atlas.v1.AccountGroup.address', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='activated_product_ids', full_name='atlas.v1.AccountGroup.activated_product_ids', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='url', full_name='atlas.v1.AccountGroup.url', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2330,
  serialized_end=2445,
)


_LOCATION = _descriptor.Descriptor(
  name='Location',
  full_name='atlas.v1.Location',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group', full_name='atlas.v1.Location.account_group', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='brand', full_name='atlas.v1.Location.brand', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='location', full_name='atlas.v1.Location.location',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=2447,
  serialized_end=2552,
)


_ASSOCIATEDLOCATIONIDS = _descriptor.Descriptor(
  name='AssociatedLocationIDs',
  full_name='atlas.v1.AssociatedLocationIDs',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group_ids', full_name='atlas.v1.AssociatedLocationIDs.account_group_ids', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='group_paths', full_name='atlas.v1.AssociatedLocationIDs.group_paths', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2554,
  serialized_end=2625,
)


_THEMING = _descriptor.Descriptor(
  name='Theming',
  full_name='atlas.v1.Theming',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='primary_color', full_name='atlas.v1.Theming.primary_color', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='primary_hover_color', full_name='atlas.v1.Theming.primary_hover_color', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='primary_active_color', full_name='atlas.v1.Theming.primary_active_color', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='secondary_color', full_name='atlas.v1.Theming.secondary_color', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='secondary_hover_color', full_name='atlas.v1.Theming.secondary_hover_color', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='secondary_active_color', full_name='atlas.v1.Theming.secondary_active_color', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='font_color', full_name='atlas.v1.Theming.font_color', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='font_disabled_color', full_name='atlas.v1.Theming.font_disabled_color', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='accents_color', full_name='atlas.v1.Theming.accents_color', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='accents_active_color', full_name='atlas.v1.Theming.accents_active_color', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='focus_color', full_name='atlas.v1.Theming.focus_color', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='border_color', full_name='atlas.v1.Theming.border_color', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2628,
  serialized_end=2952,
)


_RETENTIONCONFIG = _descriptor.Descriptor(
  name='RetentionConfig',
  full_name='atlas.v1.RetentionConfig',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='cancellation_notification_email', full_name='atlas.v1.RetentionConfig.cancellation_notification_email', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2954,
  serialized_end=3012,
)


_TOTALLOCATIONS = _descriptor.Descriptor(
  name='TotalLocations',
  full_name='atlas.v1.TotalLocations',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='accounts', full_name='atlas.v1.TotalLocations.accounts', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='brands', full_name='atlas.v1.TotalLocations.brands', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3014,
  serialized_end=3064,
)


_GETNAVIGATIONDATARESPONSE = _descriptor.Descriptor(
  name='GetNavigationDataResponse',
  full_name='atlas.v1.GetNavigationDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_items', full_name='atlas.v1.GetNavigationDataResponse.account_items', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='branding', full_name='atlas.v1.GetNavigationDataResponse.branding', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='sales_info', full_name='atlas.v1.GetNavigationDataResponse.sales_info', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='pinned_items', full_name='atlas.v1.GetNavigationDataResponse.pinned_items', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='associated_location_ids', full_name='atlas.v1.GetNavigationDataResponse.associated_location_ids', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='default_location', full_name='atlas.v1.GetNavigationDataResponse.default_location', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='brand_items', full_name='atlas.v1.GetNavigationDataResponse.brand_items', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='language', full_name='atlas.v1.GetNavigationDataResponse.language', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='dropdown_items', full_name='atlas.v1.GetNavigationDataResponse.dropdown_items', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='current_brand_name', full_name='atlas.v1.GetNavigationDataResponse.current_brand_name', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='user_view', full_name='atlas.v1.GetNavigationDataResponse.user_view', index=10,
      number=11, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='retention_config', full_name='atlas.v1.GetNavigationDataResponse.retention_config', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='total_locations', full_name='atlas.v1.GetNavigationDataResponse.total_locations', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3067,
  serialized_end=3654,
)


_SETPINSREQUEST = _descriptor.Descriptor(
  name='SetPinsRequest',
  full_name='atlas.v1.SetPinsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='identifier', full_name='atlas.v1.SetPinsRequest.identifier', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='items', full_name='atlas.v1.SetPinsRequest.items', index=1,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3656,
  serialized_end=3729,
)


_GETPINSREQUEST = _descriptor.Descriptor(
  name='GetPinsRequest',
  full_name='atlas.v1.GetPinsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='identifier', full_name='atlas.v1.GetPinsRequest.identifier', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3731,
  serialized_end=3767,
)


_GETPINSRESPONSE = _descriptor.Descriptor(
  name='GetPinsResponse',
  full_name='atlas.v1.GetPinsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='items', full_name='atlas.v1.GetPinsResponse.items', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3769,
  serialized_end=3823,
)


_GETLOCATIONSREQUEST_ACCOUNTGROUPS = _descriptor.Descriptor(
  name='AccountGroups',
  full_name='atlas.v1.GetLocationsRequest.AccountGroups',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group_ids', full_name='atlas.v1.GetLocationsRequest.AccountGroups.account_group_ids', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3976,
  serialized_end=4018,
)

_GETLOCATIONSREQUEST_GROUPS = _descriptor.Descriptor(
  name='Groups',
  full_name='atlas.v1.GetLocationsRequest.Groups',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='group_paths', full_name='atlas.v1.GetLocationsRequest.Groups.group_paths', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4020,
  serialized_end=4049,
)

_GETLOCATIONSREQUEST = _descriptor.Descriptor(
  name='GetLocationsRequest',
  full_name='atlas.v1.GetLocationsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_groups', full_name='atlas.v1.GetLocationsRequest.account_groups', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='groups', full_name='atlas.v1.GetLocationsRequest.groups', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[_GETLOCATIONSREQUEST_ACCOUNTGROUPS, _GETLOCATIONSREQUEST_GROUPS, ],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='identifier', full_name='atlas.v1.GetLocationsRequest.identifier',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=3826,
  serialized_end=4063,
)


_GETLOCATIONSRESPONSE = _descriptor.Descriptor(
  name='GetLocationsResponse',
  full_name='atlas.v1.GetLocationsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='locations', full_name='atlas.v1.GetLocationsResponse.locations', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4065,
  serialized_end=4126,
)


_LISTELEVATEDLOCATIONSREQUEST = _descriptor.Descriptor(
  name='ListElevatedLocationsRequest',
  full_name='atlas.v1.ListElevatedLocationsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='atlas.v1.ListElevatedLocationsRequest.partner_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cursor', full_name='atlas.v1.ListElevatedLocationsRequest.cursor', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='page_size', full_name='atlas.v1.ListElevatedLocationsRequest.page_size', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='search', full_name='atlas.v1.ListElevatedLocationsRequest.search', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='account_groups', full_name='atlas.v1.ListElevatedLocationsRequest.account_groups', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='brands', full_name='atlas.v1.ListElevatedLocationsRequest.brands', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='type', full_name='atlas.v1.ListElevatedLocationsRequest.type',
      index=0, containing_type=None, fields=[]),
  ],
  serialized_start=4129,
  serialized_end=4282,
)


_LISTELEVATEDLOCATIONSRESPONSE = _descriptor.Descriptor(
  name='ListElevatedLocationsResponse',
  full_name='atlas.v1.ListElevatedLocationsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='locations', full_name='atlas.v1.ListElevatedLocationsResponse.locations', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='cursor', full_name='atlas.v1.ListElevatedLocationsResponse.cursor', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='has_more', full_name='atlas.v1.ListElevatedLocationsResponse.has_more', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4284,
  serialized_end=4388,
)


_SETLANGUAGEREQUEST = _descriptor.Descriptor(
  name='SetLanguageRequest',
  full_name='atlas.v1.SetLanguageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='language', full_name='atlas.v1.SetLanguageRequest.language', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4390,
  serialized_end=4428,
)


_GETLANGUAGEREQUEST = _descriptor.Descriptor(
  name='GetLanguageRequest',
  full_name='atlas.v1.GetLanguageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4430,
  serialized_end=4450,
)


_GETLANGUAGERESPONSE = _descriptor.Descriptor(
  name='GetLanguageResponse',
  full_name='atlas.v1.GetLanguageResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='language', full_name='atlas.v1.GetLanguageResponse.language', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4452,
  serialized_end=4491,
)


_CONTACTUSREQUEST = _descriptor.Descriptor(
  name='ContactUsRequest',
  full_name='atlas.v1.ContactUsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group_id', full_name='atlas.v1.ContactUsRequest.account_group_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='message', full_name='atlas.v1.ContactUsRequest.message', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4493,
  serialized_end=4554,
)


_SETDEFAULTLOCATIONREQUEST = _descriptor.Descriptor(
  name='SetDefaultLocationRequest',
  full_name='atlas.v1.SetDefaultLocationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  fields=[
    _descriptor.FieldDescriptor(
      name='account_group_id', full_name='atlas.v1.SetDefaultLocationRequest.account_group_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
    _descriptor.FieldDescriptor(
      name='partner_id', full_name='atlas.v1.SetDefaultLocationRequest.partner_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=_b("").decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      options=None),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4556,
  serialized_end=4629,
)

_GETDATARESPONSE.fields_by_name['user'].message_type = _USERNAVIGATIONITEM
_GETDATARESPONSE.fields_by_name['centers'].message_type = _CENTERNAVIGATIONITEM
_GETDATARESPONSE.fields_by_name['theme'].enum_type = _UITHEME
_GETDATARESPONSE.fields_by_name['theming'].message_type = _THEMING
_GETDATARESPONSE.fields_by_name['location_data'].message_type = _LOCATIONDATA
_SIDENAVIGATIONSECTION.fields_by_name['side_navigation_items'].message_type = _SIDENAVIGATIONITEM
_SIDENAVIGATIONCONTAINER.fields_by_name['side_navigation_items'].message_type = _SIDENAVIGATIONITEM
_SIDENAVIGATIONITEM.fields_by_name['side_navigation_section'].message_type = _SIDENAVIGATIONSECTION
_SIDENAVIGATIONITEM.fields_by_name['side_navigation_container'].message_type = _SIDENAVIGATIONCONTAINER
_SIDENAVIGATIONITEM.fields_by_name['side_navigation_link'].message_type = _SIDENAVIGATIONLINK
_SIDENAVIGATIONITEM.oneofs_by_name['item'].fields.append(
  _SIDENAVIGATIONITEM.fields_by_name['side_navigation_section'])
_SIDENAVIGATIONITEM.fields_by_name['side_navigation_section'].containing_oneof = _SIDENAVIGATIONITEM.oneofs_by_name['item']
_SIDENAVIGATIONITEM.oneofs_by_name['item'].fields.append(
  _SIDENAVIGATIONITEM.fields_by_name['side_navigation_container'])
_SIDENAVIGATIONITEM.fields_by_name['side_navigation_container'].containing_oneof = _SIDENAVIGATIONITEM.oneofs_by_name['item']
_SIDENAVIGATIONITEM.oneofs_by_name['item'].fields.append(
  _SIDENAVIGATIONITEM.fields_by_name['side_navigation_link'])
_SIDENAVIGATIONITEM.fields_by_name['side_navigation_link'].containing_oneof = _SIDENAVIGATIONITEM.oneofs_by_name['item']
_SALESINFO.fields_by_name['sales_contact'].message_type = _SALESCONTACT
_BRANDING.fields_by_name['theme'].enum_type = _UITHEME
_BRANDING.fields_by_name['theming'].message_type = _THEMING
_LOCATION.fields_by_name['account_group'].message_type = _ACCOUNTGROUP
_LOCATION.fields_by_name['brand'].message_type = _BRAND
_LOCATION.oneofs_by_name['location'].fields.append(
  _LOCATION.fields_by_name['account_group'])
_LOCATION.fields_by_name['account_group'].containing_oneof = _LOCATION.oneofs_by_name['location']
_LOCATION.oneofs_by_name['location'].fields.append(
  _LOCATION.fields_by_name['brand'])
_LOCATION.fields_by_name['brand'].containing_oneof = _LOCATION.oneofs_by_name['location']
_GETNAVIGATIONDATARESPONSE.fields_by_name['account_items'].message_type = _SIDENAVIGATIONITEM
_GETNAVIGATIONDATARESPONSE.fields_by_name['branding'].message_type = _BRANDING
_GETNAVIGATIONDATARESPONSE.fields_by_name['sales_info'].message_type = _SALESINFO
_GETNAVIGATIONDATARESPONSE.fields_by_name['pinned_items'].message_type = _PINNEDITEM
_GETNAVIGATIONDATARESPONSE.fields_by_name['associated_location_ids'].message_type = _ASSOCIATEDLOCATIONIDS
_GETNAVIGATIONDATARESPONSE.fields_by_name['brand_items'].message_type = _SIDENAVIGATIONITEM
_GETNAVIGATIONDATARESPONSE.fields_by_name['dropdown_items'].message_type = _DROPDOWNITEM
_GETNAVIGATIONDATARESPONSE.fields_by_name['user_view'].enum_type = _USERVIEWTYPE
_GETNAVIGATIONDATARESPONSE.fields_by_name['retention_config'].message_type = _RETENTIONCONFIG
_GETNAVIGATIONDATARESPONSE.fields_by_name['total_locations'].message_type = _TOTALLOCATIONS
_SETPINSREQUEST.fields_by_name['items'].message_type = _PINNEDITEM
_GETPINSRESPONSE.fields_by_name['items'].message_type = _PINNEDITEM
_GETLOCATIONSREQUEST_ACCOUNTGROUPS.containing_type = _GETLOCATIONSREQUEST
_GETLOCATIONSREQUEST_GROUPS.containing_type = _GETLOCATIONSREQUEST
_GETLOCATIONSREQUEST.fields_by_name['account_groups'].message_type = _GETLOCATIONSREQUEST_ACCOUNTGROUPS
_GETLOCATIONSREQUEST.fields_by_name['groups'].message_type = _GETLOCATIONSREQUEST_GROUPS
_GETLOCATIONSREQUEST.oneofs_by_name['identifier'].fields.append(
  _GETLOCATIONSREQUEST.fields_by_name['account_groups'])
_GETLOCATIONSREQUEST.fields_by_name['account_groups'].containing_oneof = _GETLOCATIONSREQUEST.oneofs_by_name['identifier']
_GETLOCATIONSREQUEST.oneofs_by_name['identifier'].fields.append(
  _GETLOCATIONSREQUEST.fields_by_name['groups'])
_GETLOCATIONSREQUEST.fields_by_name['groups'].containing_oneof = _GETLOCATIONSREQUEST.oneofs_by_name['identifier']
_GETLOCATIONSRESPONSE.fields_by_name['locations'].message_type = _LOCATION
_LISTELEVATEDLOCATIONSREQUEST.oneofs_by_name['type'].fields.append(
  _LISTELEVATEDLOCATIONSREQUEST.fields_by_name['account_groups'])
_LISTELEVATEDLOCATIONSREQUEST.fields_by_name['account_groups'].containing_oneof = _LISTELEVATEDLOCATIONSREQUEST.oneofs_by_name['type']
_LISTELEVATEDLOCATIONSREQUEST.oneofs_by_name['type'].fields.append(
  _LISTELEVATEDLOCATIONSREQUEST.fields_by_name['brands'])
_LISTELEVATEDLOCATIONSREQUEST.fields_by_name['brands'].containing_oneof = _LISTELEVATEDLOCATIONSREQUEST.oneofs_by_name['type']
_LISTELEVATEDLOCATIONSRESPONSE.fields_by_name['locations'].message_type = _LOCATION
DESCRIPTOR.message_types_by_name['UserNavigationItem'] = _USERNAVIGATIONITEM
DESCRIPTOR.message_types_by_name['CenterNavigationItem'] = _CENTERNAVIGATIONITEM
DESCRIPTOR.message_types_by_name['GetDataRequest'] = _GETDATAREQUEST
DESCRIPTOR.message_types_by_name['LocationData'] = _LOCATIONDATA
DESCRIPTOR.message_types_by_name['GetDataResponse'] = _GETDATARESPONSE
DESCRIPTOR.message_types_by_name['GetNavigationDataRequest'] = _GETNAVIGATIONDATAREQUEST
DESCRIPTOR.message_types_by_name['SideNavigationSection'] = _SIDENAVIGATIONSECTION
DESCRIPTOR.message_types_by_name['SideNavigationContainer'] = _SIDENAVIGATIONCONTAINER
DESCRIPTOR.message_types_by_name['SideNavigationLink'] = _SIDENAVIGATIONLINK
DESCRIPTOR.message_types_by_name['DropdownItem'] = _DROPDOWNITEM
DESCRIPTOR.message_types_by_name['SideNavigationItem'] = _SIDENAVIGATIONITEM
DESCRIPTOR.message_types_by_name['SalesInfo'] = _SALESINFO
DESCRIPTOR.message_types_by_name['SalesContact'] = _SALESCONTACT
DESCRIPTOR.message_types_by_name['Brand'] = _BRAND
DESCRIPTOR.message_types_by_name['PinnedItem'] = _PINNEDITEM
DESCRIPTOR.message_types_by_name['Branding'] = _BRANDING
DESCRIPTOR.message_types_by_name['AccountGroup'] = _ACCOUNTGROUP
DESCRIPTOR.message_types_by_name['Location'] = _LOCATION
DESCRIPTOR.message_types_by_name['AssociatedLocationIDs'] = _ASSOCIATEDLOCATIONIDS
DESCRIPTOR.message_types_by_name['Theming'] = _THEMING
DESCRIPTOR.message_types_by_name['RetentionConfig'] = _RETENTIONCONFIG
DESCRIPTOR.message_types_by_name['TotalLocations'] = _TOTALLOCATIONS
DESCRIPTOR.message_types_by_name['GetNavigationDataResponse'] = _GETNAVIGATIONDATARESPONSE
DESCRIPTOR.message_types_by_name['SetPinsRequest'] = _SETPINSREQUEST
DESCRIPTOR.message_types_by_name['GetPinsRequest'] = _GETPINSREQUEST
DESCRIPTOR.message_types_by_name['GetPinsResponse'] = _GETPINSRESPONSE
DESCRIPTOR.message_types_by_name['GetLocationsRequest'] = _GETLOCATIONSREQUEST
DESCRIPTOR.message_types_by_name['GetLocationsResponse'] = _GETLOCATIONSRESPONSE
DESCRIPTOR.message_types_by_name['ListElevatedLocationsRequest'] = _LISTELEVATEDLOCATIONSREQUEST
DESCRIPTOR.message_types_by_name['ListElevatedLocationsResponse'] = _LISTELEVATEDLOCATIONSRESPONSE
DESCRIPTOR.message_types_by_name['SetLanguageRequest'] = _SETLANGUAGEREQUEST
DESCRIPTOR.message_types_by_name['GetLanguageRequest'] = _GETLANGUAGEREQUEST
DESCRIPTOR.message_types_by_name['GetLanguageResponse'] = _GETLANGUAGERESPONSE
DESCRIPTOR.message_types_by_name['ContactUsRequest'] = _CONTACTUSREQUEST
DESCRIPTOR.message_types_by_name['SetDefaultLocationRequest'] = _SETDEFAULTLOCATIONREQUEST
DESCRIPTOR.enum_types_by_name['UITheme'] = _UITHEME
DESCRIPTOR.enum_types_by_name['UserViewType'] = _USERVIEWTYPE

UserNavigationItem = _reflection.GeneratedProtocolMessageType('UserNavigationItem', (_message.Message,), dict(
  DESCRIPTOR = _USERNAVIGATIONITEM,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.UserNavigationItem)
  ))
_sym_db.RegisterMessage(UserNavigationItem)

CenterNavigationItem = _reflection.GeneratedProtocolMessageType('CenterNavigationItem', (_message.Message,), dict(
  DESCRIPTOR = _CENTERNAVIGATIONITEM,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.CenterNavigationItem)
  ))
_sym_db.RegisterMessage(CenterNavigationItem)

GetDataRequest = _reflection.GeneratedProtocolMessageType('GetDataRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETDATAREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetDataRequest)
  ))
_sym_db.RegisterMessage(GetDataRequest)

LocationData = _reflection.GeneratedProtocolMessageType('LocationData', (_message.Message,), dict(
  DESCRIPTOR = _LOCATIONDATA,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.LocationData)
  ))
_sym_db.RegisterMessage(LocationData)

GetDataResponse = _reflection.GeneratedProtocolMessageType('GetDataResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETDATARESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetDataResponse)
  ))
_sym_db.RegisterMessage(GetDataResponse)

GetNavigationDataRequest = _reflection.GeneratedProtocolMessageType('GetNavigationDataRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETNAVIGATIONDATAREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetNavigationDataRequest)
  ))
_sym_db.RegisterMessage(GetNavigationDataRequest)

SideNavigationSection = _reflection.GeneratedProtocolMessageType('SideNavigationSection', (_message.Message,), dict(
  DESCRIPTOR = _SIDENAVIGATIONSECTION,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SideNavigationSection)
  ))
_sym_db.RegisterMessage(SideNavigationSection)

SideNavigationContainer = _reflection.GeneratedProtocolMessageType('SideNavigationContainer', (_message.Message,), dict(
  DESCRIPTOR = _SIDENAVIGATIONCONTAINER,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SideNavigationContainer)
  ))
_sym_db.RegisterMessage(SideNavigationContainer)

SideNavigationLink = _reflection.GeneratedProtocolMessageType('SideNavigationLink', (_message.Message,), dict(
  DESCRIPTOR = _SIDENAVIGATIONLINK,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SideNavigationLink)
  ))
_sym_db.RegisterMessage(SideNavigationLink)

DropdownItem = _reflection.GeneratedProtocolMessageType('DropdownItem', (_message.Message,), dict(
  DESCRIPTOR = _DROPDOWNITEM,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.DropdownItem)
  ))
_sym_db.RegisterMessage(DropdownItem)

SideNavigationItem = _reflection.GeneratedProtocolMessageType('SideNavigationItem', (_message.Message,), dict(
  DESCRIPTOR = _SIDENAVIGATIONITEM,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SideNavigationItem)
  ))
_sym_db.RegisterMessage(SideNavigationItem)

SalesInfo = _reflection.GeneratedProtocolMessageType('SalesInfo', (_message.Message,), dict(
  DESCRIPTOR = _SALESINFO,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SalesInfo)
  ))
_sym_db.RegisterMessage(SalesInfo)

SalesContact = _reflection.GeneratedProtocolMessageType('SalesContact', (_message.Message,), dict(
  DESCRIPTOR = _SALESCONTACT,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SalesContact)
  ))
_sym_db.RegisterMessage(SalesContact)

Brand = _reflection.GeneratedProtocolMessageType('Brand', (_message.Message,), dict(
  DESCRIPTOR = _BRAND,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.Brand)
  ))
_sym_db.RegisterMessage(Brand)

PinnedItem = _reflection.GeneratedProtocolMessageType('PinnedItem', (_message.Message,), dict(
  DESCRIPTOR = _PINNEDITEM,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.PinnedItem)
  ))
_sym_db.RegisterMessage(PinnedItem)

Branding = _reflection.GeneratedProtocolMessageType('Branding', (_message.Message,), dict(
  DESCRIPTOR = _BRANDING,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.Branding)
  ))
_sym_db.RegisterMessage(Branding)

AccountGroup = _reflection.GeneratedProtocolMessageType('AccountGroup', (_message.Message,), dict(
  DESCRIPTOR = _ACCOUNTGROUP,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.AccountGroup)
  ))
_sym_db.RegisterMessage(AccountGroup)

Location = _reflection.GeneratedProtocolMessageType('Location', (_message.Message,), dict(
  DESCRIPTOR = _LOCATION,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.Location)
  ))
_sym_db.RegisterMessage(Location)

AssociatedLocationIDs = _reflection.GeneratedProtocolMessageType('AssociatedLocationIDs', (_message.Message,), dict(
  DESCRIPTOR = _ASSOCIATEDLOCATIONIDS,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.AssociatedLocationIDs)
  ))
_sym_db.RegisterMessage(AssociatedLocationIDs)

Theming = _reflection.GeneratedProtocolMessageType('Theming', (_message.Message,), dict(
  DESCRIPTOR = _THEMING,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.Theming)
  ))
_sym_db.RegisterMessage(Theming)

RetentionConfig = _reflection.GeneratedProtocolMessageType('RetentionConfig', (_message.Message,), dict(
  DESCRIPTOR = _RETENTIONCONFIG,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.RetentionConfig)
  ))
_sym_db.RegisterMessage(RetentionConfig)

TotalLocations = _reflection.GeneratedProtocolMessageType('TotalLocations', (_message.Message,), dict(
  DESCRIPTOR = _TOTALLOCATIONS,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.TotalLocations)
  ))
_sym_db.RegisterMessage(TotalLocations)

GetNavigationDataResponse = _reflection.GeneratedProtocolMessageType('GetNavigationDataResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETNAVIGATIONDATARESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetNavigationDataResponse)
  ))
_sym_db.RegisterMessage(GetNavigationDataResponse)

SetPinsRequest = _reflection.GeneratedProtocolMessageType('SetPinsRequest', (_message.Message,), dict(
  DESCRIPTOR = _SETPINSREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SetPinsRequest)
  ))
_sym_db.RegisterMessage(SetPinsRequest)

GetPinsRequest = _reflection.GeneratedProtocolMessageType('GetPinsRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETPINSREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetPinsRequest)
  ))
_sym_db.RegisterMessage(GetPinsRequest)

GetPinsResponse = _reflection.GeneratedProtocolMessageType('GetPinsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETPINSRESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetPinsResponse)
  ))
_sym_db.RegisterMessage(GetPinsResponse)

GetLocationsRequest = _reflection.GeneratedProtocolMessageType('GetLocationsRequest', (_message.Message,), dict(

  AccountGroups = _reflection.GeneratedProtocolMessageType('AccountGroups', (_message.Message,), dict(
    DESCRIPTOR = _GETLOCATIONSREQUEST_ACCOUNTGROUPS,
    __module__ = 'atlas.v1.api_pb2'
    # @@protoc_insertion_point(class_scope:atlas.v1.GetLocationsRequest.AccountGroups)
    ))
  ,

  Groups = _reflection.GeneratedProtocolMessageType('Groups', (_message.Message,), dict(
    DESCRIPTOR = _GETLOCATIONSREQUEST_GROUPS,
    __module__ = 'atlas.v1.api_pb2'
    # @@protoc_insertion_point(class_scope:atlas.v1.GetLocationsRequest.Groups)
    ))
  ,
  DESCRIPTOR = _GETLOCATIONSREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetLocationsRequest)
  ))
_sym_db.RegisterMessage(GetLocationsRequest)
_sym_db.RegisterMessage(GetLocationsRequest.AccountGroups)
_sym_db.RegisterMessage(GetLocationsRequest.Groups)

GetLocationsResponse = _reflection.GeneratedProtocolMessageType('GetLocationsResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETLOCATIONSRESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetLocationsResponse)
  ))
_sym_db.RegisterMessage(GetLocationsResponse)

ListElevatedLocationsRequest = _reflection.GeneratedProtocolMessageType('ListElevatedLocationsRequest', (_message.Message,), dict(
  DESCRIPTOR = _LISTELEVATEDLOCATIONSREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.ListElevatedLocationsRequest)
  ))
_sym_db.RegisterMessage(ListElevatedLocationsRequest)

ListElevatedLocationsResponse = _reflection.GeneratedProtocolMessageType('ListElevatedLocationsResponse', (_message.Message,), dict(
  DESCRIPTOR = _LISTELEVATEDLOCATIONSRESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.ListElevatedLocationsResponse)
  ))
_sym_db.RegisterMessage(ListElevatedLocationsResponse)

SetLanguageRequest = _reflection.GeneratedProtocolMessageType('SetLanguageRequest', (_message.Message,), dict(
  DESCRIPTOR = _SETLANGUAGEREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SetLanguageRequest)
  ))
_sym_db.RegisterMessage(SetLanguageRequest)

GetLanguageRequest = _reflection.GeneratedProtocolMessageType('GetLanguageRequest', (_message.Message,), dict(
  DESCRIPTOR = _GETLANGUAGEREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetLanguageRequest)
  ))
_sym_db.RegisterMessage(GetLanguageRequest)

GetLanguageResponse = _reflection.GeneratedProtocolMessageType('GetLanguageResponse', (_message.Message,), dict(
  DESCRIPTOR = _GETLANGUAGERESPONSE,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.GetLanguageResponse)
  ))
_sym_db.RegisterMessage(GetLanguageResponse)

ContactUsRequest = _reflection.GeneratedProtocolMessageType('ContactUsRequest', (_message.Message,), dict(
  DESCRIPTOR = _CONTACTUSREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.ContactUsRequest)
  ))
_sym_db.RegisterMessage(ContactUsRequest)

SetDefaultLocationRequest = _reflection.GeneratedProtocolMessageType('SetDefaultLocationRequest', (_message.Message,), dict(
  DESCRIPTOR = _SETDEFAULTLOCATIONREQUEST,
  __module__ = 'atlas.v1.api_pb2'
  # @@protoc_insertion_point(class_scope:atlas.v1.SetDefaultLocationRequest)
  ))
_sym_db.RegisterMessage(SetDefaultLocationRequest)


DESCRIPTOR.has_options = True
DESCRIPTOR._options = _descriptor._ParseOptions(descriptor_pb2.FileOptions(), _b('\n\037com.vendasta.atlas.v1.generatedB\010ApiProto'))
_GETDATARESPONSE.fields_by_name['theme'].has_options = True
_GETDATARESPONSE.fields_by_name['theme']._options = _descriptor._ParseOptions(descriptor_pb2.FieldOptions(), _b('\030\001'))
_BRANDING.fields_by_name['theme'].has_options = True
_BRANDING.fields_by_name['theme']._options = _descriptor._ParseOptions(descriptor_pb2.FieldOptions(), _b('\030\001'))
import grpc
from grpc.beta import implementations as beta_implementations
from grpc.beta import interfaces as beta_interfaces
from grpc.framework.common import cardinality
from grpc.framework.interfaces.face import utilities as face_utilities


class AtlasStub(object):
  """The Atlas service
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.GetData = channel.unary_unary(
        '/atlas.v1.Atlas/GetData',
        request_serializer=GetDataRequest.SerializeToString,
        response_deserializer=GetDataResponse.FromString,
        )
    self.GetNavigationData = channel.unary_unary(
        '/atlas.v1.Atlas/GetNavigationData',
        request_serializer=GetNavigationDataRequest.SerializeToString,
        response_deserializer=GetNavigationDataResponse.FromString,
        )
    self.GetLocations = channel.unary_unary(
        '/atlas.v1.Atlas/GetLocations',
        request_serializer=GetLocationsRequest.SerializeToString,
        response_deserializer=GetLocationsResponse.FromString,
        )
    self.ListElevatedLocations = channel.unary_unary(
        '/atlas.v1.Atlas/ListElevatedLocations',
        request_serializer=ListElevatedLocationsRequest.SerializeToString,
        response_deserializer=ListElevatedLocationsResponse.FromString,
        )
    self.ContactUs = channel.unary_unary(
        '/atlas.v1.Atlas/ContactUs',
        request_serializer=ContactUsRequest.SerializeToString,
        response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
        )
    self.SetDefaultLocation = channel.unary_unary(
        '/atlas.v1.Atlas/SetDefaultLocation',
        request_serializer=SetDefaultLocationRequest.SerializeToString,
        response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
        )


class AtlasServicer(object):
  """The Atlas service
  """

  def GetData(self, request, context):
    """GetData gets the data that populates the Atlas navbar
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetNavigationData(self, request, context):
    """GetNavigationData gets the navigation data that populates the sidenav
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetLocations(self, request, context):
    """GetLocations returns associated account groups or groups
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ListElevatedLocations(self, request, context):
    """ListElevatedLocations returns a paged list of locations available to an elevated user.
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def ContactUs(self, request, context):
    """ContactUs notifies the platform that the user is interested in contacting the partner
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def SetDefaultLocation(self, request, context):
    """SetDefaultLocationRequest sets the default location on a subject
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_AtlasServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'GetData': grpc.unary_unary_rpc_method_handler(
          servicer.GetData,
          request_deserializer=GetDataRequest.FromString,
          response_serializer=GetDataResponse.SerializeToString,
      ),
      'GetNavigationData': grpc.unary_unary_rpc_method_handler(
          servicer.GetNavigationData,
          request_deserializer=GetNavigationDataRequest.FromString,
          response_serializer=GetNavigationDataResponse.SerializeToString,
      ),
      'GetLocations': grpc.unary_unary_rpc_method_handler(
          servicer.GetLocations,
          request_deserializer=GetLocationsRequest.FromString,
          response_serializer=GetLocationsResponse.SerializeToString,
      ),
      'ListElevatedLocations': grpc.unary_unary_rpc_method_handler(
          servicer.ListElevatedLocations,
          request_deserializer=ListElevatedLocationsRequest.FromString,
          response_serializer=ListElevatedLocationsResponse.SerializeToString,
      ),
      'ContactUs': grpc.unary_unary_rpc_method_handler(
          servicer.ContactUs,
          request_deserializer=ContactUsRequest.FromString,
          response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
      ),
      'SetDefaultLocation': grpc.unary_unary_rpc_method_handler(
          servicer.SetDefaultLocation,
          request_deserializer=SetDefaultLocationRequest.FromString,
          response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'atlas.v1.Atlas', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))


class BetaAtlasServicer(object):
  """The Beta API is deprecated for 0.15.0 and later.

  It is recommended to use the GA API (classes and functions in this
  file not marked beta) for all further purposes. This class was generated
  only to ease transition from grpcio<0.15.0 to grpcio>=0.15.0."""
  """The Atlas service
  """
  def GetData(self, request, context):
    """GetData gets the data that populates the Atlas navbar
    """
    context.code(beta_interfaces.StatusCode.UNIMPLEMENTED)
  def GetNavigationData(self, request, context):
    """GetNavigationData gets the navigation data that populates the sidenav
    """
    context.code(beta_interfaces.StatusCode.UNIMPLEMENTED)
  def GetLocations(self, request, context):
    """GetLocations returns associated account groups or groups
    """
    context.code(beta_interfaces.StatusCode.UNIMPLEMENTED)
  def ListElevatedLocations(self, request, context):
    """ListElevatedLocations returns a paged list of locations available to an elevated user.
    """
    context.code(beta_interfaces.StatusCode.UNIMPLEMENTED)
  def ContactUs(self, request, context):
    """ContactUs notifies the platform that the user is interested in contacting the partner
    """
    context.code(beta_interfaces.StatusCode.UNIMPLEMENTED)
  def SetDefaultLocation(self, request, context):
    """SetDefaultLocationRequest sets the default location on a subject
    """
    context.code(beta_interfaces.StatusCode.UNIMPLEMENTED)


class BetaAtlasStub(object):
  """The Beta API is deprecated for 0.15.0 and later.

  It is recommended to use the GA API (classes and functions in this
  file not marked beta) for all further purposes. This class was generated
  only to ease transition from grpcio<0.15.0 to grpcio>=0.15.0."""
  """The Atlas service
  """
  def GetData(self, request, timeout, metadata=None, with_call=False, protocol_options=None):
    """GetData gets the data that populates the Atlas navbar
    """
    raise NotImplementedError()
  GetData.future = None
  def GetNavigationData(self, request, timeout, metadata=None, with_call=False, protocol_options=None):
    """GetNavigationData gets the navigation data that populates the sidenav
    """
    raise NotImplementedError()
  GetNavigationData.future = None
  def GetLocations(self, request, timeout, metadata=None, with_call=False, protocol_options=None):
    """GetLocations returns associated account groups or groups
    """
    raise NotImplementedError()
  GetLocations.future = None
  def ListElevatedLocations(self, request, timeout, metadata=None, with_call=False, protocol_options=None):
    """ListElevatedLocations returns a paged list of locations available to an elevated user.
    """
    raise NotImplementedError()
  ListElevatedLocations.future = None
  def ContactUs(self, request, timeout, metadata=None, with_call=False, protocol_options=None):
    """ContactUs notifies the platform that the user is interested in contacting the partner
    """
    raise NotImplementedError()
  ContactUs.future = None
  def SetDefaultLocation(self, request, timeout, metadata=None, with_call=False, protocol_options=None):
    """SetDefaultLocationRequest sets the default location on a subject
    """
    raise NotImplementedError()
  SetDefaultLocation.future = None


def beta_create_Atlas_server(servicer, pool=None, pool_size=None, default_timeout=None, maximum_timeout=None):
  """The Beta API is deprecated for 0.15.0 and later.

  It is recommended to use the GA API (classes and functions in this
  file not marked beta) for all further purposes. This function was
  generated only to ease transition from grpcio<0.15.0 to grpcio>=0.15.0"""
  request_deserializers = {
    ('atlas.v1.Atlas', 'ContactUs'): ContactUsRequest.FromString,
    ('atlas.v1.Atlas', 'GetData'): GetDataRequest.FromString,
    ('atlas.v1.Atlas', 'GetLocations'): GetLocationsRequest.FromString,
    ('atlas.v1.Atlas', 'GetNavigationData'): GetNavigationDataRequest.FromString,
    ('atlas.v1.Atlas', 'ListElevatedLocations'): ListElevatedLocationsRequest.FromString,
    ('atlas.v1.Atlas', 'SetDefaultLocation'): SetDefaultLocationRequest.FromString,
  }
  response_serializers = {
    ('atlas.v1.Atlas', 'ContactUs'): google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
    ('atlas.v1.Atlas', 'GetData'): GetDataResponse.SerializeToString,
    ('atlas.v1.Atlas', 'GetLocations'): GetLocationsResponse.SerializeToString,
    ('atlas.v1.Atlas', 'GetNavigationData'): GetNavigationDataResponse.SerializeToString,
    ('atlas.v1.Atlas', 'ListElevatedLocations'): ListElevatedLocationsResponse.SerializeToString,
    ('atlas.v1.Atlas', 'SetDefaultLocation'): google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
  }
  method_implementations = {
    ('atlas.v1.Atlas', 'ContactUs'): face_utilities.unary_unary_inline(servicer.ContactUs),
    ('atlas.v1.Atlas', 'GetData'): face_utilities.unary_unary_inline(servicer.GetData),
    ('atlas.v1.Atlas', 'GetLocations'): face_utilities.unary_unary_inline(servicer.GetLocations),
    ('atlas.v1.Atlas', 'GetNavigationData'): face_utilities.unary_unary_inline(servicer.GetNavigationData),
    ('atlas.v1.Atlas', 'ListElevatedLocations'): face_utilities.unary_unary_inline(servicer.ListElevatedLocations),
    ('atlas.v1.Atlas', 'SetDefaultLocation'): face_utilities.unary_unary_inline(servicer.SetDefaultLocation),
  }
  server_options = beta_implementations.server_options(request_deserializers=request_deserializers, response_serializers=response_serializers, thread_pool=pool, thread_pool_size=pool_size, default_timeout=default_timeout, maximum_timeout=maximum_timeout)
  return beta_implementations.server(method_implementations, options=server_options)


def beta_create_Atlas_stub(channel, host=None, metadata_transformer=None, pool=None, pool_size=None):
  """The Beta API is deprecated for 0.15.0 and later.

  It is recommended to use the GA API (classes and functions in this
  file not marked beta) for all further purposes. This function was
  generated only to ease transition from grpcio<0.15.0 to grpcio>=0.15.0"""
  request_serializers = {
    ('atlas.v1.Atlas', 'ContactUs'): ContactUsRequest.SerializeToString,
    ('atlas.v1.Atlas', 'GetData'): GetDataRequest.SerializeToString,
    ('atlas.v1.Atlas', 'GetLocations'): GetLocationsRequest.SerializeToString,
    ('atlas.v1.Atlas', 'GetNavigationData'): GetNavigationDataRequest.SerializeToString,
    ('atlas.v1.Atlas', 'ListElevatedLocations'): ListElevatedLocationsRequest.SerializeToString,
    ('atlas.v1.Atlas', 'SetDefaultLocation'): SetDefaultLocationRequest.SerializeToString,
  }
  response_deserializers = {
    ('atlas.v1.Atlas', 'ContactUs'): google_dot_protobuf_dot_empty__pb2.Empty.FromString,
    ('atlas.v1.Atlas', 'GetData'): GetDataResponse.FromString,
    ('atlas.v1.Atlas', 'GetLocations'): GetLocationsResponse.FromString,
    ('atlas.v1.Atlas', 'GetNavigationData'): GetNavigationDataResponse.FromString,
    ('atlas.v1.Atlas', 'ListElevatedLocations'): ListElevatedLocationsResponse.FromString,
    ('atlas.v1.Atlas', 'SetDefaultLocation'): google_dot_protobuf_dot_empty__pb2.Empty.FromString,
  }
  cardinalities = {
    'ContactUs': cardinality.Cardinality.UNARY_UNARY,
    'GetData': cardinality.Cardinality.UNARY_UNARY,
    'GetLocations': cardinality.Cardinality.UNARY_UNARY,
    'GetNavigationData': cardinality.Cardinality.UNARY_UNARY,
    'ListElevatedLocations': cardinality.Cardinality.UNARY_UNARY,
    'SetDefaultLocation': cardinality.Cardinality.UNARY_UNARY,
  }
  stub_options = beta_implementations.stub_options(host=host, metadata_transformer=metadata_transformer, request_serializers=request_serializers, response_deserializers=response_deserializers, thread_pool=pool, thread_pool_size=pool_size)
  return beta_implementations.dynamic_stub(channel, 'atlas.v1.Atlas', cardinalities, options=stub_options)


class PinsStub(object):
  """The service for managing pinned products and navbar items
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.SetPins = channel.unary_unary(
        '/atlas.v1.Pins/SetPins',
        request_serializer=SetPinsRequest.SerializeToString,
        response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
        )
    self.GetPins = channel.unary_unary(
        '/atlas.v1.Pins/GetPins',
        request_serializer=GetPinsRequest.SerializeToString,
        response_deserializer=GetPinsResponse.FromString,
        )


class PinsServicer(object):
  """The service for managing pinned products and navbar items
  """

  def SetPins(self, request, context):
    """SetPins will set the pins for a user in an business or brand
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetPins(self, request, context):
    """GetPins will return the pins for a business or brand
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_PinsServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'SetPins': grpc.unary_unary_rpc_method_handler(
          servicer.SetPins,
          request_deserializer=SetPinsRequest.FromString,
          response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
      ),
      'GetPins': grpc.unary_unary_rpc_method_handler(
          servicer.GetPins,
          request_deserializer=GetPinsRequest.FromString,
          response_serializer=GetPinsResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'atlas.v1.Pins', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))


class BetaPinsServicer(object):
  """The Beta API is deprecated for 0.15.0 and later.

  It is recommended to use the GA API (classes and functions in this
  file not marked beta) for all further purposes. This class was generated
  only to ease transition from grpcio<0.15.0 to grpcio>=0.15.0."""
  """The service for managing pinned products and navbar items
  """
  def SetPins(self, request, context):
    """SetPins will set the pins for a user in an business or brand
    """
    context.code(beta_interfaces.StatusCode.UNIMPLEMENTED)
  def GetPins(self, request, context):
    """GetPins will return the pins for a business or brand
    """
    context.code(beta_interfaces.StatusCode.UNIMPLEMENTED)


class BetaPinsStub(object):
  """The Beta API is deprecated for 0.15.0 and later.

  It is recommended to use the GA API (classes and functions in this
  file not marked beta) for all further purposes. This class was generated
  only to ease transition from grpcio<0.15.0 to grpcio>=0.15.0."""
  """The service for managing pinned products and navbar items
  """
  def SetPins(self, request, timeout, metadata=None, with_call=False, protocol_options=None):
    """SetPins will set the pins for a user in an business or brand
    """
    raise NotImplementedError()
  SetPins.future = None
  def GetPins(self, request, timeout, metadata=None, with_call=False, protocol_options=None):
    """GetPins will return the pins for a business or brand
    """
    raise NotImplementedError()
  GetPins.future = None


def beta_create_Pins_server(servicer, pool=None, pool_size=None, default_timeout=None, maximum_timeout=None):
  """The Beta API is deprecated for 0.15.0 and later.

  It is recommended to use the GA API (classes and functions in this
  file not marked beta) for all further purposes. This function was
  generated only to ease transition from grpcio<0.15.0 to grpcio>=0.15.0"""
  request_deserializers = {
    ('atlas.v1.Pins', 'GetPins'): GetPinsRequest.FromString,
    ('atlas.v1.Pins', 'SetPins'): SetPinsRequest.FromString,
  }
  response_serializers = {
    ('atlas.v1.Pins', 'GetPins'): GetPinsResponse.SerializeToString,
    ('atlas.v1.Pins', 'SetPins'): google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
  }
  method_implementations = {
    ('atlas.v1.Pins', 'GetPins'): face_utilities.unary_unary_inline(servicer.GetPins),
    ('atlas.v1.Pins', 'SetPins'): face_utilities.unary_unary_inline(servicer.SetPins),
  }
  server_options = beta_implementations.server_options(request_deserializers=request_deserializers, response_serializers=response_serializers, thread_pool=pool, thread_pool_size=pool_size, default_timeout=default_timeout, maximum_timeout=maximum_timeout)
  return beta_implementations.server(method_implementations, options=server_options)


def beta_create_Pins_stub(channel, host=None, metadata_transformer=None, pool=None, pool_size=None):
  """The Beta API is deprecated for 0.15.0 and later.

  It is recommended to use the GA API (classes and functions in this
  file not marked beta) for all further purposes. This function was
  generated only to ease transition from grpcio<0.15.0 to grpcio>=0.15.0"""
  request_serializers = {
    ('atlas.v1.Pins', 'GetPins'): GetPinsRequest.SerializeToString,
    ('atlas.v1.Pins', 'SetPins'): SetPinsRequest.SerializeToString,
  }
  response_deserializers = {
    ('atlas.v1.Pins', 'GetPins'): GetPinsResponse.FromString,
    ('atlas.v1.Pins', 'SetPins'): google_dot_protobuf_dot_empty__pb2.Empty.FromString,
  }
  cardinalities = {
    'GetPins': cardinality.Cardinality.UNARY_UNARY,
    'SetPins': cardinality.Cardinality.UNARY_UNARY,
  }
  stub_options = beta_implementations.stub_options(host=host, metadata_transformer=metadata_transformer, request_serializers=request_serializers, response_deserializers=response_deserializers, thread_pool=pool, thread_pool_size=pool_size)
  return beta_implementations.dynamic_stub(channel, 'atlas.v1.Pins', cardinalities, options=stub_options)


class LanguagesStub(object):
  """The service for managing your preferred language
  """

  def __init__(self, channel):
    """Constructor.

    Args:
      channel: A grpc.Channel.
    """
    self.SetLanguage = channel.unary_unary(
        '/atlas.v1.Languages/SetLanguage',
        request_serializer=SetLanguageRequest.SerializeToString,
        response_deserializer=google_dot_protobuf_dot_empty__pb2.Empty.FromString,
        )
    self.GetLanguage = channel.unary_unary(
        '/atlas.v1.Languages/GetLanguage',
        request_serializer=GetLanguageRequest.SerializeToString,
        response_deserializer=GetLanguageResponse.FromString,
        )


class LanguagesServicer(object):
  """The service for managing your preferred language
  """

  def SetLanguage(self, request, context):
    """SetLanguage will remember your currently selected language
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')

  def GetLanguage(self, request, context):
    """GetLanguage will return your currently selected language
    """
    context.set_code(grpc.StatusCode.UNIMPLEMENTED)
    context.set_details('Method not implemented!')
    raise NotImplementedError('Method not implemented!')


def add_LanguagesServicer_to_server(servicer, server):
  rpc_method_handlers = {
      'SetLanguage': grpc.unary_unary_rpc_method_handler(
          servicer.SetLanguage,
          request_deserializer=SetLanguageRequest.FromString,
          response_serializer=google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
      ),
      'GetLanguage': grpc.unary_unary_rpc_method_handler(
          servicer.GetLanguage,
          request_deserializer=GetLanguageRequest.FromString,
          response_serializer=GetLanguageResponse.SerializeToString,
      ),
  }
  generic_handler = grpc.method_handlers_generic_handler(
      'atlas.v1.Languages', rpc_method_handlers)
  server.add_generic_rpc_handlers((generic_handler,))


class BetaLanguagesServicer(object):
  """The Beta API is deprecated for 0.15.0 and later.

  It is recommended to use the GA API (classes and functions in this
  file not marked beta) for all further purposes. This class was generated
  only to ease transition from grpcio<0.15.0 to grpcio>=0.15.0."""
  """The service for managing your preferred language
  """
  def SetLanguage(self, request, context):
    """SetLanguage will remember your currently selected language
    """
    context.code(beta_interfaces.StatusCode.UNIMPLEMENTED)
  def GetLanguage(self, request, context):
    """GetLanguage will return your currently selected language
    """
    context.code(beta_interfaces.StatusCode.UNIMPLEMENTED)


class BetaLanguagesStub(object):
  """The Beta API is deprecated for 0.15.0 and later.

  It is recommended to use the GA API (classes and functions in this
  file not marked beta) for all further purposes. This class was generated
  only to ease transition from grpcio<0.15.0 to grpcio>=0.15.0."""
  """The service for managing your preferred language
  """
  def SetLanguage(self, request, timeout, metadata=None, with_call=False, protocol_options=None):
    """SetLanguage will remember your currently selected language
    """
    raise NotImplementedError()
  SetLanguage.future = None
  def GetLanguage(self, request, timeout, metadata=None, with_call=False, protocol_options=None):
    """GetLanguage will return your currently selected language
    """
    raise NotImplementedError()
  GetLanguage.future = None


def beta_create_Languages_server(servicer, pool=None, pool_size=None, default_timeout=None, maximum_timeout=None):
  """The Beta API is deprecated for 0.15.0 and later.

  It is recommended to use the GA API (classes and functions in this
  file not marked beta) for all further purposes. This function was
  generated only to ease transition from grpcio<0.15.0 to grpcio>=0.15.0"""
  request_deserializers = {
    ('atlas.v1.Languages', 'GetLanguage'): GetLanguageRequest.FromString,
    ('atlas.v1.Languages', 'SetLanguage'): SetLanguageRequest.FromString,
  }
  response_serializers = {
    ('atlas.v1.Languages', 'GetLanguage'): GetLanguageResponse.SerializeToString,
    ('atlas.v1.Languages', 'SetLanguage'): google_dot_protobuf_dot_empty__pb2.Empty.SerializeToString,
  }
  method_implementations = {
    ('atlas.v1.Languages', 'GetLanguage'): face_utilities.unary_unary_inline(servicer.GetLanguage),
    ('atlas.v1.Languages', 'SetLanguage'): face_utilities.unary_unary_inline(servicer.SetLanguage),
  }
  server_options = beta_implementations.server_options(request_deserializers=request_deserializers, response_serializers=response_serializers, thread_pool=pool, thread_pool_size=pool_size, default_timeout=default_timeout, maximum_timeout=maximum_timeout)
  return beta_implementations.server(method_implementations, options=server_options)


def beta_create_Languages_stub(channel, host=None, metadata_transformer=None, pool=None, pool_size=None):
  """The Beta API is deprecated for 0.15.0 and later.

  It is recommended to use the GA API (classes and functions in this
  file not marked beta) for all further purposes. This function was
  generated only to ease transition from grpcio<0.15.0 to grpcio>=0.15.0"""
  request_serializers = {
    ('atlas.v1.Languages', 'GetLanguage'): GetLanguageRequest.SerializeToString,
    ('atlas.v1.Languages', 'SetLanguage'): SetLanguageRequest.SerializeToString,
  }
  response_deserializers = {
    ('atlas.v1.Languages', 'GetLanguage'): GetLanguageResponse.FromString,
    ('atlas.v1.Languages', 'SetLanguage'): google_dot_protobuf_dot_empty__pb2.Empty.FromString,
  }
  cardinalities = {
    'GetLanguage': cardinality.Cardinality.UNARY_UNARY,
    'SetLanguage': cardinality.Cardinality.UNARY_UNARY,
  }
  stub_options = beta_implementations.stub_options(host=host, metadata_transformer=metadata_transformer, request_serializers=request_serializers, response_deserializers=response_deserializers, thread_pool=pool, thread_pool_size=pool_size)
  return beta_implementations.dynamic_stub(channel, 'atlas.v1.Languages', cardinalities, options=stub_options)
# @@protoc_insertion_point(module_scope)
