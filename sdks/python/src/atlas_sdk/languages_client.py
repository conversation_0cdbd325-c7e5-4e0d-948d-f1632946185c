""" Client for Languages rpcs """
from _internal.client import _LanguagesClient
from ._parent_client import ParentClient

class LanguagesClient(ParentClient):
    """ Client for interacting with Atlas Language APIs """

    def get_language_for_user(self, email):
        """ Get currently selected language for a user by email """
        resp = self.get_client(_LanguagesClient).GetLanguageForUser(email)
        return resp.language

    def set_language_for_user(self, email, language):
        """ Remember currently selected language for a user by email """
        return self.get_client(_LanguagesClient).SetLanguageForUser(email, language)
