"""
# Code generated by sdkgen
# DO NOT EDIT!.

Message layer for data objects.
""" 
import vschema
import enum
import datetime
from .._generated import annotations_pb2



def extract_datetime(proto_date_attr):
    """ Extract a datetime object from a proto property """
    if not proto_date_attr:
        return None
    t = proto_date_attr
    if not isinstance(t, datetime.datetime):
        t = t.ToDatetime()
    if t == datetime.datetime.utcfromtimestamp(0) or t == datetime.datetime.min:
        return None
    return t


def extract_repeated(proto_repeated_attr):
    """ Extract a list from repeated attributes """
    return [a for a in proto_repeated_attr]


def extract_falsy_as_none(proto_descriptor):
    """
    Return any falsy value as None.
    We do this because python protobuf's metaclasses and magic methods obscure our ability to interact with these
    objects in a sane way.
    """
    if not proto_descriptor and proto_descriptor is not None:
        return None
    return proto_descriptor


class Access(vschema.VObject):
    """ Access """
    OWNER = "annotations_pb2"
    CLASS_VERSION = "1.0.0"

    scope = vschema.StringProperty(repeated=True)
    public = vschema.BooleanProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = Access(
            scope=extract_repeated(message.scope), 
            public=message.public
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = annotations_pb2.Access(
            scope=self.scope if self.scope is not None else None, 
            public=self.public if self.public is not None else None
        )
        
        
        return proto

    