"""
# Code generated by sdkgen
# DO NOT EDIT!.

"""
from vax.environment import Environment
from .transport import _get_atlas_connection, _get_pins_connection, _get_languages_connection


import api

from .._generated import api_pb2, api_pb2, api_pb2




class _AtlasClient(object):
    """
    Atlas Client
    This client is thread-safe and should be reused.  Instantiating this object opens connections and keeps them alive
    to increase RPC performance.
    """

    def __init__(self, host, scope, service_account, environment=Environment.LOCAL, cache=None, enable_caller_id=False,
                 client_key=''):
        """ constructor """
        self.__connection = _get_atlas_connection(
            host, scope, service_account, environment,
            cache=cache, enable_caller_id=enable_caller_id,
            client_key=client_key
        )
    
    def GetData(self, partner_id=None, market_id=None, account_group_id=None, sign_out_next_url=None, group_path=None):
        req = api_pb2.GetDataRequest(partner_id=partner_id, market_id=market_id, account_group_id=account_group_id, sign_out_next_url=sign_out_next_url, group_path=group_path)
        resp = self.__connection.GetData(req)
        return api.GetDataResponse.from_proto(resp)

    def GetNavigationData(self, account_group_id=None, group_path=None, partner_id=None, market_id=None, platform_mode=None):
        req = api_pb2.GetNavigationDataRequest(account_group_id=account_group_id, group_path=group_path, partner_id=partner_id, market_id=market_id, platform_mode=platform_mode)
        resp = self.__connection.GetNavigationData(req)
        return api.GetNavigationDataResponse.from_proto(resp)

    def GetSalesInfo(self, account_group_id=None, group_path=None):
        req = api_pb2.GetSalesInfoRequest(account_group_id=account_group_id, group_path=group_path)
        resp = self.__connection.GetSalesInfo(req)
        return api.GetSalesInfoResponse.from_proto(resp)

    def GetLocations(self, account_groups=None, groups=None):
        req = api_pb2.GetLocationsRequest(account_groups=account_groups, groups=groups)
        resp = self.__connection.GetLocations(req)
        return api.GetLocationsResponse.from_proto(resp)

    def ListElevatedLocations(self, partner_id=None, cursor=None, page_size=None, search=None, account_groups=None, brands=None):
        req = api_pb2.ListElevatedLocationsRequest(partner_id=partner_id, cursor=cursor, page_size=page_size, search=search, account_groups=account_groups, brands=brands)
        resp = self.__connection.ListElevatedLocations(req)
        return api.ListElevatedLocationsResponse.from_proto(resp)

    def ListLocations(self, partner_id=None, cursor=None, page_size=None, search=None, include_account_groups=None, include_brands=None):
        req = api_pb2.ListLocationsRequest(partner_id=partner_id, cursor=cursor, page_size=page_size, search=search, include_account_groups=include_account_groups, include_brands=include_brands)
        resp = self.__connection.ListLocations(req)
        return api.ListLocationsResponse.from_proto(resp)

    def ContactUs(self, account_group_id=None, message=None):
        req = api_pb2.ContactUsRequest(account_group_id=account_group_id, message=message)
        self.__connection.ContactUs(req)
        return True

    def SetDefaultLocation(self, partner_id=None, account_group_id=None, group_id=None):
        req = api_pb2.SetDefaultLocationRequest(partner_id=partner_id, account_group_id=account_group_id, group_id=group_id)
        self.__connection.SetDefaultLocation(req)
        return True

    def GetDefaultLocation(self, partner_id=None):
        req = api_pb2.GetDefaultLocationRequest(partner_id=partner_id)
        resp = self.__connection.GetDefaultLocation(req)
        return api.GetDefaultLocationResponse.from_proto(resp)

class _PinsClient(object):
    """
    Pins Client
    This client is thread-safe and should be reused.  Instantiating this object opens connections and keeps them alive
    to increase RPC performance.
    """

    def __init__(self, host, scope, service_account, environment=Environment.LOCAL, cache=None, enable_caller_id=False,
                 client_key=''):
        """ constructor """
        self.__connection = _get_pins_connection(
            host, scope, service_account, environment,
            cache=cache, enable_caller_id=enable_caller_id,
            client_key=client_key
        )
    
    def SetPins(self, identifier=None, items=None):
        req = api_pb2.SetPinsRequest(identifier=identifier, items=items)
        self.__connection.SetPins(req)
        return True

    def GetPins(self, identifier=None):
        req = api_pb2.GetPinsRequest(identifier=identifier)
        resp = self.__connection.GetPins(req)
        return api.GetPinsResponse.from_proto(resp)

class _LanguagesClient(object):
    """
    Languages Client
    This client is thread-safe and should be reused.  Instantiating this object opens connections and keeps them alive
    to increase RPC performance.
    """

    def __init__(self, host, scope, service_account, environment=Environment.LOCAL, cache=None, enable_caller_id=False,
                 client_key=''):
        """ constructor """
        self.__connection = _get_languages_connection(
            host, scope, service_account, environment,
            cache=cache, enable_caller_id=enable_caller_id,
            client_key=client_key
        )
    
    def SetLanguage(self, language=None):
        req = api_pb2.SetLanguageRequest(language=language)
        self.__connection.SetLanguage(req)
        return True

    def GetLanguage(self):
        req = api_pb2.GetLanguageRequest()
        resp = self.__connection.GetLanguage(req)
        return api.GetLanguageResponse.from_proto(resp)

