"""
# Code generated by sdkgen
# DO NOT EDIT!.

Transport layer for communicating with an API by either http/json or gRPC depending on environment.
"""
from vax.transport.transport_client import TransportOverHttp, TransportOverGRPC
from vax.errors import TransportException
from vax.transport.instance_auth_cache import InstanceAuthCache

from .._generated import api_pb2

from .._generated import api_pb2

from .._generated import api_pb2



def _get_atlas_connection(host, scope, service_account, environment, cache=None, 
                                         enable_caller_id=False, client_key=''):
    """ Gets a connection over http """
    cache = cache or InstanceAuthCache()
    return _AtlasTransportOverHttp(host, scope, service_account, environment, cache, enable_caller_id=enable_caller_id, client_key=client_key)


def _get_pins_connection(host, scope, service_account, environment, cache=None, 
                                         enable_caller_id=False, client_key=''):
    """ Gets a connection over http """
    cache = cache or InstanceAuthCache()
    return _PinsTransportOverHttp(host, scope, service_account, environment, cache, enable_caller_id=enable_caller_id, client_key=client_key)


def _get_languages_connection(host, scope, service_account, environment, cache=None, 
                                         enable_caller_id=False, client_key=''):
    """ Gets a connection over http """
    cache = cache or InstanceAuthCache()
    return _LanguagesTransportOverHttp(host, scope, service_account, environment, cache, enable_caller_id=enable_caller_id, client_key=client_key)


class _AtlasTransportOverHttp(TransportOverHttp):
    """ Atlas over HTTP """
    
    def GetData(self, request):
        response = api_pb2.GetDataResponse()
        self._do_request("/atlas.v1.Atlas/GetData", request, response)
        return response

    def GetNavigationData(self, request):
        response = api_pb2.GetNavigationDataResponse()
        self._do_request("/atlas.v1.Atlas/GetNavigationData", request, response)
        return response

    def GetSalesInfo(self, request):
        response = api_pb2.GetSalesInfoResponse()
        self._do_request("/atlas.v1.Atlas/GetSalesInfo", request, response)
        return response

    def GetLocations(self, request):
        response = api_pb2.GetLocationsResponse()
        self._do_request("/atlas.v1.Atlas/GetLocations", request, response)
        return response

    def ListElevatedLocations(self, request):
        response = api_pb2.ListElevatedLocationsResponse()
        self._do_request("/atlas.v1.Atlas/ListElevatedLocations", request, response)
        return response

    def ListLocations(self, request):
        response = api_pb2.ListLocationsResponse()
        self._do_request("/atlas.v1.Atlas/ListLocations", request, response)
        return response

    def ContactUs(self, request):
        response = api_pb2.google_dot_protobuf_dot_empty__pb2.Empty()
        self._do_request("/atlas.v1.Atlas/ContactUs", request, response)
        return response

    def SetDefaultLocation(self, request):
        response = api_pb2.google_dot_protobuf_dot_empty__pb2.Empty()
        self._do_request("/atlas.v1.Atlas/SetDefaultLocation", request, response)
        return response

    def GetDefaultLocation(self, request):
        response = api_pb2.GetDefaultLocationResponse()
        self._do_request("/atlas.v1.Atlas/GetDefaultLocation", request, response)
        return response


class _PinsTransportOverHttp(TransportOverHttp):
    """ Pins over HTTP """
    
    def SetPins(self, request):
        response = api_pb2.google_dot_protobuf_dot_empty__pb2.Empty()
        self._do_request("/atlas.v1.Pins/SetPins", request, response)
        return response

    def GetPins(self, request):
        response = api_pb2.GetPinsResponse()
        self._do_request("/atlas.v1.Pins/GetPins", request, response)
        return response


class _LanguagesTransportOverHttp(TransportOverHttp):
    """ Languages over HTTP """
    
    def SetLanguage(self, request):
        response = api_pb2.google_dot_protobuf_dot_empty__pb2.Empty()
        self._do_request("/atlas.v1.Languages/SetLanguage", request, response)
        return response

    def GetLanguage(self, request):
        response = api_pb2.GetLanguageResponse()
        self._do_request("/atlas.v1.Languages/GetLanguage", request, response)
        return response

