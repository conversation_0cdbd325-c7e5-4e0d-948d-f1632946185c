"""
# Code generated by sdkgen
# DO NOT EDIT!.

Message layer for data objects.
""" 
import vschema
import enum
import datetime
from .._generated import api_pb2

from annotations import *


def extract_datetime(proto_date_attr):
    """ Extract a datetime object from a proto property """
    if not proto_date_attr:
        return None
    t = proto_date_attr
    if not isinstance(t, datetime.datetime):
        t = t.ToDatetime()
    if t == datetime.datetime.utcfromtimestamp(0) or t == datetime.datetime.min:
        return None
    return t


def extract_repeated(proto_repeated_attr):
    """ Extract a list from repeated attributes """
    return [a for a in proto_repeated_attr]


def extract_falsy_as_none(proto_descriptor):
    """
    Return any falsy value as None.
    We do this because python protobuf's metaclasses and magic methods obscure our ability to interact with these
    objects in a sane way.
    """
    if not proto_descriptor and proto_descriptor is not None:
        return None
    return proto_descriptor


class UITheme(enum.Enum):
    """ UITheme """
    UI_THEME_DARK = 0
    UI_THEME_LIGHT = 1
    UI_THEME_USER_PREFERENCE = 2


class UserViewType(enum.Enum):
    """ UserViewType """
    USER_VIEW_TYPE_SMB = 0
    USER_VIEW_TYPE_ADMIN = 1


class PlatformMode(enum.Enum):
    """ PlatformMode """
    WEB = 0
    MOBILE = 1


class UserNavigationItem(vschema.VObject):
    """ UserNavigationItem """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    text = vschema.StringProperty()
    url = vschema.StringProperty()
    route_id = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = UserNavigationItem(
            text=extract_falsy_as_none(message.text), 
            url=extract_falsy_as_none(message.url), 
            route_id=extract_falsy_as_none(message.route_id)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.UserNavigationItem(
            text=self.text if self.text is not None else None, 
            url=self.url if self.url is not None else None, 
            route_id=self.route_id if self.route_id is not None else None
        )
        
        
        return proto

    
class CenterNavigationItem(vschema.VObject):
    """ CenterNavigationItem """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    name = vschema.StringProperty()
    entry_url = vschema.StringProperty()
    center_id = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = CenterNavigationItem(
            name=extract_falsy_as_none(message.name), 
            entry_url=extract_falsy_as_none(message.entry_url), 
            center_id=extract_falsy_as_none(message.center_id)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.CenterNavigationItem(
            name=self.name if self.name is not None else None, 
            entry_url=self.entry_url if self.entry_url is not None else None, 
            center_id=self.center_id if self.center_id is not None else None
        )
        
        
        return proto

    
class GetDataRequest(vschema.VObject):
    """ GetDataRequest """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    partner_id = vschema.StringProperty()
    market_id = vschema.StringProperty()
    account_group_id = vschema.StringProperty()
    sign_out_next_url = vschema.StringProperty()
    group_path = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = GetDataRequest(
            partner_id=extract_falsy_as_none(message.partner_id), 
            market_id=extract_falsy_as_none(message.market_id), 
            account_group_id=extract_falsy_as_none(message.account_group_id), 
            sign_out_next_url=extract_falsy_as_none(message.sign_out_next_url), 
            group_path=extract_falsy_as_none(message.group_path)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetDataRequest(
            partner_id=self.partner_id if self.partner_id is not None else None, 
            market_id=self.market_id if self.market_id is not None else None, 
            account_group_id=self.account_group_id if self.account_group_id is not None else None, 
            sign_out_next_url=self.sign_out_next_url if self.sign_out_next_url is not None else None, 
            group_path=self.group_path if self.group_path is not None else None
        )
        
        
        return proto

    
class LocationData(vschema.VObject):
    """ LocationData """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    business_name = vschema.StringProperty()
    address = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = LocationData(
            business_name=extract_falsy_as_none(message.business_name), 
            address=extract_falsy_as_none(message.address)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.LocationData(
            business_name=self.business_name if self.business_name is not None else None, 
            address=self.address if self.address is not None else None
        )
        
        
        return proto

    
class UserSwitcherData(vschema.VObject):
    """ UserSwitcherData """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    user_id = vschema.StringProperty()
    partner_id = vschema.StringProperty()
    partner_name = vschema.StringProperty()
    entry_url = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = UserSwitcherData(
            user_id=extract_falsy_as_none(message.user_id), 
            partner_id=extract_falsy_as_none(message.partner_id), 
            partner_name=extract_falsy_as_none(message.partner_name), 
            entry_url=extract_falsy_as_none(message.entry_url)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.UserSwitcherData(
            user_id=self.user_id if self.user_id is not None else None, 
            partner_id=self.partner_id if self.partner_id is not None else None, 
            partner_name=self.partner_name if self.partner_name is not None else None, 
            entry_url=self.entry_url if self.entry_url is not None else None
        )
        
        
        return proto

    
class GetDataResponse(vschema.VObject):
    """ GetDataResponse """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    user = vschema.StructuredProperty(UserNavigationItem, repeated=True)
    centers = vschema.StructuredProperty(CenterNavigationItem, repeated=True)
    username = vschema.StringProperty()
    email = vschema.StringProperty()
    sign_out_url = vschema.StringProperty()
    theme = vschema.IntegerProperty(choices=UITheme)
    language = vschema.StringProperty()
    theming = vschema.StructuredProperty(Theming)
    notifications_enabled = vschema.BooleanProperty()
    user_id = vschema.StringProperty()
    location_data = vschema.StructuredProperty(LocationData)
    impersonatee_username = vschema.StringProperty()
    email_verified = vschema.BooleanProperty()
    user_switcher_data = vschema.StructuredProperty(UserSwitcherData, repeated=True)
    partner_name = vschema.StringProperty()
    business_app_ui_theme = vschema.IntegerProperty(choices=UITheme)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = GetDataResponse(
            user=[UserNavigationItem.from_proto(x) for x in message.user], 
            centers=[CenterNavigationItem.from_proto(x) for x in message.centers], 
            username=extract_falsy_as_none(message.username), 
            email=extract_falsy_as_none(message.email), 
            sign_out_url=extract_falsy_as_none(message.sign_out_url), 
            theme=message.theme, 
            language=extract_falsy_as_none(message.language), 
            theming=Theming.from_proto(message.theming) if message.HasField('theming') else None, 
            notifications_enabled=message.notifications_enabled, 
            user_id=extract_falsy_as_none(message.user_id), 
            location_data=LocationData.from_proto(message.location_data) if message.HasField('location_data') else None, 
            impersonatee_username=extract_falsy_as_none(message.impersonatee_username), 
            email_verified=message.email_verified, 
            user_switcher_data=[UserSwitcherData.from_proto(x) for x in message.user_switcher_data], 
            partner_name=extract_falsy_as_none(message.partner_name), 
            business_app_ui_theme=message.business_app_ui_theme
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetDataResponse(
            user=[x.to_proto() for x in self.user] if self.user is not None else None, 
            centers=[x.to_proto() for x in self.centers] if self.centers is not None else None, 
            username=self.username if self.username is not None else None, 
            email=self.email if self.email is not None else None, 
            sign_out_url=self.sign_out_url if self.sign_out_url is not None else None, 
            theme=self.theme.value if self.theme is not None else None, 
            language=self.language if self.language is not None else None, 
            theming=self.theming.to_proto() if self.theming is not None else None, 
            notifications_enabled=self.notifications_enabled if self.notifications_enabled is not None else None, 
            user_id=self.user_id if self.user_id is not None else None, 
            location_data=self.location_data.to_proto() if self.location_data is not None else None, 
            impersonatee_username=self.impersonatee_username if self.impersonatee_username is not None else None, 
            email_verified=self.email_verified if self.email_verified is not None else None, 
            user_switcher_data=[x.to_proto() for x in self.user_switcher_data] if self.user_switcher_data is not None else None, 
            partner_name=self.partner_name if self.partner_name is not None else None, 
            business_app_ui_theme=self.business_app_ui_theme.value if self.business_app_ui_theme is not None else None
        )
        
        
        return proto

    
class GetNavigationDataRequest(vschema.VObject):
    """ GetNavigationDataRequest """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    account_group_id = vschema.StringProperty()
    group_path = vschema.StringProperty()
    partner_id = vschema.StringProperty()
    market_id = vschema.StringProperty()
    platform_mode = vschema.IntegerProperty(choices=PlatformMode)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = GetNavigationDataRequest(
            account_group_id=extract_falsy_as_none(message.account_group_id), 
            group_path=extract_falsy_as_none(message.group_path), 
            partner_id=extract_falsy_as_none(message.partner_id), 
            market_id=extract_falsy_as_none(message.market_id), 
            platform_mode=message.platform_mode
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetNavigationDataRequest(
            account_group_id=self.account_group_id if self.account_group_id is not None else None, 
            group_path=self.group_path if self.group_path is not None else None, 
            partner_id=self.partner_id if self.partner_id is not None else None, 
            market_id=self.market_id if self.market_id is not None else None, 
            platform_mode=self.platform_mode.value if self.platform_mode is not None else None
        )
        
        
        return proto

    
class GetSalesInfoRequest(vschema.VObject):
    """ GetSalesInfoRequest """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    account_group_id = vschema.StringProperty()
    group_path = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = GetSalesInfoRequest(
            account_group_id=extract_falsy_as_none(message.account_group_id), 
            group_path=extract_falsy_as_none(message.group_path)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetSalesInfoRequest(
            account_group_id=self.account_group_id if self.account_group_id is not None else None, 
            group_path=self.group_path if self.group_path is not None else None
        )
        
        
        return proto

    
class GetSalesInfoResponse(vschema.VObject):
    """ GetSalesInfoResponse """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    sales_info = vschema.StructuredProperty(SalesInfo)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = GetSalesInfoResponse(
            sales_info=SalesInfo.from_proto(message.sales_info) if message.HasField('sales_info') else None
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetSalesInfoResponse(
            sales_info=self.sales_info.to_proto() if self.sales_info is not None else None
        )
        
        
        return proto

    
class SideNavigationSection(vschema.VObject):
    """ SideNavigationSection """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    translation_id = vschema.StringProperty()
    side_navigation_items = vschema.StructuredProperty(SideNavigationItem, repeated=True)
    label = vschema.StringProperty()
    chip_content = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = SideNavigationSection(
            translation_id=extract_falsy_as_none(message.translation_id), 
            side_navigation_items=[SideNavigationItem.from_proto(x) for x in message.side_navigation_items], 
            label=extract_falsy_as_none(message.label), 
            chip_content=extract_falsy_as_none(message.chip_content)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.SideNavigationSection(
            translation_id=self.translation_id if self.translation_id is not None else None, 
            side_navigation_items=[x.to_proto() for x in self.side_navigation_items] if self.side_navigation_items is not None else None, 
            label=self.label if self.label is not None else None, 
            chip_content=self.chip_content if self.chip_content is not None else None
        )
        
        
        return proto

    
class SideNavigationContainer(vschema.VObject):
    """ SideNavigationContainer """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    translation_id = vschema.StringProperty()
    side_navigation_items = vschema.StructuredProperty(SideNavigationItem, repeated=True)
    icon = vschema.StringProperty()
    logo_url = vschema.StringProperty()
    label = vschema.StringProperty()
    show_icon = vschema.BooleanProperty()
    chip_content = vschema.StringProperty()
    url = vschema.StringProperty()
    pinnable = vschema.BooleanProperty()
    navigation_id = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = SideNavigationContainer(
            translation_id=extract_falsy_as_none(message.translation_id), 
            side_navigation_items=[SideNavigationItem.from_proto(x) for x in message.side_navigation_items], 
            icon=extract_falsy_as_none(message.icon), 
            logo_url=extract_falsy_as_none(message.logo_url), 
            label=extract_falsy_as_none(message.label), 
            show_icon=message.show_icon, 
            chip_content=extract_falsy_as_none(message.chip_content), 
            url=extract_falsy_as_none(message.url), 
            pinnable=message.pinnable, 
            navigation_id=extract_falsy_as_none(message.navigation_id)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.SideNavigationContainer(
            translation_id=self.translation_id if self.translation_id is not None else None, 
            side_navigation_items=[x.to_proto() for x in self.side_navigation_items] if self.side_navigation_items is not None else None, 
            icon=self.icon if self.icon is not None else None, 
            logo_url=self.logo_url if self.logo_url is not None else None, 
            label=self.label if self.label is not None else None, 
            show_icon=self.show_icon if self.show_icon is not None else None, 
            chip_content=self.chip_content if self.chip_content is not None else None, 
            url=self.url if self.url is not None else None, 
            pinnable=self.pinnable if self.pinnable is not None else None, 
            navigation_id=self.navigation_id if self.navigation_id is not None else None
        )
        
        
        return proto

    
class SideNavigationLink(vschema.VObject):
    """ SideNavigationLink """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    navigation_id = vschema.StringProperty()
    url = vschema.StringProperty()
    path = vschema.StringProperty()
    service_provider_id = vschema.StringProperty()
    logo_url = vschema.StringProperty()
    icon = vschema.StringProperty()
    translation_id = vschema.StringProperty()
    external = vschema.BooleanProperty()
    label = vschema.StringProperty()
    show_icon = vschema.BooleanProperty()
    pinnable = vschema.BooleanProperty()
    chip_content = vschema.StringProperty()
    is_trial = vschema.BooleanProperty()
    user_required = vschema.BooleanProperty()
    open_in_new_tab = vschema.BooleanProperty()
    sub_links = vschema.StructuredProperty(SideNavigationLink, repeated=True)
    launch_url = vschema.StringProperty()
    description_translation_id = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = SideNavigationLink(
            navigation_id=extract_falsy_as_none(message.navigation_id), 
            url=extract_falsy_as_none(message.url), 
            path=extract_falsy_as_none(message.path), 
            service_provider_id=extract_falsy_as_none(message.service_provider_id), 
            logo_url=extract_falsy_as_none(message.logo_url), 
            icon=extract_falsy_as_none(message.icon), 
            translation_id=extract_falsy_as_none(message.translation_id), 
            external=message.external, 
            label=extract_falsy_as_none(message.label), 
            show_icon=message.show_icon, 
            pinnable=message.pinnable, 
            chip_content=extract_falsy_as_none(message.chip_content), 
            is_trial=message.is_trial, 
            user_required=message.user_required, 
            open_in_new_tab=message.open_in_new_tab, 
            sub_links=[SideNavigationLink.from_proto(x) for x in message.sub_links], 
            launch_url=extract_falsy_as_none(message.launch_url), 
            description_translation_id=extract_falsy_as_none(message.description_translation_id)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.SideNavigationLink(
            navigation_id=self.navigation_id if self.navigation_id is not None else None, 
            url=self.url if self.url is not None else None, 
            path=self.path if self.path is not None else None, 
            service_provider_id=self.service_provider_id if self.service_provider_id is not None else None, 
            logo_url=self.logo_url if self.logo_url is not None else None, 
            icon=self.icon if self.icon is not None else None, 
            translation_id=self.translation_id if self.translation_id is not None else None, 
            external=self.external if self.external is not None else None, 
            label=self.label if self.label is not None else None, 
            show_icon=self.show_icon if self.show_icon is not None else None, 
            pinnable=self.pinnable if self.pinnable is not None else None, 
            chip_content=self.chip_content if self.chip_content is not None else None, 
            is_trial=self.is_trial if self.is_trial is not None else None, 
            user_required=self.user_required if self.user_required is not None else None, 
            open_in_new_tab=self.open_in_new_tab if self.open_in_new_tab is not None else None, 
            sub_links=[x.to_proto() for x in self.sub_links] if self.sub_links is not None else None, 
            launch_url=self.launch_url if self.launch_url is not None else None, 
            description_translation_id=self.description_translation_id if self.description_translation_id is not None else None
        )
        
        
        return proto

    
class DropdownItem(vschema.VObject):
    """ DropdownItem """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    url = vschema.StringProperty()
    path = vschema.StringProperty()
    translation_id = vschema.StringProperty()
    label = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = DropdownItem(
            url=extract_falsy_as_none(message.url), 
            path=extract_falsy_as_none(message.path), 
            translation_id=extract_falsy_as_none(message.translation_id), 
            label=extract_falsy_as_none(message.label)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.DropdownItem(
            url=self.url if self.url is not None else None, 
            path=self.path if self.path is not None else None, 
            translation_id=self.translation_id if self.translation_id is not None else None, 
            label=self.label if self.label is not None else None
        )
        
        
        return proto

    
class SideNavigationItem(vschema.VObject):
    """ SideNavigationItem """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    _item = None
    side_navigation_section = vschema.StructuredProperty(SideNavigationSection)
    side_navigation_container = vschema.StructuredProperty(SideNavigationContainer)
    side_navigation_link = vschema.StructuredProperty(SideNavigationLink)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = SideNavigationItem(
            side_navigation_section=SideNavigationSection.from_proto(message.side_navigation_section) if message.HasField('side_navigation_section') else None, 
            side_navigation_container=SideNavigationContainer.from_proto(message.side_navigation_container) if message.HasField('side_navigation_container') else None, 
            side_navigation_link=SideNavigationLink.from_proto(message.side_navigation_link) if message.HasField('side_navigation_link') else None
        )
        obj._item = message.WhichOneof('item')
        
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.SideNavigationItem(
            side_navigation_section=self.side_navigation_section.to_proto() if self.side_navigation_section is not None else None, 
            side_navigation_container=self.side_navigation_container.to_proto() if self.side_navigation_container is not None else None, 
            side_navigation_link=self.side_navigation_link.to_proto() if self.side_navigation_link is not None else None
        )
        
        
        return proto

    def which_one_of_item(self):
        """ returns which oneof item this is """
        return self._item

    
class SalesInfo(vschema.VObject):
    """ SalesInfo """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    market_name = vschema.StringProperty()
    sales_contact = vschema.StructuredProperty(SalesContact)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = SalesInfo(
            market_name=extract_falsy_as_none(message.market_name), 
            sales_contact=SalesContact.from_proto(message.sales_contact) if message.HasField('sales_contact') else None
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.SalesInfo(
            market_name=self.market_name if self.market_name is not None else None, 
            sales_contact=self.sales_contact.to_proto() if self.sales_contact is not None else None
        )
        
        
        return proto

    
class SalesContact(vschema.VObject):
    """ SalesContact """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    sales_person_id = vschema.StringProperty()
    email = vschema.StringProperty()
    first_name = vschema.StringProperty()
    last_name = vschema.StringProperty()
    phone_number = vschema.StringProperty()
    photo_url_secure = vschema.StringProperty()
    job_title = vschema.StringProperty()
    country = vschema.StringProperty()
    meeting_booking_url = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = SalesContact(
            sales_person_id=extract_falsy_as_none(message.sales_person_id), 
            email=extract_falsy_as_none(message.email), 
            first_name=extract_falsy_as_none(message.first_name), 
            last_name=extract_falsy_as_none(message.last_name), 
            phone_number=extract_falsy_as_none(message.phone_number), 
            photo_url_secure=extract_falsy_as_none(message.photo_url_secure), 
            job_title=extract_falsy_as_none(message.job_title), 
            country=extract_falsy_as_none(message.country), 
            meeting_booking_url=extract_falsy_as_none(message.meeting_booking_url)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.SalesContact(
            sales_person_id=self.sales_person_id if self.sales_person_id is not None else None, 
            email=self.email if self.email is not None else None, 
            first_name=self.first_name if self.first_name is not None else None, 
            last_name=self.last_name if self.last_name is not None else None, 
            phone_number=self.phone_number if self.phone_number is not None else None, 
            photo_url_secure=self.photo_url_secure if self.photo_url_secure is not None else None, 
            job_title=self.job_title if self.job_title is not None else None, 
            country=self.country if self.country is not None else None, 
            meeting_booking_url=self.meeting_booking_url if self.meeting_booking_url is not None else None
        )
        
        
        return proto

    
class Brand(vschema.VObject):
    """ Brand """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    name = vschema.StringProperty()
    path_nodes = vschema.StringProperty(repeated=True)
    has_access = vschema.BooleanProperty()
    url = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = Brand(
            name=extract_falsy_as_none(message.name), 
            path_nodes=extract_repeated(message.path_nodes), 
            has_access=message.has_access, 
            url=extract_falsy_as_none(message.url)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.Brand(
            name=self.name if self.name is not None else None, 
            path_nodes=self.path_nodes if self.path_nodes is not None else None, 
            has_access=self.has_access if self.has_access is not None else None, 
            url=self.url if self.url is not None else None
        )
        
        
        return proto

    
class PinnedItem(vschema.VObject):
    """ PinnedItem """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    navigation_id = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = PinnedItem(
            navigation_id=extract_falsy_as_none(message.navigation_id)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.PinnedItem(
            navigation_id=self.navigation_id if self.navigation_id is not None else None
        )
        
        
        return proto

    
class Branding(vschema.VObject):
    """ Branding """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    theme = vschema.IntegerProperty(choices=UITheme)
    logo_url = vschema.StringProperty()
    partner_name = vschema.StringProperty()
    center_name = vschema.StringProperty()
    theming = vschema.StructuredProperty(Theming)
    cobranding_logo_url = vschema.StringProperty()
    market_name = vschema.StringProperty()
    dark_mode_logo_url = vschema.StringProperty()
    business_app_ui_theme = vschema.IntegerProperty(choices=UITheme)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = Branding(
            theme=message.theme, 
            logo_url=extract_falsy_as_none(message.logo_url), 
            partner_name=extract_falsy_as_none(message.partner_name), 
            center_name=extract_falsy_as_none(message.center_name), 
            theming=Theming.from_proto(message.theming) if message.HasField('theming') else None, 
            cobranding_logo_url=extract_falsy_as_none(message.cobranding_logo_url), 
            market_name=extract_falsy_as_none(message.market_name), 
            dark_mode_logo_url=extract_falsy_as_none(message.dark_mode_logo_url), 
            business_app_ui_theme=message.business_app_ui_theme
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.Branding(
            theme=self.theme.value if self.theme is not None else None, 
            logo_url=self.logo_url if self.logo_url is not None else None, 
            partner_name=self.partner_name if self.partner_name is not None else None, 
            center_name=self.center_name if self.center_name is not None else None, 
            theming=self.theming.to_proto() if self.theming is not None else None, 
            cobranding_logo_url=self.cobranding_logo_url if self.cobranding_logo_url is not None else None, 
            market_name=self.market_name if self.market_name is not None else None, 
            dark_mode_logo_url=self.dark_mode_logo_url if self.dark_mode_logo_url is not None else None, 
            business_app_ui_theme=self.business_app_ui_theme.value if self.business_app_ui_theme is not None else None
        )
        
        
        return proto

    
class AccountGroup(vschema.VObject):
    """ AccountGroup """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    account_group_id = vschema.StringProperty()
    name = vschema.StringProperty()
    address = vschema.StringProperty()
    activated_product_ids = vschema.StringProperty(repeated=True)
    url = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = AccountGroup(
            account_group_id=extract_falsy_as_none(message.account_group_id), 
            name=extract_falsy_as_none(message.name), 
            address=extract_falsy_as_none(message.address), 
            activated_product_ids=extract_repeated(message.activated_product_ids), 
            url=extract_falsy_as_none(message.url)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.AccountGroup(
            account_group_id=self.account_group_id if self.account_group_id is not None else None, 
            name=self.name if self.name is not None else None, 
            address=self.address if self.address is not None else None, 
            activated_product_ids=self.activated_product_ids if self.activated_product_ids is not None else None, 
            url=self.url if self.url is not None else None
        )
        
        
        return proto

    
class Location(vschema.VObject):
    """ Location """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    _location = None
    account_group = vschema.StructuredProperty(AccountGroup)
    brand = vschema.StructuredProperty(Brand)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = Location(
            account_group=AccountGroup.from_proto(message.account_group) if message.HasField('account_group') else None, 
            brand=Brand.from_proto(message.brand) if message.HasField('brand') else None
        )
        obj._location = message.WhichOneof('location')
        
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.Location(
            account_group=self.account_group.to_proto() if self.account_group is not None else None, 
            brand=self.brand.to_proto() if self.brand is not None else None
        )
        
        
        return proto

    def which_one_of_location(self):
        """ returns which oneof location this is """
        return self._location

    
class AssociatedLocationIDs(vschema.VObject):
    """ AssociatedLocationIDs """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    account_group_ids = vschema.StringProperty(repeated=True)
    group_paths = vschema.StringProperty(repeated=True)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = AssociatedLocationIDs(
            account_group_ids=extract_repeated(message.account_group_ids), 
            group_paths=extract_repeated(message.group_paths)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.AssociatedLocationIDs(
            account_group_ids=self.account_group_ids if self.account_group_ids is not None else None, 
            group_paths=self.group_paths if self.group_paths is not None else None
        )
        
        
        return proto

    
class Theming(vschema.VObject):
    """ Theming """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    primary_color = vschema.StringProperty()
    primary_hover_color = vschema.StringProperty()
    primary_active_color = vschema.StringProperty()
    secondary_color = vschema.StringProperty()
    secondary_hover_color = vschema.StringProperty()
    secondary_active_color = vschema.StringProperty()
    font_color = vschema.StringProperty()
    font_disabled_color = vschema.StringProperty()
    accents_color = vschema.StringProperty()
    accents_active_color = vschema.StringProperty()
    focus_color = vschema.StringProperty()
    border_color = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = Theming(
            primary_color=extract_falsy_as_none(message.primary_color), 
            primary_hover_color=extract_falsy_as_none(message.primary_hover_color), 
            primary_active_color=extract_falsy_as_none(message.primary_active_color), 
            secondary_color=extract_falsy_as_none(message.secondary_color), 
            secondary_hover_color=extract_falsy_as_none(message.secondary_hover_color), 
            secondary_active_color=extract_falsy_as_none(message.secondary_active_color), 
            font_color=extract_falsy_as_none(message.font_color), 
            font_disabled_color=extract_falsy_as_none(message.font_disabled_color), 
            accents_color=extract_falsy_as_none(message.accents_color), 
            accents_active_color=extract_falsy_as_none(message.accents_active_color), 
            focus_color=extract_falsy_as_none(message.focus_color), 
            border_color=extract_falsy_as_none(message.border_color)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.Theming(
            primary_color=self.primary_color if self.primary_color is not None else None, 
            primary_hover_color=self.primary_hover_color if self.primary_hover_color is not None else None, 
            primary_active_color=self.primary_active_color if self.primary_active_color is not None else None, 
            secondary_color=self.secondary_color if self.secondary_color is not None else None, 
            secondary_hover_color=self.secondary_hover_color if self.secondary_hover_color is not None else None, 
            secondary_active_color=self.secondary_active_color if self.secondary_active_color is not None else None, 
            font_color=self.font_color if self.font_color is not None else None, 
            font_disabled_color=self.font_disabled_color if self.font_disabled_color is not None else None, 
            accents_color=self.accents_color if self.accents_color is not None else None, 
            accents_active_color=self.accents_active_color if self.accents_active_color is not None else None, 
            focus_color=self.focus_color if self.focus_color is not None else None, 
            border_color=self.border_color if self.border_color is not None else None
        )
        
        
        return proto

    
class RetentionConfig(vschema.VObject):
    """ RetentionConfig """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    cancellation_notification_email = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = RetentionConfig(
            cancellation_notification_email=extract_falsy_as_none(message.cancellation_notification_email)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.RetentionConfig(
            cancellation_notification_email=self.cancellation_notification_email if self.cancellation_notification_email is not None else None
        )
        
        
        return proto

    
class TotalLocations(vschema.VObject):
    """ TotalLocations """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    accounts = vschema.IntegerProperty()
    brands = vschema.IntegerProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = TotalLocations(
            accounts=message.accounts, 
            brands=message.brands
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.TotalLocations(
            accounts=self.accounts if self.accounts is not None else None, 
            brands=self.brands if self.brands is not None else None
        )
        
        
        return proto

    
class GetNavigationDataResponse(vschema.VObject):
    """ GetNavigationDataResponse """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    branding = vschema.StructuredProperty(Branding)
    sales_info = vschema.StructuredProperty(SalesInfo)
    pinned_items = vschema.StructuredProperty(PinnedItem, repeated=True)
    associated_location_ids = vschema.StructuredProperty(AssociatedLocationIDs)
    default_location = vschema.StringProperty()
    language = vschema.StringProperty()
    dropdown_items = vschema.StructuredProperty(DropdownItem, repeated=True)
    current_brand_name = vschema.StringProperty()
    user_view = vschema.IntegerProperty(choices=UserViewType)
    retention_config = vschema.StructuredProperty(RetentionConfig)
    total_locations = vschema.StructuredProperty(TotalLocations)
    user_id = vschema.StringProperty()
    business_app_branding = vschema.BooleanProperty()
    disable_business_nav = vschema.BooleanProperty()
    navigation_items = vschema.StructuredProperty(SideNavigationItem, repeated=True)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = GetNavigationDataResponse(
            branding=Branding.from_proto(message.branding) if message.HasField('branding') else None, 
            sales_info=SalesInfo.from_proto(message.sales_info) if message.HasField('sales_info') else None, 
            pinned_items=[PinnedItem.from_proto(x) for x in message.pinned_items], 
            associated_location_ids=AssociatedLocationIDs.from_proto(message.associated_location_ids) if message.HasField('associated_location_ids') else None, 
            default_location=extract_falsy_as_none(message.default_location), 
            language=extract_falsy_as_none(message.language), 
            dropdown_items=[DropdownItem.from_proto(x) for x in message.dropdown_items], 
            current_brand_name=extract_falsy_as_none(message.current_brand_name), 
            user_view=message.user_view, 
            retention_config=RetentionConfig.from_proto(message.retention_config) if message.HasField('retention_config') else None, 
            total_locations=TotalLocations.from_proto(message.total_locations) if message.HasField('total_locations') else None, 
            user_id=extract_falsy_as_none(message.user_id), 
            business_app_branding=message.business_app_branding, 
            disable_business_nav=message.disable_business_nav, 
            navigation_items=[SideNavigationItem.from_proto(x) for x in message.navigation_items]
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetNavigationDataResponse(
            branding=self.branding.to_proto() if self.branding is not None else None, 
            sales_info=self.sales_info.to_proto() if self.sales_info is not None else None, 
            pinned_items=[x.to_proto() for x in self.pinned_items] if self.pinned_items is not None else None, 
            associated_location_ids=self.associated_location_ids.to_proto() if self.associated_location_ids is not None else None, 
            default_location=self.default_location if self.default_location is not None else None, 
            language=self.language if self.language is not None else None, 
            dropdown_items=[x.to_proto() for x in self.dropdown_items] if self.dropdown_items is not None else None, 
            current_brand_name=self.current_brand_name if self.current_brand_name is not None else None, 
            user_view=self.user_view.value if self.user_view is not None else None, 
            retention_config=self.retention_config.to_proto() if self.retention_config is not None else None, 
            total_locations=self.total_locations.to_proto() if self.total_locations is not None else None, 
            user_id=self.user_id if self.user_id is not None else None, 
            business_app_branding=self.business_app_branding if self.business_app_branding is not None else None, 
            disable_business_nav=self.disable_business_nav if self.disable_business_nav is not None else None, 
            navigation_items=[x.to_proto() for x in self.navigation_items] if self.navigation_items is not None else None
        )
        
        
        return proto

    
class SetPinsRequest(vschema.VObject):
    """ SetPinsRequest """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    identifier = vschema.StringProperty()
    items = vschema.StructuredProperty(PinnedItem, repeated=True)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = SetPinsRequest(
            identifier=extract_falsy_as_none(message.identifier), 
            items=[PinnedItem.from_proto(x) for x in message.items]
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.SetPinsRequest(
            identifier=self.identifier if self.identifier is not None else None, 
            items=[x.to_proto() for x in self.items] if self.items is not None else None
        )
        
        
        return proto

    
class GetPinsRequest(vschema.VObject):
    """ GetPinsRequest """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    identifier = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = GetPinsRequest(
            identifier=extract_falsy_as_none(message.identifier)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetPinsRequest(
            identifier=self.identifier if self.identifier is not None else None
        )
        
        
        return proto

    
class GetPinsResponse(vschema.VObject):
    """ GetPinsResponse """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    items = vschema.StructuredProperty(PinnedItem, repeated=True)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = GetPinsResponse(
            items=[PinnedItem.from_proto(x) for x in message.items]
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetPinsResponse(
            items=[x.to_proto() for x in self.items] if self.items is not None else None
        )
        
        
        return proto

    
class AccountGroups(vschema.VObject):
    """ AccountGroups """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    account_group_ids = vschema.StringProperty(repeated=True)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = AccountGroups(
            account_group_ids=extract_repeated(message.account_group_ids)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetLocationsRequest.AccountGroups(
            account_group_ids=self.account_group_ids if self.account_group_ids is not None else None
        )
        
        
        return proto

    
class Groups(vschema.VObject):
    """ Groups """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    group_paths = vschema.StringProperty(repeated=True)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = Groups(
            group_paths=extract_repeated(message.group_paths)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetLocationsRequest.Groups(
            group_paths=self.group_paths if self.group_paths is not None else None
        )
        
        
        return proto

    
class GetLocationsRequest(vschema.VObject):
    """ GetLocationsRequest """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    _identifier = None
    account_groups = vschema.StructuredProperty(AccountGroups)
    groups = vschema.StructuredProperty(Groups)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = GetLocationsRequest(
            account_groups=AccountGroups.from_proto(message.account_groups) if message.HasField('account_groups') else None, 
            groups=Groups.from_proto(message.groups) if message.HasField('groups') else None
        )
        obj._identifier = message.WhichOneof('identifier')
        
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetLocationsRequest(
            account_groups=self.account_groups.to_proto() if self.account_groups is not None else None, 
            groups=self.groups.to_proto() if self.groups is not None else None
        )
        
        
        return proto

    def which_one_of_identifier(self):
        """ returns which oneof identifier this is """
        return self._identifier

    
class GetLocationsResponse(vschema.VObject):
    """ GetLocationsResponse """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    locations = vschema.StructuredProperty(Location, repeated=True)
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = GetLocationsResponse(
            locations=[Location.from_proto(x) for x in message.locations]
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetLocationsResponse(
            locations=[x.to_proto() for x in self.locations] if self.locations is not None else None
        )
        
        
        return proto

    
class ListElevatedLocationsRequest(vschema.VObject):
    """ ListElevatedLocationsRequest """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    _type = None
    partner_id = vschema.StringProperty()
    cursor = vschema.StringProperty()
    page_size = vschema.IntegerProperty()
    search = vschema.StringProperty()
    account_groups = vschema.BooleanProperty()
    brands = vschema.BooleanProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = ListElevatedLocationsRequest(
            partner_id=extract_falsy_as_none(message.partner_id), 
            cursor=extract_falsy_as_none(message.cursor), 
            page_size=message.page_size, 
            search=extract_falsy_as_none(message.search), 
            account_groups=message.account_groups, 
            brands=message.brands
        )
        obj._type = message.WhichOneof('type')
        
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.ListElevatedLocationsRequest(
            partner_id=self.partner_id if self.partner_id is not None else None, 
            cursor=self.cursor if self.cursor is not None else None, 
            page_size=self.page_size if self.page_size is not None else None, 
            search=self.search if self.search is not None else None, 
            account_groups=self.account_groups if self.account_groups is not None else None, 
            brands=self.brands if self.brands is not None else None
        )
        
        
        return proto

    def which_one_of_type(self):
        """ returns which oneof type this is """
        return self._type

    
class ListElevatedLocationsResponse(vschema.VObject):
    """ ListElevatedLocationsResponse """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    locations = vschema.StructuredProperty(Location, repeated=True)
    cursor = vschema.StringProperty()
    has_more = vschema.BooleanProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = ListElevatedLocationsResponse(
            locations=[Location.from_proto(x) for x in message.locations], 
            cursor=extract_falsy_as_none(message.cursor), 
            has_more=message.has_more
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.ListElevatedLocationsResponse(
            locations=[x.to_proto() for x in self.locations] if self.locations is not None else None, 
            cursor=self.cursor if self.cursor is not None else None, 
            has_more=self.has_more if self.has_more is not None else None
        )
        
        
        return proto

    
class ListLocationsRequest(vschema.VObject):
    """ ListLocationsRequest """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    partner_id = vschema.StringProperty()
    cursor = vschema.StringProperty()
    page_size = vschema.IntegerProperty()
    search = vschema.StringProperty()
    include_account_groups = vschema.BooleanProperty()
    include_brands = vschema.BooleanProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = ListLocationsRequest(
            partner_id=extract_falsy_as_none(message.partner_id), 
            cursor=extract_falsy_as_none(message.cursor), 
            page_size=message.page_size, 
            search=extract_falsy_as_none(message.search), 
            include_account_groups=message.include_account_groups, 
            include_brands=message.include_brands
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.ListLocationsRequest(
            partner_id=self.partner_id if self.partner_id is not None else None, 
            cursor=self.cursor if self.cursor is not None else None, 
            page_size=self.page_size if self.page_size is not None else None, 
            search=self.search if self.search is not None else None, 
            include_account_groups=self.include_account_groups if self.include_account_groups is not None else None, 
            include_brands=self.include_brands if self.include_brands is not None else None
        )
        
        
        return proto

    
class ListLocationsResponse(vschema.VObject):
    """ ListLocationsResponse """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    locations = vschema.StructuredProperty(Location, repeated=True)
    cursor = vschema.StringProperty()
    has_more = vschema.BooleanProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = ListLocationsResponse(
            locations=[Location.from_proto(x) for x in message.locations], 
            cursor=extract_falsy_as_none(message.cursor), 
            has_more=message.has_more
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.ListLocationsResponse(
            locations=[x.to_proto() for x in self.locations] if self.locations is not None else None, 
            cursor=self.cursor if self.cursor is not None else None, 
            has_more=self.has_more if self.has_more is not None else None
        )
        
        
        return proto

    
class SetLanguageRequest(vschema.VObject):
    """ SetLanguageRequest """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    language = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = SetLanguageRequest(
            language=extract_falsy_as_none(message.language)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.SetLanguageRequest(
            language=self.language if self.language is not None else None
        )
        
        
        return proto

    
class GetLanguageRequest(vschema.VObject):
    """ GetLanguageRequest """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = GetLanguageRequest(
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetLanguageRequest(
        )
        
        
        return proto

    
class GetLanguageResponse(vschema.VObject):
    """ GetLanguageResponse """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    language = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = GetLanguageResponse(
            language=extract_falsy_as_none(message.language)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetLanguageResponse(
            language=self.language if self.language is not None else None
        )
        
        
        return proto

    
class ContactUsRequest(vschema.VObject):
    """ ContactUsRequest """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    account_group_id = vschema.StringProperty()
    message = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = ContactUsRequest(
            account_group_id=extract_falsy_as_none(message.account_group_id), 
            message=extract_falsy_as_none(message.message)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.ContactUsRequest(
            account_group_id=self.account_group_id if self.account_group_id is not None else None, 
            message=self.message if self.message is not None else None
        )
        
        
        return proto

    
class SetDefaultLocationRequest(vschema.VObject):
    """ SetDefaultLocationRequest """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    _location = None
    partner_id = vschema.StringProperty()
    account_group_id = vschema.StringProperty()
    group_id = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = SetDefaultLocationRequest(
            partner_id=extract_falsy_as_none(message.partner_id), 
            account_group_id=extract_falsy_as_none(message.account_group_id), 
            group_id=extract_falsy_as_none(message.group_id)
        )
        obj._location = message.WhichOneof('location')
        
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.SetDefaultLocationRequest(
            partner_id=self.partner_id if self.partner_id is not None else None, 
            account_group_id=self.account_group_id if self.account_group_id is not None else None, 
            group_id=self.group_id if self.group_id is not None else None
        )
        
        
        return proto

    def which_one_of_location(self):
        """ returns which oneof location this is """
        return self._location

    
class GetDefaultLocationRequest(vschema.VObject):
    """ GetDefaultLocationRequest """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    partner_id = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = GetDefaultLocationRequest(
            partner_id=extract_falsy_as_none(message.partner_id)
        )
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetDefaultLocationRequest(
            partner_id=self.partner_id if self.partner_id is not None else None
        )
        
        
        return proto

    
class GetDefaultLocationResponse(vschema.VObject):
    """ GetDefaultLocationResponse """
    OWNER = "api_pb2"
    CLASS_VERSION = "1.0.0"

    _location = None
    account_group_id = vschema.StringProperty()
    group_id = vschema.StringProperty()
    
    @classmethod
    def from_proto(cls, message):
        """ Convert from proto """
        obj = GetDefaultLocationResponse(
            account_group_id=extract_falsy_as_none(message.account_group_id), 
            group_id=extract_falsy_as_none(message.group_id)
        )
        obj._location = message.WhichOneof('location')
        
        return obj

    def to_proto(self):
        """ Convert to proto """
        proto = api_pb2.GetDefaultLocationResponse(
            account_group_id=self.account_group_id if self.account_group_id is not None else None, 
            group_id=self.group_id if self.group_id is not None else None
        )
        
        
        return proto

    def which_one_of_location(self):
        """ returns which oneof location this is """
        return self._location

    