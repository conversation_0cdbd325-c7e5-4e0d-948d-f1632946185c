""" Parent Client """
from vax.environment import Environment
from .config import EN<PERSON><PERSON><PERSON>MENT_PARAMS
from vax.errors import UninitializedException


class ParentClient(object):
    """
    ParentClient
    This client is thread-safe and should be reused.  Instantiating this object opens connections and keeps them alive
    to increase RPC performance.
    This should be the parent class for all of your external clients
    """
    __ENVIRONMENT = None
    __CACHE_CLIENT = None
    __CLIENTS = {}
    __CLIENT_KEY = None

    @staticmethod
    def set_environment(environment=Environment.PROD):
        """ Set the environment """
        ParentClient.__ENVIRONMENT = environment

    @staticmethod
    def get_environment():
        """ Get the environment """
        if not ParentClient.__ENVIRONMENT:
            raise UninitializedException()
        return ParentClient.__ENVIRONMENT

    @staticmethod
    def set_cache_client(cache=None):
        """
        Set the cache mechanism used by this client. By default the client will cache on the instance.
        'cache' must implement vax.auth_cache.py
        """
        ParentClient.__CACHE_CLIENT = cache

    @staticmethod
    def set_client_key(client_key=''):
        """
        Set the client key to use. By default the client will try to use the vendasta default location of the client key
        If using this client externally, this must be set or your calls will fail IAM checks.
        """
        ParentClient.__CLIENT_KEY = client_key

    @staticmethod
    def get_client(internal_client):
        """
        :param internal_client: the class of the client you want to instantiate. a class from ./_internal/
        """
        """ Get the API client """
        env = ParentClient.get_environment()
        env_config = ENVIRONMENT_PARAMS[env]
        if not ParentClient.__CLIENTS.get(internal_client.__name__):
            ParentClient.__CLIENTS[internal_client.__name__] = internal_client(
                env_config.get('url'),
                env_config.get('scope'),
                env_config.get('service_account'),
                environment=env,
                cache=ParentClient.__CACHE_CLIENT,
                client_key=ParentClient.__CLIENT_KEY
            )
        return ParentClient.__CLIENTS[internal_client.__name__]
