
import os
import sys

from setuptools import setup
from setuptools import find_packages

REQUIREMENTS = [
    'httplib2 >= 0.9.1',
    'oauth2client >= 2.0.1',
    'protobuf >= 3.0.0b2, != 3.0.0.b2.post1',
    'googleapis-common-protos',
    'six',
    'enum34',
    'vax >= 1.3.0',
    'vschema >= 0.1.1',
    'vendastatypes'
]

with open(os.path.join(os.path.dirname(os.path.realpath(__file__)), 'atlas_sdk', 'VERSION')) as f:
    version = f.read().strip()

found_packages = find_packages()
proto_package = ['atlas_sdk._generated.proto.atlas',
  'atlas_sdk._generated.proto.atlas.v1'
]

found_packages = found_packages + proto_package
package_data = {pkg_name: ['README.md', 'VERSION'] for pkg_name in found_packages}

# include our secret JSON files:
package_data[''] = ['*.json']

setup(
    name='atlas',
    version=version,
    description='Python API Client library for atlas',
    author='Vendasta',
    url='https://github.com/vendasta/atlas',
    packages=package_data,
    install_requires=REQUIREMENTS,
    package_data=package_data,
)
