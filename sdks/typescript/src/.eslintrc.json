{"root": true, "ignorePatterns": ["**/*"], "plugins": [], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["tsconfig.json"], "createDefaultProgram": true}, "extends": ["plugin:@typescript-eslint/recommended-requiring-type-checking", "plugin:@angular-eslint/all"], "rules": {"@angular-eslint/sort-ngmodule-metadata-arrays": "off"}}, {"files": ["*.html"], "extends": ["plugin:@angular-eslint/template/all"], "rules": {"@angular-eslint/template/i18n": "off"}}]}