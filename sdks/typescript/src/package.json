{"name": "@vendasta/atlas_builder", "version": "0.0.1", "scripts": {"test": "ng test", "test:watch": "ng test --watch-all", "test:ci": "ng test --pass-with-no-tests", "lint": "ng lint", "build": "ng build", "build:prod": "ng build --configuration production"}, "devDependencies": {"@angular-builders/jest": "^15.0.0", "@angular-eslint/builder": "^15.2.0", "@angular-eslint/eslint-plugin": "^15.2.0", "@angular-eslint/eslint-plugin-template": "^15.2.0", "@angular/cli": "^15.1.1", "@types/jest": "^28.1.6", "@typescript-eslint/eslint-plugin": "^5.12.0", "ng-packagr": "^15.1.1"}, "dependencies": {"@angular/common": "^15.1.0", "@angular/core": "^15.1.0"}, "author": "Vendasta R&D", "description": "SDK to interact with the atlas service", "typings": "index.d.ts", "license": "ISC", "main": "index.js", "engines": {"node": ">=20.10.0 <21.0.0", "npm": ">=10.2.0 <12.0.0"}}