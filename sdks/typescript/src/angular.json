{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"atlas": {"root": "atlas_sdk", "sourceRoot": "atlas_sdk/src", "projectType": "library", "prefix": "atlas", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"tsConfig": "atlas_sdk/tsconfig.lib.json", "project": "atlas_sdk/ng-package.json"}, "configurations": {"production": {"tsConfig": "atlas_sdk/tsconfig.lib.prod.json"}}}, "test": {"builder": "@angular-builders/jest:run", "options": {"tsConfig": "atlas_sdk/tsconfig.spec.json"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["atlas_sdk/**/*.ts", "atlas_sdk/**/*.html"]}}}}}, "defaultProject": "atlas"}