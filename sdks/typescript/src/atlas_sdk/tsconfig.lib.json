{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "./out-tsc/lib", "baseUrl": ".", "target": "es2015", "module": "esnext", "moduleResolution": "node", "declaration": true, "sourceMap": true, "inlineSources": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "types": [], "lib": ["dom", "es2018"]}, "angularCompilerOptions": {"skipTemplateCodegen": true, "strictMetadataEmit": true, "fullTemplateTypeCheck": true, "strictInjectionParameters": true, "enableResourceInlining": true, "flatModuleId": "AUTOGENERATED", "flatModuleOutFile": "AUTOGENERATED"}, "exclude": ["src/test.ts", "**/*.spec.ts", "**/_internal/**"]}