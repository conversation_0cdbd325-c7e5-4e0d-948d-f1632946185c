{"extends": "../.eslintrc.json", "ignorePatterns": ["!**/*", "**/_generated/**", "**/_internal/**"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["atlas_sdk/tsconfig.lib.json", "atlas_sdk/tsconfig.spec.json"], "createDefaultProgram": true}, "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "marketplace-apps", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "marketplace-apps", "style": "kebab-case"}]}}, {"files": ["*.html"], "rules": {}}]}