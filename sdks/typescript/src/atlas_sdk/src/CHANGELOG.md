## 8.19.0
- Regeneration sdk which includes indicators to disable the product switcher

## 8.16.0
- Add `business_app_ui_theme` to branding

## 8.15.0

- Add `UI_THEME_USER_PREFERENCE` as an enum value for `UITheme` field in branding

## 8.14.0

- Add `dark_mode_logo_url` to branding response

## 8.13.0

- Add `platform_mode` to `GetNavigationDataRequest` to generate mobile tabs

## 8.12.0

- Provide `settingsLinkConfig` with `settingsContainerConfig` as fallback

## 8.11.0

- Add `EntryUrl` to `UserSwitcherData` and `PartnerName` to `GetDataResponse`

## 8.10.0

- Add `UserSwitcherData` to `GetDataResponse`

## 8.9.0

- Allow npm 10

## 8.8.0

- Add `entryUrl` field to `SideNavigationLink`

## 8.7.0

- Add `subLinks` to `SideNavigationLink`

## 8.6.0

- Add `emailVerified` field to `GetDataResponse`

## 8.5.0

- Update to Angular 15

## 8.4.0

- Add `openInNewTab` to `SideNavigationLink`

## 8.3.0

- Add `navigationId` to `SideNavigationContainer`

## 8.2.0

- Add `pinnable` to `SideNavigationContainer`

## 8.1.0

- Add `url` to `SideNavigationContainer`

## 8.0.0

- Moved to version 8 because versions 1 through 7 were used by a previous version of an atlas sdk on npm.
- Remove deprecated fields `GetNavigationDataResponse.BrandItems` and `GetNavigationDataResponse.AccountgroupItems`

## 0.18.0

- Support `GetNavigationDataResponse.NavigationItems`

## 0.17.0

- Add `getShouldShowEmailVerificationInterstitial` and `remindMeLaterEmailVerification` to SDK

## 0.16.1

- Update IAM sdk

## 0.16.0

- Add `meeting-booking-ul` to `SalesContact`

## 0.15.0

- Update to Angular 13

## 0.14.0

- Add `disableBusinessNav` to `GetNavigationDataResponse`

## 0.13.0

- Add `GetDefaultLocation`, update `SetDefaultLocation` to optionally accept a group ID.

## 0.12.1

- Provide host service in root

## 0.12.0

- Add ListLocations to SDK

## 0.11.4

- Publish with Ivy

## 0.11.3

- Organize imports

## 0.11.2

- Update to NG 12

## 0.11.1

- Fix the vendasta/core dependency

## 0.11.0

- Regenerate SDK from scratch

## 0.9.0

- Add `impersonateeUsername` to GetDataResponse

## 0.8.0

- Add cobranding-logo-url to navigation data

## 0.7.0

- Add email config to VBC sidenav

## 0.6.0

- Add ListElevatedLocations

## 0.5.0

- Add Retention Config

## 0.4.0

- Add notifications enablement status

## 0.3.0

- Add Center Name and Partner Name

## 0.2.0

- Add Email to the data response

## 0.1.1

- Setup module correctly

## 0.1.0

- Generate SDK, add relevant items to module
