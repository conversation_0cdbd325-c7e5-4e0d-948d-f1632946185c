import { Injectable } from '@angular/core';

declare const window: any;
const environment: string = (window ? window['environment'] : 'prod') ?? 'prod';
const hostMap: { [key: string]: string } = {
    'local': 'atlas-api.vendasta-local.com',
    'test': '',
    'demo': 'atlas-api-demo.apigateway.co',
    'prod': 'atlas-api-prod.apigateway.co',
    'production': 'atlas-api-prod.apigateway.co',
};

@Injectable({providedIn: 'root'})
export class HostService {
    get host(): string {
        return hostMap[environment.toLowerCase()];
    }

    get hostWithScheme(): string {
        return 'https://' + this.host;
    }
}
