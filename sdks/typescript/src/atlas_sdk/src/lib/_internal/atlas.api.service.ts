// *********************************
// Code generated by sdkgen
// DO NOT EDIT!.
//
// API Service.
// *********************************
import {Injectable} from '@angular/core';
import {
        ContactUsRequest,
        GetDataRequest,
        GetDataResponse,
        GetDefaultLocationRequest,
        GetDefaultLocationResponse,
        GetLocationsRequest,
        GetLocationsResponse,
        GetNavigationDataRequest,
        GetNavigationDataResponse,
        GetSalesInfoRequest,
        GetSalesInfoResponse,
        ListElevatedLocationsRequest,
        ListElevatedLocationsResponse,
        ListLocationsRequest,
        ListLocationsResponse,
        SetDefaultLocationRequest,
} from './objects/';
import {
        ContactUsRequestInterface,
        GetDataRequestInterface,
        GetDataResponseInterface,
        GetDefaultLocationRequestInterface,
        GetDefaultLocationResponseInterface,
        GetLocationsRequestInterface,
        GetLocationsResponseInterface,
        GetNavigationDataRequestInterface,
        GetNavigationDataResponseInterface,
        GetSalesInfoRequestInterface,
        GetSalesInfoResponseInterface,
        ListElevatedLocationsRequestInterface,
        ListElevatedLocationsResponseInterface,
        ListLocationsRequestInterface,
        ListLocationsResponseInterface,
        SetDefaultLocationRequestInterface,
} from './interfaces/';
import {HttpHeaders, HttpClient, HttpResponse} from '@angular/common/http';
import {inject} from '@angular/core';
import {HostService} from '../_generated/host.service';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';

@Injectable({providedIn: 'root'})
export class AtlasApiService {
    private readonly hostService = inject(HostService);
    private readonly http = inject(HttpClient);
    private _host = this.hostService.hostWithScheme;

    private apiOptions(): {headers: HttpHeaders, withCredentials: boolean} {
        return {
            headers: new HttpHeaders({
                'Content-Type': 'application/json'
            }),
            withCredentials: true
        };
    }

    getData(r: GetDataRequest | GetDataRequestInterface): Observable<GetDataResponse> {
        const request = ((<GetDataRequest>r).toApiJson) ? (<GetDataRequest>r) : new GetDataRequest(r);
        return this.http.post<GetDataResponseInterface>(this._host + "/atlas.v1.Atlas/GetData", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetDataResponse.fromProto(resp))
            );
    }
    getNavigationData(r: GetNavigationDataRequest | GetNavigationDataRequestInterface): Observable<GetNavigationDataResponse> {
        const request = ((<GetNavigationDataRequest>r).toApiJson) ? (<GetNavigationDataRequest>r) : new GetNavigationDataRequest(r);
        return this.http.post<GetNavigationDataResponseInterface>(this._host + "/atlas.v1.Atlas/GetNavigationData", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetNavigationDataResponse.fromProto(resp))
            );
    }
    getSalesInfo(r: GetSalesInfoRequest | GetSalesInfoRequestInterface): Observable<GetSalesInfoResponse> {
        const request = ((<GetSalesInfoRequest>r).toApiJson) ? (<GetSalesInfoRequest>r) : new GetSalesInfoRequest(r);
        return this.http.post<GetSalesInfoResponseInterface>(this._host + "/atlas.v1.Atlas/GetSalesInfo", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetSalesInfoResponse.fromProto(resp))
            );
    }
    getLocations(r: GetLocationsRequest | GetLocationsRequestInterface): Observable<GetLocationsResponse> {
        const request = ((<GetLocationsRequest>r).toApiJson) ? (<GetLocationsRequest>r) : new GetLocationsRequest(r);
        return this.http.post<GetLocationsResponseInterface>(this._host + "/atlas.v1.Atlas/GetLocations", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetLocationsResponse.fromProto(resp))
            );
    }
    listElevatedLocations(r: ListElevatedLocationsRequest | ListElevatedLocationsRequestInterface): Observable<ListElevatedLocationsResponse> {
        const request = ((<ListElevatedLocationsRequest>r).toApiJson) ? (<ListElevatedLocationsRequest>r) : new ListElevatedLocationsRequest(r);
        return this.http.post<ListElevatedLocationsResponseInterface>(this._host + "/atlas.v1.Atlas/ListElevatedLocations", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => ListElevatedLocationsResponse.fromProto(resp))
            );
    }
    listLocations(r: ListLocationsRequest | ListLocationsRequestInterface): Observable<ListLocationsResponse> {
        const request = ((<ListLocationsRequest>r).toApiJson) ? (<ListLocationsRequest>r) : new ListLocationsRequest(r);
        return this.http.post<ListLocationsResponseInterface>(this._host + "/atlas.v1.Atlas/ListLocations", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => ListLocationsResponse.fromProto(resp))
            );
    }
    contactUs(r: ContactUsRequest | ContactUsRequestInterface): Observable<HttpResponse<null>> {
        const request = ((<ContactUsRequest>r).toApiJson) ? (<ContactUsRequest>r) : new ContactUsRequest(r);
        return this.http.post<null>(this._host + "/atlas.v1.Atlas/ContactUs", request.toApiJson(), {...this.apiOptions(), observe: 'response'});
    }
    setDefaultLocation(r: SetDefaultLocationRequest | SetDefaultLocationRequestInterface): Observable<HttpResponse<null>> {
        const request = ((<SetDefaultLocationRequest>r).toApiJson) ? (<SetDefaultLocationRequest>r) : new SetDefaultLocationRequest(r);
        return this.http.post<null>(this._host + "/atlas.v1.Atlas/SetDefaultLocation", request.toApiJson(), {...this.apiOptions(), observe: 'response'});
    }
    getDefaultLocation(r: GetDefaultLocationRequest | GetDefaultLocationRequestInterface): Observable<GetDefaultLocationResponse> {
        const request = ((<GetDefaultLocationRequest>r).toApiJson) ? (<GetDefaultLocationRequest>r) : new GetDefaultLocationRequest(r);
        return this.http.post<GetDefaultLocationResponseInterface>(this._host + "/atlas.v1.Atlas/GetDefaultLocation", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetDefaultLocationResponse.fromProto(resp))
            );
    }
    
}
