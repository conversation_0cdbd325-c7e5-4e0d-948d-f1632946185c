// *********************************
// Code generated by sdkgen
// DO NOT EDIT!.
//
// Interfaces.
// *********************************

import * as e from '../enums';

export interface AccountGroupInterface {
    accountGroupId?: string;
    name?: string;
    address?: string;
    activatedProductIds?: string[];
    url?: string;
}

export interface GetLocationsRequestAccountGroupsInterface {
    accountGroupIds?: string[];
}

export interface AssociatedLocationIDsInterface {
    accountGroupIds?: string[];
    groupPaths?: string[];
}

export interface BrandInterface {
    name?: string;
    pathNodes?: string[];
    hasAccess?: boolean;
    url?: string;
}

export interface BrandingInterface {
    theme?: e.UITheme;
    logoUrl?: string;
    partnerName?: string;
    centerName?: string;
    theming?: ThemingInterface;
    cobrandingLogoUrl?: string;
    marketName?: string;
    darkModeLogoUrl?: string;
    businessAppUiTheme?: e.UITheme;
    exitLinkConfiguration?: ExitLinkConfigurationInterface;
}

export interface CenterNavigationItemInterface {
    name?: string;
    entryUrl?: string;
    centerId?: string;
}

export interface ContactUsRequestInterface {
    accountGroupId?: string;
    message?: string;
}

export interface DropdownItemInterface {
    url?: string;
    path?: string;
    translationId?: string;
    label?: string;
}

export interface ExitLinkConfigurationInterface {
    exitLinkText?: string;
    exitLinkUrl?: string;
}

export interface GetDataRequestInterface {
    partnerId?: string;
    marketId?: string;
    accountGroupId?: string;
    signOutNextUrl?: string;
    groupPath?: string;
}

export interface GetDataResponseInterface {
    user?: UserNavigationItemInterface[];
    centers?: CenterNavigationItemInterface[];
    username?: string;
    email?: string;
    signOutUrl?: string;
    theme?: e.UITheme;
    language?: string;
    theming?: ThemingInterface;
    notificationsEnabled?: boolean;
    userId?: string;
    locationData?: LocationDataInterface;
    impersonateeUsername?: string;
    emailVerified?: boolean;
    userSwitcherData?: UserSwitcherDataInterface[];
    partnerName?: string;
    businessAppUiTheme?: e.UITheme;
}

export interface GetDefaultLocationRequestInterface {
    partnerId?: string;
}

export interface GetDefaultLocationResponseInterface {
    accountGroupId?: string;
    groupId?: string;
}

export interface GetLanguageRequestInterface {
}

export interface GetLanguageResponseInterface {
    language?: string;
}

export interface GetLocationsRequestInterface {
    accountGroups?: GetLocationsRequestAccountGroupsInterface;
    groups?: GetLocationsRequestGroupsInterface;
}

export interface GetLocationsResponseInterface {
    locations?: LocationInterface[];
}

export interface GetNavigationDataRequestInterface {
    accountGroupId?: string;
    groupPath?: string;
    partnerId?: string;
    marketId?: string;
    platformMode?: e.PlatformMode;
}

export interface GetNavigationDataResponseInterface {
    branding?: BrandingInterface;
    salesInfo?: SalesInfoInterface;
    pinnedItems?: PinnedItemInterface[];
    associatedLocationIds?: AssociatedLocationIDsInterface;
    defaultLocation?: string;
    language?: string;
    dropdownItems?: DropdownItemInterface[];
    currentBrandName?: string;
    userView?: e.UserViewType;
    retentionConfig?: RetentionConfigInterface;
    totalLocations?: TotalLocationsInterface;
    userId?: string;
    businessAppBranding?: boolean;
    disableBusinessNav?: boolean;
    navigationItems?: SideNavigationItemInterface[];
    disableProductSwitcher?: boolean;
}

export interface GetPinsRequestInterface {
    identifier?: string;
}

export interface GetPinsResponseInterface {
    items?: PinnedItemInterface[];
}

export interface GetSalesInfoRequestInterface {
    accountGroupId?: string;
    groupPath?: string;
}

export interface GetSalesInfoResponseInterface {
    salesInfo?: SalesInfoInterface;
}

export interface GetLocationsRequestGroupsInterface {
    groupPaths?: string[];
}

export interface ListElevatedLocationsRequestInterface {
    partnerId?: string;
    cursor?: string;
    pageSize?: number;
    search?: string;
    accountGroups?: boolean;
    brands?: boolean;
}

export interface ListElevatedLocationsResponseInterface {
    locations?: LocationInterface[];
    cursor?: string;
    hasMore?: boolean;
}

export interface ListLocationsRequestInterface {
    partnerId?: string;
    cursor?: string;
    pageSize?: number;
    search?: string;
    includeAccountGroups?: boolean;
    includeBrands?: boolean;
}

export interface ListLocationsResponseInterface {
    locations?: LocationInterface[];
    cursor?: string;
    hasMore?: boolean;
}

export interface LocationInterface {
    accountGroup?: AccountGroupInterface;
    brand?: BrandInterface;
}

export interface LocationDataInterface {
    businessName?: string;
    address?: string;
}

export interface PinnedItemInterface {
    navigationId?: string;
}

export interface RetentionConfigInterface {
    cancellationNotificationEmail?: string;
}

export interface SalesContactInterface {
    salesPersonId?: string;
    email?: string;
    firstName?: string;
    lastName?: string;
    phoneNumber?: string;
    photoUrlSecure?: string;
    jobTitle?: string;
    country?: string;
    meetingBookingUrl?: string;
}

export interface SalesInfoInterface {
    marketName?: string;
    salesContact?: SalesContactInterface;
}

export interface SetDefaultLocationRequestInterface {
    partnerId?: string;
    accountGroupId?: string;
    groupId?: string;
}

export interface SetLanguageRequestInterface {
    language?: string;
}

export interface SetPinsRequestInterface {
    identifier?: string;
    items?: PinnedItemInterface[];
}

export interface SideNavigationContainerInterface {
    translationId?: string;
    sideNavigationItems?: SideNavigationItemInterface[];
    icon?: string;
    logoUrl?: string;
    label?: string;
    showIcon?: boolean;
    chipContent?: string;
    url?: string;
    pinnable?: boolean;
    navigationId?: string;
}

export interface SideNavigationItemInterface {
    sideNavigationSection?: SideNavigationSectionInterface;
    sideNavigationContainer?: SideNavigationContainerInterface;
    sideNavigationLink?: SideNavigationLinkInterface;
}

export interface SideNavigationLinkInterface {
    navigationId?: string;
    url?: string;
    path?: string;
    serviceProviderId?: string;
    logoUrl?: string;
    icon?: string;
    translationId?: string;
    external?: boolean;
    label?: string;
    showIcon?: boolean;
    pinnable?: boolean;
    chipContent?: string;
    isTrial?: boolean;
    userRequired?: boolean;
    openInNewTab?: boolean;
    subLinks?: SideNavigationLinkInterface[];
    launchUrl?: string;
    descriptionTranslationId?: string;
}

export interface SideNavigationSectionInterface {
    translationId?: string;
    sideNavigationItems?: SideNavigationItemInterface[];
    label?: string;
    chipContent?: string;
}

export interface ThemingInterface {
    primaryColor?: string;
    primaryHoverColor?: string;
    primaryActiveColor?: string;
    secondaryColor?: string;
    secondaryHoverColor?: string;
    secondaryActiveColor?: string;
    fontColor?: string;
    fontDisabledColor?: string;
    accentsColor?: string;
    accentsActiveColor?: string;
    focusColor?: string;
    borderColor?: string;
}

export interface TotalLocationsInterface {
    accounts?: number;
    brands?: number;
}

export interface UserNavigationItemInterface {
    text?: string;
    url?: string;
    routeId?: string;
}

export interface UserSwitcherDataInterface {
    userId?: string;
    partnerId?: string;
    partnerName?: string;
    entryUrl?: string;
}

