// *********************************
// Code generated by sdkgen
// DO NOT EDIT!.
//
// Objects.
// *********************************
import * as i from '../interfaces';

import * as e from '../enums';

export function enumStringToValue<E>(enumRef: any, value: string): E {
  if (typeof value === 'number') {
    return value;
  }
  return enumRef[value];
}

export class Access implements i.AccessInterface {
    scope: string[];
    public: boolean;

    static fromProto(proto: any): Access {
        let m = new Access();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.AccessInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.scope !== 'undefined') {toReturn['scope'] = this.scope;}
        if (typeof this.public !== 'undefined') {toReturn['public'] = this.public;}
        return toReturn;
    }
}

