// *********************************
// Code generated by sdkgen
// DO NOT EDIT!.
//
// Objects.
// *********************************
import * as i from '../interfaces';

import * as e from '../enums';

export function enumStringToValue<E>(enumRef: any, value: string): E {
  if (typeof value === 'number') {
    return value;
  }
  return enumRef[value];
}

export class AccountGroup implements i.AccountGroupInterface {
    accountGroupId: string;
    name: string;
    address: string;
    activatedProductIds: string[];
    url: string;

    static fromProto(proto: any): AccountGroup {
        let m = new AccountGroup();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.AccountGroupInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApi<PERSON>son(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.accountGroupId !== 'undefined') {toReturn['accountGroupId'] = this.accountGroupId;}
        if (typeof this.name !== 'undefined') {toReturn['name'] = this.name;}
        if (typeof this.address !== 'undefined') {toReturn['address'] = this.address;}
        if (typeof this.activatedProductIds !== 'undefined') {toReturn['activatedProductIds'] = this.activatedProductIds;}
        if (typeof this.url !== 'undefined') {toReturn['url'] = this.url;}
        return toReturn;
    }
}

export class GetLocationsRequestAccountGroups implements i.GetLocationsRequestAccountGroupsInterface {
    accountGroupIds: string[];

    static fromProto(proto: any): GetLocationsRequestAccountGroups {
        let m = new GetLocationsRequestAccountGroups();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetLocationsRequestAccountGroupsInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.accountGroupIds !== 'undefined') {toReturn['accountGroupIds'] = this.accountGroupIds;}
        return toReturn;
    }
}

export class AssociatedLocationIDs implements i.AssociatedLocationIDsInterface {
    accountGroupIds: string[];
    groupPaths: string[];

    static fromProto(proto: any): AssociatedLocationIDs {
        let m = new AssociatedLocationIDs();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.AssociatedLocationIDsInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.accountGroupIds !== 'undefined') {toReturn['accountGroupIds'] = this.accountGroupIds;}
        if (typeof this.groupPaths !== 'undefined') {toReturn['groupPaths'] = this.groupPaths;}
        return toReturn;
    }
}

export class Brand implements i.BrandInterface {
    name: string;
    pathNodes: string[];
    hasAccess: boolean;
    url: string;

    static fromProto(proto: any): Brand {
        let m = new Brand();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.BrandInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.name !== 'undefined') {toReturn['name'] = this.name;}
        if (typeof this.pathNodes !== 'undefined') {toReturn['pathNodes'] = this.pathNodes;}
        if (typeof this.hasAccess !== 'undefined') {toReturn['hasAccess'] = this.hasAccess;}
        if (typeof this.url !== 'undefined') {toReturn['url'] = this.url;}
        return toReturn;
    }
}

export class Branding implements i.BrandingInterface {
    theme: e.UITheme;
    logoUrl: string;
    partnerName: string;
    centerName: string;
    theming: Theming;
    cobrandingLogoUrl: string;
    marketName: string;
    darkModeLogoUrl: string;
    businessAppUiTheme: e.UITheme;
    exitLinkConfiguration: ExitLinkConfiguration;

    static fromProto(proto: any): Branding {
        let m = new Branding();
        m = Object.assign(m, proto);
        if (proto.theme) {m.theme = enumStringToValue<e.UITheme>(e.UITheme, proto.theme);}
        if (proto.theming) {m.theming = Theming.fromProto(proto.theming);}
        if (proto.businessAppUiTheme) {m.businessAppUiTheme = enumStringToValue<e.UITheme>(e.UITheme, proto.businessAppUiTheme);}
        if (proto.exitLinkConfiguration) {m.exitLinkConfiguration = ExitLinkConfiguration.fromProto(proto.exitLinkConfiguration);}
        return m;
    }

    constructor(kwargs?: i.BrandingInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.theme !== 'undefined') {toReturn['theme'] = this.theme;}
        if (typeof this.logoUrl !== 'undefined') {toReturn['logoUrl'] = this.logoUrl;}
        if (typeof this.partnerName !== 'undefined') {toReturn['partnerName'] = this.partnerName;}
        if (typeof this.centerName !== 'undefined') {toReturn['centerName'] = this.centerName;}
        if (typeof this.theming !== 'undefined' && this.theming !== null) {toReturn['theming'] = 'toApiJson' in this.theming ? (this.theming as any).toApiJson() : this.theming;}
        if (typeof this.cobrandingLogoUrl !== 'undefined') {toReturn['cobrandingLogoUrl'] = this.cobrandingLogoUrl;}
        if (typeof this.marketName !== 'undefined') {toReturn['marketName'] = this.marketName;}
        if (typeof this.darkModeLogoUrl !== 'undefined') {toReturn['darkModeLogoUrl'] = this.darkModeLogoUrl;}
        if (typeof this.businessAppUiTheme !== 'undefined') {toReturn['businessAppUiTheme'] = this.businessAppUiTheme;}
        if (typeof this.exitLinkConfiguration !== 'undefined' && this.exitLinkConfiguration !== null) {toReturn['exitLinkConfiguration'] = 'toApiJson' in this.exitLinkConfiguration ? (this.exitLinkConfiguration as any).toApiJson() : this.exitLinkConfiguration;}
        return toReturn;
    }
}

export class CenterNavigationItem implements i.CenterNavigationItemInterface {
    name: string;
    entryUrl: string;
    centerId: string;

    static fromProto(proto: any): CenterNavigationItem {
        let m = new CenterNavigationItem();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.CenterNavigationItemInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.name !== 'undefined') {toReturn['name'] = this.name;}
        if (typeof this.entryUrl !== 'undefined') {toReturn['entryUrl'] = this.entryUrl;}
        if (typeof this.centerId !== 'undefined') {toReturn['centerId'] = this.centerId;}
        return toReturn;
    }
}

export class ContactUsRequest implements i.ContactUsRequestInterface {
    accountGroupId: string;
    message: string;

    static fromProto(proto: any): ContactUsRequest {
        let m = new ContactUsRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.ContactUsRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.accountGroupId !== 'undefined') {toReturn['accountGroupId'] = this.accountGroupId;}
        if (typeof this.message !== 'undefined') {toReturn['message'] = this.message;}
        return toReturn;
    }
}

export class DropdownItem implements i.DropdownItemInterface {
    url: string;
    path: string;
    translationId: string;
    label: string;

    static fromProto(proto: any): DropdownItem {
        let m = new DropdownItem();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.DropdownItemInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.url !== 'undefined') {toReturn['url'] = this.url;}
        if (typeof this.path !== 'undefined') {toReturn['path'] = this.path;}
        if (typeof this.translationId !== 'undefined') {toReturn['translationId'] = this.translationId;}
        if (typeof this.label !== 'undefined') {toReturn['label'] = this.label;}
        return toReturn;
    }
}

export class ExitLinkConfiguration implements i.ExitLinkConfigurationInterface {
    exitLinkText: string;
    exitLinkUrl: string;

    static fromProto(proto: any): ExitLinkConfiguration {
        let m = new ExitLinkConfiguration();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.ExitLinkConfigurationInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.exitLinkText !== 'undefined') {toReturn['exitLinkText'] = this.exitLinkText;}
        if (typeof this.exitLinkUrl !== 'undefined') {toReturn['exitLinkUrl'] = this.exitLinkUrl;}
        return toReturn;
    }
}

export class GetDataRequest implements i.GetDataRequestInterface {
    partnerId: string;
    marketId: string;
    accountGroupId: string;
    signOutNextUrl: string;
    groupPath: string;

    static fromProto(proto: any): GetDataRequest {
        let m = new GetDataRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetDataRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.partnerId !== 'undefined') {toReturn['partnerId'] = this.partnerId;}
        if (typeof this.marketId !== 'undefined') {toReturn['marketId'] = this.marketId;}
        if (typeof this.accountGroupId !== 'undefined') {toReturn['accountGroupId'] = this.accountGroupId;}
        if (typeof this.signOutNextUrl !== 'undefined') {toReturn['signOutNextUrl'] = this.signOutNextUrl;}
        if (typeof this.groupPath !== 'undefined') {toReturn['groupPath'] = this.groupPath;}
        return toReturn;
    }
}

export class GetDataResponse implements i.GetDataResponseInterface {
    user: UserNavigationItem[];
    centers: CenterNavigationItem[];
    username: string;
    email: string;
    signOutUrl: string;
    theme: e.UITheme;
    language: string;
    theming: Theming;
    notificationsEnabled: boolean;
    userId: string;
    locationData: LocationData;
    impersonateeUsername: string;
    emailVerified: boolean;
    userSwitcherData: UserSwitcherData[];
    partnerName: string;
    businessAppUiTheme: e.UITheme;

    static fromProto(proto: any): GetDataResponse {
        let m = new GetDataResponse();
        m = Object.assign(m, proto);
        if (proto.user) {m.user = proto.user.map(UserNavigationItem.fromProto);}
        if (proto.centers) {m.centers = proto.centers.map(CenterNavigationItem.fromProto);}
        if (proto.theme) {m.theme = enumStringToValue<e.UITheme>(e.UITheme, proto.theme);}
        if (proto.theming) {m.theming = Theming.fromProto(proto.theming);}
        if (proto.locationData) {m.locationData = LocationData.fromProto(proto.locationData);}
        if (proto.userSwitcherData) {m.userSwitcherData = proto.userSwitcherData.map(UserSwitcherData.fromProto);}
        if (proto.businessAppUiTheme) {m.businessAppUiTheme = enumStringToValue<e.UITheme>(e.UITheme, proto.businessAppUiTheme);}
        return m;
    }

    constructor(kwargs?: i.GetDataResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.user !== 'undefined' && this.user !== null) {toReturn['user'] = 'toApiJson' in this.user ? (this.user as any).toApiJson() : this.user;}
        if (typeof this.centers !== 'undefined' && this.centers !== null) {toReturn['centers'] = 'toApiJson' in this.centers ? (this.centers as any).toApiJson() : this.centers;}
        if (typeof this.username !== 'undefined') {toReturn['username'] = this.username;}
        if (typeof this.email !== 'undefined') {toReturn['email'] = this.email;}
        if (typeof this.signOutUrl !== 'undefined') {toReturn['signOutUrl'] = this.signOutUrl;}
        if (typeof this.theme !== 'undefined') {toReturn['theme'] = this.theme;}
        if (typeof this.language !== 'undefined') {toReturn['language'] = this.language;}
        if (typeof this.theming !== 'undefined' && this.theming !== null) {toReturn['theming'] = 'toApiJson' in this.theming ? (this.theming as any).toApiJson() : this.theming;}
        if (typeof this.notificationsEnabled !== 'undefined') {toReturn['notificationsEnabled'] = this.notificationsEnabled;}
        if (typeof this.userId !== 'undefined') {toReturn['userId'] = this.userId;}
        if (typeof this.locationData !== 'undefined' && this.locationData !== null) {toReturn['locationData'] = 'toApiJson' in this.locationData ? (this.locationData as any).toApiJson() : this.locationData;}
        if (typeof this.impersonateeUsername !== 'undefined') {toReturn['impersonateeUsername'] = this.impersonateeUsername;}
        if (typeof this.emailVerified !== 'undefined') {toReturn['emailVerified'] = this.emailVerified;}
        if (typeof this.userSwitcherData !== 'undefined' && this.userSwitcherData !== null) {toReturn['userSwitcherData'] = 'toApiJson' in this.userSwitcherData ? (this.userSwitcherData as any).toApiJson() : this.userSwitcherData;}
        if (typeof this.partnerName !== 'undefined') {toReturn['partnerName'] = this.partnerName;}
        if (typeof this.businessAppUiTheme !== 'undefined') {toReturn['businessAppUiTheme'] = this.businessAppUiTheme;}
        return toReturn;
    }
}

export class GetDefaultLocationRequest implements i.GetDefaultLocationRequestInterface {
    partnerId: string;

    static fromProto(proto: any): GetDefaultLocationRequest {
        let m = new GetDefaultLocationRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetDefaultLocationRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.partnerId !== 'undefined') {toReturn['partnerId'] = this.partnerId;}
        return toReturn;
    }
}

export class GetDefaultLocationResponse implements i.GetDefaultLocationResponseInterface {
    accountGroupId: string;
    groupId: string;

    static fromProto(proto: any): GetDefaultLocationResponse {
        let m = new GetDefaultLocationResponse();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetDefaultLocationResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.accountGroupId !== 'undefined') {toReturn['accountGroupId'] = this.accountGroupId;}
        if (typeof this.groupId !== 'undefined') {toReturn['groupId'] = this.groupId;}
        return toReturn;
    }
}

export class GetLanguageRequest implements i.GetLanguageRequestInterface {

    static fromProto(proto: any): GetLanguageRequest {
        let m = new GetLanguageRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetLanguageRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        return toReturn;
    }
}

export class GetLanguageResponse implements i.GetLanguageResponseInterface {
    language: string;

    static fromProto(proto: any): GetLanguageResponse {
        let m = new GetLanguageResponse();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetLanguageResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.language !== 'undefined') {toReturn['language'] = this.language;}
        return toReturn;
    }
}

export class GetLocationsRequest implements i.GetLocationsRequestInterface {
    accountGroups: GetLocationsRequestAccountGroups;
    groups: GetLocationsRequestGroups;

    static fromProto(proto: any): GetLocationsRequest {
        let m = new GetLocationsRequest();
        m = Object.assign(m, proto);
        if (proto.accountGroups) {m.accountGroups = GetLocationsRequestAccountGroups.fromProto(proto.accountGroups);}
        if (proto.groups) {m.groups = GetLocationsRequestGroups.fromProto(proto.groups);}
        return m;
    }

    constructor(kwargs?: i.GetLocationsRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.accountGroups !== 'undefined' && this.accountGroups !== null) {toReturn['accountGroups'] = 'toApiJson' in this.accountGroups ? (this.accountGroups as any).toApiJson() : this.accountGroups;}
        if (typeof this.groups !== 'undefined' && this.groups !== null) {toReturn['groups'] = 'toApiJson' in this.groups ? (this.groups as any).toApiJson() : this.groups;}
        return toReturn;
    }
}

export class GetLocationsResponse implements i.GetLocationsResponseInterface {
    locations: Location[];

    static fromProto(proto: any): GetLocationsResponse {
        let m = new GetLocationsResponse();
        m = Object.assign(m, proto);
        if (proto.locations) {m.locations = proto.locations.map(Location.fromProto);}
        return m;
    }

    constructor(kwargs?: i.GetLocationsResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.locations !== 'undefined' && this.locations !== null) {toReturn['locations'] = 'toApiJson' in this.locations ? (this.locations as any).toApiJson() : this.locations;}
        return toReturn;
    }
}

export class GetNavigationDataRequest implements i.GetNavigationDataRequestInterface {
    accountGroupId: string;
    groupPath: string;
    partnerId: string;
    marketId: string;
    platformMode: e.PlatformMode;

    static fromProto(proto: any): GetNavigationDataRequest {
        let m = new GetNavigationDataRequest();
        m = Object.assign(m, proto);
        if (proto.platformMode) {m.platformMode = enumStringToValue<e.PlatformMode>(e.PlatformMode, proto.platformMode);}
        return m;
    }

    constructor(kwargs?: i.GetNavigationDataRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.accountGroupId !== 'undefined') {toReturn['accountGroupId'] = this.accountGroupId;}
        if (typeof this.groupPath !== 'undefined') {toReturn['groupPath'] = this.groupPath;}
        if (typeof this.partnerId !== 'undefined') {toReturn['partnerId'] = this.partnerId;}
        if (typeof this.marketId !== 'undefined') {toReturn['marketId'] = this.marketId;}
        if (typeof this.platformMode !== 'undefined') {toReturn['platformMode'] = this.platformMode;}
        return toReturn;
    }
}

export class GetNavigationDataResponse implements i.GetNavigationDataResponseInterface {
    branding: Branding;
    salesInfo: SalesInfo;
    pinnedItems: PinnedItem[];
    associatedLocationIds: AssociatedLocationIDs;
    defaultLocation: string;
    language: string;
    dropdownItems: DropdownItem[];
    currentBrandName: string;
    userView: e.UserViewType;
    retentionConfig: RetentionConfig;
    totalLocations: TotalLocations;
    userId: string;
    businessAppBranding: boolean;
    disableBusinessNav: boolean;
    navigationItems: SideNavigationItem[];
    disableProductSwitcher: boolean;

    static fromProto(proto: any): GetNavigationDataResponse {
        let m = new GetNavigationDataResponse();
        m = Object.assign(m, proto);
        if (proto.branding) {m.branding = Branding.fromProto(proto.branding);}
        if (proto.salesInfo) {m.salesInfo = SalesInfo.fromProto(proto.salesInfo);}
        if (proto.pinnedItems) {m.pinnedItems = proto.pinnedItems.map(PinnedItem.fromProto);}
        if (proto.associatedLocationIds) {m.associatedLocationIds = AssociatedLocationIDs.fromProto(proto.associatedLocationIds);}
        if (proto.dropdownItems) {m.dropdownItems = proto.dropdownItems.map(DropdownItem.fromProto);}
        if (proto.userView) {m.userView = enumStringToValue<e.UserViewType>(e.UserViewType, proto.userView);}
        if (proto.retentionConfig) {m.retentionConfig = RetentionConfig.fromProto(proto.retentionConfig);}
        if (proto.totalLocations) {m.totalLocations = TotalLocations.fromProto(proto.totalLocations);}
        if (proto.navigationItems) {m.navigationItems = proto.navigationItems.map(SideNavigationItem.fromProto);}
        return m;
    }

    constructor(kwargs?: i.GetNavigationDataResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.branding !== 'undefined' && this.branding !== null) {toReturn['branding'] = 'toApiJson' in this.branding ? (this.branding as any).toApiJson() : this.branding;}
        if (typeof this.salesInfo !== 'undefined' && this.salesInfo !== null) {toReturn['salesInfo'] = 'toApiJson' in this.salesInfo ? (this.salesInfo as any).toApiJson() : this.salesInfo;}
        if (typeof this.pinnedItems !== 'undefined' && this.pinnedItems !== null) {toReturn['pinnedItems'] = 'toApiJson' in this.pinnedItems ? (this.pinnedItems as any).toApiJson() : this.pinnedItems;}
        if (typeof this.associatedLocationIds !== 'undefined' && this.associatedLocationIds !== null) {toReturn['associatedLocationIds'] = 'toApiJson' in this.associatedLocationIds ? (this.associatedLocationIds as any).toApiJson() : this.associatedLocationIds;}
        if (typeof this.defaultLocation !== 'undefined') {toReturn['defaultLocation'] = this.defaultLocation;}
        if (typeof this.language !== 'undefined') {toReturn['language'] = this.language;}
        if (typeof this.dropdownItems !== 'undefined' && this.dropdownItems !== null) {toReturn['dropdownItems'] = 'toApiJson' in this.dropdownItems ? (this.dropdownItems as any).toApiJson() : this.dropdownItems;}
        if (typeof this.currentBrandName !== 'undefined') {toReturn['currentBrandName'] = this.currentBrandName;}
        if (typeof this.userView !== 'undefined') {toReturn['userView'] = this.userView;}
        if (typeof this.retentionConfig !== 'undefined' && this.retentionConfig !== null) {toReturn['retentionConfig'] = 'toApiJson' in this.retentionConfig ? (this.retentionConfig as any).toApiJson() : this.retentionConfig;}
        if (typeof this.totalLocations !== 'undefined' && this.totalLocations !== null) {toReturn['totalLocations'] = 'toApiJson' in this.totalLocations ? (this.totalLocations as any).toApiJson() : this.totalLocations;}
        if (typeof this.userId !== 'undefined') {toReturn['userId'] = this.userId;}
        if (typeof this.businessAppBranding !== 'undefined') {toReturn['businessAppBranding'] = this.businessAppBranding;}
        if (typeof this.disableBusinessNav !== 'undefined') {toReturn['disableBusinessNav'] = this.disableBusinessNav;}
        if (typeof this.navigationItems !== 'undefined' && this.navigationItems !== null) {toReturn['navigationItems'] = 'toApiJson' in this.navigationItems ? (this.navigationItems as any).toApiJson() : this.navigationItems;}
        if (typeof this.disableProductSwitcher !== 'undefined') {toReturn['disableProductSwitcher'] = this.disableProductSwitcher;}
        return toReturn;
    }
}

export class GetPinsRequest implements i.GetPinsRequestInterface {
    identifier: string;

    static fromProto(proto: any): GetPinsRequest {
        let m = new GetPinsRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetPinsRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.identifier !== 'undefined') {toReturn['identifier'] = this.identifier;}
        return toReturn;
    }
}

export class GetPinsResponse implements i.GetPinsResponseInterface {
    items: PinnedItem[];

    static fromProto(proto: any): GetPinsResponse {
        let m = new GetPinsResponse();
        m = Object.assign(m, proto);
        if (proto.items) {m.items = proto.items.map(PinnedItem.fromProto);}
        return m;
    }

    constructor(kwargs?: i.GetPinsResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.items !== 'undefined' && this.items !== null) {toReturn['items'] = 'toApiJson' in this.items ? (this.items as any).toApiJson() : this.items;}
        return toReturn;
    }
}

export class GetSalesInfoRequest implements i.GetSalesInfoRequestInterface {
    accountGroupId: string;
    groupPath: string;

    static fromProto(proto: any): GetSalesInfoRequest {
        let m = new GetSalesInfoRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetSalesInfoRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.accountGroupId !== 'undefined') {toReturn['accountGroupId'] = this.accountGroupId;}
        if (typeof this.groupPath !== 'undefined') {toReturn['groupPath'] = this.groupPath;}
        return toReturn;
    }
}

export class GetSalesInfoResponse implements i.GetSalesInfoResponseInterface {
    salesInfo: SalesInfo;

    static fromProto(proto: any): GetSalesInfoResponse {
        let m = new GetSalesInfoResponse();
        m = Object.assign(m, proto);
        if (proto.salesInfo) {m.salesInfo = SalesInfo.fromProto(proto.salesInfo);}
        return m;
    }

    constructor(kwargs?: i.GetSalesInfoResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.salesInfo !== 'undefined' && this.salesInfo !== null) {toReturn['salesInfo'] = 'toApiJson' in this.salesInfo ? (this.salesInfo as any).toApiJson() : this.salesInfo;}
        return toReturn;
    }
}

export class GetLocationsRequestGroups implements i.GetLocationsRequestGroupsInterface {
    groupPaths: string[];

    static fromProto(proto: any): GetLocationsRequestGroups {
        let m = new GetLocationsRequestGroups();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.GetLocationsRequestGroupsInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.groupPaths !== 'undefined') {toReturn['groupPaths'] = this.groupPaths;}
        return toReturn;
    }
}

export class ListElevatedLocationsRequest implements i.ListElevatedLocationsRequestInterface {
    partnerId: string;
    cursor: string;
    pageSize: number;
    search: string;
    accountGroups: boolean;
    brands: boolean;

    static fromProto(proto: any): ListElevatedLocationsRequest {
        let m = new ListElevatedLocationsRequest();
        m = Object.assign(m, proto);
        if (proto.pageSize) {m.pageSize = parseInt(proto.pageSize, 10);}
        return m;
    }

    constructor(kwargs?: i.ListElevatedLocationsRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.partnerId !== 'undefined') {toReturn['partnerId'] = this.partnerId;}
        if (typeof this.cursor !== 'undefined') {toReturn['cursor'] = this.cursor;}
        if (typeof this.pageSize !== 'undefined') {toReturn['pageSize'] = this.pageSize;}
        if (typeof this.search !== 'undefined') {toReturn['search'] = this.search;}
        if (typeof this.accountGroups !== 'undefined') {toReturn['accountGroups'] = this.accountGroups;}
        if (typeof this.brands !== 'undefined') {toReturn['brands'] = this.brands;}
        return toReturn;
    }
}

export class ListElevatedLocationsResponse implements i.ListElevatedLocationsResponseInterface {
    locations: Location[];
    cursor: string;
    hasMore: boolean;

    static fromProto(proto: any): ListElevatedLocationsResponse {
        let m = new ListElevatedLocationsResponse();
        m = Object.assign(m, proto);
        if (proto.locations) {m.locations = proto.locations.map(Location.fromProto);}
        return m;
    }

    constructor(kwargs?: i.ListElevatedLocationsResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.locations !== 'undefined' && this.locations !== null) {toReturn['locations'] = 'toApiJson' in this.locations ? (this.locations as any).toApiJson() : this.locations;}
        if (typeof this.cursor !== 'undefined') {toReturn['cursor'] = this.cursor;}
        if (typeof this.hasMore !== 'undefined') {toReturn['hasMore'] = this.hasMore;}
        return toReturn;
    }
}

export class ListLocationsRequest implements i.ListLocationsRequestInterface {
    partnerId: string;
    cursor: string;
    pageSize: number;
    search: string;
    includeAccountGroups: boolean;
    includeBrands: boolean;

    static fromProto(proto: any): ListLocationsRequest {
        let m = new ListLocationsRequest();
        m = Object.assign(m, proto);
        if (proto.pageSize) {m.pageSize = parseInt(proto.pageSize, 10);}
        return m;
    }

    constructor(kwargs?: i.ListLocationsRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.partnerId !== 'undefined') {toReturn['partnerId'] = this.partnerId;}
        if (typeof this.cursor !== 'undefined') {toReturn['cursor'] = this.cursor;}
        if (typeof this.pageSize !== 'undefined') {toReturn['pageSize'] = this.pageSize;}
        if (typeof this.search !== 'undefined') {toReturn['search'] = this.search;}
        if (typeof this.includeAccountGroups !== 'undefined') {toReturn['includeAccountGroups'] = this.includeAccountGroups;}
        if (typeof this.includeBrands !== 'undefined') {toReturn['includeBrands'] = this.includeBrands;}
        return toReturn;
    }
}

export class ListLocationsResponse implements i.ListLocationsResponseInterface {
    locations: Location[];
    cursor: string;
    hasMore: boolean;

    static fromProto(proto: any): ListLocationsResponse {
        let m = new ListLocationsResponse();
        m = Object.assign(m, proto);
        if (proto.locations) {m.locations = proto.locations.map(Location.fromProto);}
        return m;
    }

    constructor(kwargs?: i.ListLocationsResponseInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.locations !== 'undefined' && this.locations !== null) {toReturn['locations'] = 'toApiJson' in this.locations ? (this.locations as any).toApiJson() : this.locations;}
        if (typeof this.cursor !== 'undefined') {toReturn['cursor'] = this.cursor;}
        if (typeof this.hasMore !== 'undefined') {toReturn['hasMore'] = this.hasMore;}
        return toReturn;
    }
}

export class Location implements i.LocationInterface {
    accountGroup: AccountGroup;
    brand: Brand;

    static fromProto(proto: any): Location {
        let m = new Location();
        m = Object.assign(m, proto);
        if (proto.accountGroup) {m.accountGroup = AccountGroup.fromProto(proto.accountGroup);}
        if (proto.brand) {m.brand = Brand.fromProto(proto.brand);}
        return m;
    }

    constructor(kwargs?: i.LocationInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.accountGroup !== 'undefined' && this.accountGroup !== null) {toReturn['accountGroup'] = 'toApiJson' in this.accountGroup ? (this.accountGroup as any).toApiJson() : this.accountGroup;}
        if (typeof this.brand !== 'undefined' && this.brand !== null) {toReturn['brand'] = 'toApiJson' in this.brand ? (this.brand as any).toApiJson() : this.brand;}
        return toReturn;
    }
}

export class LocationData implements i.LocationDataInterface {
    businessName: string;
    address: string;

    static fromProto(proto: any): LocationData {
        let m = new LocationData();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.LocationDataInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.businessName !== 'undefined') {toReturn['businessName'] = this.businessName;}
        if (typeof this.address !== 'undefined') {toReturn['address'] = this.address;}
        return toReturn;
    }
}

export class PinnedItem implements i.PinnedItemInterface {
    navigationId: string;

    static fromProto(proto: any): PinnedItem {
        let m = new PinnedItem();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.PinnedItemInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.navigationId !== 'undefined') {toReturn['navigationId'] = this.navigationId;}
        return toReturn;
    }
}

export class RetentionConfig implements i.RetentionConfigInterface {
    cancellationNotificationEmail: string;

    static fromProto(proto: any): RetentionConfig {
        let m = new RetentionConfig();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.RetentionConfigInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.cancellationNotificationEmail !== 'undefined') {toReturn['cancellationNotificationEmail'] = this.cancellationNotificationEmail;}
        return toReturn;
    }
}

export class SalesContact implements i.SalesContactInterface {
    salesPersonId: string;
    email: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    photoUrlSecure: string;
    jobTitle: string;
    country: string;
    meetingBookingUrl: string;

    static fromProto(proto: any): SalesContact {
        let m = new SalesContact();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.SalesContactInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.salesPersonId !== 'undefined') {toReturn['salesPersonId'] = this.salesPersonId;}
        if (typeof this.email !== 'undefined') {toReturn['email'] = this.email;}
        if (typeof this.firstName !== 'undefined') {toReturn['firstName'] = this.firstName;}
        if (typeof this.lastName !== 'undefined') {toReturn['lastName'] = this.lastName;}
        if (typeof this.phoneNumber !== 'undefined') {toReturn['phoneNumber'] = this.phoneNumber;}
        if (typeof this.photoUrlSecure !== 'undefined') {toReturn['photoUrlSecure'] = this.photoUrlSecure;}
        if (typeof this.jobTitle !== 'undefined') {toReturn['jobTitle'] = this.jobTitle;}
        if (typeof this.country !== 'undefined') {toReturn['country'] = this.country;}
        if (typeof this.meetingBookingUrl !== 'undefined') {toReturn['meetingBookingUrl'] = this.meetingBookingUrl;}
        return toReturn;
    }
}

export class SalesInfo implements i.SalesInfoInterface {
    marketName: string;
    salesContact: SalesContact;

    static fromProto(proto: any): SalesInfo {
        let m = new SalesInfo();
        m = Object.assign(m, proto);
        if (proto.salesContact) {m.salesContact = SalesContact.fromProto(proto.salesContact);}
        return m;
    }

    constructor(kwargs?: i.SalesInfoInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.marketName !== 'undefined') {toReturn['marketName'] = this.marketName;}
        if (typeof this.salesContact !== 'undefined' && this.salesContact !== null) {toReturn['salesContact'] = 'toApiJson' in this.salesContact ? (this.salesContact as any).toApiJson() : this.salesContact;}
        return toReturn;
    }
}

export class SetDefaultLocationRequest implements i.SetDefaultLocationRequestInterface {
    partnerId: string;
    accountGroupId: string;
    groupId: string;

    static fromProto(proto: any): SetDefaultLocationRequest {
        let m = new SetDefaultLocationRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.SetDefaultLocationRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.partnerId !== 'undefined') {toReturn['partnerId'] = this.partnerId;}
        if (typeof this.accountGroupId !== 'undefined') {toReturn['accountGroupId'] = this.accountGroupId;}
        if (typeof this.groupId !== 'undefined') {toReturn['groupId'] = this.groupId;}
        return toReturn;
    }
}

export class SetLanguageRequest implements i.SetLanguageRequestInterface {
    language: string;

    static fromProto(proto: any): SetLanguageRequest {
        let m = new SetLanguageRequest();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.SetLanguageRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.language !== 'undefined') {toReturn['language'] = this.language;}
        return toReturn;
    }
}

export class SetPinsRequest implements i.SetPinsRequestInterface {
    identifier: string;
    items: PinnedItem[];

    static fromProto(proto: any): SetPinsRequest {
        let m = new SetPinsRequest();
        m = Object.assign(m, proto);
        if (proto.items) {m.items = proto.items.map(PinnedItem.fromProto);}
        return m;
    }

    constructor(kwargs?: i.SetPinsRequestInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.identifier !== 'undefined') {toReturn['identifier'] = this.identifier;}
        if (typeof this.items !== 'undefined' && this.items !== null) {toReturn['items'] = 'toApiJson' in this.items ? (this.items as any).toApiJson() : this.items;}
        return toReturn;
    }
}

export class SideNavigationContainer implements i.SideNavigationContainerInterface {
    translationId: string;
    sideNavigationItems: SideNavigationItem[];
    icon: string;
    logoUrl: string;
    label: string;
    showIcon: boolean;
    chipContent: string;
    url: string;
    pinnable: boolean;
    navigationId: string;

    static fromProto(proto: any): SideNavigationContainer {
        let m = new SideNavigationContainer();
        m = Object.assign(m, proto);
        if (proto.sideNavigationItems) {m.sideNavigationItems = proto.sideNavigationItems.map(SideNavigationItem.fromProto);}
        return m;
    }

    constructor(kwargs?: i.SideNavigationContainerInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.translationId !== 'undefined') {toReturn['translationId'] = this.translationId;}
        if (typeof this.sideNavigationItems !== 'undefined' && this.sideNavigationItems !== null) {toReturn['sideNavigationItems'] = 'toApiJson' in this.sideNavigationItems ? (this.sideNavigationItems as any).toApiJson() : this.sideNavigationItems;}
        if (typeof this.icon !== 'undefined') {toReturn['icon'] = this.icon;}
        if (typeof this.logoUrl !== 'undefined') {toReturn['logoUrl'] = this.logoUrl;}
        if (typeof this.label !== 'undefined') {toReturn['label'] = this.label;}
        if (typeof this.showIcon !== 'undefined') {toReturn['showIcon'] = this.showIcon;}
        if (typeof this.chipContent !== 'undefined') {toReturn['chipContent'] = this.chipContent;}
        if (typeof this.url !== 'undefined') {toReturn['url'] = this.url;}
        if (typeof this.pinnable !== 'undefined') {toReturn['pinnable'] = this.pinnable;}
        if (typeof this.navigationId !== 'undefined') {toReturn['navigationId'] = this.navigationId;}
        return toReturn;
    }
}

export class SideNavigationItem implements i.SideNavigationItemInterface {
    sideNavigationSection: SideNavigationSection;
    sideNavigationContainer: SideNavigationContainer;
    sideNavigationLink: SideNavigationLink;

    static fromProto(proto: any): SideNavigationItem {
        let m = new SideNavigationItem();
        m = Object.assign(m, proto);
        if (proto.sideNavigationSection) {m.sideNavigationSection = SideNavigationSection.fromProto(proto.sideNavigationSection);}
        if (proto.sideNavigationContainer) {m.sideNavigationContainer = SideNavigationContainer.fromProto(proto.sideNavigationContainer);}
        if (proto.sideNavigationLink) {m.sideNavigationLink = SideNavigationLink.fromProto(proto.sideNavigationLink);}
        return m;
    }

    constructor(kwargs?: i.SideNavigationItemInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.sideNavigationSection !== 'undefined' && this.sideNavigationSection !== null) {toReturn['sideNavigationSection'] = 'toApiJson' in this.sideNavigationSection ? (this.sideNavigationSection as any).toApiJson() : this.sideNavigationSection;}
        if (typeof this.sideNavigationContainer !== 'undefined' && this.sideNavigationContainer !== null) {toReturn['sideNavigationContainer'] = 'toApiJson' in this.sideNavigationContainer ? (this.sideNavigationContainer as any).toApiJson() : this.sideNavigationContainer;}
        if (typeof this.sideNavigationLink !== 'undefined' && this.sideNavigationLink !== null) {toReturn['sideNavigationLink'] = 'toApiJson' in this.sideNavigationLink ? (this.sideNavigationLink as any).toApiJson() : this.sideNavigationLink;}
        return toReturn;
    }
}

export class SideNavigationLink implements i.SideNavigationLinkInterface {
    navigationId: string;
    url: string;
    path: string;
    serviceProviderId: string;
    logoUrl: string;
    icon: string;
    translationId: string;
    external: boolean;
    label: string;
    showIcon: boolean;
    pinnable: boolean;
    chipContent: string;
    isTrial: boolean;
    userRequired: boolean;
    openInNewTab: boolean;
    subLinks: SideNavigationLink[];
    launchUrl: string;
    descriptionTranslationId: string;

    static fromProto(proto: any): SideNavigationLink {
        let m = new SideNavigationLink();
        m = Object.assign(m, proto);
        if (proto.subLinks) {m.subLinks = proto.subLinks.map(SideNavigationLink.fromProto);}
        return m;
    }

    constructor(kwargs?: i.SideNavigationLinkInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.navigationId !== 'undefined') {toReturn['navigationId'] = this.navigationId;}
        if (typeof this.url !== 'undefined') {toReturn['url'] = this.url;}
        if (typeof this.path !== 'undefined') {toReturn['path'] = this.path;}
        if (typeof this.serviceProviderId !== 'undefined') {toReturn['serviceProviderId'] = this.serviceProviderId;}
        if (typeof this.logoUrl !== 'undefined') {toReturn['logoUrl'] = this.logoUrl;}
        if (typeof this.icon !== 'undefined') {toReturn['icon'] = this.icon;}
        if (typeof this.translationId !== 'undefined') {toReturn['translationId'] = this.translationId;}
        if (typeof this.external !== 'undefined') {toReturn['external'] = this.external;}
        if (typeof this.label !== 'undefined') {toReturn['label'] = this.label;}
        if (typeof this.showIcon !== 'undefined') {toReturn['showIcon'] = this.showIcon;}
        if (typeof this.pinnable !== 'undefined') {toReturn['pinnable'] = this.pinnable;}
        if (typeof this.chipContent !== 'undefined') {toReturn['chipContent'] = this.chipContent;}
        if (typeof this.isTrial !== 'undefined') {toReturn['isTrial'] = this.isTrial;}
        if (typeof this.userRequired !== 'undefined') {toReturn['userRequired'] = this.userRequired;}
        if (typeof this.openInNewTab !== 'undefined') {toReturn['openInNewTab'] = this.openInNewTab;}
        if (typeof this.subLinks !== 'undefined' && this.subLinks !== null) {toReturn['subLinks'] = 'toApiJson' in this.subLinks ? (this.subLinks as any).toApiJson() : this.subLinks;}
        if (typeof this.launchUrl !== 'undefined') {toReturn['launchUrl'] = this.launchUrl;}
        if (typeof this.descriptionTranslationId !== 'undefined') {toReturn['descriptionTranslationId'] = this.descriptionTranslationId;}
        return toReturn;
    }
}

export class SideNavigationSection implements i.SideNavigationSectionInterface {
    translationId: string;
    sideNavigationItems: SideNavigationItem[];
    label: string;
    chipContent: string;

    static fromProto(proto: any): SideNavigationSection {
        let m = new SideNavigationSection();
        m = Object.assign(m, proto);
        if (proto.sideNavigationItems) {m.sideNavigationItems = proto.sideNavigationItems.map(SideNavigationItem.fromProto);}
        return m;
    }

    constructor(kwargs?: i.SideNavigationSectionInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.translationId !== 'undefined') {toReturn['translationId'] = this.translationId;}
        if (typeof this.sideNavigationItems !== 'undefined' && this.sideNavigationItems !== null) {toReturn['sideNavigationItems'] = 'toApiJson' in this.sideNavigationItems ? (this.sideNavigationItems as any).toApiJson() : this.sideNavigationItems;}
        if (typeof this.label !== 'undefined') {toReturn['label'] = this.label;}
        if (typeof this.chipContent !== 'undefined') {toReturn['chipContent'] = this.chipContent;}
        return toReturn;
    }
}

export class Theming implements i.ThemingInterface {
    primaryColor: string;
    primaryHoverColor: string;
    primaryActiveColor: string;
    secondaryColor: string;
    secondaryHoverColor: string;
    secondaryActiveColor: string;
    fontColor: string;
    fontDisabledColor: string;
    accentsColor: string;
    accentsActiveColor: string;
    focusColor: string;
    borderColor: string;

    static fromProto(proto: any): Theming {
        let m = new Theming();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.ThemingInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.primaryColor !== 'undefined') {toReturn['primaryColor'] = this.primaryColor;}
        if (typeof this.primaryHoverColor !== 'undefined') {toReturn['primaryHoverColor'] = this.primaryHoverColor;}
        if (typeof this.primaryActiveColor !== 'undefined') {toReturn['primaryActiveColor'] = this.primaryActiveColor;}
        if (typeof this.secondaryColor !== 'undefined') {toReturn['secondaryColor'] = this.secondaryColor;}
        if (typeof this.secondaryHoverColor !== 'undefined') {toReturn['secondaryHoverColor'] = this.secondaryHoverColor;}
        if (typeof this.secondaryActiveColor !== 'undefined') {toReturn['secondaryActiveColor'] = this.secondaryActiveColor;}
        if (typeof this.fontColor !== 'undefined') {toReturn['fontColor'] = this.fontColor;}
        if (typeof this.fontDisabledColor !== 'undefined') {toReturn['fontDisabledColor'] = this.fontDisabledColor;}
        if (typeof this.accentsColor !== 'undefined') {toReturn['accentsColor'] = this.accentsColor;}
        if (typeof this.accentsActiveColor !== 'undefined') {toReturn['accentsActiveColor'] = this.accentsActiveColor;}
        if (typeof this.focusColor !== 'undefined') {toReturn['focusColor'] = this.focusColor;}
        if (typeof this.borderColor !== 'undefined') {toReturn['borderColor'] = this.borderColor;}
        return toReturn;
    }
}

export class TotalLocations implements i.TotalLocationsInterface {
    accounts: number;
    brands: number;

    static fromProto(proto: any): TotalLocations {
        let m = new TotalLocations();
        m = Object.assign(m, proto);
        if (proto.accounts) {m.accounts = parseInt(proto.accounts, 10);}
        if (proto.brands) {m.brands = parseInt(proto.brands, 10);}
        return m;
    }

    constructor(kwargs?: i.TotalLocationsInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.accounts !== 'undefined') {toReturn['accounts'] = this.accounts;}
        if (typeof this.brands !== 'undefined') {toReturn['brands'] = this.brands;}
        return toReturn;
    }
}

export class UserNavigationItem implements i.UserNavigationItemInterface {
    text: string;
    url: string;
    routeId: string;

    static fromProto(proto: any): UserNavigationItem {
        let m = new UserNavigationItem();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.UserNavigationItemInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.text !== 'undefined') {toReturn['text'] = this.text;}
        if (typeof this.url !== 'undefined') {toReturn['url'] = this.url;}
        if (typeof this.routeId !== 'undefined') {toReturn['routeId'] = this.routeId;}
        return toReturn;
    }
}

export class UserSwitcherData implements i.UserSwitcherDataInterface {
    userId: string;
    partnerId: string;
    partnerName: string;
    entryUrl: string;

    static fromProto(proto: any): UserSwitcherData {
        let m = new UserSwitcherData();
        m = Object.assign(m, proto);
        return m;
    }

    constructor(kwargs?: i.UserSwitcherDataInterface) {
        if (!kwargs) {
            return;
        }
        Object.assign(this, kwargs);
    }

    toApiJson(): object {
        const toReturn: {
            [key: string]: any;
        } = {};
        
        if (typeof this.userId !== 'undefined') {toReturn['userId'] = this.userId;}
        if (typeof this.partnerId !== 'undefined') {toReturn['partnerId'] = this.partnerId;}
        if (typeof this.partnerName !== 'undefined') {toReturn['partnerName'] = this.partnerName;}
        if (typeof this.entryUrl !== 'undefined') {toReturn['entryUrl'] = this.entryUrl;}
        return toReturn;
    }
}

