// *********************************
// Code generated by sdkgen
// DO NOT EDIT!.
//
// API Service.
// *********************************
import {Injectable} from '@angular/core';
import {
        GetLanguageRequest,
        GetLanguageResponse,
        SetLanguageRequest,
} from './objects/';
import {
        GetLanguageRequestInterface,
        GetLanguageResponseInterface,
        SetLanguageRequestInterface,
} from './interfaces/';
import {HttpHeaders, HttpClient, HttpResponse} from '@angular/common/http';
import {inject} from '@angular/core';
import {HostService} from '../_generated/host.service';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';

@Injectable({providedIn: 'root'})
export class LanguagesApiService {
    private readonly hostService = inject(HostService);
    private readonly http = inject(HttpClient);
    private _host = this.hostService.hostWithScheme;

    private apiOptions(): {headers: HttpHeaders, withCredentials: boolean} {
        return {
            headers: new HttpHeaders({
                'Content-Type': 'application/json'
            }),
            withCredentials: true
        };
    }

    setLanguage(r: SetLanguageRequest | SetLanguageRequestInterface): Observable<HttpResponse<null>> {
        const request = ((<SetLanguageRequest>r).toApiJson) ? (<SetLanguageRequest>r) : new SetLanguageRequest(r);
        return this.http.post<null>(this._host + "/atlas.v1.Languages/SetLanguage", request.toApiJson(), {...this.apiOptions(), observe: 'response'});
    }
    getLanguage(r: GetLanguageRequest | GetLanguageRequestInterface): Observable<GetLanguageResponse> {
        const request = ((<GetLanguageRequest>r).toApiJson) ? (<GetLanguageRequest>r) : new GetLanguageRequest(r);
        return this.http.post<GetLanguageResponseInterface>(this._host + "/atlas.v1.Languages/GetLanguage", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetLanguageResponse.fromProto(resp))
            );
    }
    
}
