// *********************************
// Code generated by sdkgen
// DO NOT EDIT!.
//
// API Service.
// *********************************
import {Injectable} from '@angular/core';
import {
        GetPinsRequest,
        GetPinsResponse,
        SetPinsRequest,
} from './objects/';
import {
        GetPinsRequestInterface,
        GetPinsResponseInterface,
        SetPinsRequestInterface,
} from './interfaces/';
import {HttpHeaders, HttpClient, HttpResponse} from '@angular/common/http';
import {inject} from '@angular/core';
import {HostService} from '../_generated/host.service';
import {Observable} from 'rxjs';
import {map} from 'rxjs/operators';

@Injectable({providedIn: 'root'})
export class PinsApiService {
    private readonly hostService = inject(HostService);
    private readonly http = inject(HttpClient);
    private _host = this.hostService.hostWithScheme;

    private apiOptions(): {headers: HttpHeaders, withCredentials: boolean} {
        return {
            headers: new HttpHeaders({
                'Content-Type': 'application/json'
            }),
            withCredentials: true
        };
    }

    setPins(r: SetPinsRequest | SetPinsRequestInterface): Observable<HttpResponse<null>> {
        const request = ((<SetPinsRequest>r).toApiJson) ? (<SetPinsRequest>r) : new SetPinsRequest(r);
        return this.http.post<null>(this._host + "/atlas.v1.Pins/SetPins", request.toApiJson(), {...this.apiOptions(), observe: 'response'});
    }
    getPins(r: GetPinsRequest | GetPinsRequestInterface): Observable<GetPinsResponse> {
        const request = ((<GetPinsRequest>r).toApiJson) ? (<GetPinsRequest>r) : new GetPinsRequest(r);
        return this.http.post<GetPinsResponseInterface>(this._host + "/atlas.v1.Pins/GetPins", request.toApiJson(), this.apiOptions())
            .pipe(
                map(resp => GetPinsResponse.fromProto(resp))
            );
    }
    
}
