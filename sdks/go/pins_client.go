package atlas

import (
	"context"
	"github.com/vendasta/gosdks/config"
	"github.com/vendasta/gosdks/vax"
	"github.com/vendasta/gosdks/verrors"
	"google.golang.org/grpc"
)

type PinsClient struct {
	*PinsGRPCClient
}

// NewPinsClient creates a new instance of the PinsClient
// Deprecated: Use NewPinsClientWithOptions instead
func NewPinsClient(ctx context.Context, e config.Env, enableAuth bool, options ...grpc.DialOption) (*PinsClient, error) {
	env, err := vax.GetSDKEnvironment(e)
	if err != nil {
		return nil, err
	}
	c, ok := environmentConfig[env]
	if !ok {
		return nil, verrors.New(verrors.InvalidArgument, "must provide a valid environment")
	}

	grpcClient, err := NewPinsGRPCClient(ctx, c.Host, c.Secure, c<PERSON>, enableAuth, options...)
	if err != nil {
		return nil, err
	}

	return &PinsClient{
		PinsGRPCClient: grpcClient,
	}, nil
}

// NewPinsClientWithOptions creates a new instance of the PinsClient allowing passing in vax options
func NewPinsClientWithOptions(ctx context.Context, e config.Env, enableAuth bool, options ...vax.ClientOption) (*PinsClient, error) {
	env, err := vax.GetSDKEnvironment(e)
	if err != nil {
		return nil, err
	}
	c, ok := environmentConfig[env]
	if !ok {
		return nil, verrors.New(verrors.InvalidArgument, "must provide a valid environment")
	}

	grpcClient, err := NewPinsGRPCClientWithOptions(ctx, c.Host, c.Secure, c.Scope, enableAuth, options...)
	if err != nil {
		return nil, err
	}

	return &PinsClient{
		PinsGRPCClient: grpcClient,
	}, nil
}
