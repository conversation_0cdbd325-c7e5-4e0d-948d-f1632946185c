package atlas

import (
	"context"
	"github.com/vendasta/gosdks/config"
	"github.com/vendasta/gosdks/vax"
	"github.com/vendasta/gosdks/verrors"
	"google.golang.org/grpc"
)

type AtlasClient struct {
	*AtlasGRPCClient
}

var environmentConfig = map[config.Env]vax.ClientEnvConfig{}

// NewAtlasClient creates a new instance of the AtlasClient
// Deprecated: Use NewAtlasClientWithOptions instead
func NewAtlasClient(ctx context.Context, e config.Env, enableAuth bool, options ...grpc.DialOption) (*AtlasClient, error) {
	env, err := vax.GetSDKEnvironment(e)
	if err != nil {
		return nil, err
	}
	c, ok := environmentConfig[env]
	if !ok {
		return nil, verrors.New(verrors.InvalidArgument, "must provide a valid environment")
	}

	grpcClient, err := NewAtlasGRPCClient(ctx, c.<PERSON>, c<PERSON><PERSON>, c<PERSON>, enableAuth, options...)
	if err != nil {
		return nil, err
	}

	return &AtlasClient{
		AtlasGRPCClient: grpcClient,
	}, nil
}

// NewAtlasClientWithOptions creates a new instance of the AtlasClient allowing passing in vax options
func NewAtlasClientWithOptions(ctx context.Context, e config.Env, enableAuth bool, options ...vax.ClientOption) (*AtlasClient, error) {
	env, err := vax.GetSDKEnvironment(e)
	if err != nil {
		return nil, err
	}
	c, ok := environmentConfig[env]
	if !ok {
		return nil, verrors.New(verrors.InvalidArgument, "must provide a valid environment")
	}

	grpcClient, err := NewAtlasGRPCClientWithOptions(ctx, c.Host, c.Secure, c.Scope, enableAuth, options...)
	if err != nil {
		return nil, err
	}

	return &AtlasClient{
		AtlasGRPCClient: grpcClient,
	}, nil
}
