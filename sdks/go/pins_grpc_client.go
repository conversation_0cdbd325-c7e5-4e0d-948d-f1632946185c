// Code generated by sdkgen
// Generated on 2024-08-06 14:07:03.438191714 +0000 UTC using container gcr.io/repcore-prod/sdkgen:latest
// DO NOT EDIT!
package atlas

import (
	"context"
	google_protobuf "github.com/golang/protobuf/ptypes/empty"
	pb "github.com/vendasta/generated-protos-go/atlas/v1"
	"github.com/vendasta/gosdks/vax"
	"google.golang.org/grpc"
)

// PinsClientInterface The service for managing pinned products and navbar items
type PinsClientInterface interface {
	// SetPins SetPins will set the pins for a user in an business or brand
	SetPins(ctx context.Context, in *pb.SetPinsRequest, opts ...grpc.CallOption) (*google_protobuf.Empty, error)
	// GetPins GetPins will return the pins for a business or brand
	GetPins(ctx context.Context, in *pb.GetPinsRequest, opts ...grpc.CallOption) (*pb.GetPinsResponse, error)
}

// PinsGRPCClient a GRPC client for the Pins service.
type PinsGRPCClient struct {
	client pb.PinsClient
}

// NewPinsGRPCClient returns a new Pins instance.
// Deprecated: Use NewPinsGRPCClientWithOptions instead
func NewPinsGRPCClient(ctx context.Context, address string, useTLS bool, scope string, enableAuth bool, dialOptions ...grpc.DialOption) (*PinsGRPCClient, error) {
	conn, err := vax.NewGRPCConnection(ctx, address, useTLS, scope, enableAuth, dialOptions...)
	if err != nil {
		return nil, err
	}
	return &PinsGRPCClient{client: pb.NewPinsClient(conn)}, nil
}

// NewPinsGRPCClientWithOptions returns a new Pins instance allowing you to pass in vax options
// Deprecated: Use NewPinsGRPCClientV3 instead
func NewPinsGRPCClientWithOptions(ctx context.Context, address string, useTLS bool, scope string, enableAuth bool, clientOptions ...vax.ClientOption) (*PinsGRPCClient, error) {
	client, _, err := NewPinsGRPCClientV3(ctx, address, useTLS, scope, enableAuth, clientOptions...)
	return client, err
}

// NewPinsGRPCClientV3 returns a new Pins instance allowing you to pass in vax options as well as returning a closer function
// that you should call to clean up the GRPC connection this makes after you are done using this sdk
func NewPinsGRPCClientV3(ctx context.Context, address string, useTLS bool, scope string, enableAuth bool, clientOptions ...vax.ClientOption) (*PinsGRPCClient, func() error, error) {
	opts := []vax.ClientOption{
		vax.WithAddress(address),
		vax.UseTLS(useTLS),
		vax.WithAudience(scope),
		vax.EnableToken(enableAuth),
	}

	for _, clientOption := range clientOptions {
		opts = append(opts, clientOption)
	}

	conn, err := vax.NewGRPCConnectionWithOptions(ctx, opts...)
	if err != nil {
		return nil, nil, err
	}

	return &PinsGRPCClient{client: pb.NewPinsClient(conn)}, conn.Close, nil
}

func (c *PinsGRPCClient) buildCallOptions(opts []grpc.CallOption) []grpc.CallOption {
	if opts == nil {
		opts = []grpc.CallOption{}
	}
	return append(opts, grpc.FailFast(false))
}

// SetPins SetPins will set the pins for a user in an business or brand
func (c *PinsGRPCClient) SetPins(ctx context.Context, in *pb.SetPinsRequest, opts ...grpc.CallOption) (*google_protobuf.Empty, error) {
	var out *google_protobuf.Empty
	err := vax.Invoke(ctx, func(ctx context.Context, settings vax.CallSettings) error {
		_, err := c.client.SetPins(ctx, in, c.buildCallOptions(opts)...)
		return err
	}, defaultRetryCallOptions)
	return out, err
}

// GetPins GetPins will return the pins for a business or brand
func (c *PinsGRPCClient) GetPins(ctx context.Context, in *pb.GetPinsRequest, opts ...grpc.CallOption) (*pb.GetPinsResponse, error) {
	var out *pb.GetPinsResponse
	err := vax.Invoke(ctx, func(ctx context.Context, settings vax.CallSettings) error {
		var err error
		out, err = c.client.GetPins(ctx, in, c.buildCallOptions(opts)...)
		return err
	}, defaultRetryCallOptions)
	return out, err
}
