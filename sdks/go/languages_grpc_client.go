// Code generated by sdkgen
// Generated on 2024-08-06 14:07:03.438191714 +0000 UTC using container gcr.io/repcore-prod/sdkgen:latest
// DO NOT EDIT!
package atlas

import (
	"context"
	google_protobuf "github.com/golang/protobuf/ptypes/empty"
	pb "github.com/vendasta/generated-protos-go/atlas/v1"
	"github.com/vendasta/gosdks/vax"
	"google.golang.org/grpc"
)

// LanguagesClientInterface The service for managing your preferred language
type LanguagesClientInterface interface {
	// SetLanguage SetLanguage will remember your currently selected language
	SetLanguage(ctx context.Context, in *pb.SetLanguageRequest, opts ...grpc.CallOption) (*google_protobuf.Empty, error)
	// GetLanguage GetLanguage will return your currently selected language
	GetLanguage(ctx context.Context, in *pb.GetLanguageRequest, opts ...grpc.CallOption) (*pb.GetLanguageResponse, error)
}

// LanguagesGRPCClient a GRPC client for the Languages service.
type LanguagesGRPCClient struct {
	client pb.LanguagesClient
}

// NewLanguagesGRPCClient returns a new Languages instance.
// Deprecated: Use NewLanguagesGRPCClientWithOptions instead
func NewLanguagesGRPCClient(ctx context.Context, address string, useTLS bool, scope string, enableAuth bool, dialOptions ...grpc.DialOption) (*LanguagesGRPCClient, error) {
	conn, err := vax.NewGRPCConnection(ctx, address, useTLS, scope, enableAuth, dialOptions...)
	if err != nil {
		return nil, err
	}
	return &LanguagesGRPCClient{client: pb.NewLanguagesClient(conn)}, nil
}

// NewLanguagesGRPCClientWithOptions returns a new Languages instance allowing you to pass in vax options
// Deprecated: Use NewLanguagesGRPCClientV3 instead
func NewLanguagesGRPCClientWithOptions(ctx context.Context, address string, useTLS bool, scope string, enableAuth bool, clientOptions ...vax.ClientOption) (*LanguagesGRPCClient, error) {
	client, _, err := NewLanguagesGRPCClientV3(ctx, address, useTLS, scope, enableAuth, clientOptions...)
	return client, err
}

// NewLanguagesGRPCClientV3 returns a new Languages instance allowing you to pass in vax options as well as returning a closer function
// that you should call to clean up the GRPC connection this makes after you are done using this sdk
func NewLanguagesGRPCClientV3(ctx context.Context, address string, useTLS bool, scope string, enableAuth bool, clientOptions ...vax.ClientOption) (*LanguagesGRPCClient, func() error, error) {
	opts := []vax.ClientOption{
		vax.WithAddress(address),
		vax.UseTLS(useTLS),
		vax.WithAudience(scope),
		vax.EnableToken(enableAuth),
	}

	for _, clientOption := range clientOptions {
		opts = append(opts, clientOption)
	}

	conn, err := vax.NewGRPCConnectionWithOptions(ctx, opts...)
	if err != nil {
		return nil, nil, err
	}

	return &LanguagesGRPCClient{client: pb.NewLanguagesClient(conn)}, conn.Close, nil
}

func (c *LanguagesGRPCClient) buildCallOptions(opts []grpc.CallOption) []grpc.CallOption {
	if opts == nil {
		opts = []grpc.CallOption{}
	}
	return append(opts, grpc.FailFast(false))
}

// SetLanguage SetLanguage will remember your currently selected language
func (c *LanguagesGRPCClient) SetLanguage(ctx context.Context, in *pb.SetLanguageRequest, opts ...grpc.CallOption) (*google_protobuf.Empty, error) {
	var out *google_protobuf.Empty
	err := vax.Invoke(ctx, func(ctx context.Context, settings vax.CallSettings) error {
		_, err := c.client.SetLanguage(ctx, in, c.buildCallOptions(opts)...)
		return err
	}, defaultRetryCallOptions)
	return out, err
}

// GetLanguage GetLanguage will return your currently selected language
func (c *LanguagesGRPCClient) GetLanguage(ctx context.Context, in *pb.GetLanguageRequest, opts ...grpc.CallOption) (*pb.GetLanguageResponse, error) {
	var out *pb.GetLanguageResponse
	err := vax.Invoke(ctx, func(ctx context.Context, settings vax.CallSettings) error {
		var err error
		out, err = c.client.GetLanguage(ctx, in, c.buildCallOptions(opts)...)
		return err
	}, defaultRetryCallOptions)
	return out, err
}
