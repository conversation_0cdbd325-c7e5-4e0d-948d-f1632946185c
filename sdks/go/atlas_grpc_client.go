// Code generated by sdkgen
// Generated on 2024-08-06 14:07:03.438191714 +0000 UTC using container gcr.io/repcore-prod/sdkgen:latest
// DO NOT EDIT!
package atlas

import (
	"context"
	google_protobuf "github.com/golang/protobuf/ptypes/empty"
	pb "github.com/vendasta/generated-protos-go/atlas/v1"
	"github.com/vendasta/gosdks/vax"
	"google.golang.org/grpc"
)

// AtlasClientInterface The Atlas service
type AtlasClientInterface interface {
	// GetData GetData gets the data that populates the Atlas navbar
	GetData(ctx context.Context, in *pb.GetDataRequest, opts ...grpc.CallOption) (*pb.GetDataResponse, error)
	// GetNavigationData GetNavigationData gets the navigation data that populates the sidenav
	GetNavigationData(ctx context.Context, in *pb.GetNavigationDataRequest, opts ...grpc.CallOption) (*pb.GetNavigationDataResponse, error)
	// GetSalesInfo GetSalesInfo gets the sales info that populates the sidenav
	GetSalesInfo(ctx context.Context, in *pb.GetSalesInfoRequest, opts ...grpc.CallOption) (*pb.GetSalesInfoResponse, error)
	// GetLocations Deprecated (use ListLocations): GetLocations returns associated account groups or groups
	GetLocations(ctx context.Context, in *pb.GetLocationsRequest, opts ...grpc.CallOption) (*pb.GetLocationsResponse, error)
	// ListElevatedLocations Deprecated (use ListLocations): ListElevatedLocations returns a paged list of locations available to an elevated user.
	ListElevatedLocations(ctx context.Context, in *pb.ListElevatedLocationsRequest, opts ...grpc.CallOption) (*pb.ListElevatedLocationsResponse, error)
	// ListLocations ListLocations returns a paged list of locations available to a user.
	ListLocations(ctx context.Context, in *pb.ListLocationsRequest, opts ...grpc.CallOption) (*pb.ListLocationsResponse, error)
	// ContactUs ContactUs notifies the platform that the user is interested in contacting the partner
	ContactUs(ctx context.Context, in *pb.ContactUsRequest, opts ...grpc.CallOption) (*google_protobuf.Empty, error)
	// SetDefaultLocation SetDefaultLocationRequest sets a user's default location for the given partner.
	SetDefaultLocation(ctx context.Context, in *pb.SetDefaultLocationRequest, opts ...grpc.CallOption) (*google_protobuf.Empty, error)
	// GetDefaultLocation GetDefaultLocation` gets a user's default location that they have set for the given partner.`
	GetDefaultLocation(ctx context.Context, in *pb.GetDefaultLocationRequest, opts ...grpc.CallOption) (*pb.GetDefaultLocationResponse, error)
}

// AtlasGRPCClient a GRPC client for the Atlas service.
type AtlasGRPCClient struct {
	client pb.AtlasClient
}

// NewAtlasGRPCClient returns a new Atlas instance.
// Deprecated: Use NewAtlasGRPCClientWithOptions instead
func NewAtlasGRPCClient(ctx context.Context, address string, useTLS bool, scope string, enableAuth bool, dialOptions ...grpc.DialOption) (*AtlasGRPCClient, error) {
	conn, err := vax.NewGRPCConnection(ctx, address, useTLS, scope, enableAuth, dialOptions...)
	if err != nil {
		return nil, err
	}
	return &AtlasGRPCClient{client: pb.NewAtlasClient(conn)}, nil
}

// NewAtlasGRPCClientWithOptions returns a new Atlas instance allowing you to pass in vax options
// Deprecated: Use NewAtlasGRPCClientV3 instead
func NewAtlasGRPCClientWithOptions(ctx context.Context, address string, useTLS bool, scope string, enableAuth bool, clientOptions ...vax.ClientOption) (*AtlasGRPCClient, error) {
	client, _, err := NewAtlasGRPCClientV3(ctx, address, useTLS, scope, enableAuth, clientOptions...)
	return client, err
}

// NewAtlasGRPCClientV3 returns a new Atlas instance allowing you to pass in vax options as well as returning a closer function
// that you should call to clean up the GRPC connection this makes after you are done using this sdk
func NewAtlasGRPCClientV3(ctx context.Context, address string, useTLS bool, scope string, enableAuth bool, clientOptions ...vax.ClientOption) (*AtlasGRPCClient, func() error, error) {
	opts := []vax.ClientOption{
		vax.WithAddress(address),
		vax.UseTLS(useTLS),
		vax.WithAudience(scope),
		vax.EnableToken(enableAuth),
	}

	for _, clientOption := range clientOptions {
		opts = append(opts, clientOption)
	}

	conn, err := vax.NewGRPCConnectionWithOptions(ctx, opts...)
	if err != nil {
		return nil, nil, err
	}

	return &AtlasGRPCClient{client: pb.NewAtlasClient(conn)}, conn.Close, nil
}

func (c *AtlasGRPCClient) buildCallOptions(opts []grpc.CallOption) []grpc.CallOption {
	if opts == nil {
		opts = []grpc.CallOption{}
	}
	return append(opts, grpc.FailFast(false))
}

// GetData GetData gets the data that populates the Atlas navbar
func (c *AtlasGRPCClient) GetData(ctx context.Context, in *pb.GetDataRequest, opts ...grpc.CallOption) (*pb.GetDataResponse, error) {
	var out *pb.GetDataResponse
	err := vax.Invoke(ctx, func(ctx context.Context, settings vax.CallSettings) error {
		var err error
		out, err = c.client.GetData(ctx, in, c.buildCallOptions(opts)...)
		return err
	}, defaultRetryCallOptions)
	return out, err
}

// GetNavigationData GetNavigationData gets the navigation data that populates the sidenav
func (c *AtlasGRPCClient) GetNavigationData(ctx context.Context, in *pb.GetNavigationDataRequest, opts ...grpc.CallOption) (*pb.GetNavigationDataResponse, error) {
	var out *pb.GetNavigationDataResponse
	err := vax.Invoke(ctx, func(ctx context.Context, settings vax.CallSettings) error {
		var err error
		out, err = c.client.GetNavigationData(ctx, in, c.buildCallOptions(opts)...)
		return err
	}, defaultRetryCallOptions)
	return out, err
}

// GetSalesInfo GetSalesInfo gets the sales info that populates the sidenav
func (c *AtlasGRPCClient) GetSalesInfo(ctx context.Context, in *pb.GetSalesInfoRequest, opts ...grpc.CallOption) (*pb.GetSalesInfoResponse, error) {
	var out *pb.GetSalesInfoResponse
	err := vax.Invoke(ctx, func(ctx context.Context, settings vax.CallSettings) error {
		var err error
		out, err = c.client.GetSalesInfo(ctx, in, c.buildCallOptions(opts)...)
		return err
	}, defaultRetryCallOptions)
	return out, err
}

// GetLocations Deprecated (use ListLocations): GetLocations returns associated account groups or groups
func (c *AtlasGRPCClient) GetLocations(ctx context.Context, in *pb.GetLocationsRequest, opts ...grpc.CallOption) (*pb.GetLocationsResponse, error) {
	var out *pb.GetLocationsResponse
	err := vax.Invoke(ctx, func(ctx context.Context, settings vax.CallSettings) error {
		var err error
		out, err = c.client.GetLocations(ctx, in, c.buildCallOptions(opts)...)
		return err
	}, defaultRetryCallOptions)
	return out, err
}

// ListElevatedLocations Deprecated (use ListLocations): ListElevatedLocations returns a paged list of locations available to an elevated user.
func (c *AtlasGRPCClient) ListElevatedLocations(ctx context.Context, in *pb.ListElevatedLocationsRequest, opts ...grpc.CallOption) (*pb.ListElevatedLocationsResponse, error) {
	var out *pb.ListElevatedLocationsResponse
	err := vax.Invoke(ctx, func(ctx context.Context, settings vax.CallSettings) error {
		var err error
		out, err = c.client.ListElevatedLocations(ctx, in, c.buildCallOptions(opts)...)
		return err
	}, defaultRetryCallOptions)
	return out, err
}

// ListLocations ListLocations returns a paged list of locations available to a user.
func (c *AtlasGRPCClient) ListLocations(ctx context.Context, in *pb.ListLocationsRequest, opts ...grpc.CallOption) (*pb.ListLocationsResponse, error) {
	var out *pb.ListLocationsResponse
	err := vax.Invoke(ctx, func(ctx context.Context, settings vax.CallSettings) error {
		var err error
		out, err = c.client.ListLocations(ctx, in, c.buildCallOptions(opts)...)
		return err
	}, defaultRetryCallOptions)
	return out, err
}

// ContactUs ContactUs notifies the platform that the user is interested in contacting the partner
func (c *AtlasGRPCClient) ContactUs(ctx context.Context, in *pb.ContactUsRequest, opts ...grpc.CallOption) (*google_protobuf.Empty, error) {
	var out *google_protobuf.Empty
	err := vax.Invoke(ctx, func(ctx context.Context, settings vax.CallSettings) error {
		_, err := c.client.ContactUs(ctx, in, c.buildCallOptions(opts)...)
		return err
	}, defaultRetryCallOptions)
	return out, err
}

// SetDefaultLocation SetDefaultLocationRequest sets a user's default location for the given partner.
func (c *AtlasGRPCClient) SetDefaultLocation(ctx context.Context, in *pb.SetDefaultLocationRequest, opts ...grpc.CallOption) (*google_protobuf.Empty, error) {
	var out *google_protobuf.Empty
	err := vax.Invoke(ctx, func(ctx context.Context, settings vax.CallSettings) error {
		_, err := c.client.SetDefaultLocation(ctx, in, c.buildCallOptions(opts)...)
		return err
	}, defaultRetryCallOptions)
	return out, err
}

// GetDefaultLocation GetDefaultLocation` gets a user's default location that they have set for the given partner.`
func (c *AtlasGRPCClient) GetDefaultLocation(ctx context.Context, in *pb.GetDefaultLocationRequest, opts ...grpc.CallOption) (*pb.GetDefaultLocationResponse, error) {
	var out *pb.GetDefaultLocationResponse
	err := vax.Invoke(ctx, func(ctx context.Context, settings vax.CallSettings) error {
		var err error
		out, err = c.client.GetDefaultLocation(ctx, in, c.buildCallOptions(opts)...)
		return err
	}, defaultRetryCallOptions)
	return out, err
}
