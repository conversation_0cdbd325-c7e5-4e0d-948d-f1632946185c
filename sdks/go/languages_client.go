package atlas

import (
	"context"
	"github.com/vendasta/gosdks/config"
	"github.com/vendasta/gosdks/vax"
	"github.com/vendasta/gosdks/verrors"
	"google.golang.org/grpc"
)

type LanguagesClient struct {
	*LanguagesGRPCClient
}

// NewLanguagesClient creates a new instance of the LanguagesClient
// Deprecated: Use NewLanguagesClientWithOptions instead
func NewLanguagesClient(ctx context.Context, e config.Env, enableAuth bool, options ...grpc.DialOption) (*LanguagesClient, error) {
	env, err := vax.GetSDKEnvironment(e)
	if err != nil {
		return nil, err
	}
	c, ok := environmentConfig[env]
	if !ok {
		return nil, verrors.New(verrors.InvalidArgument, "must provide a valid environment")
	}

	grpcClient, err := NewLanguagesGRPCClient(ctx, c.Host, c.Se<PERSON>, c<PERSON>, enableAuth, options...)
	if err != nil {
		return nil, err
	}

	return &LanguagesClient{
		LanguagesGRPCClient: grpcClient,
	}, nil
}

// NewLanguagesClientWithOptions creates a new instance of the LanguagesClient allowing passing in vax options
func NewLanguagesClientWithOptions(ctx context.Context, e config.Env, enableAuth bool, options ...vax.ClientOption) (*LanguagesClient, error) {
	env, err := vax.GetSDKEnvironment(e)
	if err != nil {
		return nil, err
	}
	c, ok := environmentConfig[env]
	if !ok {
		return nil, verrors.New(verrors.InvalidArgument, "must provide a valid environment")
	}

	grpcClient, err := NewLanguagesGRPCClientWithOptions(ctx, c.Host, c.Secure, c.Scope, enableAuth, options...)
	if err != nil {
		return nil, err
	}

	return &LanguagesClient{
		LanguagesGRPCClient: grpcClient,
	}, nil
}
